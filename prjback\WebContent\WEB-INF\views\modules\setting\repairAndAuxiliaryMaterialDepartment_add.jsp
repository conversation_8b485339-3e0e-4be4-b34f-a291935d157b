<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>补辅部门添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/setting/repairAndAuxiliaryMaterialDepartment/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-inline layui-col-md5">
                         <label class="layui-form-label" ><span class="star">*</span>年度:</label>
	                     <div class="layui-input-block">
	                     	 <input type="text" class="layui-input" id="year"  name="year" value=""   required=""  lay-verify="required"/>
                         </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>部门:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="department" name="department" value=""/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>部门属性:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="departmentAttributes" name="departmentAttributes" lay-verify="required"  required>
                                <option value="1">直接部门</option>
                                <option value="2">辅助部门</option>
                                <option value="3">共通部门</option>
                                <option value="4">特殊部门</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>类别:</label>
                        <div class="layui-input-block">
                         	<select class="layui-select" id="category" name="category" lay-verify="required"  required>
                                <option value="1">MW</option>
                                <option value="2">UF</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">分配部门:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="assignDepartment" name="assignDepartment" value=""/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">分配比例:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ratio" name="ratio" value=""/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/setting/repairAndAuxiliaryMaterialDepartment_add.js?time=1"></script>
</myfooter>
</body>
</html>
