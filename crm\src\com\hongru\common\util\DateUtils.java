package com.hongru.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 
* 类名称：DateUtils   
* 类描述：DateUtils工具类：提供一些日期通用处理的方法      
* 创建人：hongru   
* 创建时间：2017年3月31日 下午2:27:09   
*
 */
public class DateUtils{
	
	private static Logger logger = LoggerFactory.getLogger(DateUtils.class);
	
	/** 毫秒 */
	public static final long MS = 1;
	/** 每秒钟的毫秒数 */
	public static final long SECOND_MS = MS * 1000;
	/** 每分钟的毫秒数 */
	public static final long MINUTE_MS = SECOND_MS * 60;
	/** 每小时的毫秒数 */
	public static final long HOUR_MS = MINUTE_MS * 60;
	/** 每天的毫秒数 */
	public static final long DAY_MS = HOUR_MS * 24;
	/** 每月的毫秒数 */
	public static final long MONTH_MS = DAY_MS * 30;
	/** 每年的毫秒数 */
	public static final long YEAR_MS = MONTH_MS * 12;

	/** 标准日期（不含时间）格式化器 */
	private  static final String NORM_DATE_FORMAT = new String("yyyy-MM-dd");
	/** 标准日期时间格式化器 */
	private  static final String NORM_DATETIME_FORMAT = new String("yyyy-MM-dd HH:mm:ss");
	
	private DateUtils() {
		throw new AssertionError();
	}
	
	/**
	 * 当前时间，格式 yyyy-MM-dd HH:mm:ss
	 * @return 当前时间的标准形式字符串
	 */
	public static String now() {
		return formatDateTime(new Date());
	}
	
	/**
	 * 格式 yyyy-MM-dd HH:mm:ss
	 * @param date 被格式化的日期
	 * @return 格式化后的日期
	 */
	public static String formatDateTime(Date date) {
		return new SimpleDateFormat(NORM_DATETIME_FORMAT).format(date);
	}

	/**
	 * 当前日期，格式 yyyy-MM-dd
	 * @return 当前日期的标准形式字符串
	 */
	public static String today() {
		return formatDate(new Date());
	}

	/**
	 * 根据特定格式格式化日期
	 * @param date 被格式化的日期
	 * @param format 格式
	 * @return 格式化后的字符串
	 */
	public static String format(Date date, String format) {
		return new SimpleDateFormat(format).format(date);
	}

	/**
	 * 格式 yyyy-MM-dd 
	 * @param date 被格式化的日期
	 * @return 格式化后的字符串
	 */
	public static String formatDate(Date date) {
		return new SimpleDateFormat(NORM_DATE_FORMAT).format(date);
	}

	/**
	 * 将特定格式的日期转换为Date对象
	 * @param dateString 特定格式的日期
	 * @param format 格式，例如yyyy-MM-dd
	 * @return 日期对象
	 */
	public static Date parse(String dateString, String format) {
		try {
			return (new SimpleDateFormat(format)).parse(dateString);
		} catch (ParseException e) {
			logger.error("Parse" + dateString + " with format " + format + " error!", e);
		}
		return null;
	}

	/**
	 * 格式yyyy-MM-dd HH:mm:ss
	 * @param dateString 标准形式的时间字符串
	 * @return 日期对象
	 */
	public static Date parseDateTime(String dateString) {
		try {
			return new SimpleDateFormat(NORM_DATETIME_FORMAT).parse(dateString);
		} catch (ParseException e) {
			logger.error("Parse " + dateString + " with format "
					+ new SimpleDateFormat(NORM_DATETIME_FORMAT).toPattern() + " error!", e);
		}
		return null;
	}

	/**
	 * 格式yyyy-MM-dd
	 * @param dateString  标准形式的日期字符串
	 * @return  标准形式的日期字符串
	 */
	public static Date parseDate(String dateString) {
		try {
			return new SimpleDateFormat(NORM_DATE_FORMAT).parse(dateString);
		} catch (ParseException e) {
			logger.error("Parse " + dateString + " with format " + new SimpleDateFormat(NORM_DATE_FORMAT).toPattern()
					+ " error!", e);
		}
		return null;
	}

	/**
	 * 获取指定日期偏移指定时间后的时间
	 * @param date 基准日期
	 * @param calendarField 偏移的粒度大小（小时、天、月等）使用Calendar中的常数
	 * @param offsite 偏移量，正数为向后偏移，负数为向前偏移
	 * @return 偏移后的日期
	 * 
	 */
	public static Date getOffsiteDate(Date date, int calendarField, int offsite) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(calendarField, offsite);
		return cal.getTime();
	}

	/**
	 * 判断两个日期相差的时长<br/>(列：1年前7月25日)
	 * 返回 minuend - subtrahend 的差
	 * @param subtrahend 减数日期
	 * @param minuend 被减数日期
	 * @return 日期差
	 */
	public static String dateDiff(Date subtrahend, Date minuend) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(subtrahend);
		long diff = minuend.getTime() - subtrahend.getTime();
		if (diff <= HOUR_MS)
			return diff / MINUTE_MS + "分钟前";
		else if (diff <= DAY_MS)
			return diff / HOUR_MS + "小时" + (diff % HOUR_MS / MINUTE_MS) + "分钟前";
		else if (diff <= DAY_MS * 2)
			return "昨天" + calendar.get(Calendar.HOUR_OF_DAY) + "点" + calendar.get(Calendar.MINUTE) + "分";
		else if (diff <= DAY_MS * 3)
			return "前天" + calendar.get(Calendar.HOUR_OF_DAY) + "点" + calendar.get(Calendar.MINUTE) + "分";
		else if (diff <= MONTH_MS)
			return diff / DAY_MS + "天前" + calendar.get(Calendar.HOUR_OF_DAY) + "点" + calendar.get(Calendar.MINUTE)
					+ "分";
		else if (diff <= YEAR_MS)
			return diff / MONTH_MS + "个月" + (diff % MONTH_MS) / DAY_MS + "天前" + calendar.get(Calendar.HOUR_OF_DAY) + "点"
					+ calendar.get(Calendar.MINUTE) + "分";
		else
			return diff / YEAR_MS + "年前" + (calendar.get(Calendar.MONTH) + 1) + "月" + calendar.get(Calendar.DATE) + "日";
	}
	
	/**
	 * 距离截止日期还有多长时间
	 * @param date
	 * @return
	 */
	public static String fromDeadline(Date date) {
		long deadline = date.getTime();
		long now = new Date().getTime();
		long remain = deadline - now;
		if (remain <= HOUR_MS)
			return "只剩下" + remain / MINUTE_MS + "分钟";
		else if (remain <= DAY_MS)
			return "只剩下" + remain / HOUR_MS + "小时" + (remain % HOUR_MS / MINUTE_MS) + "分钟";
		else {
			long day = remain / DAY_MS;
			long hour = remain % DAY_MS / HOUR_MS;
			long minute = remain % DAY_MS % HOUR_MS / MINUTE_MS;
			return "只剩下" + day + "天" + hour + "小时" + minute + "分钟";
		}
	}
	
	/**
	 * 获取当前时间的毫米数
	 * @return
	 * <AUTHOR> 
	 * @create 2018年2月23日
	 */
	public static int getCurrentTime(){
		return (int) (System.currentTimeMillis()/1000);
	}
	
	
	   /**
     * This method generates a string representation of a date/time
     * in the format you specify on input
     *
     * @param aMask the date pattern the string is in
     * @param strDate a string representation of a date
     * @return a converted Date object
     * @see java.text.SimpleDateFormat
     * @throws ParseException
     */
    public static final Date convertStringToDate(String aMask, String strDate)
      throws ParseException {
        SimpleDateFormat df = null;
        Date date = null;
        df = new SimpleDateFormat(aMask);

        if (logger.isDebugEnabled()) {
        	logger.debug("converting '" + strDate + "' to date with mask '"
                      + aMask + "'");
        }

        try {
            date = df.parse(strDate);
        } catch (ParseException pe) {
            //log.error("ParseException: " + pe);
            throw new ParseException(pe.getMessage(), pe.getErrorOffset());
        }

        return (date);
    }
    
    /**
   	 * 将页面上传过来的字符串型时间转化成long类型
   	 * 
   	 * @param dateInString
   	 *            字符串型时间
   	 * @return
   	 * datecode == "yyyy-MM-dd" or datecode == "cc HH:mm:ss"
   	 */
   	public static Integer convertDateInStringToInt(String dateInString, String datecode) {
   		if (dateInString == null || dateInString.equals(""))
   			return null;
   		SimpleDateFormat dateFormat = new SimpleDateFormat(datecode);
   		try {
   			return new Integer((int) (dateFormat.parse(dateInString).getTime()/1000));
   		} catch (ParseException parseException) {
   			return new Integer((int) (new Date().getTime()/1000));
   		}
   	}
   	
   	
   	
	/**
	 * 根据时戳获取月份
	 * @param timeStemp
	 * @return
	 * <AUTHOR>
	 * @create 2018年1月16日
	 */
	public static final int getMONTH(int timeStemp) {
		Calendar cal = convertLongToCalendar(timeStemp);
	    int month = cal.get(Calendar.MONTH) + 1;
	    return month;
	}

	/**
	 * 获取月份
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2022/12/29 11:57
	 * @return int
	*/
	public static final int getMONTH() {
		Calendar cal = Calendar.getInstance();
		int month = cal.get(Calendar.MONTH) + 1;
		return month;
	}
	
	/**
	 * 获得当前日
	 * @return
	 * <AUTHOR>
	 * @create 2015年7月10日 上午10:42:42
	 */
	public static final int getDAY() {
		Calendar cal = Calendar.getInstance();
		int day = cal.get(Calendar.DAY_OF_MONTH);
	    return day;
	}
	
	/**
	 * 根据时戳获取日 
	 * @param timeStemp
	 * @return
	 * <AUTHOR>
	 * @create 2018年1月16日
	 */
	public static final int getDAY(int timeStemp) {
		Calendar cal = convertLongToCalendar(timeStemp);
		int day = cal.get(Calendar.DAY_OF_MONTH);
	    return day;
	}
	
	/**
	 * 获得当前周
	 * @return
	 * <AUTHOR>
	 * @create 2015年7月10日 上午10:42:42
	 */
	public static final int getWEEK() {
		Calendar cal = Calendar.getInstance();
		cal.setFirstDayOfWeek(Calendar.SUNDAY);
		int week = cal.get(Calendar.WEEK_OF_YEAR);
	    return week;
	}
	
	/**
	 * 根据时间戳获取周几
	 * @param timeStemp
	 * @return
	 * <AUTHOR>
	 * @create 2018年1月16日
	 */
	public static final int getWEEK(int timeStemp) {
		Calendar cal = convertLongToCalendar(timeStemp);
		int week = cal.get(Calendar.DAY_OF_WEEK);  
	    return week;
	}
	
	/**
	 * 获得当前年
	 * @return
	 * <AUTHOR>
	 * @create 2015年7月10日 上午10:42:42
	 */
	public static final int getYEAR() {
		Calendar cal = Calendar.getInstance();
		int year = cal.get(Calendar.YEAR);
	    return year;
	}
	
	/**
	 * 根据时戳获取年
	 * @param timeStemp
	 * @return
	 * <AUTHOR>
	 * @create 2018年1月16日
	 */
	public static final int getYEAR(int timeStemp) {
		Calendar cal = convertLongToCalendar(timeStemp);
		int year = cal.get(Calendar.YEAR);
	    return year;
	}
	
	/** 
	* 计算两个时戳(秒)间隔天数
	*/  
    public static int daysBetween(int time1, int time2) throws ParseException{  
       long between_days=(time2-time1)/(3600*24);  
       return Integer.parseInt(String.valueOf(between_days));     
    }  
    
	public static Calendar convertLongToCalendar(int currentTimestamp)
	{
		//将毫秒数转化为日期
		Date date = new Date(currentTimestamp*1000L);
		Calendar cal = Calendar.getInstance(Locale.CHINA);
		cal.setTime(date);
		//System.out.println("初始："+new SimpleDateFormat("yyyy-MM-dd E HH:mm:ss").format(cal.getTime()));
		return cal;
	}
	
	/**
	 * 把int类型时间戳转换为字符串 
	 * @param timeStemp
	 * @param datecode(yyyy-MM-dd  yyyy-MM-dd HH....)
	 * @return
	 * <AUTHOR>
	 * @create 2016年11月8日
	 */
	public static String convertIntToDateString(int timeStemp, String datecode) {
		Calendar c = convertLongToCalendar(timeStemp);
    	SimpleDateFormat sdf =  new SimpleDateFormat(datecode);  
		return sdf.format(c.getTime());
	}

	/**
	 * 得到给定日期（long型的时间戳） 的零点和24点的时戳
	 * <p>
	 * @param currentTimestamp:当前时戳
	 * @return 该日期的零点和24点的时戳数组
	 * <AUTHOR>
	 */
	@SuppressWarnings("static-access")
	public static int[] getOneDayTimestamp(int currentTimestamp) {
		Calendar cal = convertLongToCalendar(currentTimestamp);
		int[] timeArray = new int[2];

		int dates = cal.get(cal.DATE);
		int months = cal.get(cal.MONTH);
		int years = cal.get(cal.YEAR);
		cal.set(years, months, dates, 0, 0 ,0);
		long startTime = cal.getTime().getTime();
		//System.out.println(new SimpleDateFormat("yyyy-MM-dd E HH:mm:ss").format(cal.getTime()));
		cal.set(years, months, dates, 23, 59, 59);
		long endTime = cal.getTime().getTime();
		//System.out.println(new SimpleDateFormat("yyyy-MM-dd E HH:mm:ss").format(cal.getTime()));
		timeArray[0] = (int)(startTime/1000);
		timeArray[1] = (int)(endTime/1000);

		return timeArray;
	}

	/**
	* 传入两个时间，获取两个时间包含的所有月份
	* startDateStr：2016-02
	* endDateStr：2019-12
	* @throws
	* <AUTHOR>
	* @create 2023/9/21 13:26
	* @return
	*/
	public static List<String> getAllMonth(String startDateStr,String endDateStr) throws ParseException {
		Date startDate = new SimpleDateFormat("yyyy-MM").parse(startDateStr);
		Date endDate = new SimpleDateFormat("yyyy-MM").parse(endDateStr);

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);
		// 获取开始年份和开始月份
		int startYear = calendar.get(Calendar.YEAR);
		int startMonth = calendar.get(Calendar.MONTH);
		// 获取结束年份和结束月份
		calendar.setTime(endDate);
		int endYear = calendar.get(Calendar.YEAR);
		int endMonth = calendar.get(Calendar.MONTH);
		//包含的月份
		List<String> list = new ArrayList<String>();
		for (int i = startYear; i <= endYear; i++) {
			String date = "";
			if (startYear == endYear) {
				for (int j = startMonth; j <= endMonth; j++) {
					if (j < 9) {
						date = i + "-0" + (j + 1);
					} else {
						date = i + "-" + (j + 1);
					}
					list.add(date);
				}
			} else {
				if (i == startYear) {
					for (int j = startMonth; j < 12; j++) {
						if (j < 9) {
							date = i + "-0" + (j + 1);
						} else {
							date = i + "-" + (j + 1);
						}
						list.add(date);
					}
				} else if (i == endYear) {
					for (int j = 0; j <= endMonth; j++) {
						if (j < 9) {
							date = i + "-0" + (j + 1);
						} else {
							date = i + "-" + (j + 1);
						}
						list.add(date);
					}
				} else {
					for (int j = 0; j < 12; j++) {
						if (j < 9) {
							date = i + "-0" + (j + 1);
						} else {
							date = i + "-" + (j + 1);
						}
						list.add(date);
					}
				}
			}
		}
		return list;
	}

	   /**
     * 获取两个日期相差的月数
     * @param timeStartStr
     * @param timeEndStr
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/04 09:43
     * @return int
     * @throws ParseException 
     */
    public static int getMonthDiff(String timeStartStr, String timeEndStr) throws ParseException {
    	
    	DateFormat fmt =new SimpleDateFormat("yyyy-MM");
    	Date date1 = fmt.parse(timeStartStr);
    	Date date2 = fmt.parse(timeEndStr);
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(date1);
        c2.setTime(date2);
        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);

        // 获取年的差值
        int yearInterval = year1 - year2;
        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if (month1 < month2 || month1 == month2) {
            yearInterval--;
        }
        // 获取月数差值
        int monthInterval = (month1 + 11) - month2;
        monthInterval %= 12;
        int monthsDiff = Math.abs(yearInterval * 12 + monthInterval);
    	return monthsDiff;
    }
    
	/**
	 * 获取指定月份的第一天和最后一天时戳
	 * @param year
	 * @param month
	 * @return
	 * <AUTHOR>
	 * @create 2021年6月20日
	 */
	public static int[] getOneMonthTimestamp(int year,int month) {
		int[] timeArray = new int[2];
		Calendar calendar = Calendar.getInstance();
		calendar.set(year, month-1, 1, 0, 0 ,0);
		long startTime = calendar.getTime().getTime();
		//System.out.println(new SimpleDateFormat("yyyy-MM-dd E HH:mm:ss").format(cal.getTime()));
		calendar.set(year, month, 0, 23,59, 59);
		long endTime = calendar.getTime().getTime();
		//System.out.println(new SimpleDateFormat("yyyy-MM-dd E HH:mm:ss").format(cal.getTime()));
		timeArray[0] = (int)(startTime/1000);
		timeArray[1] = (int)(endTime/1000);
		return timeArray;
	}

	public static void main(String[] args) throws ParseException {
//		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss");
//		System.out.println(format.format(DateUtils.getOffsiteDate(new Date(), Calendar.YEAR,-1)));
//		Map<EncodeHintType,SymbolShapeHint> map = new HashMap();
//		map.put(EncodeHintType.DATA_MATRIX_SHAPE,SymbolShapeHint.FORCE_SQUARE);

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
		String dateInString = "2023-09";
		Date date = formatter.parse(dateInString);
		Date date2 = getOffsiteDate(date,Calendar.MONTH,-12);

		System.out.println("date="+formatter.format(date));
		System.out.println("date2="+formatter.format(date2));

	}
}
