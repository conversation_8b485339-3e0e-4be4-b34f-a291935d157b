<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.MaterialCostMapper">
    <sql id="materialCost_sql">
		mat.[流水号] AS costId, mat.[导入标识] AS importId,mat.[年月] AS yearMonth,mat.[年] AS year,mat.[月] AS month,
		mat.[费用种类] AS expenseType,mat.[费用名称] AS expenseName,mat.[部门编码] AS departmentCode,
		mat.[部门名称] AS departmentName,mat.[摘要] AS notes,mat.[金额] AS amount,
		mat.[创建人标识] AS creatorId,mat.[创建人姓名] AS creatorName,mat.[创建时间] AS createdTime
	</sql>

	<insert id="insertAuxiliaryMaterialCost" parameterType="com.hongru.entity.cost.AuxiliaryMaterialCost">
		INSERT INTO [CostPrice].[dbo].[辅材费用表]
		(
		[导入标识],
		[年月],
		[年],
		[月],
		[费用种类],
		[费用名称],
		[部门编码],
		[部门名称],
		[摘要],
		[金额],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{auxiliaryMaterialCost.importId},
		#{auxiliaryMaterialCost.yearMonth},
		#{auxiliaryMaterialCost.year},
		#{auxiliaryMaterialCost.month},
		#{auxiliaryMaterialCost.expenseType},
		#{auxiliaryMaterialCost.expenseName},
		#{auxiliaryMaterialCost.departmentCode},
		#{auxiliaryMaterialCost.departmentName},
		#{auxiliaryMaterialCost.notes},
		#{auxiliaryMaterialCost.amount},
		#{auxiliaryMaterialCost.creatorId},
		#{auxiliaryMaterialCost.creatorName},
		#{auxiliaryMaterialCost.createdTime}
		)
	</insert>

	<select id="selectByCostId" resultType="com.hongru.entity.cost.AuxiliaryMaterialCost">
		SELECT
		<include refid="materialCost_sql"/>
		FROM [CostPrice].[dbo].[辅材费用表] mat
		<where>
			<if test="costId != null">
				AND mat.[流水号] = #{costId}
			</if>
		</where>
	</select>

	<select id="listAuxiliaryMaterialCostByYearMonth" resultType="com.hongru.entity.cost.AuxiliaryMaterialCost">
		SELECT
		<include refid="materialCost_sql"/>
		FROM [CostPrice].[dbo].[辅材费用表] mat
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND mat.[年月] = #{yearMonth}
			</if>
		</where>
	</select>
	
	<select id="listAuxiliaryMaterialCost" resultType="com.hongru.entity.cost.AuxiliaryMaterialCost">
		SELECT
		<include refid="materialCost_sql"/>
		FROM [CostPrice].[dbo].[辅材费用表] mat
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND mat.[年月] = #{yearMonth}
			</if>
			<if test="expenseType != null and expenseType != ''">
				AND mat.[费用种类] = #{expenseType}
			</if>
		</where>
	</select>
	
	<select id="listMaterialCostPage" resultType="com.hongru.entity.cost.AuxiliaryMaterialCost">
		SELECT
		<include refid="materialCost_sql"/>
		FROM [CostPrice].[dbo].[辅材费用表] mat
		<where>
			<if test="expenseType != null and expenseType != ''">
				AND mat.[费用种类] = #{expenseType}
			</if>
			<if test="yearMonth != null and yearMonth != ''">
				AND mat.[年月] = #{yearMonth}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY mat.[年月] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listMaterialCostPageCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[辅材费用表] mat
		<where>
			<if test="expenseType != null and expenseType != ''">
				AND mat.[费用种类] = #{expenseType}
			</if>
			<if test="yearMonth != null and yearMonth != ''">
				AND mat.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<delete id="deleteAuxiliaryMaterial">
		DELETE [CostPrice].[dbo].[辅材费用表]
		WHERE [流水号] = #{costId}
	</delete>

	<select id="listAuxiliaryMaterialCostByParam" resultType="com.hongru.entity.cost.AuxiliaryMaterialCost">
		SELECT
		<include refid="materialCost_sql"/>
		FROM [CostPrice].[dbo].[辅材费用表] mat
		LEFT JOIN (select DISTINCT [中分类编号],[中分类部门] from [CostPrice].[dbo].[使用区分表]) pa
		ON pa.[中分类编号] = mat.[部门编码]
		<where>
			<if test="timeMin != null and timeMin != ''">
				AND CONVERT ( DATETIME, mat.[年月] + '-01 00:00:00' ) &gt;= #{timeMin}
			</if>
			<if test="timeMax != null and timeMax != ''">
				AND CONVERT ( DATETIME, mat.[年月] + '-01 00:00:00' ) &lt;= #{timeMax}
			</if>
			<if test="yearMonth != null and yearMonth != ''">
				AND mat.[年月] = #{yearMonth}
			</if>
			<if test="year != null">
				AND mat.[年] = #{year}
			</if>
			<if test="month != null">
				AND mat.[月] = #{month}
			</if>
			<if test="expenseTypeStrArr != null and expenseTypeStrArr != ''">
				AND mat.[费用种类] IN
				<foreach collection="expenseTypeStrArr" item="expenseType" open="(" close=")" separator=",">
					#{expenseType}
				</foreach>
			</if>
			<if test="expenseCodeStrArr != null and expenseCodeStrArr != ''">
				AND pa.[中分类部门] IN
				<foreach collection="expenseCodeStrArr" item="expenseCode" open="(" close=")" separator=",">
					#{expenseCode}
				</foreach>
			</if>
		</where>
	</select>
	
	<select id="listAuxiliaryMaterialCostByDateRange" resultType="com.hongru.entity.cost.AuxiliaryMaterialCost">
		SELECT [部门编码] AS departmentCode, SUM(金额) / #{monthInterval} AS amount
		FROM [CostPrice].[dbo].[辅材费用表]
		<where>
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
             <if test="expenseType != null and expenseType != ''">
                AND [费用种类] = #{expenseType}
            </if>
             <if test="department != null and department != ''">
                AND [部门编码] IN (SELECT DISTINCT([编号]) FROM [CostPrice].[dbo].[小部门明细表] WHERE [部门]= #{department} AND [类别]= #{category} AND [年度]=#{year})
<!--                 AND [部门编码] IN (SELECT DISTINCT([编号]) FROM [CostPrice].[dbo].[小部门明细表] WHERE [部门]= #{department}  AND [年度]=#{year}) -->
            </if>  
		</where>
		GROUP BY [部门编码]
	</select>
	
	<select id="selectAuxMaterialCostSum" resultType="BigDecimal">
		SELECT SUM(金额) AS amount FROM [CostPrice].[dbo].[辅材费用表]
		<where>
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
             <if test="expenseType != null and expenseType != ''">
                AND [费用种类] = #{expenseType}
            </if>
             <if test="departmentCode != null and departmentCode != ''">
                AND [部门编码] = #{departmentCode}
            </if>
            <if test="department != null and department != ''">
                AND [部门编码] IN (SELECT [编号]  FROM [CostPrice].[dbo].[小部门明细表] WHERE [部门]= #{department} AND [类别]= #{category} GROUP BY 编号)
<!--                 AND [部门编码] IN (SELECT [编号]  FROM [CostPrice].[dbo].[小部门明细表] WHERE [部门]= #{department}  GROUP BY 编号) -->
            </if>
            AND [摘要] NOT LIKE '氮气%'   
		</where>
		GROUP BY [部门编码]
	</select>
</mapper>