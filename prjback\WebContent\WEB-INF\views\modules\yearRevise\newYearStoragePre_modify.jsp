<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>予定入库量-修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/yearRevise/newYearStoragePre/modify" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md10">
                        <label class="layui-form-label"  style="width: 150px"><span class="star">*</span>年度:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input"  style="width: 250px" id="year" name="year"  value="${newYearStoragePre.year}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width:150px"><span class="star">*</span>EM年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" style="width: 250px" id="emNewYearStoragePre" name="emNewYearStoragePre" value="${newYearStoragePre.emNewYearStoragePre}"/>
                        </div>
                    </div>
                     <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>EF年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input"  style="width: 250px" id="efNewYearStoragePre" name="efNewYearStoragePre" value="${newYearStoragePre.efNewYearStoragePre}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>EF09年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input"  style="width: 250px" id="ef09NewYearStoragePre" name="ef09NewYearStoragePre" value="${newYearStoragePre.ef09NewYearStoragePre}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>ER年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" style="width: 250px" id="erNewYearStoragePre" name="erNewYearStoragePre" value="${newYearStoragePre.erNewYearStoragePre}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>EH年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input"  style="width: 250px" id="ehNewYearStoragePre" name="ehNewYearStoragePre" value="${newYearStoragePre.ehNewYearStoragePre}"/>
                        </div>
                    </div>
   
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/yearRevise/newYearStoragePre_addOrModify.js?time=2"></script>
</myfooter>
</body>
</html>
