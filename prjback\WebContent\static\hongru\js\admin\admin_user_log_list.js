layui.config({
	base: baselocationsta+'/common/layui/'
});

layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
	var laydate = layui.laydate
		,layer = layui.layer
		,table = layui.table
		,element = layui.element
		var form = layui.form;
	var userId = $("#userId").val();
	var url = baselocation+'/administrator/list/'+userId+'/logs';
	table.render({
		elem: '#demo'
		,height: 'full-70'
		,url: url
		,parseData:function(res){
			return {
				"code": 0,
				"msg": '',
				"count": res.total,
				"data": res.rows
			};
		}
		,method:'post'
		,title: '管理员日志列表'
		,page: true
		,limits: [10,20,50,100]
		,where:$("#formSearch").serializeJsonObject()
		,toolbar: '#toolbarDemo'
		,defaultToolbar: ['filter']
		,totalRow: false
		,cols: [[
			{field: 'logId',title:'编号',width:100,fixed: 'left',align:'center'}
			,{field: 'loginTime',title: '登陆时间',align:'center',width:180,
				templet: function(d){
					return new Date(d.loginTime).FormatV2("yyyy-MM-dd HH:mm:ss");
				}
			}
			,{field: 'userIp',title: '登陆IP',align:'center'}
			,{field: 'operatingSystem',title: '操作系统',align:'center'}
			,{field: 'browser',title: '浏览器',align:'center'}
		]]
	});

	table.on('toolbar(test)', function(obj){
		var checkStatus = table.checkStatus(obj.config.id)
			,data = checkStatus.data;
		switch(obj.event){
			case 'refresh':
				search();
				break;
		};
	});

	table.on('tool(test)', function(obj){
		var data = obj.data
			,layEvent = obj.event;
	});

	$("#btn-resert").on("click",function(){
		$('#searchForm input[type="text"]').each(function (i, j) {
			$(j).attr("value", "");
		})

		$('#searchForm select').each(function (i, j) {
			$(j).find("option:selected").attr("selected", false);
			$(j).find("option").first().attr("selected", true);
		})
		$("#cityId").html('<option value="" >全部</option>');
	})

});

function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}