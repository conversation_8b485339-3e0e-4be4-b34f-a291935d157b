package com.hongru.factory.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hongru.common.enums.CloudServiceEnum;
import com.hongru.factory.CloudStorageFactory;
import com.hongru.service.IAliyunCloudStorageService;
import com.hongru.service.IBaseCloudStorageService;

/**
 * 
* 类名称：CloudStorageFactoryImpl   
* 类描述：CloudStorageFactoryImpl 云存储工厂类接口实现   
* 创建人：hongru   
* 创建时间：2017年7月30日 下午10:28:02   
*
 */
@Service
public class CloudStorageFactoryImpl implements CloudStorageFactory{
	
	@Autowired
	private IAliyunCloudStorageService aliyunCloudStorageService;
	
	/**
	 * 云存储处理器容器
	 */
	private Map<CloudServiceEnum, IBaseCloudStorageService> cloudServiceMap = new HashMap<>();
	
	/**
	 * 初始化处理器容器
	 */
	@PostConstruct
	public void init() {
		// 阿里云云存储处理器
		cloudServiceMap.put(aliyunCloudStorageService.getCloudServiceType(), aliyunCloudStorageService);
	}
	
	@Override
	public IBaseCloudStorageService getBaseCloudStorageService(CloudServiceEnum cloudServiceEnum) {
		return cloudServiceMap.get(cloudServiceEnum);
	}
}
