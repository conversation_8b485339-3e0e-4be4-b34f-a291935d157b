<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report1" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="47bd7318-3552-4d9b-a4b5-70f11b3866ba">
	<property name="ireport.zoom" value="1.3286707500000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="14d1bf7c-8998-42be-9c45-1cd91c59806c">
		<field name="productCode" class="java.lang.String"/>
		<field name="maxStockOutTime" class="java.lang.String"/>
		<field name="weight" class="java.lang.String"/>
		<field name="coil" class="java.lang.String"/>
		<field name="qcLockNum" class="java.lang.String"/>
		<field name="ypLockNum" class="java.lang.String"/>
		<field name="cqpLockNum" class="java.lang.String"/>
		<field name="qaLockNum" class="java.lang.String"/>
	</subDataset>
	<parameter name="productCode" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="totalWeight" class="java.lang.String"/>
	<parameter name="customerName" class="java.lang.String"/>
	<parameter name="palletNum" class="java.lang.String"/>
	<parameter name="printDate" class="java.lang.String"/>
	<field name="stockList" class="java.util.List"/>
	<pageHeader>
		<band height="51">
			<staticText>
				<reportElement uuid="bd66da3a-2bfd-446e-ae37-e37cc659d0f9" x="18" y="0" width="74" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[日期：]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="83d215e7-037d-4c00-b1b7-7ac3532f2f46" x="177" y="20" width="190" height="31"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="20" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[库存查询]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="acd1c687-b888-4b85-8d06-d79d76bed608" x="71" y="0" width="109" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{printDate}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="105" splitType="Stretch">
			<componentElement>
				<reportElement uuid="23c0a6e6-b896-416a-944f-e34aa444284a" key="table 2" stretchType="RelativeToBandHeight" x="0" y="10" width="555" height="56"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="Table Dataset 1" uuid="7292598a-8301-45b8-9a3e-2e23277f9e6c">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{stockList})]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="170" uuid="6cc0ced8-b4d9-4f20-8429-7993ea6c2512">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="3fc1d6b5-2119-4e19-8cdf-598513c448cb" stretchType="RelativeToBandHeight" x="0" y="0" width="170" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[产品代码]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="d3a90c9a-84f1-4752-b8f6-70de0dff6d13" stretchType="RelativeToTallestObject" x="0" y="0" width="170" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									<paragraph leftIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{productCode}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="65" uuid="a2fcabfd-f423-4839-ad49-91076fbbb607">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="9c04ebf7-6d8d-48a7-b3af-f9437b868bc3" stretchType="RelativeToBandHeight" x="0" y="0" width="65" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[重量]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="2616f072-a87a-4b19-8a07-bf15c6162296" stretchType="RelativeToTallestObject" x="0" y="0" width="65" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									<paragraph rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{weight}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="45" uuid="4a1c02fd-1d45-40fd-8b44-412d9ec669a2">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="f18cf006-02b4-48d1-9578-791c04aecded" stretchType="RelativeToBandHeight" x="0" y="0" width="45" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[线盘]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="ec9dcd44-3591-444c-941b-eee85fd05b4f" stretchType="RelativeToTallestObject" x="0" y="0" width="45" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									<paragraph rightIndent="5"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{coil}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="50" uuid="21413d65-39c0-4e62-852e-d506778be51f">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="2a63eea5-618d-4517-a261-caaae7681fcb" stretchType="RelativeToBandHeight" x="0" y="0" width="50" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[样品锁定]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="3427646b-f148-487f-a702-b85ebcb312b4" stretchType="RelativeToTallestObject" x="0" y="0" width="50" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{ypLockNum}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:columnGroup width="50" uuid="5a5a7f29-fd46-4ad6-a675-8f30cae9449e">
						<jr:column width="50" uuid="bbce0b5b-d49b-4622-b2fc-2b2811c78711">
							<jr:columnHeader height="17" rowSpan="1">
								<staticText>
									<reportElement uuid="89c9debd-6f98-4218-979f-236a1de4da68" stretchType="RelativeToBandHeight" x="0" y="0" width="50" height="17"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[QC锁定]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
									<reportElement uuid="ba132e96-0608-46f1-b847-2173d6640296" stretchType="RelativeToTallestObject" x="0" y="0" width="50" height="20"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{qcLockNum}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
					<jr:column width="50" uuid="5a647fe0-234c-44bc-a75a-bd71ec67e573">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="d07c82a8-bc1f-4e5a-a755-b7432e5fe7b2" stretchType="RelativeToBandHeight" x="0" y="0" width="50" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[QA锁定]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="08e1d4b9-4b1d-444f-aa61-a06fdad75e88" stretchType="RelativeToTallestObject" x="0" y="0" width="50" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{qaLockNum}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="55" uuid="37096d62-1216-44a1-a30b-dd3fa123293c">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="d6c65342-fc5c-47ae-941d-a8e58726d56c" stretchType="RelativeToBandHeight" x="0" y="0" width="55" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[初期品锁定]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="b0ea424c-064a-4693-abea-674a352a02c6" stretchType="RelativeToTallestObject" x="0" y="0" width="55" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{cqpLockNum}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="70" uuid="75616733-fe28-459a-b79b-ed8775cb29eb">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="889862a8-c210-49b5-83a2-e40822f43e65" stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[最后出库日期]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="8b7dae40-db0e-4e58-90d4-f73020fbf352" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{maxStockOutTime}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<pageFooter>
		<band height="21">
			<textField>
				<reportElement uuid="6913f7d8-9086-4844-886a-04df271ca78a" x="455" y="0" width="100" height="20"/>
				<textElement>
					<font fontName="宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
