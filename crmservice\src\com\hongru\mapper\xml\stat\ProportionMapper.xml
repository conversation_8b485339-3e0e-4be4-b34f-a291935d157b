<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.stat.ProportionMapper">
    <sql id="proportion_sql">
		pro.[流水号] AS statId,pro.[年度] AS year, pro.[部门] AS departmentCode,pro.[EM] AS em,pro.[EF] AS ef,pro.[UF] AS uf,
		pro.[ER] AS er,pro.[EH] AS eh
	</sql>

	<insert id="insertProportion" parameterType="com.hongru.entity.stat.Proportion">
		INSERT INTO [CostPrice].[dbo].[比例表]
		(
		[年度],
		[部门],
		[EM],
		[EF],
		[UF],
		[ER],
		[EH]
		)VALUES(
		#{proportion.year},
		#{proportion.departmentCode},
		#{proportion.em},
		#{proportion.ef},
		#{proportion.uf},
		#{proportion.er},
		#{proportion.eh}
		)
	</insert>
	    
	<update id="updateProportion">
		UPDATE [CostPrice].[dbo].[比例表]
		<set>
			<if test="proportion.year != null ">
				[年度] = #{proportion.year},
			</if>
			<if test="proportion.departmentCode != null ">
				[部门] = #{proportion.departmentCode},
			</if>
			<if test="proportion.em != null ">
				[EM] = #{proportion.em},
			</if>
			<if test="proportion.ef != null ">
				[EF] = #{proportion.ef},
			</if>
			<if test="proportion.uf != null ">
				[UF] = #{proportion.uf},
			</if>
			<if test="proportion.er != null ">
				[ER] = #{proportion.er},
			</if>
			<if test="proportion.eh != null ">
				[EH] = #{proportion.eh},
			</if>
		</set>
		WHERE [流水号] = #{proportion.statId}
	</update>

	<delete id="deleteProportion">
		DELETE [CostPrice].[dbo].[比例表]
		WHERE [流水号] = #{statId}
	</delete>

	<select id="selectBystatId" resultType="com.hongru.entity.stat.Proportion">
        SELECT
        <include refid="proportion_sql"/>
        FROM [CostPrice].[dbo].[比例表] pro
      <where>
            <if test="statId != null">
                AND pro.[流水号] = #{statId}
            </if>
        </where>
    </select>

	<select id="listProportion" resultType="com.hongru.entity.stat.Proportion">
        SELECT
        <include refid="proportion_sql"/>
        FROM [CostPrice].[dbo].[比例表] pro
       <where>
			<if test="year != null and year != '' ">
				AND pro.[年度] = #{year}
			</if>
		</where>
        ORDER BY [年度] desc, [部门] 
    </select>

	<select id="selectProportionByCode" resultType="com.hongru.entity.stat.Proportion">
		SELECT
		<include refid="proportion_sql"/>
		FROM [CostPrice].[dbo].[比例表] pro
		<where>
			<if test="year != null and year != '' ">
				AND pro.[年度] = #{year}
			</if>
			<if test="departmentCode != null and departmentCode != '' ">
				AND pro.[部门] = #{departmentCode}
			</if>
		</where>
	</select>

	<select id="selectProportionsByYearAndDepartment" resultType="com.hongru.entity.stat.Proportion">
		SELECT
		<include refid="proportion_sql"/>
		FROM [CostPrice].[dbo].[比例表] pro
		<where>
			<if test="year != null and year != '' ">
				AND pro.[年度] = #{year}
			</if>
			<if test="departmentCodeList != null and departmentCodeList.size > 0">
				AND pro.[部门] IN
				<foreach collection="departmentCodeList" item="departmentCode" open="(" separator="," close=")">
					#{departmentCode}
				</foreach>
			</if>
		</where>
	</select>

</mapper>