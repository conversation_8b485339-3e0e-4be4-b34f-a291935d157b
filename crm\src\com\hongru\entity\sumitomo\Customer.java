package com.hongru.entity.sumitomo;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 客户表实体类 (sumitomo数据库)
 * 
 * <AUTHOR>
 */
@TableName("客户表")
public class Customer {

    /* 客户代码 */
    @TableId(value = "客户代码", type = IdType.INPUT)
    protected String customerCode;

    /* 客户简称 */
    @TableField("客户简称")
    protected String customerName;

    /* 客户全称 */
    @TableField("客户全称")
    protected String customerFullName;

    // Getters and Setters
    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerFullName() {
        return customerFullName;
    }

    public void setCustomerFullName(String customerFullName) {
        this.customerFullName = customerFullName;
    }
}
