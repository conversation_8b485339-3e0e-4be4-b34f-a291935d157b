package com.hongru.entity.yearRevise;

import java.math.BigDecimal;

/**
* 部门单价汇总查询返回结果实体类
* <AUTHOR>
*/
public class SummaryOfDepartmentalUnitPricesBean {
	/* 属性 */
	protected String attribute;
	/* 费用项目 */
	protected String expenseItem;
	/* 机械类别 */
	protected String machineType;
	/* 部门编号 */
	protected String departmentCode;
	/* SH系数 */
	protected BigDecimal shCoefficient;
	/* 单价/索要率 */
	protected BigDecimal unitPrice;
	/* 项目费用 */
	protected BigDecimal projectCost;

	//------------以下字段用于整合计算------------
	/* 工时 */
	protected BigDecimal workHour;
	/* 实际金额 */
	protected BigDecimal moneyAct;
	/* 予定机械时间 */
	protected BigDecimal machineTimePreAvg;

	/* 直接部门费用合计 */
	protected BigDecimal directDeptCostTotal;
	/* 辅助部门费用合计 */
	protected BigDecimal auxDeptAllocTotal;
	/* 部门费用合计 */
	protected BigDecimal deptCostTotal;

	public String getAttribute() {
		return attribute;
	}

	public void setAttribute(String attribute) {
		this.attribute = attribute;
	}

	public String getExpenseItem() {
		return expenseItem;
	}

	public void setExpenseItem(String expenseItem) {
		this.expenseItem = expenseItem;
	}

	public String getMachineType() {
		return machineType;
	}

	public void setMachineType(String machineType) {
		this.machineType = machineType;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}

	public BigDecimal getShCoefficient() {
		return shCoefficient;
	}

	public void setShCoefficient(BigDecimal shCoefficient) {
		this.shCoefficient = shCoefficient;
	}

	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	public BigDecimal getProjectCost() {
		return projectCost;
	}

	public void setProjectCost(BigDecimal projectCost) {
		this.projectCost = projectCost;
	}

	public BigDecimal getWorkHour() {
		return workHour;
	}

	public void setWorkHour(BigDecimal workHour) {
		this.workHour = workHour;
	}

	public BigDecimal getMoneyAct() {
		return moneyAct;
	}

	public void setMoneyAct(BigDecimal moneyAct) {
		this.moneyAct = moneyAct;
	}

	public BigDecimal getMachineTimePreAvg() {
		return machineTimePreAvg;
	}

	public void setMachineTimePreAvg(BigDecimal machineTimePreAvg) {
		this.machineTimePreAvg = machineTimePreAvg;
	}

	public BigDecimal getDirectDeptCostTotal() {
		return directDeptCostTotal;
	}

	public void setDirectDeptCostTotal(BigDecimal directDeptCostTotal) {
		this.directDeptCostTotal = directDeptCostTotal;
	}

	public BigDecimal getAuxDeptAllocTotal() {
		return auxDeptAllocTotal;
	}

	public void setAuxDeptAllocTotal(BigDecimal auxDeptAllocTotal) {
		this.auxDeptAllocTotal = auxDeptAllocTotal;
	}

	public BigDecimal getDeptCostTotal() {
		return deptCostTotal;
	}

	public void setDeptCostTotal(BigDecimal deptCostTotal) {
		this.deptCostTotal = deptCostTotal;
	}
}
