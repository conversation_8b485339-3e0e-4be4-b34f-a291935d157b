<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>编辑导体成本编码</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="formEdit" class="layui-form" method="post" action="">
            <input type="hidden" name="codeId" value="${conductorCode.codeId}">
            
            <div class="layui-form-item">
                <label class="layui-form-label">成本编码:</label>
                <div class="layui-input-block">
                    <input type="text" name="costCode" value="${conductorCode.costCode}" 
                           readonly class="layui-input layui-disabled">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><span class="star">*</span>导体名称:</label>
                <div class="layui-input-block">
                    <input type="text" name="conductorName" value="${conductorCode.conductorName}" 
                           required lay-verify="required" placeholder="请输入导体名称" 
                           autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn layui-bg-blue" lay-submit lay-filter="formEdit">
                        <i class="layui-icon layui-icon-ok"></i>确定
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="cancel()">
                        <i class="layui-icon layui-icon-close"></i>取消
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form
        ,layer = layui.layer;

    //监听提交
    form.on('submit(formEdit)', function(data){
        $.ajax({
            url: baselocation + '/yearParamSet/conductorCode/edit',
            type: 'POST',
            data: data.field,
            success: function(result) {
                if(result.code == 1) {
                    layer.msg('编辑成功', {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(result.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统异常', {icon: 2});
            }
        });
        return false;
    });
});

// 取消按钮
function cancel() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>
<style>
    .layui-form-label {
        width: 120px;
    }
    .layui-disabled {
        background-color: #f2f2f2;
        cursor: not-allowed;
    }
    .star {
        color: red;
    }
</style>
</body>
</html>
