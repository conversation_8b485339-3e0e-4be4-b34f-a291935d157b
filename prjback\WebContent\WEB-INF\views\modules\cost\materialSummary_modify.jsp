<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/materialSummary/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="${auxiliaryMaterialSummary.yearMonth}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>性质:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="nature" name="nature" value="${auxiliaryMaterialSummary.nature}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>摘要:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="abstracts" name="abstracts" value="${auxiliaryMaterialSummary.abstracts}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>金额:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="amount" name="amount" value="${auxiliaryMaterialSummary.amount}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EM:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="em" name="em" value="${auxiliaryMaterialSummary.em}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ef" name="ef" value="${auxiliaryMaterialSummary.ef}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>UF:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="uf" name="uf" value="${auxiliaryMaterialSummary.uf}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>ER:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="er" name="er" value="${auxiliaryMaterialSummary.er}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eh" name="eh" value="${auxiliaryMaterialSummary.eh}"/>
                        </div>
                    </div>
                </div>

                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="costId" name="costId" value="${auxiliaryMaterialSummary.costId}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/materialSummary_addOrModify.js?time=1"></script>
</myfooter>
</body>
</html>
