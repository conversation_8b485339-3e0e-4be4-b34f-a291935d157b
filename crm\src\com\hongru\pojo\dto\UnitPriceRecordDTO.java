package com.hongru.pojo.dto;

import com.hongru.entity.stat.UnitPriceRecord;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class UnitPriceRecordDTO {

    private PageInfo pageInfo;

    private List<UnitPriceRecord> UnitPriceRecordList;

    public UnitPriceRecordDTO(PageInfo pageInfo, List<UnitPriceRecord> UnitPriceRecordList) {
        super();
        this.pageInfo = pageInfo;
        this.UnitPriceRecordList = UnitPriceRecordList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<UnitPriceRecord> getUnitPriceRecordList() {
        return UnitPriceRecordList;
    }

    public void setUnitPriceRecordList(List<UnitPriceRecord> UnitPriceRecordList) {
        this.UnitPriceRecordList = UnitPriceRecordList;
    }
}
