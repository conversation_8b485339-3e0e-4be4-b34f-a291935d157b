# 产品成本设计模块分页查询逻辑更新说明

## 概述
根据用户要求，参考了 `com.hongru.service.impl.cost.CostServiceImpl#listDepartmentUnitPricePage` 方法的分页查询模式，对产品成本设计模块的分页查询逻辑进行了标准化更新。

## 参考的标准分页模式

### 1. 标准分页查询模式特点
- 使用 `PageInfo` 对象进行分页参数传递
- 返回专用的DTO对象包含数据列表和分页信息
- Mapper层分离查询数据和查询总数的方法
- 使用SQL Server的 `OFFSET...FETCH NEXT` 语法进行分页
- 控制器层使用 `isSearch` 参数控制是否执行查询

### 2. 参考方法分析
```java
// 服务层方法签名
DepartmentUnitPriceDTO listDepartmentUnitPricePage(String year, String machineType, String factoryType, PageInfo pageInfo)

// 控制器层调用模式
if (isSearch == 1) {
    DepartmentUnitPriceDTO dto = costService.listDepartmentUnitPricePage(year, machineType, factoryType, pageInfo);
    return new HrPageResult(dto.getDepartmentUnitPriceList(), dto.getPageInfo().getTotal());
} else {
    return new HrPageResult(new ArrayList<>(), 0);
}
```

## 已完成的更新

### 1. 创建DTO类
**文件**: `crm/src/com/hongru/entity/cost/ProductCostDesignDTO.java`
```java
public class ProductCostDesignDTO {
    private List<ProductCostDesign> productCostDesignList;
    private PageInfo pageInfo;
    // 构造方法和getter/setter
}
```

### 2. 更新服务接口
**文件**: `crmservice/src/com/hongru/service/cost/IProductCostDesignService.java`

添加了标准分页查询方法：
```java
ProductCostDesignDTO listProductCostDesignPage(String year, String customerName, String productCategory, 
                                              String productCode, PageInfo pageInfo) throws Exception;
```

保留了原有的兼容方法：
```java
HrPageResult<ProductCostDesign> listProductCostDesign(Map<String, Object> params);
```

### 3. 更新Mapper接口
**文件**: `crmservice/src/com/hongru/mapper/cost/ProductCostDesignMapper.java`

添加了标准分页查询方法：
```java
// 分页查询数据
List<ProductCostDesign> listProductCostDesignPage(@Param("year") String year,
                                                 @Param("customerName") String customerName,
                                                 @Param("productCategory") String productCategory,
                                                 @Param("productCode") String productCode,
                                                 @Param("pageInfo") PageInfo pageInfo);

// 查询总数
int listProductCostDesignPageCount(@Param("year") String year,
                                  @Param("customerName") String customerName,
                                  @Param("productCategory") String productCategory,
                                  @Param("productCode") String productCode);
```

### 4. 更新XML映射文件
**文件**: `crmservice/src/com/hongru/mapper/xml/cost/ProductCostDesignMapper.xml`

添加了标准分页查询SQL：
```xml
<!-- 分页查询产品成本设计列表 -->
<select id="listProductCostDesignPage" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM [CostPrice].[dbo].[产品成本设计表] pcd
    <where>
        <if test="year != null and year != ''">
            AND pcd.[年度] = #{year}
        </if>
        <if test="customerName != null and customerName != ''">
            AND pcd.[客户简称] LIKE '%' + #{customerName} + '%'
        </if>
        <if test="productCategory != null and productCategory != ''">
            AND pcd.[产品分类] = #{productCategory}
        </if>
        <if test="productCode != null and productCode != ''">
            AND pcd.[产品代码] LIKE '%' + #{productCode} + '%'
        </if>
    </where>
    ORDER BY pcd.[年度] DESC, pcd.[流水号] DESC
    <if test="pageInfo != null">
        OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
    </if>
</select>

<!-- 查询总数 -->
<select id="listProductCostDesignPageCount" resultType="int">
    SELECT COUNT(1)
    FROM [CostPrice].[dbo].[产品成本设计表] pcd
    <!-- 相同的where条件 -->
</select>
```

### 5. 更新服务实现
**文件**: `crmservice/src/com/hongru/service/impl/cost/ProductCostDesignServiceImpl.java`

实现了标准分页查询方法：
```java
@Override
public ProductCostDesignDTO listProductCostDesignPage(String year, String customerName, String productCategory, 
                                                     String productCode, PageInfo pageInfo) throws Exception {
    // 调用Mapper进行分页查询
    List<ProductCostDesign> productCostDesignList = productCostDesignMapper.listProductCostDesignPage(
        year, customerName, productCategory, productCode, pageInfo);
    
    // 查询总数
    int total = productCostDesignMapper.listProductCostDesignPageCount(
        year, customerName, productCategory, productCode);
    
    // 设置总数到分页信息
    pageInfo.setTotal(total);
    
    // 构建返回对象
    ProductCostDesignDTO productCostDesignDTO = new ProductCostDesignDTO();
    productCostDesignDTO.setProductCostDesignList(productCostDesignList);
    productCostDesignDTO.setPageInfo(pageInfo);
    
    return productCostDesignDTO;
}
```

### 6. 更新控制器
**文件**: `prjback/src/com/hongru/controller/cost/ProductCostDesignController.java`

更新了分页查询接口以符合标准模式：
```java
@PostMapping("/list")
@ResponseBody
public Object list(short isSearch, String year, String customerName, String productCategory, 
                  String productCode, PageInfo pageInfo) {
    try {
        if (isSearch == 1) {
            ProductCostDesignDTO productCostDesignDTO = productCostDesignService.listProductCostDesignPage(
                year, customerName, productCategory, productCode, pageInfo);
            return new HrPageResult(productCostDesignDTO.getProductCostDesignList(), 
                                   productCostDesignDTO.getPageInfo().getTotal());
        } else {
            return new HrPageResult(new ArrayList<ProductCostDesign>(), 0);
        }
    } catch (Exception e) {
        logger.error("查询产品成本设计列表异常：", e);
        return new HrResult(CommonReturnCode.FAILED, e.getMessage());
    }
}
```

## 主要改进点

### 1. 标准化分页参数
- 使用 `PageInfo` 对象替代 `Map<String, Object>` 参数
- 明确的查询条件参数，提高代码可读性

### 2. 标准化返回结果
- 使用专用DTO对象封装返回结果
- 分离数据查询和总数查询，提高性能

### 3. 标准化SQL分页
- 使用SQL Server标准的 `OFFSET...FETCH NEXT` 语法
- 统一的查询条件处理方式

### 4. 标准化控制器接口
- 使用 `isSearch` 参数控制查询执行
- 统一的异常处理和返回格式

## 兼容性说明
- 保留了原有的 `listProductCostDesign(Map<String, Object> params)` 方法以确保向后兼容
- 新的标准分页方法可以与现有前端代码无缝集成
- 所有查询条件都支持模糊查询和精确查询

## 使用建议
1. 新的功能开发建议使用标准分页方法 `listProductCostDesignPage`
2. 前端调用时需要传递 `isSearch=1` 参数来触发查询
3. 分页参数通过 `PageInfo` 对象传递，包含 `page`、`limit` 等信息

---
**更新完成日期**: 2025-07-29
**参考标准**: CostServiceImpl.listDepartmentUnitPricePage方法
