package com.hongru.entity.yearRevise;

import java.math.BigDecimal;

/**
* 人件工时查询返回结果实体类
* <AUTHOR>
* @create 2024/01/04 09:55
*/
public class PersonManHourShCoefficientBean {
	/* 工时 */
	protected BigDecimal workHour;
	/* 部门编号 */
	protected String departmentCode;
	/* 工种 */
	protected String workType;
	/* 机器类别 */
	protected String machineType;
	/* 予定机械时间 */
	protected BigDecimal machineTimePreAvg;
	/* 予定人件工时 */
	protected BigDecimal personManHourPreAvg;
	/* 实际人件工时 */
	protected BigDecimal personManHourActAvg;
	/* 人件工时SH系数 */
	protected String personManHourSH;
	/* 工作类别 */
	protected String workClass;

	public BigDecimal getWorkHour() {
		return workHour;
	}
	public void setWorkHour(BigDecimal workHour) {
		this.workHour = workHour;
	}
	public String getDepartmentCode() {
		return departmentCode;
	}
	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}
	public String getWorkType() {
		return workType;
	}
	public void setWorkType(String workType) {
		this.workType = workType;
	}
	public String getMachineType() {
		return machineType;
	}
	public void setMachineType(String machineType) {
		this.machineType = machineType;
	}
	public BigDecimal getMachineTimePreAvg() {
		return machineTimePreAvg;
	}
	public void setMachineTimePreAvg(BigDecimal machineTimePreAvg) {
		this.machineTimePreAvg = machineTimePreAvg;
	}
	public BigDecimal getPersonManHourPreAvg() {
		return personManHourPreAvg;
	}
	public void setPersonManHourPreAvg(BigDecimal personManHourPreAvg) {
		this.personManHourPreAvg = personManHourPreAvg;
	}
	public BigDecimal getPersonManHourActAvg() {
		return personManHourActAvg;
	}
	public void setPersonManHourActAvg(BigDecimal personManHourActAvg) {
		this.personManHourActAvg = personManHourActAvg;
	}
	public String getPersonManHourSH() {
		return personManHourSH;
	}
	public void setPersonManHourSH(String personManHourSH) {
		this.personManHourSH = personManHourSH;
	}
	public String getWorkClass() {
		return workClass;
	}
	public void setWorkClass(String workClass) {
		this.workClass = workClass;
	}
}