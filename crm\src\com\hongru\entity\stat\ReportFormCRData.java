package com.hongru.entity.stat;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

/**
* 报表_CR计划实际实体类（用来存报表4里面用的报表2中的数据）
* <AUTHOR>
* @create 2023/9/24 11:23
*/
@TableName("报表_CR计划实际")
public class ReportFormCRData {

	/* 数据类型-0:人件费 */
	public static final short DATATTYPE_REN_JIAN_FEI = 0;
	/* 数据类型-1：辅修费(補修費) */
	public static final short DATATTYPE_FU_XIU_FEI = 1;
	/* 数据类型-2：辅材费(補材費) */
	public static final short DATATTYPE_FU_CAI_FEI = 2;
	/* 数据类型-3.电力费(電力) */
	public static final short DATATTYPE_DIAN_LI_FEI = 3;
	/* 数据类型-4.输送费(輸送費) */
	public static final short DATATTYPE_SHU_SONG_FEI = 4;
	/* 数据类型-5.线盘费(ﾘｰﾙ) */
	public static final short DATATTYPE_XIAN_PAN_FEI = 5;
	/* 数据类型-6.捆包费(包装費用) */
	public static final short DATATTYPE_KUN_BAO_FEI = 6;
	/* 数据类型-7.油漆费(ﾜﾆｽ費) */
	public static final short DATATTYPE_YOU_QI_FEI = 7;
	/* 数据类型-8.屑差异(ﾛｽ差異) */
	public static final short DATATTYPE_XIE_CHA_YI_FEI = 8;
	/* 数据类型-9.铜加工费(WR加工費) */
	public static final short DATATTYPE_TONG_JIA_GONG_FEI = 9;
	/* 数据类型-10.线盘回收(ﾘｰﾙ回収費) */
	public static final short DATATTYPE_XIAN_PAN_HUI_SHOU_FEI = 10;
	/* 数据类型-11.氮气费(窒素（UFのみ)) */
	public static final short DATATTYPE_DAN_QI_FEI = 11;

	/* 流水号 */
	@TableId(value="reportFormsCRDataId", type= IdType.AUTO)
	protected int reportFormsCRDataId;
	/* 报表流水号 */
	protected int reportFormsId;
	/* 数据类型 */
	protected short dataType;
	/* 年月 */
	protected String yearAndMonth;
	/* 部门 */
	protected String department;
	/* 计划予定 */
	protected BigDecimal jiHuaYuDing;
	/* 计划实际 */
	protected BigDecimal jiHuaShiJi;
	/* 实绩予定 */
	protected BigDecimal shiJiYuDing;
	/* 实绩实际 */
	protected BigDecimal shiJiShiJi;
	/* 预计入库量 */
	protected BigDecimal yujiInStore;
	/* 实际入库量 */
	protected BigDecimal inStore;
	/* 单价（=实绩实际/总入库量） */
	protected BigDecimal singlePrice;

	public int getReportFormsCRDataId() {
		return reportFormsCRDataId;
	}

	public void setReportFormsCRDataId(int reportFormsCRDataId) {
		this.reportFormsCRDataId = reportFormsCRDataId;
	}

	public int getReportFormsId() {
		return reportFormsId;
	}

	public void setReportFormsId(int reportFormsId) {
		this.reportFormsId = reportFormsId;
	}

	public short getDataType() {
		return dataType;
	}

	public void setDataType(short dataType) {
		this.dataType = dataType;
	}

	public String getYearAndMonth() {
		return yearAndMonth;
	}

	public void setYearAndMonth(String yearAndMonth) {
		this.yearAndMonth = yearAndMonth;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public BigDecimal getJiHuaYuDing() {
		return jiHuaYuDing;
	}

	public void setJiHuaYuDing(BigDecimal jiHuaYuDing) {
		this.jiHuaYuDing = jiHuaYuDing;
	}

	public BigDecimal getJiHuaShiJi() {
		return jiHuaShiJi;
	}

	public void setJiHuaShiJi(BigDecimal jiHuaShiJi) {
		this.jiHuaShiJi = jiHuaShiJi;
	}

	public BigDecimal getShiJiYuDing() {
		return shiJiYuDing;
	}

	public void setShiJiYuDing(BigDecimal shiJiYuDing) {
		this.shiJiYuDing = shiJiYuDing;
	}

	public BigDecimal getShiJiShiJi() {
		return shiJiShiJi;
	}

	public void setShiJiShiJi(BigDecimal shiJiShiJi) {
		this.shiJiShiJi = shiJiShiJi;
	}

	public BigDecimal getInStore() {
		return inStore;
	}

	public void setInStore(BigDecimal inStore) {
		this.inStore = inStore;
	}

	public BigDecimal getSinglePrice() {
		return singlePrice;
	}

	public void setSinglePrice(BigDecimal singlePrice) {
		this.singlePrice = singlePrice;
	}

	public BigDecimal getYujiInStore() {
		return yujiInStore;
	}

	public void setYujiInStore(BigDecimal yujiInStore) {
		this.yujiInStore = yujiInStore;
	}
}