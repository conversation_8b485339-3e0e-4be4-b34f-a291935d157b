<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.admin.UserLoginLogMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.hongru.entity.admin.UserLoginLog">
		<id column="log_id" property="logId" />
		<result column="login_time" property="loginTime" />
		<result column="user_ip" property="userIp" />
		<result column="user_id" property="userId" />
		<result column="operating_system" property="operatingSystem" />
		<result column="browser" property="browser" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        log_id AS logId, login_time AS loginTime, user_ip AS userIp, user_id AS userId, operating_system AS operatingSystem, browser
    </sql>
	
	<!-- 根据管理员ID查找管理员登录日志列表 -->
	<sql id="listByPage_where">
		<if test="userId	 != null">
			AND user_id = #{userId}
		</if>
		<if test="search != null">
			AND (
				user_ip LIKE  #{search}
				OR operating_system LIKE  #{search}
				OR browser LIKE  #{search}
			)
		</if>
	</sql>
	<select id="listByPage" resultType="com.hongru.entity.admin.UserLoginLog">
		SELECT
	        log_id AS logId, 
	        login_time AS loginTime, 
	        user_ip AS userIp, 
	        user_id AS userId, 
	        operating_system AS operatingSystem, 
	        browser
		FROM
			hr_admin_user_login_log
		<where>
			<include refid="listByPage_where"></include>
		</where>
		ORDER BY log_id DESC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	<select id="listByPageCount" resultType="integer">
		SELECT
			COUNT(1)
		FROM
			hr_admin_user_login_log
		<where>
			<include refid="listByPage_where"></include>
		</where>
	</select>
</mapper>
