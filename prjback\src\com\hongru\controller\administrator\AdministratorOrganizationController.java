package com.hongru.controller.administrator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.hongru.base.BaseController;
import com.hongru.base.BasePageDTO;
import com.hongru.common.Token;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.Organization;
import com.hongru.pojo.dto.UserPageDTO;
import com.hongru.service.admin.IOrganizationService;
import com.hongru.service.admin.IUserService;
import com.hongru.support.page.PageInfo;

/**
 * 
* 类名称：AdministratorOrganizationController   
* 类描述：部门管理表示层控制器        
* 创建人：hongru   
* 创建时间：2017年4月7日 上午11:17:12   
*
 */
@Controller
@RequestMapping(value = "/administrator/organization")
public class AdministratorOrganizationController extends BaseController {
	
	@Autowired
	private IOrganizationService organizationService;
	@Autowired
	private IUserService userService;
	
	/**
	 * GET 部门列表页面
	 * @return
	 */
	@GetMapping(value = "/view")
	public String getListPage(Model model) {
		return "/modules/admin/admin_organization_list";
	}
	
	/**
	 * Post 部门列表
	 * @param page
	 * @param limit
	 * @return
	 */
	@PostMapping(value = "/")
	@ResponseBody
	public Object listOrganization(Integer page, Integer limit, String searchStr) {
		PageInfo pageInfo = new PageInfo(limit, page);
		BasePageDTO<Organization> basePageDTO = organizationService.listByPage(pageInfo, searchStr);
		return new HrPageResult(basePageDTO.getList(), basePageDTO.getPageInfo().getTotal());
	}
	
	/**
	 * GET 部门分类下管理员列表页面
	 * @return
	 */
	@GetMapping(value = "/{organizationId}/list")
	public String list(Model model, @PathVariable("organizationId") Long organizationId) {
		model.addAttribute("organizationId", organizationId);
		return "/modules/admin/admin_user_organization";
	}
	
	/**
	 * GET 部门分类下管理员列表
	 * @return
	 */
	@GetMapping(value = "/{organizationId}/lists")
	@ResponseBody
	public Object listLogs(@PathVariable("organizationId") Long organizationId, Integer page, Integer limit,String searchStr) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			PageInfo pageInfo = new PageInfo(limit, page);
			// 用户日志
			UserPageDTO userPageDTO = userService.listByOrganizationId(organizationId, pageInfo, searchStr);
			return new HrPageResult(userPageDTO.getUserVOs(), userPageDTO.getPageInfo().getTotal());
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * PUT 启用/冻结部门
	 * @return
	 */
	@PutMapping(value = "/{organizationId}/audit")
	@ResponseBody
	public Object audit(@PathVariable("organizationId") Long organizationId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			Integer count = organizationService.updateStatus(organizationId);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * DELETE 删除部门
	 * @return
	 */
	@DeleteMapping(value = "/{organizationId}")
	@ResponseBody
	public Object delete(@PathVariable("organizationId") Long organizationId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			Integer count = organizationService.deleteByOrganizationId(organizationId);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}

	/**
	* 批量删除部门
	* @throws
	* <AUTHOR>
	* @create 2022/2/11 11:32
	* @return
	*/
	@PostMapping(value = "/delete/forBatch")
	@ResponseBody
	public Object deleteOrganizationsForBatch(String organizationIds) throws Exception {
		try {
			String[] organizationIdStrArr = organizationIds.split(",");
			Integer[] organizationIdArr = new Integer[organizationIdStrArr.length];
			for(int i=0;i<organizationIdArr.length;i++){
				organizationIdArr[i] = Integer.parseInt(organizationIdStrArr[i]);
			}
			organizationService.deleteOrganizations(organizationIdArr);
			return new HrResult(CommonReturnCode.SUCCESS);
		}catch (Exception e) {
			e.printStackTrace();
			return new HrResult(CommonReturnCode.FAILED.getCode(),"操作失败");
		}
	}
	
	/**
	 * GET 创建部门页面
	 * @return
	 */
	@Token(save=true)
	@GetMapping(value = "/create/view")
	public String getInsertPage(Model model) {
		return "/modules/admin/admin_organization_add";
	}
	
	/**
	 * POST 创建部门
	 * @return
	 */
	@Token(remove=true)
	@PostMapping(value = "/create")
	@ResponseBody
	public Object insert(Organization organization) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			// 创建部门及插入部门目录记录
			Integer count = organizationService.insertOrganization(organization, authorizingUser.getUserName());
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * GET 更新部门页面
	 * @return
	 */
	@GetMapping(value = "/{organizationId}/edit/view")
	public String getUpdatePage(Model model, @PathVariable("organizationId") Long organizationId) {
		// 部门信息
		Organization organization = organizationService.selectById(organizationId);
		model.addAttribute("organization", organization);
		
		return "/modules/admin/admin_organization_edit";
	}
	
	/**
	 * PUT 更新部门信息
	 * @return
	 */
	@PostMapping(value = "/{organizationId}/edit")
	@ResponseBody
	public Object update(Organization organization, @PathVariable("organizationId") Long organizationId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			// 更新部门信息
			Integer count = organizationService.updateOrganization(organization, authorizingUser.getUserName());
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
}
