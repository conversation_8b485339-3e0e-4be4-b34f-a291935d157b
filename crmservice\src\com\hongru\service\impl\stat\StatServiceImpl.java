package com.hongru.service.impl.stat;

import com.hongru.entity.cost.ExcelImport;
import com.hongru.entity.stat.BoundQuantity;
import com.hongru.entity.stat.Proportion;
import com.hongru.entity.stat.ReportFormCRData;
import com.hongru.entity.stat.UnitPriceRecord;
import com.hongru.mapper.cost.ExeclImportMapper;
import com.hongru.mapper.stat.BoundQuantityMapper;
import com.hongru.mapper.stat.ProportionMapper;
import com.hongru.mapper.stat.ReportFormCRDataMapper;
import com.hongru.mapper.stat.UnitPriceRecordMapper;
import com.hongru.pojo.dto.BoundQuantityDTO;
import com.hongru.pojo.dto.UnitPriceRecordDTO;
import com.hongru.service.stat.IStatService;
import com.hongru.support.page.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class StatServiceImpl implements IStatService {

    @Autowired
    private ExeclImportMapper importMapper;
    @Autowired
    private ProportionMapper proportionMapper;
    @Autowired
    private ReportFormCRDataMapper reportFormCRDataMapper;
    @Autowired
    private UnitPriceRecordMapper unitPriceRecordMapper;
    @Autowired
    private BoundQuantityMapper boundQuantityMapper;

    /*=================================文件导入======================================*/
    /**
     * 修改文件导入状态
     * @param importId
     * @throws
     * <AUTHOR>
     * @create 2023/7/27 9:47
     * @return com.hongru.entity.cost.ExcelImpot
     */
    @Override
    public void modifyImportState(Integer importId,Integer userId,String userName){
//        importMapper.modifyImportState(importId,userId,userName);
    }

    /**
     * 添加文件导入
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 11:35
     * @return
     */
    @Override
    public int addExcelImport(ExcelImport excelImport) throws Exception {
        importMapper.insertExcelImpot(excelImport);
        return excelImport.getImportId();
    }

    /*=================================报表_CR计划实际======================================*/
    /**
     * 新增报表_CR计划实际
     * @param reportFormCRData
     * @throws
     * <AUTHOR>
     * @create 2023/9/24 13:51
     * @return int
     */
    @Override
    public int addReportFormCRData(ReportFormCRData reportFormCRData) {
        reportFormCRDataMapper.insertReportFormCRData(reportFormCRData);
        return reportFormCRData.getReportFormsCRDataId();
    }

    /**
     * 组装报表_CR计划实际
     * @param reportFormsId
     * @param dataType
     * @param yearAndMonth
     * @param department
     * @param jiHuaYuDing
     * @param jiHuaShiJi
     * @param shiJiYuDing
     * @param shiJiShiJi
     * @param inStore
     * @param singlePrice
     * @throws
     * <AUTHOR>
     * @create 2023/9/24 14:58
     * @return com.hongru.entity.stat.ReportFormCRData
     */
    @Override
    public ReportFormCRData configReportFormCRData(int reportFormsId, short dataType, String yearAndMonth, String department, BigDecimal jiHuaYuDing, BigDecimal jiHuaShiJi, BigDecimal shiJiYuDing, BigDecimal shiJiShiJi, BigDecimal inStore, BigDecimal singlePrice) {
        ReportFormCRData reportFormCRData = new ReportFormCRData();
        reportFormCRData.setReportFormsId(reportFormsId);
        reportFormCRData.setDataType(dataType);
        reportFormCRData.setYearAndMonth(yearAndMonth);
        reportFormCRData.setDepartment(department);
        reportFormCRData.setJiHuaShiJi(jiHuaShiJi);
        reportFormCRData.setJiHuaYuDing(jiHuaYuDing);
        reportFormCRData.setShiJiShiJi(shiJiShiJi);
        reportFormCRData.setShiJiYuDing(shiJiYuDing);
        reportFormCRData.setSinglePrice(singlePrice);
        reportFormCRData.setInStore(inStore);
        return reportFormCRData;
    }

    /**
     * 查询 报表_CR计划实际
     * @param yearMonth
     * @param department
     * @param dataType
     * @throws
     * <AUTHOR>
     * @create 2023/9/24 13:53
     * @return java.util.List<com.hongru.entity.stat.ReportFormCRData>
     */
    @Override
    public List<ReportFormCRData> listReportFormCRData(String yearMonth, String department, Short dataType) {
        return reportFormCRDataMapper.selectReportFormCRData(yearMonth, department, dataType);
    }

    /**
     * 插入数据到比例表明细
     * @param proportion
     * @throws
     * <AUTHOR>
     * @create 2024/02/05 10:09
     * @return com.hongru.entity.stat.Proportion
     */
    @Override
    public int addProportion(Proportion proportion) {
    	return proportionMapper.insertProportion(proportion);
    }
    
    /**
     * 更新比例表明细
     * @param proportion
     * @throws
     * <AUTHOR>
     * @create 2024/02/05 10:29
     * @return 
     */
    @Override
    public void modifyProportion(Proportion proportion) {
    	proportionMapper.updateProportion(proportion);
    }
    
    /**
     * 根据流水号删除比例表
     * @param statId
     * @throws
     * <AUTHOR>
     * @create 2024/02/05 11:09
     * @return
     */
    @Override
    public void removeProportion(Integer statId) {
    	proportionMapper.deleteProportion(statId);
    }
    
    /**
     * 根据流水号检索比例表
     * @param statId
     * @throws
     * <AUTHOR>
     * @create 2024/02/05 11:09
     * @return com.hongru.entity.stat.Proportion
     */
    @Override
    public Proportion selectBystatId(Integer statId) {
    	return proportionMapper.selectBystatId(statId);
    }
    
    /**
     * 检索比例表
     * @throws
     * <AUTHOR>
     * @create 2024/02/05 11:09
     * @return com.hongru.entity.stat.Proportion
     */
    @Override
    public List<Proportion> listProportion(String year){
    	return proportionMapper.listProportion(year);
    }
    
    /**
     * 根据参数获取比例表明细
     * @param year 年度
     * @param departmentCode 部门
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 11:35
     * @return
     */
    @Override
    public Proportion selectProportionByCode(String year, String departmentCode) throws Exception {
        return proportionMapper.selectProportionByCode(year, departmentCode);
    }

    /**
     * 根据年度及部门批量查询相关比例信息集合
     *
     * @param yearList 年度集合
     * @param departmentCodeList 部门集合
     * @return 比例信息集合
     */
    @Override
    public List<Proportion> selectProportionsByYearAndDepartment(String year, List<String> departmentCodeList) {
        return proportionMapper.selectProportionsByYearAndDepartment(year, departmentCodeList);
    }

    /**
     * 添加文件导入
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 11:35
     * @return
     */
    @Override
    public int addUnitPriceRecord(UnitPriceRecord unitPriceRecord) throws Exception {
        unitPriceRecordMapper.insertUnitPriceRecord(unitPriceRecord);
        return unitPriceRecord.getStatId();
    }

    /**
     * 根据年月获取最新的单价数据
     * @param
     * @throws
     * <AUTHOR>
     * @create 2023/8/28 11:09
     * @return com.hongru.entity.cost.ElectricityFee
     */
    @Override
    public UnitPriceRecord selectUnitPriceRecordByYearMonth(String year) throws Exception {
        return unitPriceRecordMapper.selectUnitPriceRecordByYearMonth(year);
    }

    /**
     * 根据id获取
     * @param statId
     * @throws
     * <AUTHOR>
     * @create 2023/8/11 11:08
     * @return com.hongru.entity.cost.CuProceCost
     */
    @Override
    public UnitPriceRecord selectUnitPriceRecord(int statId) throws Exception {
        return unitPriceRecordMapper.selectByCostId(statId);
    }

    /**
     * 分页获取单价列表
     * @param
     * @throws
     * <AUTHOR>
     * @create 2023/8/11 11:09
     * @return com.hongru.entity.cost.CuProceCost
     */
    @Override
    public UnitPriceRecordDTO listUnitPriceRecordPage(String year, PageInfo pageInfo) throws Exception {
        List<UnitPriceRecord> unitPriceRecordList = unitPriceRecordMapper.listUnitPriceRecordPage(year, pageInfo);
        pageInfo.setTotal(1);
        return new UnitPriceRecordDTO(pageInfo, unitPriceRecordList);
    }

    /**
     * 编辑价格
     * @param unitPriceRecord
     * @throws
     * <AUTHOR>
     * @create 2023/8/11 11:08
     * @return int
     */
    @Override
    public void modifyUnitPriceRecord(UnitPriceRecord unitPriceRecord) {
        unitPriceRecordMapper.updateUnitPriceRecord(unitPriceRecord);
    }

    /**
     * 添加文件导入
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 11:35
     * @return
     */
    @Override
    public int addBoundQuantity(BoundQuantity boundQuantity) throws Exception {
        boundQuantityMapper.insertBoundQuantity(boundQuantity);
        return boundQuantity.getStatId();
    }

    /**
     * 根据年月获取最新的单价数据
     * @param
     * @throws
     * <AUTHOR>
     * @create 2023/8/28 11:09
     * @return com.hongru.entity.cost.ElectricityFee
     */
    @Override
    public BoundQuantity selectBoundQuantityByYearMonth(String yearMonth) throws Exception {
        return boundQuantityMapper.getBoundQuantityByYearMonth(yearMonth);
    }

    /**
     * 根据id获取数据
     * @param
     * @throws
     * <AUTHOR>
     * @create 2023/8/28 11:09
     * @return BoundQuantity
     */
    @Override
    public BoundQuantity selectBoundQuantityById(int statId) throws Exception {
        return boundQuantityMapper.getBoundQuantityById(statId);
    }

    /**
     * 获取单价数据
     * @param
     * @throws
     * <AUTHOR>
     * @create 2023/8/28 11:09
     * @return com.hongru.entity.cost.ElectricityFee
     */
    @Override
    public BoundQuantityDTO getBoundQuantityList(PageInfo pageInfo) throws Exception {
        List<BoundQuantity> boundQuantitys = boundQuantityMapper.selectBoundQuantityList(pageInfo);
        Integer total = boundQuantityMapper.selectBoundQuantityCount();
        pageInfo.setTotal(total);
        return new BoundQuantityDTO(pageInfo, boundQuantitys);
    }

    /**
     * 编辑
     * @param boundQuantity
     * @throws
     * <AUTHOR>
     * @create 2023/8/11 11:08
     * @return int
     */
    @Override
    public void modifyBoundQuantity(BoundQuantity boundQuantity) {
        boundQuantityMapper.updateBoundQuantity(boundQuantity);
    }

    /**
     * 删除
     * @param statId
     * @throws
     * <AUTHOR>
     * @create 2023/8/11 11:08
     * @return int
     */
    @Override
    public void removeBoundQuantity(Integer statId, Short state) {
        boundQuantityMapper.updateState(statId,state);
    }
}