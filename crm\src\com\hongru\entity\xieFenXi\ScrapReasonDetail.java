package com.hongru.entity.xieFenXi;

public class ScrapReasonDetail {
	protected int orderNumber;//序号
	protected int scrapReasonDetailId;//子项代码
	protected int scrapReasonId;//代码
	protected String scrapReasonDetail;//屑原因子项
	protected int sceneDisplay;//现场显示
	protected int indexDisplay;//显示索引
	protected String code;//屑原因
	
	public int getOrderNumber() {
		return orderNumber;
	}
	public void setOrderNumber(int orderNumber) {
		this.orderNumber = orderNumber;
	}
	public int getScrapReasonDetailId() {
		return scrapReasonDetailId;
	}
	public void setScrapReasonDetailId(int scrapReasonDetailId) {
		this.scrapReasonDetailId = scrapReasonDetailId;
	}
	public int getScrapReasonId() {
		return scrapReasonId;
	}
	public void setScrapReasonId(int scrapReasonId) {
		this.scrapReasonId = scrapReasonId;
	}
	public String getScrapReasonDetail() {
		return scrapReasonDetail;
	}
	public void setScrapReasonDetail(String scrapReasonDetail) {
		this.scrapReasonDetail = scrapReasonDetail;
	}
	public int getSceneDisplay() {
		return sceneDisplay;
	}
	public void setSceneDisplay(int sceneDisplay) {
		this.sceneDisplay = sceneDisplay;
	}
	public int getIndexDisplay() {
		return indexDisplay;
	}
	public void setIndexDisplay(int indexDisplay) {
		this.indexDisplay = indexDisplay;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	
	
}
