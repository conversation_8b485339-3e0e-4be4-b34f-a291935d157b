layui.config({
	base: baselocationsta+'/common/layui/'
});

layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
	var laydate = layui.laydate
		,layer = layui.layer
		,table = layui.table
		,element = layui.element
	var form = layui.form;

	var url = baselocation+'/administrator/organization/';
	table.render({
		elem: '#demo'
		,height: 'full-70'
		,url: url
		,parseData:function(res){
			return {
				"code": 0,
				"msg": '',
				"count": res.total,
				"data": res.rows
			};
		}
		,method:'post'
		,title: '部门列表'
		,page: true
		,limits: [10,20,50,100]
		,where:$("#formSearch").serializeJsonObject()
		,toolbar: '#toolbarDemo'
		,defaultToolbar: ['filter']
		,totalRow: false
		,cols: [[
			{type: 'checkbox', fixed: 'left'}
			,{field: 'zizeng', title: '', width:50, fixed:'left', type:"numbers", align:'center'}
			,{field: 'organizationName',width:100,title: '部门名称',align:'left'}
			,{field: 'status', title: '状态',width:100, align: 'center',
				templet: function(d){
					if (d.status == 1) {
						var str="";
						str += '<form class="layui-form" action="">';
						str += '<input type="checkbox" name="status" lay-skin="switch" value="'+d.organizationId+'" lay-text="正常|冻结" checked lay-filter="statusFun">';
						str += '</form>';
						return str;
					} else if (d.status == 0) {
						var str="";
						str += '<form class="layui-form" action="">';
						str += '<input type="checkbox" name="status" lay-skin="switch" value="'+d.organizationId+'" lay-text="正常|冻结" lay-filter="statusFun">';
						str += '</form>';
						return str;
					}
				}
			}
			,{field: 'orderNo',width:100,title: '序号',align:'center'}
			,{field: 'remarks',title: '备注',align:'left', width:300,
				templet: function(d){
					if(d.remarks !=null){
						return '<div style="text-align: left">'+d.remarks+'</div>';
					}
				}
			}
			,{title: '操作',minWidth:315,align:'left',fixed:'right',toolbar:'#barDemo'}
		]]
	});


	table.on('toolbar(test)', function(obj){
		var checkStatus = table.checkStatus(obj.config.id)
			,data = checkStatus.data;
		switch(obj.event){
			case 'toAdd':
				var title = "添加部门";
				var httpsrc = baselocation+"/administrator/organization/create/view";
				layer_show(title,httpsrc,document.body.clientWidth-10, document.body.clientHeight-10);
				break;
			case 'refresh':
				var temp = $("#formSearch").serializeJsonObject();
				console.info(temp);
				layui.table.reload('demo', {
					page: {
						curr: 1
					}
					,where: temp
				}, 'data');
				break;
			case 'shanChu':
				if(data.length == 0){
					layer.alert("请选择要删除的数据！");
					return;
				}else{
					var organizationIds = "";
					for(var i=0; i < data.length; i++){
						if(organizationIds==""){
							organizationIds = data[i].organizationId;
						}else{
							organizationIds+= ","+data[i].organizationId;
						}
					}
					var index = layer.load(2,{
						shade:[0.1,'#fff']
					});
					$.ajax({
						type : 'post',
						dataType : 'json',
						data: {"organizationIds":organizationIds},
						url : baselocation + '/administrator/organization/delete/forBatch',
						success : function(result) {
							layer.closeAll();
							if (result.code == 1) {
								layer.msg("操作成功!", {
									shade : 0.3,
									time : 1500
								}, function() {
									layui.table.reload('demo');
								});
							} else {
								layer.alert(result.message, {
									icon : 2
								});
							}
						}
					})
				}
				break;
		};
	});

	table.on('tool(test)', function(obj){
		var data = obj.data
			,layEvent = obj.event;
		if(layEvent === 'freeze'){
			status_stop(data.organizationId);
		}else if(layEvent === 'normal'){
			status_start(data.organizationId);
		}else if(layEvent === 'edit'){
			var title = "编辑部门";
			var httpsrc = baselocation + '/administrator/organization/' + data.organizationId + '/edit/view';
			layer_show(title,httpsrc,document.body.clientWidth-10, document.body.clientHeight-10);
		}else if(layEvent === 'remove'){
			admin_delete(data.organizationId);
		}else if(layEvent === 'userList'){
			var title = "管理员列表";
			var httpsrc = baselocation + '/administrator/role/' + data.organizationId + '/list';
			layer_show(title,httpsrc,document.body.clientWidth-10, document.body.clientHeight-10);
		}
	});

	$("#btn-resert").on("click",function(){
		$('#searchForm input[type="text"]').each(function (i, j) {
			$(j).attr("value", "");
		})

		$('#searchForm select').each(function (i, j) {
			$(j).find("option:selected").attr("selected", false);
			$(j).find("option").first().attr("selected", true);
		})
		$("#cityId").html('<option value="" >全部</option>');
	})

});

layui.use(['form'], function(){
	var form = layui.form
		,layer = layui.layer
	form.on('switch(statusFun)', function(data){
		if(this.checked){
			$.ajax({
				dataType : 'json',
				type : 'put',
				url : baselocation + '/administrator/organization/' + data.value + '/audit',
				success : function(result) {
					if (result.code == 1) {
						layer.msg('该部门启用成功!', {
							icon : 6,
							time : 1000
						}, function() {
							window.location.reload();
						});
					} else {
						layer.alert(result.message, {
							icon : 2
						});
					}
				}
			})
		}else{
			$.ajax({
				dataType : 'json',
				type : 'put',
				url : baselocation + '/administrator/organization/' + data.value + '/audit',
				success : function(result) {
					if (result.code == 1) {
						layer.msg('该部门冻结成功!', {
							icon : 5,
							time : 1000
						}, function() {
							window.location.reload();
						});
					} else {
						layer.alert(result.message, {
							icon : 2
						});
					}
				}
			})
		}
	});
});

function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}
function status_stop(value) {
	layer.confirm('确认要冻结该部门吗？', {
		btn : [ '确定', '取消' ]
	}, function() {
		$.ajax({
			dataType : 'json',
			type : 'put',
			url : baselocation + '/administrator/organization/' + value + '/audit',
			success : function(result) {
				if (result.code == 1) {
					layer.msg("操作成功", {
						time : 1000
					});
					layui.table.reload('demo');
				} else {
					layer.alert(result.message, {
						icon : 2
					});
				}
			}
		})
	});
}

function status_start(value) {
	layer.confirm('确认要启用该部门吗？', {
		btn : [ '确定', '取消' ]
	}, function() {
		$.ajax({
			dataType : 'json',
			type : 'put',
			url : baselocation + '/administrator/organization/' + value + '/audit',
			success : function(result) {
				if (result.code == 1) {
					layer.msg("操作成功", {
						time : 1000
					});
					layui.table.reload('demo');
				} else {
					layer.alert(result.message, {
						icon : 2
					});
				}
			}
		})
	});
}

function admin_delete(value) {
	layer.confirm('确认要删除该部门吗？', {
		btn : [ '确定', '取消' ]
	}, function() {
		$.ajax({
			type : 'delete',
			dataType : 'json',
			url : baselocation + '/administrator/organization/' + value,
			success : function(result) {
				if (result.code == 1) {
					layer.msg("操作成功", {
						time : 1000
					});
					layui.table.reload('demo');
				} else {
					layer.alert(result.message, {
						icon : 2
					});
				}
			}
		})
	});
}