package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("预定芯线使用量")//CostPrice
public class ReserveCoreWireUsage {
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int reserveCoreWireUsageId;
	/* 导入标识 */
	protected int importId;
	/* 年月 */
	protected String yearMonth;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 区分 */
	protected String distinguish;
	/* 部门 */
	protected String department;
	/* 尺寸 */
	protected BigDecimal size;
	/* 预定屑量 */
	protected BigDecimal reserveCrumbsAmount;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	public int getReserveCoreWireUsageId() {
		return reserveCoreWireUsageId;
	}

	public void setReserveCoreWireUsageId(int reserveCoreWireUsageId) {
		this.reserveCoreWireUsageId = reserveCoreWireUsageId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public String getDistinguish() {
		return distinguish;
	}

	public void setDistinguish(String distinguish) {
		this.distinguish = distinguish;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public BigDecimal getSize() {
		return size;
	}

	public void setSize(BigDecimal size) {
		this.size = size;
	}

	public BigDecimal getReserveCrumbsAmount() {
		return reserveCrumbsAmount;
	}

	public void setReserveCrumbsAmount(BigDecimal reserveCrumbsAmount) {
		this.reserveCrumbsAmount = reserveCrumbsAmount;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public int getImportId() {
		return importId;
	}

	public void setImportId(int importId) {
		this.importId = importId;
	}
}