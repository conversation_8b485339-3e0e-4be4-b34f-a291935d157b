<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <title>添加部门</title>
</head>
<body>

<form class="layui-form hr-form-add" action="${ctx}/administrator/organization/create" method="post" id="submitForm">
	<div class="layui-fluid hr-layui-fluid">
		<div class="layui-card">
			<div class="layui-card-body">
				<div class="layui-form-item layui-row">
					<div class="layui-inline layui-col-md5">
						<label class="layui-form-label"><span class="star">*</span>部门名称：</label>
						<div class="layui-input-block">
							<input type="text" id="organizationName" name="organizationName" required=""
								   lay-verify="required" placeholder="请输入部门名称" class="layui-input" />
						</div>
					</div>
					<div class="layui-inline layui-col-md5">
						<label class="layui-form-label"><span  class="star">*</span>状态：</label>
						<div class="layui-input-block">
							<input type="radio" name="status" value="1" title="开启" checked>
							<input type="radio" name="status" value="0" title="冻结"></div>
					</div>
					<div class="layui-inline layui-col-md5">
						<label class="layui-form-label"><span  class="star">*</span>序号：</label>
						<div class="layui-input-block">
							<input type="text" name="orderNo" required="" lay-verify="required" placeholder="请输入序号"
								   autocomplete="off" class="layui-input" onkeyup="value=value.replace(/[^\d\.]/g,'');">
						</div>
					</div>
					<div class="layui-inline layui-col-md12">
						<label class="layui-form-label">备注：</label>
						<div class="layui-input-block">
							<textarea class="layui-textarea" style="resize: none;" name="remarks" maxlength="220"></textarea>
						</div>
					</div>
				</div>
				<div class="layui-form-item layui-row">
					<div class="layui-inline layui-col-md12">
						<label class="layui-form-label"></label>
						<div class="layui-input-block">
							<button type="button" class="layui-btn" lay-submit lay-filter="formDemo"><i class="layui-icon layui-icon-ok"></i>保存</button>
							<button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();"><i class="layui-icon layui-icon-close"></i>取消</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<input type="hidden" name="token" value="${token}" />
</form>
<myfooter>

  <script src="${ctxsta}/hongru/js/admin/admin_organization_addOrModify.js?timer = 1"></script>
</myfooter>
</body>
</html>
