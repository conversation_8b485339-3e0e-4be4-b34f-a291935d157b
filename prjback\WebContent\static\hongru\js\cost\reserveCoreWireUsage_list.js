
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#yearMonth', //指定元素
        btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
    });

    //执行一个 table 实例
    var url = baselocation+'/costPrice/reserveCoreWireUsage/list';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '列表'
        ,page: true //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
        	 {type: 'checkbox',width:50,title: '全选', fixed: 'left'}
            ,{field: 'yearMonth',title: '年月',align:'center', width:150}
            ,{field: 'distinguish',title: '区分',align:'center', width:150,templet: function (d) {
                    if(d.distinguish ==1){
                        return "EM";
                    }else if(d.distinguish ==2){
                        return "EF";
                    }else if (d.distinguish ==3){
                        return "EF09";
                    }else if (d.distinguish ==4){
                        return "ER";
                    }else if (d.distinguish ==5){
                        return "EH";
                    }else{
                        return "UF";
                    }
                }
            }
            ,{field: 'department',title: '部门',align:'center', width:150}
            ,{field: 'size',title: '尺寸',align:'center', width:150}
            ,{field: 'reserveCrumbsAmount',title: '预定屑量',align:'center', width:150}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'delete':
                if(data.length ==0){
                    layer.alert("请选择要操作的数据！");
                    return;
                }else{
                    layer.confirm('确认要删除吗？', {
                        btn : [ '确定', '取消' ] //按钮
                    }, function() {
                        var reserveCoreWireUsageIds = "";
                        for(var i=0; i < data.length; i++){
                            if(reserveCoreWireUsageIds==""){
                            	reserveCoreWireUsageIds = data[i].reserveCoreWireUsageId;
                            }else{
                            	reserveCoreWireUsageIds+= ","+data[i].reserveCoreWireUsageId;
                            }
                        }
                        var index = layer.load(2,{
                            shade:[0.1,'#fff']
                        });
                        $.ajax({
                            type : 'post',
                            dataType : 'json',
                            data: {"reserveCoreWireUsageIds":reserveCoreWireUsageIds},
                            url : baselocation + '/costPrice/reserveCoreWireUsage/delBatch',
                            success : function(result) {
                                layer.closeAll();
                                if (result.code == 1) {
                                    layer.msg("操作成功!", {
                                        shade : 0.3,
                                        time : 1500
                                    }, function() {
                                        layui.table.reload('demo');
                                    });
                                } else {
                                    layer.alert(result.message, {
                                        icon : 2
                                    });
                                }
                            }
                        })
                    });
                }
                break;
        };
    });

});

function search() {
    var arr = new Array();
    $("input:checkbox[name='processIds']:checked").each(function(i){
        arr[i] = $(this).val();
    });
    $("#processIdStr").val(arr.join(","));
    $("#isSearch").val(1);
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    //执行重载
    layui.table.reload('demo', {
        page: {
            curr: 1 //重新从第 1 页开始${ctx}
        }
        ,where: temp
    }, 'data');
}

function toPage(orders) {
    if(orders == 1){
        window.location.href = baselocation+'/costPrice/directRecycling/list/view';
    }else if(orders == 2){
        window.location.href = baselocation+'/costPrice/auxiliaryRecycling/list/view';
    }else if(orders == 3){
        window.location.href = baselocation+'/costPrice/auxiliaryRecyclingArtificial/list/view';
    }else if(orders == 4){
        window.location.href = baselocation+'/costPrice/bookingBundling/list/view';
    }else if(orders == 5){
        window.location.href = baselocation+'/costPrice/reservedRecycling/list/view';
    }else if(orders == 6){
        window.location.href = baselocation+'/costPrice/reserveCrumbsUsage/list/view';
    }else if(orders == 7){
        window.location.href = baselocation+'/costPrice/reserveCoreWireUsage/list/view';
    }else if(orders == 8){
        window.location.href = baselocation+'/costPrice/reservePaintUsage/list/view';
    }else if(orders == 9){
        window.location.href = baselocation+'/costPrice/reserveFreight/list/view';
    }

}
