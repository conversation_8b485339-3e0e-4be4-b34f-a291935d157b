package com.hongru.common.util;

import java.util.regex.Pattern;

/**
* code128校验(专用于住友出入库系统中的条码)
* <AUTHOR>
* @create 2022/09/08 16:44
*/
public class Code128CheckUtils {

	/**
	* 获取完整条码
	* @param code  条码+批号+序列号
	* @throws
	* <AUTHOR>
	* @create 2022/9/9 10:27
	* @return java.lang.String
	*/
	public static String getFullCode(String code) {
		//只保留数字部分
		String REGEX = "[^0-9]";
		String checkData = Pattern.compile(REGEX).matcher(code).replaceAll("").trim();
		int chkSum = 0;
		for(int i=checkData.length();i>1;i--){
			if((checkData.length()+1-i)%2==1){
				int numTemp = Integer.parseInt(checkData.substring(i-1,i)) * 2;
				String  numStr = "";
				if(numTemp<10){
					numStr="0"+numTemp;
				}else{
					numStr = ""+numTemp;
				}
				chkSum = chkSum+Integer.parseInt(numStr.substring(0,1))+Integer.parseInt(numStr.substring(1,2));
			}else{
				chkSum = chkSum+Integer.parseInt(checkData.substring(i-1,i));
			}
		}
		String str = ""+chkSum;
		int chkDg =10-Integer.parseInt(str.substring(str.length()-1,str.length()));
		String chkDgStr = ""+chkDg;
		String dg = chkDgStr.substring(chkDgStr.length()-1,chkDgStr.length());
		return "0"+code+dg;
	}

	/**
	* 校验条码正确性
	* @param code
	* @throws
	* <AUTHOR>
	* @create 2022/9/9 10:29
	* @return true:正确 false:错误
	*/
	public static boolean checkCode(String code){
		boolean result =false;
		if (!StringUtil.isStringEmpty(code) && (code.trim().length() == 14 || code.trim().length() == 25)) {
			String code2 = code.substring(1,code.length()-1);
			String fullCode = getFullCode(code2);
			if(code.equals(fullCode)){
				result =true;
			}
		}
		return result;
	}

	public static void main(String[] args) {
//		System.out.println(checkCode("00433503100110"));
		System.out.println(getFullCode("053060910066"));
	}

}