package com.hongru.common.util;

/**
 * 常用常量
 * <AUTHOR>
 * @create 2015年12月16日 下午5:03:01
 * @version 1.0
 */
public class ChangLiang {
	/* 一天的秒数 */
	public static final int ONT_DAY_SECOND = 86400;
	
	/* 操作人类型 -普通会员 */
	public static final short OPERATOR_TYPE_MEMBER = 1;
	/* 操作人类型 -系统后台人员 */
	public static final short OPERATOR_TYPE_SYSTEM = 2;
	
	/* 是  */
	public static final short COMMON_YES = 1;
	/* 否 */
	public static final short COMMON_NO = 0;
	
	/* 内容类型-文本  */
	public static final short CONTENT_TYPE_TXT = 1;
	/* 内容类型-图片  */
	public static final short CONTENT_TYPE_PIC = 2;
	
	/* 支付方式-支付宝 */
	public static final short PAYTYPE_ALIPAY = 1;
	/* 支付方式-微信支付 */
	public static final short PAYTYPE_WEIXIN = 2;
	/* 支付方式-货到付款 */
	public static final short PAYTYPE_MONEY = 3;
	/* 支付方式-积分支付 */
	public static final short PAYTYPE_INTEGRAL = 4;
	/* 支付方式-账户余额支付 */
	public static final short PAYTYPE_BALANCE = 5;
	/* 支付方式-国库集中支付（转帐） */
	public static final short PAYTYPE_GUOKU = 6;
	/* 支付方式-在线支付 */
	public static final short PAYTYPE_ZAIXIAN = 7;
	/* 支付方式-支票*/
	public static final short PAYTYPE_ZHIPIAO = 8;
	/* 支付方式-无需支付 */
	public static final short PAYTYPE_NONE = -1;
	/* 支付类型-线下支付*/
	public static final short PAYTYPE_OFFLINE = 9;

	/* 支付作用-尾款*/
	public static final short PAYFUNCTION_BALANCE = 0;
	/* 支付作用-定金*/
	public static final short PAYFUNCTION_DEPOSIT = 1;
	
	/* 订单模式-普通订单 */
	public static final short ORDER_MODE_PUTONG = 0;
	/* 订单模式-初始开团 */
	public static final short ORDER_MODE_START_PIN = 1;
	/* 订单模式-参加别人的拼团 */
	public static final short ORDER_MODE_JOIN_PIN = 2;
	
	/*订单取消原因-会员主动取消订单*/
	public static final String CANCEL_ORDER_REASON_MEMBER = "会员主动取消订单";
	/*订单取消原因-后台管理员取消订单*/
	public static final String CANCEL_ORDER_REASON_ADMIN = "后台管理员取消订单";
	/*订单取消原因-供应商取消订单*/
	public static final String CANCEL_ORDER_REASON_SUPPLIER = "供应商取消订单";
	/*订单取消原因-超时未支付系统取消订单*/
	public static final String CANCEL_ORDER_REASON_SYSTEM = "超时未支付系统取消订单";
	/*订单取消原因-拼团过期取消订单*/
	public static final String CANCEL_ORDER_REASON_PINTUAN_GUOQI = "拼团过期取消订单";
	
	/* 配送方式-商家配送-有时间 */
	public static final short DELIVERY_METHOD_PEI_SONG = 0;
	/* 配送方式-上门自提 */
	public static final short DELIVERY_METHOD_ZI_TI = 1;
	/* 配送方式-商家配送-无时间 */
	public static final short DELIVERY_METHOD_PEI_SONG_NOTIME = 2;
	/* 配送方式-跑腿 */
	public static final short DELIVERY_METHOD_PAO_TUI = 3;
	
	/* 广告类型-跟团游 */
	public static final short ADTYPE_GROUP = 1;
	/* 广告类型-自由行 */
	public static final short ADTYPE_FREE = 2;
}
