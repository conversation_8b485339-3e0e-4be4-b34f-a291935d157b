<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/views/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>产品成本设计</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="${ctxStatic}/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="${ctxStatic}/css/admin.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2 class="header-title">产品成本设计</h2>
            </div>
            <div class="layui-card-body">
                <!-- 查询条件 -->
                <form class="layui-form" lay-filter="searchForm">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">年度</label>
                            <div class="layui-input-inline">
                                <input type="text" name="year" placeholder="请输入年度" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">成本键</label>
                            <div class="layui-input-inline">
                                <input type="text" name="costKey" placeholder="请输入成本键" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">客户简称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="customerName" placeholder="请输入客户简称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">产品代码</label>
                            <div class="layui-input-inline">
                                <input type="text" name="productCode" placeholder="请输入产品代码" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="button" class="layui-btn" lay-submit lay-filter="search">查询</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>

                <!-- 工具栏 -->
                <div class="layui-btn-group">
                    <button class="layui-btn" id="addBtn">新增</button>
                    <button class="layui-btn layui-btn-danger" id="deleteBtn">删除</button>
                </div>

                <!-- 数据表格 -->
                <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
            </div>
        </div>
    </div>

    <!-- 表格操作列模板 -->
    <script type="text/html" id="operationTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    </script>

    <script src="${ctxStatic}/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.$;

            // 渲染表格
            var tableIns = table.render({
                elem: '#dataTable',
                url: '${ctx}/productCostDesign/list',
                method: 'post',
                page: true,
                limits: [10, 20, 50, 100],
                limit: 10,
                cols: [[
                    {type: 'checkbox', fixed: 'left'},
                    {field: 'serialNumber', title: '流水号', width: 80, sort: true},
                    {field: 'year', title: '年度', width: 80},
                    {field: 'costKey', title: '成本键', width: 120},
                    {field: 'customerName', title: '客户简称', width: 120},
                    {field: 'productCode', title: '产品代码', width: 120},
                    {field: 'productBarcode', title: '产品条码', width: 120},
                    {field: 'wireDiscName', title: '线盘名称', width: 120},
                    {field: 'productSize', title: '产品尺寸', width: 120},
                    {field: 'productCategory', title: '产品分类', width: 100},
                    {field: 'calculationType', title: '计算区分', width: 100},
                    {field: 'creatorName', title: '创建者', width: 100},
                    {field: 'createdTime', title: '创建时间', width: 150},
                    {title: '操作', toolbar: '#operationTpl', fixed: 'right', width: 150}
                ]]
            });

            // 查询
            form.on('submit(search)', function(data){
                tableIns.reload({
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 新增
            $('#addBtn').on('click', function(){
                layer.open({
                    type: 2,
                    title: '新增产品成本设计',
                    shadeClose: true,
                    shade: 0.8,
                    area: ['90%', '90%'],
                    content: '${ctx}/productCostDesign/add/view',
                    end: function(){
                        tableIns.reload();
                    }
                });
            });

            // 批量删除
            $('#deleteBtn').on('click', function(){
                var checkStatus = table.checkStatus('dataTable');
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要删除的数据');
                    return;
                }
                
                layer.confirm('确定要删除选中的数据吗？', function(index){
                    var ids = [];
                    for(var i = 0; i < data.length; i++){
                        ids.push(data[i].serialNumber);
                    }
                    
                    // 批量删除需要循环调用单个删除接口
                    var deleteCount = 0;
                    var totalCount = ids.length;

                    for(var i = 0; i < ids.length; i++){
                        $.post('${ctx}/productCostDesign/delete', {
                            serialNumber: ids[i]
                        }, function(res){
                            deleteCount++;
                            if(deleteCount === totalCount){
                                layer.msg('删除成功');
                                tableIns.reload();
                            }
                        });
                    }

                    
                    layer.close(index);
                });
            });

            // 监听工具条
            table.on('tool(dataTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'edit'){
                    layer.open({
                        type: 2,
                        title: '编辑产品成本设计',
                        shadeClose: true,
                        shade: 0.8,
                        area: ['90%', '90%'],
                        content: '${ctx}/productCostDesign/edit/view?serialNumber=' + data.serialNumber,
                        end: function(){
                            tableIns.reload();
                        }
                    });
                } else if(obj.event === 'delete'){
                    layer.confirm('确定要删除这条数据吗？', function(index){
                        $.post('${ctx}/productCostDesign/delete', {
                            serialNumber: data.serialNumber
                        }, function(res){
                            if(res.code === 200){
                                layer.msg('删除成功');
                                obj.del();
                            } else {
                                layer.msg(res.msg || '删除失败');
                            }
                        });
                        layer.close(index);
                    });
                }
            });
        });
    </script>
</body>
</html>
