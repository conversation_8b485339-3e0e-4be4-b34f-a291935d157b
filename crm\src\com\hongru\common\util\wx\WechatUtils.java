package com.hongru.common.util.wx;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import net.sf.json.JSONObject;

/**
 * 微信的工具类
 * <AUTHOR>
 * @created 下午3:55:32
 */
public class WechatUtils {
	private static final Logger logger = Logger.getLogger("xmlInfo");
	
	//微信公众平台的AppID--微信web登录校验、支付生成prepayId
//	public static String getWechatAppid() {
//		return WECHAT_APPID;
//	}
	
	//微信公众平台的AppSecret--微信web登录校验
//	public static String getWechatAppSecret(){
//		return WECHAT_APPSECRET;
//	}
	
	
	/*
	 * 微信支付密钥 --用于微信web支付生成Sign
	 */
//	public static String getWechatKey(){
//		return WECHAT_KEY;
//	}
	
	//商户号 --用于微信web支付生成Sign
	
	/*
	 * 微信授权URL
	 * 参数说明 :
	 * appid:公众号的唯一标识;
	 * edirect_uri:授权后重定向的回调链接地址;
	 * response_type:返回类型，请填写code
	 * scope:应用授权作用域，snsapi_base （不弹出授权页面，直接跳转，只能获取用户openid），snsapi_userinfo （弹出授权页面，可通过openid拿到昵称、性别、所在地。并且，即使在未关注的情况下，只要用户授权，也能获取其信息）
	 * state:重定向后会带上state参数，开发者可以填写任意参数值
	 * #wechat_redirect	直接在微信打开链接，可以不填此参数。做页面302重定向时候，必须带此参数
	 */
	public static String  codeRequestURL = "https://open.weixin.qq.com/connect/oauth2/authorize?"
			+ "appid=APPID&redirect_uri=REDIRECT_URI&response_type=code&scope=SCOPE&state=STATE#wechat_redirect";
	
	/*
	 * 通过code换取网页授权access_token URL
	 * 参数说明 :
	 * appid:公众号的唯一标识;
	 * secret：公众号的appsecret密钥(公众微信平台下可以找到)
	 * code：根据微信授权URL获取的code参数
	 * grant_type：填写为authorization_code
	 */
	public static String  accessTokenRequestURL = "https://api.weixin.qq.com/sns/oauth2/access_token?"
			+ "appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code";
	
	public static String tokenRequestURL = "https://api.weixin.qq.com/cgi-bin/token?"
			+ "grant_type=client_credential&appid=APPID&secret=APPSECRET";
	
	public static String userInfoRequestURL = "https://api.weixin.qq.com/sns/userinfo?"
			+ "access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN";
	
	public static String sessionKeyRequestURL = "https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code";
	
	/*
	 * jsapi_ticket,公众号用于调用微信JS接口的临时票据
	 * 参数说明 :
	 * 	access_token:公众号的全局唯一票据
	 * 	type:jsapi
	 */
	public static String ticketRequestURL = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?"
			+ "access_token=ACCESS_TOKEN&type=jsapi";
	/*
	 * 生成签名的时间戳
	 */
	public static int currentTimestamp;
	/*
	 * 生成签名的随机串
	 */
	public static String noncestr = "asd123";
	/*
	 * accesstoken常量，缓存
	 */
	public static String access_token;
	/*
	 * jsapi_ticket常量，缓存
	 */
	public static String js_ticket;

	private static FileOutputStream outputStream;

	
	/**
	 * 将input用sha1算法hash
	 * @param input 需要hash的字符串
	 * @return hash过的字符串
	 * <AUTHOR>
	 * @created 2013-4-23 下午4:14:30
	 */
	public static String sha1(String input) {
		logger.trace("sha1  start.");
		try {
			MessageDigest md = MessageDigest.getInstance("SHA1");
			byte[] result = md.digest(input.getBytes());
	        StringBuilder sb = new StringBuilder();
	        for (int i = 0; i < result.length; i++) {
	            sb.append(Integer.toString((result[i] & 0xff) + 0x100, 16).substring(1));
	        }
	        logger.trace("weixin SHA  end."); 
	        return sb.toString();
		} catch (NoSuchAlgorithmException e) {
			logger.error("exception threw.", e);
			logger.trace("sha1  end.");
			return "";
		}
	}
	
	/**
	 * 判断是否从微信的服务器发过来的消息。一是在认证开发者时用，另外，从微信服务器发过来的每条消息都会带有认证的信息，我们可以选择是否需要做相应的认证。
	 * @param expectedSignature 期望的认证字符串
	 * @param timestamp 时间戳
	 * @param nonce 附加字符串
	 * @param token 
	 * @return
	 */
	public static boolean isFromWechatServer(String expectedSignature, String timestamp, String nonce, String token) {
		logger.trace("isFromWechatServer  start.");
		List<String> list = Arrays.asList(new String[] {token,timestamp, nonce});
		Collections.sort(list);
		StringBuilder sb = new StringBuilder();
		for (String s : list) {
			sb.append(s);
		}

		logger.trace("isFromWechatServer  end.");
		return sha1(sb.toString()).equals(expectedSignature);
		
	}
	
	
	/**
	 * 
	 * 获得微信授权URL（可以获得对应的code）
	 * @param edirect_uri 授权后重定向的回调链接地址;
	 * @param scope 应用授权作用域，snsapi_base（不弹出授权页面，直接跳转，只能获取用户openid）snsapi_userinfo （弹出授权页面，可通过openid拿到昵称、性别、所在地。并且，即使在未关注的情况下，只要用户授权，也能获取其信息）
	 * @param state 重定向后会带上state参数 开发者可以填写任意参数值 (随便一个数字，这里填1)
	 * @return 
	 * <AUTHOR>
	 * @create 2014年9月15日 下午2:25:10
	 */
    public static String getCodeRequest(String redirect_uri,String scope,String state, String appId){
    	logger.trace("getCodeRequest  start.");
        String result = null;
        result  = codeRequestURL.replace("APPID",appId);
        result  = result.replace("REDIRECT_URI", redirect_uri);
//        result  = result.replace("REDIRECT_URI",urlEnodeUTF8(redirect_uri));
        result = result.replace("SCOPE", scope);
        result = result.replace("STATE", state);

        logger.trace("getCodeRequest  end.");       	
        return result;
    }
    
    /**
     * 
     * 通过code换取网页授权access_token　URL
     * @param Code
     * @return
     * <AUTHOR>
     * @create 2014年9月15日 下午3:01:18
     */
    public static String getAccessTokenRequest(String code, String appId, String appSecret)
    {
    	logger.trace("getAccessTokenRequest  start.");
    	String result = null;
    	result  = accessTokenRequestURL.replace("APPID", appId);
    	result  = result.replace("SECRET", appSecret);
    	result  = result.replace("CODE",code);

        logger.trace("getAccessTokenRequest  end.");   
        return result;
    }
    
    /**
     * 
     * 通过code换取网页授权access_token
     * @param result
     * @return
     * <AUTHOR>
     * @throws JSONException 
     * @create 2014年9月15日 下午3:01:18
     */
    public static OAuthToken getAccessToken(String result) throws Exception
    {
    	logger.trace("getAccessToken  start.");
    	JSONObject jsonObject = JSONObject.fromObject(result);
		OAuthToken oAuthToken = new OAuthToken();
		oAuthToken.setAccessToken(jsonObject.getString("access_token"));
		oAuthToken.setExpiresIn(jsonObject.getInt("expires_in"));
		oAuthToken.setRefreshToken(jsonObject.getString("refresh_token"));
		oAuthToken.setOpenid(jsonObject.getString("openid"));
		oAuthToken.setScope(jsonObject.getString("scope"));
        logger.trace("getAccessToken  end.");  
        return oAuthToken;
    }
    
    /**
     * 取access_token
     * @return
     * <AUTHOR>
     * @create 2014/12/23 14:30:00
     */
    public static String getAccessTokenRequest(String appId, String appSecret) throws Exception
    {
    	logger.trace("getTokenRequest  start.");
    	String requestURL = null;
    	requestURL = tokenRequestURL.replace("APPID", appId);
    	requestURL = requestURL.replace("APPSECRET", appSecret);
    	logger.debug("requestURL:" + requestURL);
		String resultJSONStr = WechatUtils.getResultFromHttpUrl(requestURL);
		logger.debug("resultJSONStr:" + resultJSONStr);
		net.sf.json.JSONObject resultJsonObject = net.sf.json.JSONObject.fromObject(resultJSONStr);
		AccessTokenBase accessToken= (AccessTokenBase)net.sf.json.JSONObject.toBean(resultJsonObject, AccessTokenBase.class);
		logger.debug("access_token:" + accessToken.getAccess_token());
		logger.trace("getTokenRequest  end.");   
		return accessToken.getAccess_token();	
    }
    
    /**
     * 获取用户基本信息--无accessToken
     * @param openId
     * @return
     * <AUTHOR>
     * @create 2014/12/23 14:50:00
     */
    public static UserBaseInformation getUserBaseInformation(String openId, String appId, String appSecret) throws Exception
    {
    	logger.trace("getUserBaseInformation  start.");
    	logger.debug("openId:" + openId);
    	String requestURL = null;
    	requestURL = userInfoRequestURL.replace("ACCESS_TOKEN", WechatUtils.getAccessTokenRequest(appId, appSecret));
    	requestURL = requestURL.replace("OPENID", openId);
    	logger.debug("requestURL:" + requestURL);
    	String resultJSONStr = WechatUtils.getResultFromHttpUrl(requestURL);
    	logger.debug("resultJSONStr:" + resultJSONStr);
    	net.sf.json.JSONObject resultJsonObject = net.sf.json.JSONObject.fromObject(resultJSONStr);
		UserBaseInformation userBaseInformation= (UserBaseInformation)net.sf.json.JSONObject.toBean(resultJsonObject, UserBaseInformation.class);
		logger.info("nickname:" + userBaseInformation.getNickname());
		logger.trace("getUserBaseInformation  end.");
		return userBaseInformation;
    }
    /**
     * 获取用户基本信息--有accessToken
     * @param openId
     * @return
     * <AUTHOR>
     * @create 2014/12/23 14:50:00
     */
    public static UserBaseInformation getUserBaseInformation1(String openId, String accessToken) throws Exception
    {
    	logger.trace("getUserBaseInformation  start.");
    	logger.debug("openId:" + openId);
    	String requestURL = null;
    	requestURL = userInfoRequestURL.replace("ACCESS_TOKEN", accessToken);
    	requestURL = requestURL.replace("OPENID", openId);
    	logger.debug("requestURL:" + requestURL);
    	String resultJSONStr = WechatUtils.getResultFromHttpUrl(requestURL);
    	logger.debug("resultJSONStr:" + resultJSONStr);
    	net.sf.json.JSONObject resultJsonObject = net.sf.json.JSONObject.fromObject(resultJSONStr);
    	UserBaseInformation userBaseInformation= (UserBaseInformation)net.sf.json.JSONObject.toBean(resultJsonObject, UserBaseInformation.class);
    	logger.info("nickname:" + userBaseInformation.getNickname());
    	logger.trace("getUserBaseInformation  end.");
    	return userBaseInformation;
    }
   
    /**
     * 获取jsapi_ticket（有效期7200秒），公众号用于调用微信JS接口的临时票据
     * @return String
     * quguanglei
     * 2015年1月19日 下午5:21:04
     */
    public static String getTicketRequest(String appId, String appSecret) throws Exception{
    	logger.trace("getTicketRequest  start.");
    	String requestURL = null;
    	String token = access_token;
    	if(token == null){
    		access_token = getAccessTokenRequest(appId, appSecret);
    		token = access_token;
    	}
    	if(token == null || "".equals(token)){
			return null;
		}
    	requestURL = ticketRequestURL.replace("ACCESS_TOKEN", token);
    	logger.info("requestURL:" + requestURL);
		String resultJSONStr = WechatUtils.getResultFromHttpUrl(requestURL);
		if(resultJSONStr == null || "".equals(resultJSONStr)){
			return null;
		}
		logger.info("resultJSONStr:" + resultJSONStr);
		net.sf.json.JSONObject resultJsonObject = net.sf.json.JSONObject.fromObject(resultJSONStr);
		String ticket = (String) resultJsonObject.get("ticket");
		logger.info("ticket:" + ticket);
		logger.trace("getTicketRequest  end.");   
		return ticket;
    }
    /**
     * 计算 签名
     * @param url 完整的路径
     * @param timestamp 时间戳
     * @param noncestr 随机字符串
     * @return String
     * @throws Exception 
     * quguanglei
     * 2015年1月19日 下午5:23:20
     * 用指定参数替换jsapi_ticket=%s&noncestr=%s&timestamp=%s&url=%s
     * 用sha1加密
     */
    public static String getSignature(String noncestr, Integer timestamp, String url, String appId, String appSecret) throws Exception{
    	logger.trace("getSignature  start.");
    	//jsapi_ticket
    	String jsapi_ticket = js_ticket;
    	if(jsapi_ticket == null){
    		js_ticket = getTicketRequest(appId, appSecret);
    		jsapi_ticket = js_ticket;
    	}
    	StringBuffer sb = new StringBuffer();
    	sb.append("jsapi_ticket=").append(jsapi_ticket).append("&noncestr=").append(noncestr)
    		.append("&timestamp=").append(timestamp).append("&url=").append(url);
    	logger.info("getSignatureParam(before sha1):" + sb.toString());
    	String signature = sha1(sb.toString());
    	logger.trace("getSignature  end.");  
    	return signature;
    }
    
    
    /**
     * 
     * 将String编码（UTF-8）
     * @param str
     * @return
     * <AUTHOR>
     * @create 2014年9月15日 下午2:39:06
     */
    public static String urlEnodeUTF8(String str){
    	logger.trace("urlEnodeUTF8  start.");
        String result = str;
        try {
            result = URLEncoder.encode(str,"UTF-8");
        } catch (Exception e) {
        	logger.error("exception threw.", e);
        }
        logger.trace("urlEnodeUTF8  end.");
        return result;
    }
    
    /**
     * 
     * 发送某个请求，获得请求返回值
     * @param httpUrl
     * @return
     * <AUTHOR>
     * @create 2014年9月15日 下午3:53:19
     */
    public static String getResultFromHttpUrl(String httpUrl) {
    	logger.trace("getResultFromHttpUrl  start.");
		StringBuffer buffer = new StringBuffer();
/*		System.setProperty("sun.net.client.defaultConnectTimeout", "8000");
		System.setProperty("sun.net.client.defaultReadTimeout", "8000");*/
		try {
			URL newUrl = new URL(httpUrl);
			HttpURLConnection hConnect = (HttpURLConnection) newUrl
					.openConnection();
//			hConnect.setConnectTimeout(10000);// 10s内连不上就断开
			hConnect.setDoOutput(true);
			hConnect.setDoInput(true);
			
			BufferedReader rd = new BufferedReader(new InputStreamReader(
					hConnect.getInputStream(), "UTF-8"));
			
			int ch;
			for (int length = 0; (ch = rd.read()) > -1
					&& (200500 <= 0 || length < 200500); length++)
				buffer.append((char) ch);
			rd.close();
			hConnect.disconnect();
			logger.trace("getResultFromHttpUrl  end. result is success");
			return buffer.toString().trim();
		} catch (Exception e) {
			e.getStackTrace();
			logger.error("exception threw.", e);
			logger.trace("getResultFromHttpUrl  end.");
			return "";
		}
	}

	public static Jscode2SessionReturn getSessionKeyAndOpenId(String code, String wxopenAppId, String wxopenAppSecret) {
		logger.trace("getSessionKeyAndOpenId  start.");
    	logger.debug("code:" + code);
    	logger.debug("wxopenAppId:" + wxopenAppId);
    	logger.debug("wxopenAppSecret:" + wxopenAppSecret);
    	String requestURL = null;
    	requestURL = sessionKeyRequestURL.replace("APPID", wxopenAppId);
    	requestURL = requestURL.replace("SECRET", wxopenAppSecret);
    	requestURL = requestURL.replace("JSCODE", code);
    	logger.debug("requestURL:" + requestURL);
    	System.out.println("requestURL"+requestURL);
    	String resultJSONStr = WechatUtils.getResultFromHttpUrl(requestURL);
    	logger.debug("resultJSONStr:" + resultJSONStr);
    	System.out.println("resultJSONStr:" + resultJSONStr);
    	net.sf.json.JSONObject resultJsonObject = net.sf.json.JSONObject.fromObject(resultJSONStr);
    	Jscode2SessionReturn jscode2SessionReturn= (Jscode2SessionReturn)net.sf.json.JSONObject.toBean(resultJsonObject, Jscode2SessionReturn.class);
		logger.info("openId:" + jscode2SessionReturn.getOpenid());
		logger.info("sessionKey:" + jscode2SessionReturn.getSession_key());
		logger.trace("getSessionKeyAndOpenId  end.");
		return jscode2SessionReturn;
	}
	
	/**
	 * 微信小程序生成小程序码并上传到oss
	 * @param sceneStr 
	 * @param accessToken
	 * @return
	 * <AUTHOR>
	 * @create 2018年4月25日 下午2:27:13
	 * 方法介绍：
	 * 官方api文档：https://developers.weixin.qq.com/miniprogram/dev/api/qrcode.html
	 * 接口B：适用于需要的码数量极多的业务场景，不限调用次数
	 */
	public static File getWxopenQrB(String sceneStr, String accessToken, String page, Integer width, String fileName) {
        RestTemplate rest = new RestTemplate();
        try {
            String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+accessToken;
            Map<String,Object> param = new HashMap<>();
            if(sceneStr == null) {
            	sceneStr = "0";
            }
            param.put("scene", sceneStr);//最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
            if(page != null) {
            	param.put("page", page);//必须是已经发布的小程序存在的页面（否则报错），例如 "pages/index/index" ,根路径前不要填加'/',不能携带参数（参数请放在scene字段里），如果不填写这个字段，默认跳主页面
            }
            int width1 = 430;
            if(width != null) {
            	width1 = width;
            }
            param.put("width", width1);//二维码的宽度
            param.put("auto_color", false);//自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调
            Map<String,Object> line_color = new HashMap<>();
            line_color.put("r", 0);
            line_color.put("g", 0);
            line_color.put("b", 0);
            param.put("line_color", line_color);//auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {"r":"xxx","g":"xxx","b":"xxx"} 十进制表示
            logger.error("调用生成微信URL接口传参:" + param);
            MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
            @SuppressWarnings({ "rawtypes", "unchecked" })
			HttpEntity requestEntity = new HttpEntity(param, headers);
            ResponseEntity<byte[]> entity = rest.exchange(url, HttpMethod.POST, requestEntity, byte[].class, new Object[0]);
            logger.error("调用小程序生成微信永久小程序码URL接口返回结果:" + entity.getBody());
            byte[] result = entity.getBody();
//            return result;
           // logger.info(Base64.encoder encodeBase64String(result));
            ByteArrayInputStream inputStream = new ByteArrayInputStream(result);

            File file = new File("C:/upload/"+fileName);
            if (!file.exists()){
                file.createNewFile();
            }
            outputStream = new FileOutputStream(file);
            int len = 0;
            byte[] buf = new byte[1024];
            while ((len = inputStream.read(buf, 0, 1024)) != -1) {
                outputStream.write(buf, 0, len);
            }
            outputStream.flush();
            return file;
//            
//            //把文件上传到OSS 返回图片名称
//            String url = imgu
        } catch (Exception e) {
            logger.error("调用小程序生成微信永久小程序码URL接口异常",e);
        } finally {
        	try {
				outputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
        }
        return null;    
    }
	
}
