package com.hongru.entity.stat;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("报表")//sumitomo
public class ReportForms {

	/* 报表类型-月度原价CR实绩 */
	public static final short REPORTTYPE_MONTHCOSTPRICE = 0;
	/* 报表类型-CR計画 */
	public static final short REPORTTYPE_CRPLAN = 1;
	/* 报表类型-管财差 */
	public static final short REPORTTYPE_GUANCAICHA = 2;
	/* 报表类型-WIN-W製造CR実績 */
	public static final short REPORTTYPE_WIN = 3;

	/* 流水号 */
	@TableId(value="reportFormsId", type= IdType.AUTO)
	protected int reportFormsId;
	/* 状态 */
	protected short state;
	/* 报表类型 */
	protected short reportType;
	/* 标题 */
	protected String title;
	/* 年 */
	protected String year;
	/* 年月 */
	protected String yearAndMonth;
	/* 文件路径 */
	protected String filePath;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	/* 开始年月 */
	@TableField(exist = false)
	protected String startYearAndMonth;
	/* 结束年月 */
	@TableField(exist = false)
	protected String endYearAndMonth;

	public int getReportFormsId() {
		return reportFormsId;
	}

	public void setReportFormsId(int reportFormsId) {
		this.reportFormsId = reportFormsId;
	}

	public short getState() {
		return state;
	}

	public void setState(short state) {
		this.state = state;
	}

	public short getReportType() {
		return reportType;
	}

	public void setReportType(short reportType) {
		this.reportType = reportType;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getYearAndMonth() {
		return yearAndMonth;
	}

	public void setYearAndMonth(String yearAndMonth) {
		this.yearAndMonth = yearAndMonth;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public String getStartYearAndMonth() {
		return startYearAndMonth;
	}

	public void setStartYearAndMonth(String startYearAndMonth) {
		this.startYearAndMonth = startYearAndMonth;
	}

	public String getEndYearAndMonth() {
		return endYearAndMonth;
	}

	public void setEndYearAndMonth(String endYearAndMonth) {
		this.endYearAndMonth = endYearAndMonth;
	}
}