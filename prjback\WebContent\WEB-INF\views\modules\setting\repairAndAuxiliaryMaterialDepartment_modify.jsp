<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>编辑</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/setting/repairAndAuxiliaryMaterialDepartment/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                   <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年度:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="year" name="year" value="${repairAndAuxiliaryMaterialDepartment.year}"/>
                        </div>
                    </div>
                   <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>部门:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="department" name="department" value="${repairAndAuxiliaryMaterialDepartment.department}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>部门属性:</label>
                         <div class="layui-input-block">
                            <select class="layui-select" id="departmentAttributes" name="departmentAttributes" required>
                                <option value="">${repairAndAuxiliaryMaterialDepartment.departmentAttributesStr}</option>
                                <option value="1">直接部门</option>
                                <option value="2">辅助部门</option>
                                <option value="3">共通部门</option>
                                <option value="4">特殊部门</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>类别:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="category" name="category" required>
                                <option value="">${repairAndAuxiliaryMaterialDepartment.categoryStr}</option>
                                <option value="1">MW</option>
                                <option value="2">UF</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">分配部门:</label>
                        <div class="layui-input-block">
 								<input type="text" class="layui-input" id="assignDepartment" name="assignDepartment" value="${repairAndAuxiliaryMaterialDepartment.assignDepartment}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">分配比例:</label>
                        <div class="layui-input-block">
 								<input type="text" class="layui-input" id="ratio" name="ratio" value="${repairAndAuxiliaryMaterialDepartment.ratio}"/>
                        </div>
                    </div>
                 <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="id" name="id" value="${repairAndAuxiliaryMaterialDepartment.id}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/setting/repairAndAuxiliaryMaterialDepartment_modify.js?time=2"></script>
</myfooter>
</body>
</html>
