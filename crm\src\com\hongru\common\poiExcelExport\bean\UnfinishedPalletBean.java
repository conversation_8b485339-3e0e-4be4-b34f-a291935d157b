package com.hongru.common.poiExcelExport.bean;

public class UnfinishedPalletBean {
	/*产品代码*/
	protected String productCode;
	/*木托号*/
	protected String palletNo;
	/*重量*/
	protected String weight;
	/*线圈*/
	protected String coil;
	/*线盘名称*/
	protected String wireReelName;
	/*木托容量*/
	protected String woodenPalletCapacity;
	/*最早入库日期*/
	protected String minStockInTime;

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getPalletNo() {
		return palletNo;
	}

	public void setPalletNo(String palletNo) {
		this.palletNo = palletNo;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getCoil() {
		return coil;
	}

	public void setCoil(String coil) {
		this.coil = coil;
	}

	public String getWireReelName() {
		return wireReelName;
	}

	public void setWireReelName(String wireReelName) {
		this.wireReelName = wireReelName;
	}

	public String getWoodenPalletCapacity() {
		return woodenPalletCapacity;
	}

	public void setWoodenPalletCapacity(String woodenPalletCapacity) {
		this.woodenPalletCapacity = woodenPalletCapacity;
	}

	public String getMinStockInTime() {
		return minStockInTime;
	}

	public void setMinStockInTime(String minStockInTime) {
		this.minStockInTime = minStockInTime;
	}
}
