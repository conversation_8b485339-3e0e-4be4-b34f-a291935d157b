package com.hongru.common.util.poiExcelExport;

import java.util.List;

public class ExcelExp {

    private String fileName;// sheet的名称
    private String[] handers;// sheet里的标题
    private List<String[]> dataset;// sheet里的数据集
    private Integer[] numericColumnArr;// 数字列

    public ExcelExp(String fileName, String[] handers, List<String[]> dataset, Integer[] numericColumnArr) {
        this.fileName = fileName;
        this.handers = handers;
        this.dataset = dataset;
        this.numericColumnArr = numericColumnArr;
    }

    public Integer[] getNumericColumnArr() {
        return numericColumnArr;
    }

    public void setNumericColumnArr(Integer[] numericColumnArr) {
        this.numericColumnArr = numericColumnArr;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String[] getHanders() {
        return handers;
    }

    public void setHanders(String[] handers) {
        this.handers = handers;
    }

    public List<String[]> getDataset() {
        return dataset;
    }

    public void setDataset(List<String[]> dataset) {
        this.dataset = dataset;
    }
}