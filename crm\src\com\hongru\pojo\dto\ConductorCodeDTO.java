package com.hongru.pojo.dto;

import com.hongru.entity.cost.ConductorCode;
import com.hongru.support.page.PageInfo;

import java.util.List;

/**
 * 导体成本编码DTO
 * <AUTHOR>
 */
public class ConductorCodeDTO {
    
    private PageInfo pageInfo;
    private List<ConductorCode> conductorCodeList;
    
    public ConductorCodeDTO() {
    }
    
    public ConductorCodeDTO(PageInfo pageInfo, List<ConductorCode> conductorCodeList) {
        this.pageInfo = pageInfo;
        this.conductorCodeList = conductorCodeList;
    }
    
    public PageInfo getPageInfo() {
        return pageInfo;
    }
    
    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
    
    public List<ConductorCode> getConductorCodeList() {
        return conductorCodeList;
    }
    
    public void setConductorCodeList(List<ConductorCode> conductorCodeList) {
        this.conductorCodeList = conductorCodeList;
    }
}
