<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>油漆单价添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/paintCostPrice/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label" style="width: 100px"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value=""/>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label" style="width: 100px"><span class="star">*</span>供应商油漆名:</label>
                        <div class="layui-input-block" style="margin-left: 130px">
                            <select class="layui-select" id="outPaintName" name="outPaintName" lay-filter="outPaintNameFun" lay-search="true">
                                <option value="">请选择</option>
                                <c:forEach items="${fullPaintNameList}" var="fullPaintName">
                                    <option value="${fullPaintName.fullPaintName}" >${fullPaintName.fullPaintName}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label" style="width: 100px"><span class="star">*</span>供应商:</label>
                        <div class="layui-input-block" style="margin-left: 130px">
                            <select class="layui-select" id="producer" name="producer" lay-search="true">
                                <option value="">全部</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label" style="width: 100px"><span class="star">*</span>购入单价:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="paintPrice" name="paintPrice" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/paintCostPrice_add.js?time=9"></script>
</myfooter>
</body>
</html>
