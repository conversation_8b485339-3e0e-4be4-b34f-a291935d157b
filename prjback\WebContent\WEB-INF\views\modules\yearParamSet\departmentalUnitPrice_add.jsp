<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>新增部门单价</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="formAdd" class="layui-form" method="post" action="">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年度<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="year" id="year" lay-verify="required" placeholder="请选择年度" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">机械类别<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="machineType" id="machineType" lay-verify="required" placeholder="请输入机械类别" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">属性<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <select name="attribute" id="attribute" lay-verify="required">
                            <option value="">请选择属性</option>
                            <option value="01">直接</option>
                            <option value="02">辅助</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">费用项目<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="expenseItem" id="expenseItem" lay-verify="required" placeholder="请输入费用项目" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">工场区分<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <select name="factoryType" id="factoryType" lay-verify="required">
                            <option value="">请选择工场区分</option>
                            <option value="MW" <c:if test="${factoryType == 'MW'}">selected</c:if>>MW</option>
                            <option value="UF" <c:if test="${factoryType == 'UF'}">selected</c:if>>UF</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">SH系数<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="shCoefficient" id="shCoefficient" lay-verify="required|number" placeholder="请输入SH系数" autocomplete="off" class="layui-input" onchange="calculateProjectCost()" oninput="validateNumberInput(this)">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">单价<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="unitPrice" id="unitPrice" lay-verify="required|number" placeholder="请输入单价" autocomplete="off" class="layui-input" onchange="calculateProjectCost()" oninput="validateNumberInput(this)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">项目费用:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="projectCost" id="projectCost" placeholder="自动计算" autocomplete="off" class="layui-input layui-disabled" readonly>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formAdd">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'layer', 'laydate'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    //监听提交
    form.on('submit(formAdd)', function(data){
        $.ajax({
            url: baselocation + '/yearParamSet/departmentalUnitPrice/add',
            type: 'POST',
            data: data.field,
            success: function(result) {
                if(result.code == 1) {
                    layer.msg('新增成功', {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统异常', {icon: 2});
            }
        });
        return false;
    });
});

// 计算项目费用
function calculateProjectCost() {
    var shCoefficientVal = $("#shCoefficient").val();
    var unitPriceVal = $("#unitPrice").val();

    // 验证是否为有效数字
    var shCoefficient = parseFloat(shCoefficientVal);
    var unitPrice = parseFloat(unitPriceVal);

    if (!isNaN(shCoefficient) && !isNaN(unitPrice) && shCoefficient > 0 && unitPrice > 0) {
        var projectCost = shCoefficient * unitPrice;
        $("#projectCost").val(projectCost.toFixed(4));
    } else {
        $("#projectCost").val('');
    }
}

// 限制只能输入数字和小数点
function validateNumberInput(input) {
    input.value = input.value.replace(/[^0-9.]/g, '');
    // 防止输入多个小数点
    var parts = input.value.split('.');
    if (parts.length > 2) {
        input.value = parts[0] + '.' + parts.slice(1).join('');
    }
}
</script>
</body>
</html>
