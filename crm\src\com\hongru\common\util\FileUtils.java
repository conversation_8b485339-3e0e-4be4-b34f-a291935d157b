package com.hongru.common.util;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;

/**
 * 
* 类名称：FileUtils   
* 类描述：FileUtils工具类：提供一些文件操作的方法   
* 创建人：hongru   
* 创建时间：2017年4月8日 下午4:13:04   
*
 */
public class FileUtils {
	
	/**
	 * 分割符号
	 */
	public static final String SPLIT_SYMBOL = "_";
	
	private FileUtils() {
		throw new AssertionError();
	}
	
    /**
     * 根据文件所属类获取文件名称
     * @param belong          文件存放归属
     * @param sourceFileName  原文件名字
     * @return                返回上传路径
     */
	public static String getFileName(String belong, String sourceFileName) {
		StringBuilder fileName = new StringBuilder();
		// 文件存放归属
		fileName.append(belong);
		// 文件时间
		fileName.append(SPLIT_SYMBOL);
		fileName.append(DateUtils.format(new Date(), "yyyyMMdd"));
		// 文件随机名称
		fileName.append(SPLIT_SYMBOL);
		fileName.append(RandomUtils.uuid());
		// 获取文件类型
		fileName.append(sourceFileName.substring(sourceFileName.lastIndexOf('.')));
		return fileName.toString();
	}
	
	/**
	 * 根据文件所属类、16进制获取文件名称
	 * @param belong          文件存放归属
	 * @param sourceFileName  原文件名字
	 * @return                返回上传路径
	 * <AUTHOR>
	 * 因为上面那种生成文件名太长了，所以有了这个生成文件名的方法
	 */
	public static String getFileNameHex(String belong, String sourceFileName) {
		//我们自己写的
		StringBuilder fileName = new StringBuilder();
		String newFileName = Long.toHexString(System.nanoTime());
		int nameLength = newFileName.length();
		if (nameLength < 6) {
			newFileName = newFileName + Long.toHexString(System.nanoTime());
		}
		if(!StringUtil.isStringEmpty(belong)){
			fileName.append(belong).append("/");
		}
		fileName.append(newFileName);
		// 获取文件类型
		fileName.append(sourceFileName.substring(sourceFileName.lastIndexOf('.')));

		return fileName.toString();
	}
	
	/**
	 * 根据云存储系统域名以及文件名获取文件外链地址
	 * @param address		云存储系统域名
	 * @param fileName      文件名
	 * @return              文件外链地址
	 */
	public static String getImageUrl(String address, String fileName) {
		StringBuilder imageUrl = new StringBuilder();
		// 云存储系统域名
		imageUrl.append(address);
		// 分隔符
		imageUrl.append("/");
		// 资源名字
		imageUrl.append(fileName);
		return imageUrl.toString();
	}

	/**
	 * 复制文件
	 * @param multipartFile
	 * @throws
	 * <AUTHOR>
	 * @create 2023/5/11 17:39
	 * @return java.lang.String
	 */
	public static String copyFile(MultipartFile multipartFile) {
		CommonsMultipartFile cf= (CommonsMultipartFile)multipartFile; //获取本地存储路径
		File file = new  File(FileUtils.getSavePath());
		//创建一个目录 （它的路径名由当前 File 对象指定，包括任一必须的父路径。）
		if (!file.exists()) file.mkdirs();
		//新建一个文件
		long time = new Date().getTime();
		String fileName = multipartFile.getOriginalFilename();
		String extension = fileName.lastIndexOf(".") == -1 ? "" : fileName
				.substring(fileName.lastIndexOf(".") + 1);
		File file1 = new File(FileUtils.getSavePath() +"//"+ time + "."+extension);
		//将上传的文件写入新建的文件中
		try {
			cf.getFileItem().write(file1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return file1.getName();
	}

	/**
	 * 复制文件
	 * @param multipartFile
	 * @throws
	 * <AUTHOR>
	 * @create 2023/5/11 17:39
	 * @return java.lang.String
	 */
	public static String copyFileV2(MultipartFile multipartFile) {
		CommonsMultipartFile cf= (CommonsMultipartFile)multipartFile; //获取本地存储路径
		File file = new  File(FileUtils.getSavePath());
		//创建一个目录 （它的路径名由当前 File 对象指定，包括任一必须的父路径。）
		if (!file.exists()) file.mkdirs();
		//新建一个文件
		long time = new Date().getTime();
		String fileName = multipartFile.getOriginalFilename();
//		String extension = fileName.lastIndexOf(".") == -1 ? "" : fileName
//				.substring(fileName.lastIndexOf(".") + 1);
		File file1 = new File(FileUtils.getSavePath() +"//"+ fileName);
		//将上传的文件写入新建的文件中
		try {
			cf.getFileItem().write(file1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return fileName;
	}

	/**
	 * 文件下载
	 * @throws
	 * <AUTHOR>
	 * @create 2023/3/10 9:48
	 * @return
	 */
	public static void download(String urlString, String filename,String savePath) throws Exception {
		String saveUrl = getSavePath()+savePath;
		// 构造URL
		URL url = new URL(urlString);
		// 打开连接
		URLConnection con = url.openConnection();
		//设置请求超时为5s
		con.setConnectTimeout(5*1000);
		// 输入流
		InputStream is = con.getInputStream();

		// 1K的数据缓冲
		byte[] bs = new byte[1024];
		// 读取到的数据长度
		int len;
		// 输出的文件流
		File sf=new File(savePath);
		if(!sf.exists()){
			sf.mkdirs();
		}

		OutputStream os = new FileOutputStream(sf.getPath()+"\\"+filename);
		// 开始读取
		while ((len = is.read(bs)) != -1) {
			os.write(bs, 0, len);
		}
		// 完毕，关闭所有链接
		os.close();
		is.close();
	}

	/**
	* 文件下载V2
	* @throws
	* <AUTHOR>
	* @create 2023/9/19 15:34
	* @return
	*/
	public static String downloadV2(MultipartFile multipartFile,String fileName,String savePath) throws Exception {
		String saveUrl = getSavePath()+savePath;
		CommonsMultipartFile cf= (CommonsMultipartFile)multipartFile; //获取本地存储路径
		File file = new File(saveUrl);
		//创建一个目录 （它的路径名由当前 File 对象指定，包括任一必须的父路径。）
		if (!file.exists()) file.mkdirs();
		//新建一个文件
		String fileNameOld = multipartFile.getOriginalFilename();
		String extension = fileNameOld.lastIndexOf(".") == -1 ? "" : fileNameOld
				.substring(fileNameOld.lastIndexOf(".") + 1);
		File file1 = new File(saveUrl + fileName + "." + extension);
		//将上传的文件写入新建的文件中
		try {
			cf.getFileItem().write(file1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return savePath + fileName + "." + extension;
	}

	/**
	* excel插入数据-单个插入-数据行为String-小数点2位
	* @throws
	* <AUTHOR>
	* @create 2023/9/18 15:42
	* @return
	*/
	public static void inserSheetData(int sheet,String filePath ,String hang ,String lie,String shuju) throws Exception {
		FileInputStream fs=new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);
		//向文件中写入数据
		FileOutputStream out=new FileOutputStream(filePath);

		//自动设置的
		String[] hangs = hang.split(",");
		String[] lies = lie.split(",");
		String[] shujus = shuju.split(",");
		for (int i = 0; i <hangs.length ; i++) {
			for (int j = 0; j <lies.length ; j++) {
				int a =Integer.parseInt(hangs[i]);
				int b =Integer.parseInt(lies[j]);
				//在指定行后追加数据
				HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
				if(row == null){
					row = InsertSheet.createRow(a);
				}

				//新增的四句话，设置CELL格式为文本格式
				HSSFCellStyle cellStyle2 = row.getCell(b).getCellStyle();
				HSSFDataFormat format = hssfWorkbook.createDataFormat();
				cellStyle2.setDataFormat(format.getFormat("0.00"));
				row.getCell(b).setCellStyle(cellStyle2);

				//设置第一个（从0开始）单元格的数据
				if(StringUtil.isStringEmpty(shujus[j]) || !StringUtil.isNumber(shujus[j])){
					continue;
				}
				row.getCell(b).setCellValue(Double.valueOf(shujus[j]));//列+1   1则在第2列插入
				row.getCell(b).setCellType(CellType.NUMERIC);
			}
		}
		InsertSheet.setForceFormulaRecalculation(true);
		out.flush();
		hssfWorkbook.write(out);
		out.close();
	}

	/**
	 * excel插入数据-单个插入-数据行为BigDecimal-小数点2位
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/18 15:42
	 * @return
	 */
	public static void inserSheetDataV2(int sheet, String filePath , String hang , String lie, BigDecimal shuju) throws Exception {
		if(shuju.compareTo(BigDecimal.ZERO) == 1 || shuju.compareTo(BigDecimal.ZERO) == -1){
			FileInputStream fs=new FileInputStream(filePath);
			//使用POI提供的方法得到excel的信息
			POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
			HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
			//获取到工作表，因为一个excel可能有多个工作表
			HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);

			//向文件中写入数据
			FileOutputStream out=new FileOutputStream(filePath);

			//自动设置的
			String[] hangs = hang.split(",");
			String[] lies = lie.split(",");
			String[] shujus = shuju.toString().split(",");
			for (int i = 0; i <hangs.length ; i++) {
				for (int j = 0; j <lies.length ; j++) {
					int a =Integer.parseInt(hangs[i]);
					int b =Integer.parseInt(lies[j]);
					//在指定行后追加数据
					HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
					if(row == null){
						row = InsertSheet.createRow(a);
					}

//					//新增的四句话，设置CELL格式为文本格式
					HSSFCellStyle cellStyle2 = row.getCell(b).getCellStyle();
					HSSFDataFormat format = hssfWorkbook.createDataFormat();
					cellStyle2.setDataFormat(format.getFormat("0.00"));
					row.getCell(b).setCellStyle(cellStyle2);

					//设置第一个（从0开始）单元格的数据
					row.getCell(b).setCellValue(Double.valueOf(shujus[j]));//列+1   1则在第2列插入
					row.getCell(b).setCellType(CellType.NUMERIC);
				}
			}
			InsertSheet.setForceFormulaRecalculation(true);
			out.flush();
			hssfWorkbook.write(out);
			out.close();
		}
	}

	/**
	 * excel插入数据-多行插入-数据行为BigDecimal-小数点2位
	 * @param sheet
	 * @param filePath
	 * @param hangs
	 * @param lies
	 * @param shujus
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/24 16:25
	 * @return void
	*/
	public static void inserSheetDataV3(int sheet, String filePath , int[] hangs , int[] lies, BigDecimal[] shujus) throws Exception {
		FileInputStream fs=new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);

		//向文件中写入数据
		FileOutputStream out=new FileOutputStream(filePath);

		//自动设置的
		for (int i = 0; i <hangs.length ; i++) {
			if(shujus[i]== null || shujus[i].compareTo(BigDecimal.ZERO) == 0){
				continue;
			}
			int a =hangs[i];
			int b =lies[i];
			//在指定行后追加数据
			HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
			if(row == null){
				row = InsertSheet.createRow(a);
			}

			HSSFCell cell = row.getCell(b);
			if(cell == null){
				cell = row.createCell(b);
			}

			//新增的四句话，设置CELL格式为文本格式
			HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
			if(cell.getCellStyle() != null){
				cellStyle2 = cell.getCellStyle();
			}
			HSSFDataFormat format = hssfWorkbook.createDataFormat();
			cellStyle2.setDataFormat(format.getFormat("0.00"));
			row.getCell(b).setCellStyle(cellStyle2);

			row.getCell(b).setCellValue(shujus[i].doubleValue());//列+1   1则在第2列插入
			row.getCell(b).setCellType(CellType.NUMERIC);
		}
		InsertSheet.setForceFormulaRecalculation(true);
		out.flush();
		hssfWorkbook.write(out);
		out.close();
	}

	/**
	* excel插入数据，多行插入-数据行为String-小数点2位
	* @throws
	* <AUTHOR>
	* @create 2023/9/25 19:01
	* @return
	*/
	public static void inserSheetDataV4(int sheet, String filePath , int[] hangs , int[] lies, String[] shujus) throws Exception {
		FileInputStream fs=new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);

		//向文件中写入数据
		FileOutputStream out=new FileOutputStream(filePath);

		//自动设置的
		for (int i = 0; i < hangs.length ; i++) {
			if(StringUtil.isStringEmpty(shujus[i]) || "0".equals(shujus[i].trim())){
				continue;
			}
			int a = hangs[i];
			int b = lies[i];
			//在指定行后追加数据
			HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
			if(row == null){
				row = InsertSheet.createRow(a);
			}
			HSSFCell cell = row.getCell(b);
			if(cell == null){
				cell = row.createCell(b);
			}

			if(StringUtil.isNumber(shujus[i])){
				//新增的四句话，设置CELL格式为文本格式
				HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
				if(cell.getCellStyle() != null){
					cellStyle2 = cell.getCellStyle();
				}
				HSSFDataFormat format = hssfWorkbook.createDataFormat();
				cellStyle2.setDataFormat(format.getFormat("0.00"));
				cell.setCellStyle(cellStyle2);

				cell.setCellValue(Double.valueOf(shujus[i]));//列+1   1则在第2列插入
				cell.setCellType(CellType.NUMERIC);
			}else{
				//新增的四句话，设置CELL格式为文本格式
				HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
				if(cell.getCellStyle() != null){
					cellStyle2 = cell.getCellStyle();
				}
				HSSFDataFormat format = hssfWorkbook.createDataFormat();
				cellStyle2.setDataFormat(format.getFormat("@"));
				cell.setCellStyle(cellStyle2);

				cell.setCellValue(shujus[i]);//列+1   1则在第2列插入
				cell.setCellType(CellType.STRING);
			}
		}
		InsertSheet.setForceFormulaRecalculation(true);
		out.flush();
		hssfWorkbook.write(out);
		out.close();
	}

	/**
	 * excel插入数据-单行插入-数据行为String-不控制小数点个数
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/18 15:42
	 * @return
	 */
	public static void inserSheetDataV5(int sheet,String filePath ,String hang ,String lie,String shuju) throws Exception {
		FileInputStream fs=new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);
		//向文件中写入数据
		FileOutputStream out=new FileOutputStream(filePath);

		//自动设置的
		String[] hangs = hang.split(",");
		String[] lies = lie.split(",");
		String[] shujus = shuju.split(",");
		for (int i = 0; i <hangs.length ; i++) {
			for (int j = 0; j <lies.length ; j++) {
				int a =Integer.parseInt(hangs[i]);
				int b =Integer.parseInt(lies[j]);
				//在指定行后追加数据
				HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
				if(row == null){
					row = InsertSheet.createRow(a);
				}

				//新增的四句话，设置CELL格式为文本格式
				HSSFCellStyle cellStyle2 = row.getCell(b).getCellStyle();
				HSSFDataFormat format = hssfWorkbook.createDataFormat();
				cellStyle2.setDataFormat(format.getFormat("@"));
				row.getCell(b).setCellStyle(cellStyle2);

				//设置第一个（从0开始）单元格的数据
				row.getCell(b).setCellValue(shujus[j]);//列+1   1则在第2列插入
				row.getCell(b).setCellType(CellType.STRING);
			}
		}
		InsertSheet.setForceFormulaRecalculation(true);
		out.flush();
		hssfWorkbook.write(out);
		out.close();
	}

	/**
	 * excel插入数据-多行插入-数据行为String-并且数据行单位为千元，所有插入的数据/1000-小数点后两位
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/25 19:01
	 * @return
	 */
	public static void inserSheetDataV6(int sheet, String filePath , int[] hangs , int[] lies, String[] shujus) throws Exception {
		FileInputStream fs=new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);

		//向文件中写入数据
		FileOutputStream out=new FileOutputStream(filePath);

		//自动设置的
		for (int i = 0; i < hangs.length ; i++) {
//			if(StringUtil.isStringEmpty(shujus[i]) || "0".equals(shujus[i].trim())){
//				continue;
//			}
			if(StringUtil.isStringEmpty(shujus[i])){
				shujus[i] = "0";
			}
			int a = hangs[i];
			int b = lies[i];
			//在指定行后追加数据
			HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
			if(row == null){
				row = InsertSheet.createRow(a);
			}
			HSSFCell cell = row.getCell(b);
			if(cell == null){
				cell = row.createCell(b);
			}

			if(StringUtil.isNumber(shujus[i])){
				//新增的四句话，设置CELL格式为文本格式
				HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
				if(cell.getCellStyle() != null){
					cellStyle2 = cell.getCellStyle();
				}
				HSSFDataFormat format = hssfWorkbook.createDataFormat();
				cellStyle2.setDataFormat(format.getFormat("0.00"));
				cell.setCellStyle(cellStyle2);
				if("0".equals(shujus[i].trim())){
					cell.setCellValue(Double.valueOf(0));//列+1   1则在第2列插入
				}else{
					cell.setCellValue(Double.valueOf(shujus[i])/1000);//列+1   1则在第2列插入
				}
				cell.setCellType(CellType.NUMERIC);
			}else{
				//新增的四句话，设置CELL格式为文本格式
				HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
				if(cell.getCellStyle() != null){
					cellStyle2 = cell.getCellStyle();
				}
				HSSFDataFormat format = hssfWorkbook.createDataFormat();
				cellStyle2.setDataFormat(format.getFormat("@"));
				cell.setCellStyle(cellStyle2);

				cell.setCellValue(shujus[i]);//列+1   1则在第2列插入
				cell.setCellType(CellType.STRING);
			}
		}
		InsertSheet.setForceFormulaRecalculation(true);
		out.flush();
		hssfWorkbook.write(out);
		out.close();
	}

	/**
	 * excel插入数据-多行插入-数据行为BigDecimal-如果是值为0则传0-小数点后两位
	 * @param sheet
	 * @param filePath
	 * @param hangs
	 * @param lies
	 * @param shujus
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/24 16:25
	 * @return void
	 */
	public static void inserSheetDataV7(int sheet, String filePath , int[] hangs , int[] lies, BigDecimal[] shujus) throws Exception {
		FileInputStream fs=new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);

		//向文件中写入数据
		FileOutputStream out=new FileOutputStream(filePath);

		//自动设置的
		for (int i = 0; i <hangs.length ; i++) {
//			if(shujus[i]== null || shujus[i].compareTo(BigDecimal.ZERO) == 0){
//				continue;
//			}
			if(shujus[i]== null ){
				shujus[i] = new BigDecimal(0) ;
			}
			int a =hangs[i];
			int b =lies[i];
			//在指定行后追加数据
			HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
			if(row == null){
				row = InsertSheet.createRow(a);
			}

			HSSFCell cell = row.getCell(b);
			if(cell == null){
				cell = row.createCell(b);
			}

			//新增的四句话，设置CELL格式为文本格式
			HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
			if(cell.getCellStyle() != null){
				cellStyle2 = cell.getCellStyle();
			}
			HSSFDataFormat format = hssfWorkbook.createDataFormat();
			cellStyle2.setDataFormat(format.getFormat("0.00"));
			row.getCell(b).setCellStyle(cellStyle2);

			row.getCell(b).setCellValue(shujus[i].doubleValue());//列+1   1则在第2列插入
			row.getCell(b).setCellType(CellType.NUMERIC);
		}
		InsertSheet.setForceFormulaRecalculation(true);
		out.flush();
		hssfWorkbook.write(out);
		out.close();
	}
	/**
	 * excel插入数据-多行插入-数据行为BigDecimal-如果是值为0则传0-小数点后两位
	 * @param sheet
	 * @param filePath
	 * @param hangs
	 * @param lies
	 * @param shujus
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/24 16:25
	 * @return void
	 */
	public static void inserSheetDataV9(int sheet, String filePath , int[] hangs , int[] lies, BigDecimal[] shujus) throws Exception {
		FileInputStream fs=new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);
		
		//向文件中写入数据
		FileOutputStream out=new FileOutputStream(filePath);
		
		//自动设置的
		for (int i = 0; i <hangs.length ; i++) {
			if(shujus[i]== null ){
				shujus[i] = new BigDecimal(0) ;
			}
			int a =hangs[i];
			int b =lies[i];
			//在指定行后追加数据
			HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
			if(row == null){
				row = InsertSheet.createRow(a);
			}
			
			HSSFCell cell = row.getCell(b);
			if(cell == null){
				cell = row.createCell(b);
			}
			
			//新增的四句话，设置CELL格式为文本格式
			HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
			if(cell.getCellStyle() != null){
				cellStyle2 = cell.getCellStyle();
			}
			HSSFDataFormat format = hssfWorkbook.createDataFormat();
			if(b==9) {
				cellStyle2.setDataFormat(format.getFormat("0"));
			}else {
				cellStyle2.setDataFormat(format.getFormat("0.000"));
			}
			
			row.getCell(b).setCellStyle(cellStyle2);
			
			row.getCell(b).setCellValue(shujus[i].doubleValue());//列+1   1则在第2列插入
			row.getCell(b).setCellType(CellType.NUMERIC);
		}
		InsertSheet.setForceFormulaRecalculation(true);
		out.flush();
		hssfWorkbook.write(out);
		out.close();
	}

	/**
	 * excel插入数据-多行插入-数据行为String-如果是值为0则传0-小数点2位
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/25 19:01
	 * @return
	 */
	public static void inserSheetDataV8(int sheet, String filePath , int[] hangs , int[] lies, String[] shujus) throws Exception {
		FileInputStream fs=new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(sheet);

		//向文件中写入数据
		FileOutputStream out=new FileOutputStream(filePath);

		//自动设置的
		for (int i = 0; i < hangs.length ; i++) {
			if(StringUtil.isStringEmpty(shujus[i])){
				shujus[i] = "0";
			}
			int a = hangs[i];
			int b = lies[i];
			//在指定行后追加数据
			HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
			if(row == null){
				row = InsertSheet.createRow(a);
			}
			HSSFCell cell = row.getCell(b);
			if(cell == null){
				cell = row.createCell(b);
			}

			if(StringUtil.isNumber(shujus[i])){
				//新增的四句话，设置CELL格式为文本格式
				HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
				if(cell.getCellStyle() != null){
					cellStyle2 = cell.getCellStyle();
				}
				HSSFDataFormat format = hssfWorkbook.createDataFormat();
				cellStyle2.setDataFormat(format.getFormat("0.00"));
				cell.setCellStyle(cellStyle2);

				cell.setCellValue(Double.valueOf(shujus[i]));//列+1   1则在第2列插入
				cell.setCellType(CellType.NUMERIC);
			}else{
				//新增的四句话，设置CELL格式为文本格式
				HSSFCellStyle cellStyle2 = hssfWorkbook.createCellStyle();
				if(cell.getCellStyle() != null){
					cellStyle2 = cell.getCellStyle();
				}
				HSSFDataFormat format = hssfWorkbook.createDataFormat();
				cellStyle2.setDataFormat(format.getFormat("@"));
				cell.setCellStyle(cellStyle2);

				cell.setCellValue(shujus[i]);//列+1   1则在第2列插入
				cell.setCellType(CellType.STRING);
			}
		}
		InsertSheet.setForceFormulaRecalculation(true);
		out.flush();
		hssfWorkbook.write(out);
		out.close();
	}

	/**
	* 根据参数读取数据数组
	* @throws
	* <AUTHOR>
	* @create 2023/9/27 9:35
	* @return
	*/
	public static String[] readExcelData(int sheet, String filePath , int[] hangs , int[] lies) throws Exception {
		//数据数组
		String[] dataArr = new String[hangs.length];

		FileInputStream fs = new FileInputStream(filePath);
		//使用POI提供的方法得到excel的信息
		POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
		HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
		//获取到工作表，因为一个excel可能有多个工作表
		HSSFSheet InsertSheet = hssfWorkbook.getSheetAt(sheet);

		//自动设置的
		for (int i = 0; i < hangs.length ; i++) {
			int a = hangs[i];
			int b = lies[i];
			//在指定行后追加数据
			HSSFRow row=InsertSheet.getRow((short)(a));//只更新本单元格
			if(row == null){
				row = InsertSheet.createRow(a);
			}
			HSSFCell cell = row.getCell(b);
			if(cell == null){
				cell = row.createCell(b);
			}
			cell.setCellType(CellType.STRING);
			dataArr[i] = cell.getStringCellValue();
		}

		return dataArr;
	}

	/**
	 * 获取存储路径
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/3 11:41
	 * @return java.lang.String
	 */
	public static String getSavePath() {
		// 获取服务器的实际路径
		String path = ServletUtils.getRequest().getSession().getServletContext().getRealPath("");
		path = path.substring(0, path.lastIndexOf("\\")); // 去掉倒数第一个斜杠 到达项目文件夹下
		path = path.substring(0, path.lastIndexOf("\\"));// 再去掉倒数第一个斜杠 到达webapps文件夹下
		// 文件保存路径
		StringBuilder uploadPath = new StringBuilder(path);
		uploadPath.append("/");
		uploadPath.append("reportFile");
		File file = new File(uploadPath.toString());
		if (!file.exists()) {
			file.mkdirs();
		}
		return file.getPath();
	}

	public static void main(String[] args) throws Exception {
		int[] hangs = {1,1,1};
		int[] lies  = {1,2,3};
		String[] dataArr = readExcelData(1,"C:/upload/2023-09/报表4--WIN-W製造CR実績(2023-09)1695778866.xls",hangs,lies);
		System.out.println(dataArr);
	}
}