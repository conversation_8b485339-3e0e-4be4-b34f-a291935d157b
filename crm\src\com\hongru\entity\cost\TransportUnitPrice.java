package com.hongru.entity.cost;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 运费单价表实体类
 * 
 * <AUTHOR>
 */
@TableName("运费单价表")
public class TransportUnitPrice {

    /* 流水号 */
    @TableId(value = "流水号", type = IdType.AUTO)
    protected int serialNumber;

    /* 年度 */
    @TableField("年度")
    protected String year;

    /* 地区 */
    @TableField("地区")
    protected String region;

    /* 单价 */
    @TableField("单价")
    protected BigDecimal unitPrice;

    // Getters and Setters
    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
}
