
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#yearMonth', //指定元素
        btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
    });

    //执行一个 table 实例
    var url = baselocation+'/costPrice/directRecycling/list';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '列表'
        ,page: true //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
        	{type: 'checkbox',width:50,title: '全选', fixed: 'left'}
            ,{field: 'yearMonth',title: '年月',align:'center', width:130}
            ,{field: 'directCode',title: '区分',align:'center', width:130,templet: function (d) {
                    if(d.directCode ==1){
                        return "EM";
                    }else if(d.directCode ==2){
                        return "EF";
                    }else if (d.directCode ==3){
                        return "EF09";
                    }else if (d.directCode ==4){
                        return "ER";
                    }else if (d.directCode ==5){
                        return "EH";
                    }else{
                        return "UF";
                    }
                }
            }
            ,{field: 'departmentCode',title: '部门',align:'center', width:130}
            ,{field: 'sMCHNum',title: 'SMCH',align:'center', width:130}
            ,{field: 'operatorCostSH',title: '操作工人件费SH',align:'center', width:130}
            ,{field: 'operatorCost',title: '操作工人件费费用',align:'center', width:130}
            ,{field: 'laborCostSH',title: '劳务工人件费SH',align:'center', width:130}
            ,{field: 'laborCost',title: '劳务工人件费费用',align:'center', width:130}
            ,{field: 'electricCostSH',title: '电费SH',align:'center', width:130}
            ,{field: 'electricCost',title: '电费费用',align:'center', width:130}
            ,{field: 'gasCostSH',title: '煤气SH',align:'center', width:130}
            ,{field: 'gasCost',title: '煤气费费用',align:'center', width:130}
            ,{field: 'waterCostSH',title: '水费SH',align:'center', width:130}
            ,{field: 'waterCost',title: '水费费用',align:'center', width:130}
            ,{field: 'nitrogenCostSH',title: '氮气费SH',align:'center', width:130}
            ,{field: 'nitrogenCost',title: '氮气费费用',align:'center', width:130}
            ,{field: 'securityCostSH',title: '保全操作工人件费SH',align:'center', width:130}
            ,{field: 'securityCost',title: '保全操作工人件费费用',align:'center', width:130}
            ,{field: 'repairCostSH',title: '补修费SH',align:'center', width:130}
            ,{field: 'repairCost',title: '补修费费用',align:'center', width:130}
            ,{field: 'materialCostSH',title: '辅材费SH',align:'center', width:130}
            ,{field: 'materialCost',title: '辅材费费用',align:'center', width:130}
        ]]
    });
    
    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'delete':
                if(data.length ==0){
                    layer.alert("请选择要操作的数据！");
                    return;
                }else{
                    layer.confirm('确认要删除吗？', {
                        btn : [ '确定', '取消' ] //按钮
                    }, function() {
                        var costIds = "";
                        for(var i=0; i < data.length; i++){
                            if(costIds==""){
                                costIds = data[i].costId;
                            }else{
                                costIds+= ","+data[i].costId;
                            }
                        }
                        var index = layer.load(2,{
                            shade:[0.1,'#fff']
                        });
                        $.ajax({
                            type : 'post',
                            dataType : 'json',
                            data: {"costIds":costIds},
                            url : baselocation + '/costPrice/directRecyclingl/delBatch',
                            success : function(result) {
                                layer.closeAll();
                                if (result.code == 1) {
                                    layer.msg("操作成功!", {
                                        shade : 0.3,
                                        time : 1500
                                    }, function() {
                                        layui.table.reload('demo');
                                    });
                                } else {
                                    layer.alert(result.message, {
                                        icon : 2
                                    });
                                }
                            }
                        })
                    });
                }
                break;
        };
    });

});

function search() {
    var arr = new Array();
    $("input:checkbox[name='processIds']:checked").each(function(i){
        arr[i] = $(this).val();
    });
    $("#processIdStr").val(arr.join(","));
    $("#isSearch").val(1);
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    //执行重载
    layui.table.reload('demo', {
        page: {
            curr: 1 //重新从第 1 页开始${ctx}
        }
        ,where: temp
    }, 'data');
}

function toPage(orders) {
    if(orders == 1){
        window.location.href = baselocation+'/costPrice/directRecycling/list/view';
    }else if(orders == 2){
        window.location.href = baselocation+'/costPrice/auxiliaryRecycling/list/view';
    }else if(orders == 3){
        window.location.href = baselocation+'/costPrice/auxiliaryRecyclingArtificial/list/view';
    }else if(orders == 4){
        window.location.href = baselocation+'/costPrice/bookingBundling/list/view';
    }else if(orders == 5){
        window.location.href = baselocation+'/costPrice/reservedRecycling/list/view';
    }else if(orders == 6){
        window.location.href = baselocation+'/costPrice/reserveCrumbsUsage/list/view';
    }else if(orders == 7){
        window.location.href = baselocation+'/costPrice/reserveCoreWireUsage/list/view';
    }else if(orders == 8){
        window.location.href = baselocation+'/costPrice/reservePaintUsage/list/view';
    }else if(orders == 9){
        window.location.href = baselocation+'/costPrice/reserveFreight/list/view';
    }

}
