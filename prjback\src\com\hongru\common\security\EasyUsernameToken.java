package com.hongru.common.security;

import org.apache.shiro.authc.UsernamePasswordToken;

/**
* 自定义token 实现免密和密码登录
* <AUTHOR>
* @create 2023/5/30 16:54
*/
//参考地址：https://blog.csdn.net/weixin_40816738/article/details/120904486
public class EasyUsernameToken extends UsernamePasswordToken {

	private static final long serialVersionUID = -2564928913725078138L;

	private ShiroApproveLoginType type;

	public EasyUsernameToken() {
		super();
	}

	/**
	 * 免密登录
	 */
	public EasyUsernameToken(String username) {
		super(username, "", false, null);
		this.type = ShiroApproveLoginType.NOPASSWD;
	}

	/**
	 * 账号密码登录
	 */
	public EasyUsernameToken(String username, String password, boolean rememberMe) {
		super(username, password, rememberMe, null);
		this.type = ShiroApproveLoginType.PASSWORD;
	}

	public ShiroApproveLoginType getType() {
		return type;
	}

	public void setType(ShiroApproveLoginType type) {
		this.type = type;
	}
}