package com.hongru.pojo.dto;

import com.hongru.entity.cost.SmallDepartment;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class SmallDepartmentDTO {

    private PageInfo pageInfo;

    private List<SmallDepartment> smallDepartmentList;

    public SmallDepartmentDTO(PageInfo pageInfo, List<SmallDepartment> smallDepartmentList) {
        super();
        this.pageInfo = pageInfo;
        this.smallDepartmentList = smallDepartmentList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<SmallDepartment> getSmallDepartmentList() {
        return smallDepartmentList;
    }

    public void setSmallDepartmentList(List<SmallDepartment> smallDepartmentList) {
        this.smallDepartmentList = smallDepartmentList;
    }
}
