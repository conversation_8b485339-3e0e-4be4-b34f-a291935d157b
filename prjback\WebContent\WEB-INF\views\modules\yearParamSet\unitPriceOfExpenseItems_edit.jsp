<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>编辑费用项目单价</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="formEdit" class="layui-form" method="post" action="">
            <input type="hidden" name="serialNumber" value="${expenseItemUnitPrice.serialNumber}">

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年度<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="year" id="year" value="${expenseItemUnitPrice.year}" lay-verify="required" placeholder="请选择年度" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">费用项目名称:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="expenseItemName" id="expenseItemName" value="${expenseItemUnitPrice.expenseItemName}" readonly autocomplete="off" class="layui-input layui-disabled">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">费用项目单价<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="expenseItemUnitPrice" id="expenseItemUnitPrice" value="${expenseItemUnitPrice.expenseItemUnitPrice}" lay-verify="required|number" placeholder="请输入费用项目单价" autocomplete="off" class="layui-input" oninput="validateNumberInput(this)">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">比例<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="ratio" id="ratio" value="${expenseItemUnitPrice.ratio}" lay-verify="required|number" placeholder="请输入比例" autocomplete="off" class="layui-input" oninput="validateNumberInput(this)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formEdit">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="cancel();">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'layer', 'laydate'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    //监听提交
    form.on('submit(formEdit)', function(data){
        $.ajax({
            url: baselocation + '/yearParamSet/unitPriceOfExpenseItems/edit',
            type: 'POST',
            data: data.field,
            success: function(result) {
                if(result.code == 1) {
                    layer.msg('编辑成功', {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统异常', {icon: 2});
            }
        });
        return false;
    });
});

// 限制只能输入数字和小数点
function validateNumberInput(input) {
    input.value = input.value.replace(/[^0-9.]/g, '');
    // 防止输入多个小数点
    var parts = input.value.split('.');
    if (parts.length > 2) {
        input.value = parts[0] + '.' + parts.slice(1).join('');
    }
}

// 取消按钮
function cancel() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>

<style>
    .layui-form-label {
        width: 120px;
    }
</style>
</body>
</html>
