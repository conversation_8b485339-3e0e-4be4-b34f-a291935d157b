<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.ExpenseItemUnitPriceMapper">
    
    <sql id="expenseItemUnitPrice_sql">
        eiup.[流水号] AS serialNumber,
        eiup.[年度] AS year,
        eiup.[费用项目名称] AS expenseItemName,
        eiup.[费用项目单价] AS expenseItemUnitPrice,
        eiup.[比例] AS ratio,
        eiup.[创建人姓名] AS creatorName,
        eiup.[创建时间] AS createdTime,
        eiup.[更新人姓名] AS updaterName,
        eiup.[更新时间] AS updatedTime
    </sql>

    <select id="listExpenseItemUnitPricePage" resultType="com.hongru.entity.cost.ExpenseItemUnitPrice">
        SELECT
        <include refid="expenseItemUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[费用项目单价表] eiup
        <where>
            <if test="year != null and year != ''">
                AND eiup.[年度] = #{year}
            </if>
        </where>
        ORDER BY eiup.[年度] DESC, eiup.[费用项目名称]
        <if test="pageInfo != null and pageInfo.limit != null">
            OFFSET #{pageInfo.offset} ROWS
            FETCH NEXT #{pageInfo.limit} ROWS ONLY
        </if>
    </select>

    <select id="listExpenseItemUnitPricePageCount" resultType="int">
        SELECT COUNT(1)
        FROM [CostPrice].[dbo].[费用项目单价表] eiup
        <where>
            <if test="year != null and year != ''">
                AND eiup.[年度] = #{year}
            </if>
        </where>
    </select>

    <select id="selectBySerialNumber" resultType="com.hongru.entity.cost.ExpenseItemUnitPrice">
        SELECT
        <include refid="expenseItemUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[费用项目单价表] eiup
        WHERE eiup.[流水号] = #{serialNumber}
    </select>

    <insert id="insertExpenseItemUnitPrice" parameterType="com.hongru.entity.cost.ExpenseItemUnitPrice">
        INSERT INTO [CostPrice].[dbo].[费用项目单价表]
        (
            [年度],
            [费用项目名称],
            [费用项目单价],
            [比例],
            [创建人姓名],
            [创建时间],
            [更新人姓名],
            [更新时间]
        )
        VALUES
        (
            #{expenseItemUnitPrice.year},
            #{expenseItemUnitPrice.expenseItemName},
            #{expenseItemUnitPrice.expenseItemUnitPrice},
            #{expenseItemUnitPrice.ratio},
            #{expenseItemUnitPrice.creatorName},
            #{expenseItemUnitPrice.createdTime},
            #{expenseItemUnitPrice.updaterName},
            #{expenseItemUnitPrice.updatedTime}
        )
    </insert>

    <update id="updateExpenseItemUnitPrice" parameterType="com.hongru.entity.cost.ExpenseItemUnitPrice">
        UPDATE [CostPrice].[dbo].[费用项目单价表]
        SET
            [年度] = #{expenseItemUnitPrice.year},
            [费用项目单价] = #{expenseItemUnitPrice.expenseItemUnitPrice},
            [比例] = #{expenseItemUnitPrice.ratio},
            [更新人姓名] = #{expenseItemUnitPrice.updaterName},
            [更新时间] = #{expenseItemUnitPrice.updatedTime}
        WHERE [流水号] = #{expenseItemUnitPrice.serialNumber}
    </update>

    <select id="selectExpenseItemUnitPriceByYear" resultType="com.hongru.entity.cost.ExpenseItemUnitPrice">
        SELECT
        <include refid="expenseItemUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[费用项目单价表] eiup
        WHERE eiup.[年度] = #{year}
        ORDER BY eiup.[费用项目名称]
    </select>

    <delete id="deleteExpenseItemUnitPriceByYear">
        DELETE FROM [CostPrice].[dbo].[费用项目单价表]
        WHERE [年度] = #{year}
    </delete>

    <insert id="batchInsertExpenseItemUnitPrice" parameterType="java.util.List">
        INSERT INTO [CostPrice].[dbo].[费用项目单价表]
        (
            [年度],
            [费用项目名称],
            [费用项目单价],
            [比例],
            [创建人姓名],
            [创建时间],
            [更新人姓名],
            [更新时间]
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.year},
            #{item.expenseItemName},
            #{item.expenseItemUnitPrice},
            #{item.ratio},
            #{item.creatorName},
            #{item.createdTime},
            #{item.updaterName},
            #{item.updatedTime}
        )
        </foreach>
    </insert>

    <select id="selectByExpenseItemNameAndYear" resultType="com.hongru.entity.cost.ExpenseItemUnitPrice">
        SELECT
        <include refid="expenseItemUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[费用项目单价表] eiup
        WHERE eiup.[费用项目名称] = #{expenseItemName}
        AND eiup.[年度] = #{year}
    </select>

</mapper>
