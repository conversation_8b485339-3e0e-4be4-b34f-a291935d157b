package com.hongru.common.security;

/**
* Shiro认证枚举
* <AUTHOR>
* @create 2023/5/30 16:52
*/
//参考地址：https://blog.csdn.net/weixin_40816738/article/details/120904486
public enum ShiroApproveLoginType {

	/** 密码登录 */
	PASSWORD("PASSWORD"),
	/** 密码登录 */
	NOPASSWD("NOPASSWORD");
	/** 状态值 */
	private String code;
	private ShiroApproveLoginType(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}
}
