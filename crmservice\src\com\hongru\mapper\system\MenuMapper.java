package com.hongru.mapper.system;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.system.Menu;

/**
 * 
* 类名称：MenuMapper   
* 类描述：Menu / 目录表 数据访问层接口   
* 创建人：hongru   
* 创建时间：2017年4月7日 下午2:13:28   
*
 */
public interface MenuMapper extends BaseMapper<Menu> {
	
	/**
	 * 根据目录类型查询目录列表
	 * @param menuType 目录类型
	 * @return
	 */
	List<Menu> listByType(@Param("menuType") Integer menuType);
	
	/**
	 * 更新目录状态,冻结目录及其及目录
	 * @param menuIds 目录ID列表
	 * @param status 目录状态
	 * @return
	 */
	Integer updateStatusByIds(@Param("menuIds") List<Long> menuIds, @Param("status") Integer status);
	
	/**
	 * 通过目录ID删除角色授权记录
	 * @param menuIds 目录ID列表
	 * @return
	 */
	Integer deleteRoleMenus(@Param("menuIds") List<Long> menuIds);

	/**
	 * 查找某父级下的菜单列表，冗余是否有子集字段
	 * @param parentId
	 * @return
	 * <AUTHOR>
	 * @create 2022年1月19日 下午1:45:07
	 */
	List<Menu> selectMenuForTree(@Param("parentId")Long parentId,@Param("status")Integer status);
}