<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>新增油漆成本编码</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="formAdd" class="layui-form" method="post" action="">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="star">*</span>社内油漆名:</label>
                <div class="layui-input-block">
                    <select name="insidePaintName" lay-verify="required" lay-search>
                        <option value="">请选择油漆品种</option>
                        <c:forEach items="${paintVarieties}" var="variety">
                            <option value="${variety}">${variety}</option>
                        </c:forEach>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn layui-bg-blue" lay-submit lay-filter="formAdd">
                        <i class="layui-icon layui-icon-ok"></i>确定
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="cancel()">
                        <i class="layui-icon layui-icon-close"></i>取消
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form
        ,layer = layui.layer;

    //监听提交
    form.on('submit(formAdd)', function(data){
        $.ajax({
            url: baselocation + '/yearParamSet/paintCode/add',
            type: 'POST',
            data: data.field,
            success: function(result) {
                if(result.code == 1) {
                    layer.msg('新增成功', {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(result.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统异常', {icon: 2});
            }
        });
        return false;
    });
});

// 取消按钮
function cancel() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>
<style>
    .layui-form-label {
        width: 120px;
    }
    .star {
        color: red;
    }
</style>
</body>
</html>
