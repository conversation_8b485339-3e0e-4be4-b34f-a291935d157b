<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.yearRevise.NewYearStoragePreMapper">
    <sql id="newYearStoragePre_sql">
		pc.[年度] AS year,pc.[EM年度予定入库量] AS emNewYearStoragePre,pc.[EF年度予定入库量] AS efNewYearStoragePre,
		pc.[EF09年度予定入库量] AS ef09NewYearStoragePre,pc.[ER年度予定入库量] AS erNewYearStoragePre,
		pc.[EH年度予定入库量] AS ehNewYearStoragePre,
		pc.[创建人标识] AS creatorId,pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[最后修改人标识] AS lastModifierId,pc.[最后修改人姓名] AS lastModifierName,pc.[最后修改时间] AS lastModifiedTime
	</sql>

	<insert id="insertNewYearStoragePre" parameterType="com.hongru.entity.yearRevise.NewYearStoragePre">
		INSERT INTO [CostPrice].[dbo].[年度予定入库量表]
		(
		[年度],
		[EM年度予定入库量],
		[EF年度予定入库量],
		[EF09年度予定入库量],
		[ER年度予定入库量],
		[EH年度予定入库量],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[最后修改人标识],
		[最后修改人姓名],
		[最后修改时间]
		)VALUES(
		#{newYearStoragePre.year},
		#{newYearStoragePre.emNewYearStoragePre},
		#{newYearStoragePre.efNewYearStoragePre},
		#{newYearStoragePre.ef09NewYearStoragePre},
		#{newYearStoragePre.erNewYearStoragePre},
		#{newYearStoragePre.ehNewYearStoragePre},
		#{newYearStoragePre.creatorId},
		#{newYearStoragePre.creatorName},
		#{newYearStoragePre.createdTime},
		#{newYearStoragePre.lastModifierId},
		#{newYearStoragePre.lastModifierName},
		#{newYearStoragePre.lastModifiedTime}
		)
	</insert>

	<select id="selectNewYearStoragePreByYear" resultType="com.hongru.entity.yearRevise.NewYearStoragePre">
		SELECT
		<include refid="newYearStoragePre_sql"/>
		FROM [CostPrice].[dbo].[年度予定入库量表] pc
		<where>
			<if test="year != null and year != ''">
				AND pc.[年度] = #{year}
			</if>
		</where>
	</select>

	<select id="listNewYearStoragePrePage" resultType="com.hongru.entity.yearRevise.NewYearStoragePre">
		SELECT
		<include refid="newYearStoragePre_sql"/>
		FROM [CostPrice].[dbo].[年度予定入库量表] pc
		<where>
			<if test="year != null and year != ''">
				AND pc.[年度] = #{year}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY pc.[年度] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listNewYearStoragePreCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[年度予定入库量表] pc
		<where>
			<if test="year != null and year != ''">
				AND pc.[年度] = #{year}
			</if>
		</where>
	</select>

	<update id="updateNewYearStoragePreInfo">
		UPDATE [CostPrice].[dbo].[年度予定入库量表]
		<set>
			<if test="newYearStoragePre.year != null and newYearStoragePre.year != ''">
				[年度] = #{newYearStoragePre.year},
			</if>
			<if test="newYearStoragePre.emNewYearStoragePre != null and newYearStoragePre.emNewYearStoragePre != ''">
				[EM年度予定入库量] = #{newYearStoragePre.emNewYearStoragePre},
			</if>
			<if test="newYearStoragePre.efNewYearStoragePre != null and newYearStoragePre.efNewYearStoragePre != ''">
				[EF年度予定入库量] = #{newYearStoragePre.efNewYearStoragePre},
			</if>
			<if test="newYearStoragePre.ef09NewYearStoragePre != null and newYearStoragePre.ef09NewYearStoragePre != ''">
				[EF09年度予定入库量] = #{newYearStoragePre.ef09NewYearStoragePre},
			</if>
			<if test="newYearStoragePre.erNewYearStoragePre != null and newYearStoragePre.erNewYearStoragePre != ''">
				[ER年度予定入库量] = #{newYearStoragePre.erNewYearStoragePre},
			</if>
			<if test="newYearStoragePre.ehNewYearStoragePre != null and newYearStoragePre.ehNewYearStoragePre != ''">
				[EH年度予定入库量] = #{newYearStoragePre.ehNewYearStoragePre},
			</if>
			<if test="newYearStoragePre.lastModifierId != null">
				[最后修改人标识] = #{newYearStoragePre.lastModifierId},
			</if>
			<if test="newYearStoragePre.lastModifierName != null and newYearStoragePre.lastModifierName != ''">
				[最后修改人姓名] = #{newYearStoragePre.lastModifierName},
			</if>
			<if test="newYearStoragePre.lastModifiedTime != null">
				[最后修改时间] = #{newYearStoragePre.lastModifiedTime},
			</if>
		</set>
		WHERE [年度] = #{newYearStoragePre.year}
	</update>

	<delete id="deleteNewYearStoragePreByYear">
		DELETE FROM  [CostPrice].[dbo].[年度予定入库量表]
		WHERE [年度] = #{year}
	</delete>
</mapper>