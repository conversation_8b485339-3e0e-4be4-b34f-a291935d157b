<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>部门单价汇总列表</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
          <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <div class="layui-tab layui-tab-brief">
                        <ul class="layui-tab-title">
                            <li class="layui-this" id="MW" onclick="toPage('MW')">MW</li>
                            <li id="UF" onclick="toPage('UF')">UF</li>
                        </ul>
                        <div class="layui-tab-content">

                            <div class="layui-tab-item layui-show">
                                <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                                    <div class="layui-form-item">
                                        <div class="layui-inline layui-col-md3">
											<label class="layui-form-label">日期：</label>
											 <div class="layui-input-inline">
   													<input type="text" class="layui-input" id="time"  name="time" placeholder=" ~ ">
   											 </div>
                                        </div>
                                        <div class="layui-inline layui-col-md2">
			                                <label class="layui-form-label">机械类别:</label>
			                                <div class="layui-input-block">
			                                    <input type="text" class="layui-input" id="machineType" name="machineType" value="" />
			                                </div>                                        
                                        </div>
                                        <div class="layui-inline layui-col-md1 hr-div-btn">
                                            <input type="hidden" id="isSearch" name="isSearch" value="0" />
                                            <input type="hidden" id="pageType" name="pageType" value="MW" />
                                            <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                                        </div>
                                    </div>

                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="demo" lay-filter="test"></table>
        <!-- 自定义表中工具栏 -->
        <script type="text/html" id="barDemo">
        </script>
        <!-- 自定义头部工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
           		<button class="layui-btn layui-btn-sm" lay-event="toExport"><i class="layui-icon layui-icon-download-circle"></i>汇总导出Excel</button>
			 </div>
        </script>
         <!-- 导出Excel  -->
        <form id="formExcell" method="post" action="${ctx}/yearRevise/summaryOfDepartmentalUnitPrices/export"  style="display: none">
			<input type="hidden" id="timeForExcell" name="timeForExcell" />
			<input type="hidden" id="machineTypeForExcell" name="machineTypeForExcell" />
			<input type="hidden" id="pageTypeForExcell" name="pageTypeForExcell" />
		</form>
    </div>
</div>
<myfooter>
    <script src="${ctxsta}/hongru/js/yearRevise/summaryOfDepartmentalUnitPrices_list.js?time=3"></script>
</myfooter>
</body>
</html>
