-- 添加产品成本设计模块菜单配置
-- 执行前请确认成本参数模块的parent_id

-- 1. 首先查询成本参数模块的菜单ID（假设为成本相关的一级菜单）
-- SELECT menu_id, menu_name FROM hr_system_menu WHERE menu_name LIKE '%成本%' AND menu_type = 1;

-- 2. 添加产品成本设计二级菜单（假设成本参数模块的menu_id为某个值，需要根据实际情况调整）
-- 注意：请根据实际的成本参数模块menu_id替换下面的parent_id值

INSERT INTO hr_system_menu (
    parent_id, 
    menu_type, 
    menu_code, 
    menu_name, 
    sort, 
    href, 
    icon, 
    status, 
    permission, 
    create_time, 
    create_by, 
    remarks
) VALUES (
    -- parent_id: 需要替换为实际的成本参数模块menu_id
    (SELECT menu_id FROM hr_system_menu WHERE menu_name LIKE '%成本参数%' AND menu_type = 1 LIMIT 1),
    2, -- menu_type: 2表示二级菜单
    'product_cost_design', -- menu_code
    '产品成本设计', -- menu_name
    100, -- sort: 排序号，可根据需要调整
    '/productCostDesign/list/view', -- href: 链接地址
    'layui-icon-template-1', -- icon: 图标
    1, -- status: 1表示显示
    'cost:productCostDesign:view', -- permission: 权限标识
    GETDATE(), -- create_time
    'system', -- create_by
    '产品成本设计模块菜单' -- remarks
);

-- 3. 获取刚插入的产品成本设计菜单ID
DECLARE @productCostDesignMenuId BIGINT;
SET @productCostDesignMenuId = SCOPE_IDENTITY();

-- 4. 添加产品成本设计的操作权限菜单

-- 4.1 查看权限
INSERT INTO hr_system_menu (
    parent_id, 
    menu_type, 
    menu_code, 
    menu_name, 
    sort, 
    href, 
    icon, 
    status, 
    permission, 
    create_time, 
    create_by, 
    remarks
) VALUES (
    @productCostDesignMenuId,
    0, -- menu_type: 0表示操作
    'product_cost_design_view',
    '查看',
    1,
    '',
    '',
    1,
    'cost:productCostDesign:view',
    GETDATE(),
    'system',
    '产品成本设计查看权限'
);

-- 4.2 新增权限
INSERT INTO hr_system_menu (
    parent_id, 
    menu_type, 
    menu_code, 
    menu_name, 
    sort, 
    href, 
    icon, 
    status, 
    permission, 
    create_time, 
    create_by, 
    remarks
) VALUES (
    @productCostDesignMenuId,
    0, -- menu_type: 0表示操作
    'product_cost_design_add',
    '新增',
    2,
    '',
    '',
    1,
    'cost:productCostDesign:add',
    GETDATE(),
    'system',
    '产品成本设计新增权限'
);

-- 4.3 编辑权限
INSERT INTO hr_system_menu (
    parent_id, 
    menu_type, 
    menu_code, 
    menu_name, 
    sort, 
    href, 
    icon, 
    status, 
    permission, 
    create_time, 
    create_by, 
    remarks
) VALUES (
    @productCostDesignMenuId,
    0, -- menu_type: 0表示操作
    'product_cost_design_edit',
    '编辑',
    3,
    '',
    '',
    1,
    'cost:productCostDesign:edit',
    GETDATE(),
    'system',
    '产品成本设计编辑权限'
);

-- 4.4 删除权限
INSERT INTO hr_system_menu (
    parent_id, 
    menu_type, 
    menu_code, 
    menu_name, 
    sort, 
    href, 
    icon, 
    status, 
    permission, 
    create_time, 
    create_by, 
    remarks
) VALUES (
    @productCostDesignMenuId,
    0, -- menu_type: 0表示操作
    'product_cost_design_delete',
    '删除',
    4,
    '',
    '',
    1,
    'cost:productCostDesign:delete',
    GETDATE(),
    'system',
    '产品成本设计删除权限'
);

-- 4.5 导出权限
INSERT INTO hr_system_menu (
    parent_id, 
    menu_type, 
    menu_code, 
    menu_name, 
    sort, 
    href, 
    icon, 
    status, 
    permission, 
    create_time, 
    create_by, 
    remarks
) VALUES (
    @productCostDesignMenuId,
    0, -- menu_type: 0表示操作
    'product_cost_design_export',
    '导出',
    5,
    '',
    '',
    1,
    'cost:productCostDesign:export',
    GETDATE(),
    'system',
    '产品成本设计导出权限'
);

-- 5. 为管理员角色分配新菜单权限（假设管理员角色ID为1，需要根据实际情况调整）
-- 注意：请根据实际的管理员角色ID替换下面的role_id值

-- 获取新插入的所有菜单ID
DECLARE @menuIds TABLE (menu_id BIGINT);
INSERT INTO @menuIds (menu_id) 
SELECT menu_id FROM hr_system_menu 
WHERE parent_id = @productCostDesignMenuId OR menu_id = @productCostDesignMenuId;

-- 为管理员角色分配权限
INSERT INTO hr_admin_role_menu (role_id, menu_id, create_time, create_by)
SELECT 
    1 as role_id, -- 管理员角色ID，需要根据实际情况调整
    menu_id,
    GETDATE() as create_time,
    'system' as create_by
FROM @menuIds;

-- 查询结果验证
SELECT 
    m.menu_id,
    m.parent_id,
    m.menu_type,
    m.menu_name,
    m.href,
    m.permission,
    m.sort,
    m.status
FROM hr_system_menu m
WHERE m.menu_id = @productCostDesignMenuId 
   OR m.parent_id = @productCostDesignMenuId
ORDER BY m.menu_type DESC, m.sort;

PRINT '产品成本设计模块菜单配置完成！';
PRINT '主菜单ID: ' + CAST(@productCostDesignMenuId AS VARCHAR(10));
