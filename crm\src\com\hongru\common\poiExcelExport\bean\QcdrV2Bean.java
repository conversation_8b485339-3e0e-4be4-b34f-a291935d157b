package com.hongru.common.poiExcelExport.bean;

public class QcdrV2Bean {
	/*产品代码*/
	protected String productCode;
	/*批号*/
	protected String batchNo;
	/*序列号*/
	protected String serialNo;
	/*检查种类*/
	protected String inspectionType;
	/*检查项目*/
	protected String inspectionItems;
	/*设备代码*/
	protected String equipmentCode;
	/*线头号*/
	protected String wireNo;
	/*QCDR*/
	protected String qcdr;
	/*备注*/
	protected String remark;

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	public String getInspectionType() {
		return inspectionType;
	}

	public void setInspectionType(String inspectionType) {
		this.inspectionType = inspectionType;
	}

	public String getInspectionItems() {
		return inspectionItems;
	}

	public void setInspectionItems(String inspectionItems) {
		this.inspectionItems = inspectionItems;
	}

	public String getEquipmentCode() {
		return equipmentCode;
	}

	public void setEquipmentCode(String equipmentCode) {
		this.equipmentCode = equipmentCode;
	}

	public String getWireNo() {
		return wireNo;
	}

	public void setWireNo(String wireNo) {
		this.wireNo = wireNo;
	}

	public String getQcdr() {
		return qcdr;
	}

	public void setQcdr(String qcdr) {
		this.qcdr = qcdr;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
