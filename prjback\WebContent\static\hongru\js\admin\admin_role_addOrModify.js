layui.use(['upload','element','layer','form','laydate'], function(){
	var form = layui.form;
	var $ = layui.jquery
		,upload = layui.upload
		,element = layui.element
		,layer = layui.layer;
	var laydate = layui.laydate;

	form.on('submit(formDemo)', function(data){
		var index = layer.load(2,{
			shade:[0.1,'#fff']
		});
		var actionUrl = $("#submitForm").attr('action');
		ztreeObject = $.fn.zTree.getZTreeObj("ztree");
		var nodes = ztreeObject.getCheckedNodes(true);
		var menuIds = '';
		if (nodes != null && nodes.length > 0) {
			for (var i = 0; i < nodes.length; i++) {
				menuIds += nodes[i].menuId + ',';
			}
		}

		var params = '';
		params += $('#submitForm').serialize();
		params += "&menuIds=" + menuIds;

		$.ajax({
			url : actionUrl,
			type : 'post',
			data : params,
			success : function(result) {
				layer.closeAll();
				if(result.code == 1){
					parent.layer.msg("操作成功!", {
						shade : 0.3,
						time : 1500
					}, function() {
						window.parent.location.reload();
						parent.layer.closeAll();
					});
				}else{
					layer.alert(result.message);
				}
			}
		});
		return false;
	});

});

function closeAll(){
	parent.layer.closeAll();
}