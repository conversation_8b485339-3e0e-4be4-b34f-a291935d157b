package com.hongru.mapper.admin;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.admin.User;
import com.hongru.pojo.vo.UserVO;
import com.hongru.support.page.PageInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
* 类名称：UserMapper   
* 类描述：User / 管理员表 数据访问层接口   
* 创建人：hongru   
* 创建时间：2017年3月31日 下午5:21:11   
*
 */
public interface UserMapper extends BaseMapper<User> {
	
	/**
	 * 根据管理员ID查找管理员信息
	 * @param userId 管理员ID
	 * @return
	 */
	UserVO getUserById(@Param("userId") Long userId);
	
	/**
	 * 根据管理员信息查找管理员列表
	 * @param userVo 
	 * @return
	 */
	List<UserVO> listByUser(UserVO userVo);
	
	/**
	 * 根据分页信息/搜索内容查找管理员列表
	 * @param pageInfo 分页信息
	 * @param search 搜索内容
	 * @return
	 */
	List<UserVO> listByPage(@Param("pageInfo") PageInfo pageInfo, @Param("search") String search,
		@Param("roleIds")Integer[] roleIds,@Param("realNameLike")String realNameLike,@Param("organizationId")Integer organizationId,
		@Param("telephoneLike")String telephoneLike, @Param("emailLike")String emailLike);
	Integer listByPageCount( @Param("search") String search,@Param("roleIds")Integer[] roleIds,@Param("realNameLike")String realNameLike,
		 @Param("organizationId")Integer organizationId,@Param("telephoneLike")String telephoneLike, @Param("emailLike")String emailLike);
	
	/**
	 * 根据部门ID/分页信息/搜索内容查找管理员列表
	 * @param organizationId 部门ID
	 * @param pageInfo 分页信息
	 * @param search 搜索内容
	 * @return
	 */
	List<UserVO> listByOrganizationId(@Param("organizationId") Long organizationId,
			@Param("pageInfo") PageInfo pageInfo, @Param("search") String search);
	Integer listByOrganizationIdCount(Long organizationId, String search);
	
	/**
	 * 根据部门ID重置部门ID
	 * @param organizationId 部门ID
	 * @return
	 */
	Integer updateOrganization(@Param("organizationId") Long organizationId);

	List<UserVO> listByCompanyId(@Param("companyId")Integer companyId);

	/**
	 * 重置密码
	 * @param userId
	 * @param newPassword
	 * <AUTHOR>
	 * @create 2021/11/24 17:22
	 */
	void resetPwd(@Param("userId")long userId, @Param("newPassword")String newPassword);

    User getUserByJobNumber(@Param("jobNumber")String jobNumber);
}