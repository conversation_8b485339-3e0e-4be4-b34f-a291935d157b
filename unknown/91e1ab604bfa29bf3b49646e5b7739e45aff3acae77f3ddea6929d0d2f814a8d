package com.hongru.mapper.xieFenXi;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.xieFenXi.XieFenXiBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface YieldMapper extends BaseMapper<XieFenXiBean> {

    /**
    * 月度原价CR实绩报表所需数据
    * @throws
    * <AUTHOR>
    * @create 2023/9/22 16:29
    * @return
    */
    List<XieFenXiBean> listXieFenXiBean(@Param("produceDate")String produceDate);

    /**
    * ef设备断线时间
    * @throws
    * <AUTHOR>
    * @create 2023/9/22 17:25
    * @return
    */
    List<XieFenXiBean> efBreakNumInfoMap(@Param("produceDate")String produceDate);

    /**
     * 月报表产量
     * @throws
     * <AUTHOR>
     * @create 2023/9/22 16:29
     * @return
     */
    List<XieFenXiBean> listMonthOutputproduct(@Param("produceDate")String produceDate);

    /**
     * 月报表屑量
     * @throws
     * <AUTHOR>
     * @create 2023/9/22 16:29
     * @return
     */
    List<XieFenXiBean> listMonthScrapAmount(@Param("produceDate")String produceDate);
}