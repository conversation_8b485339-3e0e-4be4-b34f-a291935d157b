package com.hongru.mapper.admin;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.admin.Organization;
import com.hongru.support.page.PageInfo;

/**
 * 
* 类名称：OrganizationMapper   
* 类描述：Organization / 部门表 数据访问层接口            
* 创建人：hongru   
* 创建时间：2017年3月31日 下午5:46:23   
*
 */
public interface OrganizationMapper extends BaseMapper<Organization> {
	
	/**
	 * 根据分页信息/搜索内容查找部门列表
	 * @param pageInfo 分页信息
	 * @param search 搜索内容
	 * @return
	 */
	List<Organization> listByPage(@Param("pageInfo") PageInfo pageInfo, @Param("search") String search);
	Integer listByPageCount(@Param("search")String search);

	/**
	* 批量删除部门
	* @throws
	* <AUTHOR>
	* @create 2022/2/11 11:34
	* @return
	*/
    void deleteOrganizations(@Param("organizationIdArr")Integer[] organizationIdArr);

}