package com.hongru.mapper.admin;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.admin.Role;
import com.hongru.support.page.PageInfo;

/**
 * 
* 类名称：RoleMapper   
* 类描述：Role / 角色表 数据访问层接口            
* 创建人：hongru   
* 创建时间：2017年4月6日 下午11:44:10   
*
 */
public interface RoleMapper extends BaseMapper<Role> {
	
	/**
	 * 根据分页信息/搜索内容查找角色列表
	 * @param pageInfo 分页信息
	 * @param search 搜索内容
	 * @return
	 */
	List<Role> listByPage(@Param("pageInfo") PageInfo pageInfo, @Param("search") String search);
	Integer listByPageCount(@Param("search") String search);

	/**
	* 批量删除角色
	* @throws
	* <AUTHOR>
	* @create 2022/2/11 15:22
	* @return
	*/
    void deleteRoles(@Param("roleIdArr")Integer[] roleIdArr);

}