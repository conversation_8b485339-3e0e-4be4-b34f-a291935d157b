package com.hongru.service.system;

import com.baomidou.mybatisplus.service.IService;
import com.hongru.entity.system.SystemOption;

public interface ISystemOptionService extends IService<SystemOption>{
	
	/**
	 * 新增系统参数
	 * @param systemOption
	 * @return
	 * <AUTHOR> 
	 * @create 2018年2月23日
	 */
	Integer insertSystemOption(SystemOption systemOption);
	
	/**
	 * 根据enName获取信息
	 * @param enName
	 * @return
	 * <AUTHOR> 
	 * @create 2018年2月23日
	 */
	SystemOption getSystemOptionByEnName(String enName);
	
	/**
	 * 根据enName更新 
	 * @param systemOption
	 * <AUTHOR> 
	 * @create 2018年2月23日
	 */
	void updateSystemOptionByEnName(SystemOption systemOption);
}
