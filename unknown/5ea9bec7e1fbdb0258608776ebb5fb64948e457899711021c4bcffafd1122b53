package com.hongru.service.xieFenXi;

import com.hongru.entity.xieFenXi.EMScrapAmountReport;
import com.hongru.entity.xieFenXi.ScrapReasonDetail;
import com.hongru.entity.xieFenXi.XieFenXiBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface IXieFenXiService {

	/**
	* 月度原价CR实绩报表所需数据
	* @throws 
	* <AUTHOR>
	* @create 2023/9/22 16:25
	* @return 
	*/
	List<XieFenXiBean> listXieFenXiBean(String produceDate) throws Exception;

	/**
	* ef设备断线时间
	* @throws
	* <AUTHOR>
	* @create 2023/9/22 17:23
	* @return
	*/
	Map<String, Float> efBreakNumInfoMap(String produceDate)throws Exception;

	/**
	 * 月报表产量
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/22 17:23
	 * @return
	 */
	List<XieFenXiBean> listMonthOutputproduct(String produceDate)throws Exception;

	/**
	 * 月报表屑量
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/22 17:23
	 * @return
	 */
	List<XieFenXiBean> listMonthScrapAmount(String produceDate)throws Exception;

	/**
	 * 屑量(月报表中使用)
	 * @param machineId
	 * @param
	 * @return
	 * <AUTHOR>
	 * @create 2022年2月25日
	 * 说明：machineId 只是前3位  如4011 传401
	 */
	List<EMScrapAmountReport> listDailyScrapAmountForMonth(String machineId, String productDate)throws Exception;

	/**
	 * 列出屑原因
	 * @return
	 * @throws Exception
	 */
	public List<ScrapReasonDetail> selectScrapReason()throws Exception;
	
	/**
	 *  单位机械时间
    * @param timeStartStr
    * @param timeEndStr
    * @throws Exception
    * <AUTHOR>
    * @create 2024/01/04 09:43
    * @return
    */
	HashMap<String,Float> listxieFenXiBeanForMachineTime(int monthInterval, String timeStartStr, String timeEndStr)throws Exception;
}