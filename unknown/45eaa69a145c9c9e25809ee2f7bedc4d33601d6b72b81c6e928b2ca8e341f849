package com.hongru.service;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;

import com.hongru.common.enums.CloudServiceEnum;
import com.hongru.common.exception.OSSException;
import com.hongru.entity.ImageLog;

/**
 * 
*    
* 类名称：IBaseCloudStorageService   
* 类描述：IBaseCloudStorageService 云存储上传文件基类接口   
* 创建人：hongru   
* 创建时间：2017年7月30日 下午10:19:37   
*
 */
public interface IBaseCloudStorageService {
	
	/**
	 * 获取处理器类型
	 * @return
	 */
	CloudServiceEnum getCloudServiceType();
	
	/**
	 * 获取上传凭证
	 * 
	 * @return 上传凭证
	 * @throws OSSException
	 */
	String getUploadToken() throws OSSException;

    /**
     * 字节数组上传,可以支持将内存中的字节数组上传到空间中
     * 
     * @param data         文件字节数组
     * @param belong       文件所属类
     * @param fileName     原文件名字
     * @return ImageLog    云存储图片记录表
     * @throws OSSException 
     */
	ImageLog upload(byte[] data, String belong, String fileName) throws OSSException;
	
	/**
	 * 上传后可直接下载
	 * @param data
	 * @param belong
	 * @param fileName
	 * @return
	 * @throws OSSException
	 * <AUTHOR>
	 * @create 2021年12月27日
	 */
	ImageLog uploadForDirectDownload(byte[] data, String belong, String fileName) throws OSSException;


    /**
     * 数据流上传,InputStream对象的上传
     * 
     * @param inputStream   文件字节流
     * @param belong        文件所属类
     * @param fileName     原文件名字     
     * @return ImageLog     云存储图片记录表
     * @throws UnsupportedEncodingException 
     */
	ImageLog upload(InputStream inputStream, String belong, String fileName) throws OSSException;

}
