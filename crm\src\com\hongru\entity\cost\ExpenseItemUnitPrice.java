package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

/**
 * 费用项目单价表实体类
 * <AUTHOR>
 */
@TableName("费用项目单价表")
public class ExpenseItemUnitPrice {
    
    /* 状态-正常 */
    public static final short STATE_NORMAL = 0;
    /* 状态-删除 */
    public static final short STATE_DELETED = 9;
    
    /* 流水号 */
    @TableId(value="流水号", type= IdType.AUTO)
    protected int serialNumber;
    
    /* 年度 */
    @TableField("年度")
    protected String year;
    
    /* 费用项目名称 */
    @TableField("费用项目名称")
    protected String expenseItemName;
    
    /* 费用项目单价 */
    @TableField("费用项目单价")
    protected BigDecimal expenseItemUnitPrice;
    
    /* 比例 */
    @TableField("比例")
    protected BigDecimal ratio;
    
    /* 创建人姓名 */
    @TableField("创建人姓名")
    protected String creatorName;
    
    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;
    
    /* 更新人姓名 */
    @TableField("更新人姓名")
    protected String updaterName;
    
    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;

    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getExpenseItemName() {
        return expenseItemName;
    }

    public void setExpenseItemName(String expenseItemName) {
        this.expenseItemName = expenseItemName;
    }

    public BigDecimal getExpenseItemUnitPrice() {
        return expenseItemUnitPrice;
    }

    public void setExpenseItemUnitPrice(BigDecimal expenseItemUnitPrice) {
        this.expenseItemUnitPrice = expenseItemUnitPrice;
    }

    public BigDecimal getRatio() {
        return ratio;
    }

    public void setRatio(BigDecimal ratio) {
        this.ratio = ratio;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        return "ExpenseItemUnitPrice{" +
                "serialNumber=" + serialNumber +
                ", year='" + year + '\'' +
                ", expenseItemName='" + expenseItemName + '\'' +
                ", expenseItemUnitPrice=" + expenseItemUnitPrice +
                ", ratio=" + ratio +
                ", creatorName='" + creatorName + '\'' +
                ", createdTime='" + createdTime + '\'' +
                ", updaterName='" + updaterName + '\'' +
                ", updatedTime='" + updatedTime + '\'' +
                '}';
    }
}
