package com.hongru.entity.cost;

import com.hongru.support.page.PageInfo;

import java.util.List;

/**
 * 产品成本设计DTO
 * 
 * <AUTHOR>
 */
public class ProductCostDesignDTO {
    
    /**
     * 产品成本设计列表
     */
    private List<ProductCostDesign> productCostDesignList;
    
    /**
     * 分页信息
     */
    private PageInfo pageInfo;
    
    public ProductCostDesignDTO() {
    }
    
    public ProductCostDesignDTO(List<ProductCostDesign> productCostDesignList, PageInfo pageInfo) {
        this.productCostDesignList = productCostDesignList;
        this.pageInfo = pageInfo;
    }
    
    public List<ProductCostDesign> getProductCostDesignList() {
        return productCostDesignList;
    }
    
    public void setProductCostDesignList(List<ProductCostDesign> productCostDesignList) {
        this.productCostDesignList = productCostDesignList;
    }
    
    public PageInfo getPageInfo() {
        return pageInfo;
    }
    
    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
}
