<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.xieFenXi.YieldMapper">

    <select id="listXieFenXiBean" resultType="com.hongru.entity.xieFenXi.XieFenXiBean">
        select
            Convert(varchar(10),a.[日期] ,120) as date,
            a.[设备号] as machineId,
            sum(b.[机械时间]) as machineTime,
            sum(DATEDIFF(MINUTE,Convert(varchar(19),b.[停机时间],121),
            Convert(varchar(19),b.[恢复时间],121))) AS diffDate
        from [屑分析].[dbo].[产量表] a LEFT JOIN [屑分析].[dbo].[稼动率表] b ON a.[设备号]=b.[设备号] AND Convert(varchar(10),a.[日期] ,120) = Convert(varchar(10),b.[日期] ,120) AND a.[产品型号] = b.[产品型号]
        <where>
            a.[计算标记] = 1
            <if test="produceDate != null and produceDate != ''">
                and Convert(varchar(7),a.日期 ,120) = #{produceDate}
            </if>
        </where>
		GROUP BY Convert(varchar(10),a.[日期] ,120),a.[设备号] order by a.[设备号] ASC
    </select>

    <select id="efBreakNumInfoMap" resultType="com.hongru.entity.xieFenXi.XieFenXiBean">
        SELECT
            Convert(varchar(10),a.[日期] ,120) as date,
            a.[设备号] as machineId,
            sum(DATEDIFF(MINUTE,Convert(varchar(19),a.[停机时间],121),
            Convert(varchar(19),a.[恢复时间],121))) AS diffDate
        FROM [屑分析].[dbo].[稼动率表] a
        <where>
            a.[停机原因]='断线' and a.[设备号]&gt;= '5000' and  a.[设备号]&lt;='6000'
            <if test="produceDate != null and produceDate != ''">
                and Convert(varchar(7),a.日期 ,120) = #{produceDate}
            </if>
        </where>
		GROUP BY  Convert(varchar(10),a.[日期] ,120),a.[设备号] order by a.[设备号],Convert(varchar(10),a.[日期] ,120) ASC
    </select>

    <select id="listMonthOutputproduct" resultType="com.hongru.entity.xieFenXi.XieFenXiBean">
        select
            Convert(varchar(7),a.[日期] ,120) as date,
            convert(varchar(3) ,a.[设备号] ,121) as machineId,
            sum([早班产量])+sum([中班产量])+sum([晚班产量]) as  outputproduct
        from [屑分析].[dbo].[产量表] a
        <where>
            <if test="produceDate != null and produceDate != ''">
                Convert(varchar(7),a.日期 ,120)  = #{produceDate}
            </if>
        </where>
        group by Convert(varchar(7),a.[日期] ,120),convert(varchar(3) ,a.[设备号] ,121)
    </select>

    <select id="listMonthScrapAmount" resultType="com.hongru.entity.xieFenXi.XieFenXiBean">
        select
            Convert(varchar(7),a.[日期] ,120) as date,
            convert(varchar(3) ,a.[设备号] ,121) as machineId,
            sum(屑量) as  scrapAmount
        from [屑分析].[dbo].[屑量表] a
        <where>
            1=1
            <if test="produceDate != null and produceDate != ''">
                AND Convert(varchar(7),a.日期 ,120)  = #produceDate# and [设备号]!=''
            </if>
        </where>
        group by Convert(varchar(7),a.[日期] ,120),convert(varchar(3) ,a.[设备号] ,121)
    </select>

</mapper>