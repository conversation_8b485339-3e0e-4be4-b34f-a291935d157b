package com.hongru.entity.yearRevise;

import java.math.BigDecimal;

/**
* 补修辅材查询返回结果实体类
* <AUTHOR>
* @create 2024/01/29 13:55
*/
public class RepairAndAuxiliaryShCoefficientBean {
	/* 机器类别 */
	protected String machineType;
	/* 部门 */
	protected String department;
	/* 费用项目 */
	protected String expenseItem ;
	/* 予定金额 */
	protected BigDecimal moneyPre;
	/* 实际金额 */
	protected BigDecimal moneyAct;
	/* 予定机械时间 */
	protected BigDecimal machineTimePreAvg;
	/* 补辅材分配额SH系数 */
	protected BigDecimal repairAndAuxiliarySH;
	
	public String getMachineType() {
		return machineType;
	}
	public void setMachineType(String machineType) {
		this.machineType = machineType;
	}
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public String getExpenseItem() {
		return expenseItem;
	}
	public void setExpenseItem(String expenseItem) {
		this.expenseItem = expenseItem;
	}
	public BigDecimal getMoneyPre() {
		return moneyPre;
	}
	public void setMoneyPre(BigDecimal moneyPre) {
		this.moneyPre = moneyPre;
	}
	public BigDecimal getMoneyAct() {
		return moneyAct;
	}
	public void setMoneyAct(BigDecimal moneyAct) {
		this.moneyAct = moneyAct;
	}
	public BigDecimal getMachineTimePreAvg() {
		return machineTimePreAvg;
	}
	public void setMachineTimePreAvg(BigDecimal machineTimePreAvg) {
		this.machineTimePreAvg = machineTimePreAvg;
	}
	public BigDecimal getRepairAndAuxiliarySH() {
		return repairAndAuxiliarySH;
	}
	public void setRepairAndAuxiliarySH(BigDecimal repairAndAuxiliarySH) {
		this.repairAndAuxiliarySH = repairAndAuxiliarySH;
	}
}