<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>编辑</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/setting/smallDepartment/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                   <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label"><span class="star">*</span>年度:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="year" name="year" value="${smallDepartment.year}"/>
                        </div>
                    </div>
                   <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label"><span class="star">*</span>编号:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="departmentCode" name="departmentCode" value="${smallDepartment.departmentCode}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label"><span class="star">*</span>部门:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="departmentName" name="departmentName" value="${smallDepartment.departmentName}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4	">
                        <label class="layui-form-label"><span class="star">*</span>类别:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="category" name="category"  required>
                                <option value="${smallDepartment.category}">${smallDepartment.category}</option>
                                <option value="1">MW</option>
                                <option value="2">UF</option>
                            </select>
                        </div>
                     </div>

                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label"><span class="star">*</span>补修UF分摊比例:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ufShareRateOfRepaire" name="ufShareRateOfRepaire" value="${smallDepartment.ufShareRateOfRepaire}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label"><span class="star">*</span>辅材UF分摊比例:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ufShareRateOfAuxiliary" name="ufShareRateOfAuxiliary" value="${smallDepartment.ufShareRateOfAuxiliary}"/>
                        </div>
                    </div>
                 </div>
                 <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="costId" name="costId" value="${smallDepartment.costId}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/setting/smallDepartment_modify.js?time=1"></script>
</myfooter>
</body>
</html>
