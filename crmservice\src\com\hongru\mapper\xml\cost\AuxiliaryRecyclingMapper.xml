<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.AuxiliaryRecyclingMapper">
    <sql id="auxiliaryRecycling_sql">
		au.[流水号] AS costId,au.[导入标识] AS importId,au.[年月] AS yearMonth,au.[年] AS year,au.[月] AS month,
		au.[区分] AS auxiliaryCode,au.[直接部门] AS directDepartmentCode,au.[辅助部门] AS auxiliaryDepartmentCode,
		au.[费用项目] AS expenseItem,au.[SMCH] AS sMCHNum,au.[SH] AS sHNum,au.[单价] AS unitPrice,
		au.[金额] AS amount,au.[创建人标识] AS creatorId,au.[创建人姓名] AS creatorName,au.[创建时间] AS createdTime
	</sql>

	<insert id="insertAuxiliaryRecycling" parameterType="com.hongru.entity.cost.AuxiliaryRecycling">
		INSERT INTO [CostPrice].[dbo].[预定辅助部门回收计算补修辅材表]
		(
		[导入标识],
		[年月],
		[年],
		[月],
		[区分],
		[直接部门],
		[辅助部门],
		[费用项目],
		[SMCH],
		[SH],
		[单价],
		[金额],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{auxiliaryRecycling.importId},
		#{auxiliaryRecycling.yearMonth},
		#{auxiliaryRecycling.year},
		#{auxiliaryRecycling.month},
		#{auxiliaryRecycling.auxiliaryCode},
		#{auxiliaryRecycling.directDepartmentCode},
		#{auxiliaryRecycling.auxiliaryDepartmentCode},
		#{auxiliaryRecycling.expenseItem},
		#{auxiliaryRecycling.sMCHNum},
		#{auxiliaryRecycling.sHNum},
		#{auxiliaryRecycling.unitPrice},
		#{auxiliaryRecycling.amount},
		#{auxiliaryRecycling.creatorId},
		#{auxiliaryRecycling.creatorName},
		#{auxiliaryRecycling.createdTime}
		)
	</insert>
	
	<delete id="deleteAuxiliaryRecyclingByCostId">
		DELETE FROM [CostPrice].[dbo].[预定辅助部门回收计算补修辅材表] WHERE [流水号] = #{costId}
	</delete>
	
	<select id="listAuxiliaryRecyclingByYearMonth" resultType="com.hongru.entity.cost.AuxiliaryRecycling">
		SELECT
		<include refid="auxiliaryRecycling_sql"/>
		FROM [CostPrice].[dbo].[预定辅助部门回收计算补修辅材表] au
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND au.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<select id="listAuxiliaryRecyclingByParam" resultType="com.hongru.entity.cost.AuxiliaryRecycling">
		SELECT
		<include refid="auxiliaryRecycling_sql"/>
		FROM [CostPrice].[dbo].[预定辅助部门回收计算补修辅材表] au
		<where>
			<if test="timeMin != null and timeMin != ''">
				AND CONVERT ( DATETIME, au.[年月] + '-01 00:00:00' ) &gt;= #{timeMin}
			</if>
			<if test="timeMax != null and timeMax != ''">
				AND CONVERT ( DATETIME, au.[年月] + '-01 00:00:00' ) &lt;= #{timeMax}
			</if>
			<if test="year != null">
				AND au.[年] = #{year}
			</if>
			<if test="month != null">
				AND au.[月] = #{month}
			</if>
			<if test="yearMonth != null and yearMonth != ''">
				AND au.[年月] = #{yearMonth}
			</if>
			<if test="expenseItemStrArr != null and expenseItemStrArr != ''">
				AND au.[费用项目] IN
				<foreach collection="expenseItemStrArr" item="expenseItem" open="(" close=")" separator=",">
					#{expenseItem}
				</foreach>
			</if>
			<if test="auxiliaryCodeStrArr != null and auxiliaryCodeStrArr != ''">
				AND au.[区分] IN
				<foreach collection="auxiliaryCodeStrArr" item="auxiliaryCode" open="(" close=")" separator=",">
					#{auxiliaryCode}
				</foreach>
			</if>
		</where>
	</select>

	<select id="listAuxiliaryRecyclingPage" resultType="com.hongru.entity.cost.AuxiliaryRecycling">
		SELECT
		<include refid="auxiliaryRecycling_sql"/>
		FROM [CostPrice].[dbo].[预定辅助部门回收计算补修辅材表] au
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND au.[年月] = #{yearMonth}
			</if>
			<if test="auxiliaryCode != null and auxiliaryCode != ''">
				AND au.[区分] = #{auxiliaryCode}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY au.[年月] DESC,au.[流水号] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listAuxiliaryRecyclingPageCount" resultType="integer">
		SELECT
		count(1)
		FROM [CostPrice].[dbo].[预定辅助部门回收计算补修辅材表] au
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND au.[年月] = #{yearMonth}
			</if>
			<if test="auxiliaryCode != null and auxiliaryCode != ''">
				AND au.[区分] = #{auxiliaryCode}
			</if>
		</where>
	</select>
	<select id="listAuxiliaryRecyclingByDateRange" resultType="com.hongru.entity.cost.AuxiliaryRecycling">
		SELECT  [直接部门] AS directDepartmentCode, [辅助部门] AS auxiliaryDepartmentCode, [费用项目] AS expenseItem, SUM([金额]) / #{monthInterval} AS amount 
		FROM [CostPrice].[dbo].[预定辅助部门回收计算补修辅材表]
		<where>
			<if test="flag == 0">
				AND [区分] !=6
			</if>
			<if test="flag == 1">
				AND [区分] =6
			</if>
			<if test="expenseItem != null and expenseItem != ''">
				AND [费用项目] = #{expenseItem}
			</if>
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
           <if test="departArr != null and departArr != ''">
                AND [直接部门]  IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>
		</where>
		GROUP BY [直接部门],[辅助部门],[费用项目]
	</select>
	<select id="listAuxiliaryRecyclingAmountTotal" resultType="com.hongru.entity.cost.AuxiliaryRecycling">
		SELECT  [辅助部门] AS auxiliaryDepartmentCode, [费用项目] AS expenseItem, SUM([金额]) / #{monthInterval} AS amount 
		FROM [CostPrice].[dbo].[预定辅助部门回收计算补修辅材表]
		<where>
			<if test="flag == 0">
				AND [区分] !=6
			</if>
			<if test="flag == 1">
				AND [区分] =6
			</if>
			<if test="expenseItem != null and expenseItem != ''">
				AND [费用项目] = #{expenseItem}
			</if>
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
           <if test="departArr != null and departArr != ''">
                AND [直接部门]  IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>
		</where>
		GROUP BY [辅助部门],[费用项目]
	</select>
</mapper>