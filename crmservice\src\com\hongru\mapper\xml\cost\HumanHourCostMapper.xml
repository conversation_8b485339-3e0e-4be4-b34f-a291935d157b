<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.HumanHourCostMapper">

    <sql id="humanHourCost_sql">
		hum.[流水号] AS costId,hum.[状态] AS state,hum.[导入标识] AS importId,hum.[年月] AS yearMonth,hum.[部门编号] AS departmentCode,hum.[工种] AS workType,
		hum.[类别] AS workClass,hum.[人数] AS humanNumber,hum.[工时] AS workHour,hum.[年] AS year,hum.[月] AS month,
		hum.[创建人标识] AS creatorId,hum.[创建人姓名] AS creatorName,hum.[创建时间] AS createdTime,
		hum.[最后修改人标识] AS lastModifierId,hum.[最后修改人姓名] AS lastModifierName,hum.[最后修改时间] AS lastModifiedTime
	</sql>

    <insert id="insertHumanHourCost" parameterType="com.hongru.entity.cost.HumanHourCost">
        INSERT INTO [CostPrice].[dbo].[人件工时表]
		(
		[状态],
		[导入标识],
		[年月],
		[部门编号],
		[工种],
		[类别],
		[人数],
		[工时],
		[年],
		[月],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[最后修改人标识],
		[最后修改人姓名],
		[最后修改时间]
		)VALUES(
		#{humanHourCost.state},
		#{humanHourCost.importId},
		#{humanHourCost.yearMonth},
		#{humanHourCost.departmentCode},
		#{humanHourCost.workType},
		#{humanHourCost.workClass},
		#{humanHourCost.humanNumber},
		#{humanHourCost.workHour},
		#{humanHourCost.year},
		#{humanHourCost.month},
		#{humanHourCost.creatorId},
		#{humanHourCost.creatorName},
		#{humanHourCost.createdTime},
		#{humanHourCost.lastModifierId},
		#{humanHourCost.lastModifierName},
		#{humanHourCost.lastModifiedTime}
		)
    </insert>

    <select id="selectByCostId" resultType="com.hongru.entity.cost.HumanHourCost">
        SELECT
        <include refid="humanHourCost_sql"/>
        FROM [CostPrice].[dbo].[人件工时表] hum
        <where>
            hum.[状态] != 9
            <if test="costId != null">
                AND hum.[流水号] = #{costId}
            </if>
        </where>
    </select>

    <select id="listHumanHourCostByYearMonth" resultType="com.hongru.entity.cost.HumanHourCost">
        SELECT
        <include refid="humanHourCost_sql"/>
        FROM [CostPrice].[dbo].[人件工时表] hum
        <where>
            hum.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND hum.[年月] = #{yearMonth}
            </if>
        </where>
    </select>

    <select id="getHumanHourCostsByYearMonth" resultType="com.hongru.entity.cost.HumanHourCost">
        SELECT
        <include refid="humanHourCost_sql"/>
        FROM [CostPrice].[dbo].[人件工时表] hum
        <where>
            hum.[状态] != 9
            <if test="startYearMonth != null and startYearMonth != ''">
                AND hum.[年月] >= #{startYearMonth}
            </if>
            <if test="endYearMonth != null and endYearMonth != ''">
                AND #{endYearMonth} >=  hum.[年月]
            </if>
        </where>
    </select>

	<select id="listCostPricePage" resultType="com.hongru.entity.cost.HumanHourCost">
		SELECT
		<include refid="humanHourCost_sql"/>
		FROM [CostPrice].[dbo].[人件工时表] hum
		<where>
			hum.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND hum.[年月] = #{yearMonth}
			</if>
			<if test="workType != null and workType != ''">
				AND hum.[工种] = #{workType}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY hum.[年月] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listCostPricePageCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[人件工时表] hum
		<where>
			hum.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND hum.[年月] = #{yearMonth}
			</if>
			<if test="workType != null and workType != ''">
				AND hum.[工种] = #{workType}
			</if>
		</where>
	</select>

	<update id="updateState">
		UPDATE [CostPrice].[dbo].[人件工时表]
		SET [状态] = #{state}
		WHERE [流水号] = #{costId}
	</update>

	<update id="updateHumanHourCost">
		UPDATE [CostPrice].[dbo].[人件工时表]
		<set>
			<if test="humanHourCost.yearMonth != null and humanHourCost.yearMonth != ''">
				[年月] = #{humanHourCost.yearMonth},
			</if>
			<if test="humanHourCost.departmentCode != null and humanHourCost.departmentCode != ''">
				[部门编号] = #{humanHourCost.departmentCode},
			</if>
			<if test="humanHourCost.workType != null and humanHourCost.workType != ''">
				[工种] = #{humanHourCost.workType},
			</if>
			<if test="humanHourCost.workClass != null and humanHourCost.workClass != ''">
				[类别] = #{humanHourCost.workClass},
			</if>
			<if test="humanHourCost.humanNumber != 0">
				[人数] = #{humanHourCost.humanNumber},
			</if>
			<if test="humanHourCost.workHour != null">
				[工时] = #{humanHourCost.workHour},
			</if>
			<if test="humanHourCost.year != null">
				[年] = #{humanHourCost.year},
			</if>
			<if test="humanHourCost.month != null">
				[月] = #{humanHourCost.month},
			</if>
			<if test="humanHourCost.lastModifierId != null">
				[最后修改人标识] = #{humanHourCost.lastModifierId},
			</if>
			<if test="humanHourCost.lastModifierName != null and humanHourCost.lastModifierName != ''">
				[最后修改人姓名] = #{humanHourCost.month},
			</if>
			<if test="humanHourCost.lastModifiedTime != null">
				[最后修改时间] = #{humanHourCost.lastModifiedTime},
			</if>
		</set>
		WHERE [流水号] = #{humanHourCost.costId}
	</update>
	
	<select id="listHumanHourCostByDateRange" resultType="com.hongru.entity.cost.HumanHourCost">
		SELECT AVG([工时]) AS workHour, [部门编号] AS departmentCode, [工种] AS workType, [类别] AS workClass
		FROM [CostPrice].[dbo].[人件工时表]
		<where>
			[状态] != 9
            <if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
             <if test="departmentCodeLIKE != null and departmentCodeLIKE != ''">
                AND [部门编号] LIKE  #{departmentCodeLIKE} 
            </if>
		</where>
		GROUP BY [部门编号],[工种],[类别]
		ORDER BY [工种] DESC,[部门编号]
	</select>
</mapper>