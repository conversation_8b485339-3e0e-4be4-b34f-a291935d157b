package com.hongru.service.impl.check;

import com.hongru.common.util.StringUtil;
import com.hongru.entity.check.HisUqcCheck;
import com.hongru.entity.check.HisUqcFrCheck;
import com.hongru.mapper.check.HisUqcCheckMapper;
import com.hongru.mapper.check.HisUqcFrCheckMapper;
import com.hongru.service.check.ICheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CheckServiceImpl implements ICheckService {
	@Autowired
	private HisUqcCheckMapper hisUqcCheckMapper;
	@Autowired
	private HisUqcFrCheckMapper hisUqcFrCheckMapper;
	/*=================================his_UQC检查表======================================*/
	/**
	 * 获取项目编号（外径）=020101，线头号=1的检查值
	 * @param productCode
	 * @param batchNo
	 * @param serialNo
	 * @throws
	 * <AUTHOR>
	 * @create 2023/1/12 10:10
	 * @return com.hongru.entity.check.HisUqcCheck
	 */
	@Override
	public HisUqcCheck getHisUqcCheck020101ForInStore(String productCode,String barCode, String batchNo, String serialNo,String checkCategory) throws Exception {
		if(!StringUtil.isStringEmpty(productCode)){
			productCode = productCode.trim();
		}else{
			productCode = null;
		}
		if(!StringUtil.isStringEmpty(barCode)){
			barCode = barCode.trim();
		}else{
			barCode = null;
		}
		if(!StringUtil.isStringEmpty(batchNo)){
			batchNo = batchNo.trim();
		}else{
			batchNo = null;
		}
		if(!StringUtil.isStringEmpty(serialNo)){
			serialNo = serialNo.trim();
		}else{
			serialNo = null;
		}
		if(!StringUtil.isStringEmpty(checkCategory)){
			checkCategory = checkCategory.trim();
		}else{
			checkCategory = null;
		}
		return hisUqcCheckMapper.getHisUqcCheck020101ForInStore(productCode,barCode, batchNo, serialNo,checkCategory);
	}

	/**
	 * 获取项目编号（电阻率）=030201或030205，线头号=1的检查值
	 * @param productCode
	 * @param batchNo
	 * @param serialNo
	 * @throws
	 * <AUTHOR>
	 * @create 2023/1/12 10:10
	 * @return com.hongru.entity.check.HisUqcCheck
	 */
	@Override
	public HisUqcCheck getHisUqcCheck0230201ForInStore(String productCode,String barCode, String batchNo, String serialNo,String checkCategory) throws Exception {
		if(!StringUtil.isStringEmpty(productCode)){
			productCode = productCode.trim();
		}else{
			productCode = null;
		}
		if(!StringUtil.isStringEmpty(barCode)){
			barCode = barCode.trim();
		}else{
			barCode = null;
		}
		if(!StringUtil.isStringEmpty(batchNo)){
			batchNo = batchNo.trim();
		}else{
			batchNo = null;
		}
		if(!StringUtil.isStringEmpty(serialNo)){
			serialNo = serialNo.trim();
		}else{
			serialNo = null;
		}
		if(!StringUtil.isStringEmpty(checkCategory)){
			checkCategory = checkCategory.trim();
		}else{
			checkCategory = null;
		}
		return hisUqcCheckMapper.getHisUqcCheck0230201ForInStore(productCode,barCode, batchNo, serialNo,checkCategory);
	}

	/*=================================his_UQC复绕检查表======================================*/

	/**
	 * 获取项目编号（外径）=020101，线头号=1的检查值
	 * @param productCodeB
	 * @param barCodeB
	 * @param batchNoB
	 * @param serialNoB
	 * @throws
	 * <AUTHOR>
	 * @create 2023/2/24 16:37
	 * @return com.hongru.entity.check.HisUqcFrCheck
	 */
	@Override
	public HisUqcFrCheck getHisUqcFrCheck020101ForInStore(String productCodeB, String barCodeB, String batchNoB, String serialNoB) throws Exception{
		if(!StringUtil.isStringEmpty(productCodeB)){
			productCodeB = productCodeB.trim();
		}else{
			productCodeB = null;
		}
		if(!StringUtil.isStringEmpty(barCodeB)){
			barCodeB = barCodeB.trim();
		}else{
			barCodeB = null;
		}
		if(!StringUtil.isStringEmpty(batchNoB)){
			batchNoB = batchNoB.trim();
		}else{
			batchNoB = null;
		}
		if(!StringUtil.isStringEmpty(serialNoB)){
			serialNoB = serialNoB.trim();
		}else{
			serialNoB = null;
		}
		return hisUqcFrCheckMapper.getHisUqcFrCheck020101ForInStore(productCodeB,barCodeB, batchNoB, serialNoB);
	}

	/**
	 * 获取项目编号（电阻率）=030201或030205，线头号=1的检查值
	 * @param productCodeB
	 * @param barCodeB
	 * @param batchNoB
	 * @param serialNoB
	 * @throws
	 * <AUTHOR>
	 * @create 2023/2/24 16:37
	 * @return com.hongru.entity.check.HisUqcFrCheck
	 */
	@Override
	public HisUqcFrCheck getHisUqcFrCheck0230201ForInStore(String productCodeB, String barCodeB, String batchNoB, String serialNoB) throws Exception{
		if(!StringUtil.isStringEmpty(productCodeB)){
			productCodeB = productCodeB.trim();
		}else{
			productCodeB = null;
		}
		if(!StringUtil.isStringEmpty(barCodeB)){
			barCodeB = barCodeB.trim();
		}else{
			barCodeB = null;
		}
		if(!StringUtil.isStringEmpty(batchNoB)){
			batchNoB = batchNoB.trim();
		}else{
			batchNoB = null;
		}
		if(!StringUtil.isStringEmpty(serialNoB)){
			serialNoB = serialNoB.trim();
		}else{
			serialNoB = null;
		}
		return hisUqcFrCheckMapper.getHisUqcFrCheck0230201ForInStore(productCodeB,barCodeB, batchNoB, serialNoB);
	}
}
