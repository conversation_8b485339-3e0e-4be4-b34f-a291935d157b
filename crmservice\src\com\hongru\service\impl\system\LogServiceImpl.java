package com.hongru.service.impl.system;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.hongru.base.BasePageDTO;
import com.hongru.common.util.StringUtil;
import com.hongru.entity.system.Log;
import com.hongru.mapper.system.LogMapper;
import com.hongru.service.system.ILogService;
import com.hongru.support.page.PageInfo;

/**
 * 
*    
* 类名称：LogServiceImpl   
* 类描述：Log / 日志记录表 业务逻辑层接口实现   
* 创建人：hongru   
* 创建时间：2017-10-25 上午10:26:58   
*
 */
@Service
public class LogServiceImpl extends ServiceImpl<LogMapper, Log> implements ILogService {
	
	@Autowired
	private LogMapper logMapper;

	@Override
	public BasePageDTO<Log> listByPage(PageInfo pageInfo, String search) {
		if(!StringUtil.isStringEmpty(search)){
			search = "%"+search.trim()+"%";
		}else{
			search = null;
		}
		List<Log> adverts = logMapper.listByPage(pageInfo, search);
		Integer total = logMapper.listByPageCount(search);
		pageInfo.setTotal(total);
		return new BasePageDTO<Log>(pageInfo, adverts);
	}

	/**
	* 根据参数删除日志
	* @throws
	* <AUTHOR>
	* @create 2022/1/14 16:12
	* @return
	*/
	@Override
	public void removeLogByParam(Integer[] logIds) {
		logMapper.deleteLogByParam(logIds);
	}

	/**
	* 根据参数获取日志列表
	* @throws
	* <AUTHOR>
	* @create 2022/1/14 16:12
	* @return
	*/
	@Override
	public List<Log> listLogByParam(Integer[] logIds) {
		return logMapper.listLogByParam(logIds);
	}

}
