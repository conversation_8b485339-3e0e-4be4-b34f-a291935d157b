<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>编辑部门单价</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="formEdit" class="layui-form" method="post" action="">
            <input type="hidden" name="serialNumber" value="${departmentUnitPrice.serialNumber}">
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年度:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="year" id="year" value="${departmentUnitPrice.year}" readonly autocomplete="off" class="layui-input layui-disabled">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">机械类别:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="machineType" id="machineType" value="${departmentUnitPrice.machineType}" readonly autocomplete="off" class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">属性:</label>
                    <div class="layui-input-inline">
                        <select name="attribute" id="attribute" disabled>
                            <option value="01" <c:if test="${departmentUnitPrice.attribute == '01'}">selected</c:if>>直接</option>
                            <option value="02" <c:if test="${departmentUnitPrice.attribute == '02'}">selected</c:if>>辅助</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">费用项目:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="expenseItem" id="expenseItem" value="${departmentUnitPrice.expenseItem}" readonly autocomplete="off" class="layui-input layui-disabled">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">工场区分:</label>
                    <div class="layui-input-inline">
                        <select name="factoryType" id="factoryType" lay-filter="factoryType">
                            <option value="MW" <c:if test="${departmentUnitPrice.factoryType == 'MW'}">selected</c:if>>MW</option>
                            <option value="UF" <c:if test="${departmentUnitPrice.factoryType == 'UF'}">selected</c:if>>UF</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">SH系数<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="shCoefficient" id="shCoefficient" value="${departmentUnitPrice.shCoefficient}" lay-verify="required|number" placeholder="请输入SH系数" autocomplete="off" class="layui-input" onchange="calculateProjectCost()" oninput="validateNumberInput(this)">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">单价<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="unitPrice" id="unitPrice" value="${departmentUnitPrice.unitPrice}" lay-verify="required|number" placeholder="请输入单价" autocomplete="off" class="layui-input" onchange="calculateProjectCost()" oninput="validateNumberInput(this)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">项目费用:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="projectCost" id="projectCost" value="${departmentUnitPrice.projectCost}" placeholder="自动计算" autocomplete="off" class="layui-input layui-disabled" readonly>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formEdit">立即提交</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="javascript:history.back(-1);">返回</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'layer', 'laydate'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;

    // 年度选择器（编辑页面年度字段只读，所以不需要配置）
    // laydate.render({
    //     trigger: 'click',
    //     type: 'year',
    //     elem: '#year',
    //     btns: ['clear','confirm']
    // });

    //监听提交
    form.on('submit(formEdit)', function(data){
        $.ajax({
            url: baselocation + '/yearParamSet/departmentalUnitPrice/edit',
            type: 'POST',
            data: data.field,
            success: function(result) {
                if(result.code == 1) {
                    layer.msg('编辑成功', {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统异常', {icon: 2});
            }
        });
        return false;
    });

    // 页面加载时计算一次项目费用
    calculateProjectCost();
});

// 计算项目费用
function calculateProjectCost() {
    var shCoefficientVal = $("#shCoefficient").val();
    var unitPriceVal = $("#unitPrice").val();

    // 验证是否为有效数字
    var shCoefficient = parseFloat(shCoefficientVal);
    var unitPrice = parseFloat(unitPriceVal);

    if (!isNaN(shCoefficient) && !isNaN(unitPrice) && shCoefficient > 0 && unitPrice > 0) {
        var projectCost = shCoefficient * unitPrice;
        $("#projectCost").val(projectCost.toFixed(4));
    } else {
        $("#projectCost").val('');
    }
}

// 限制只能输入数字和小数点
function validateNumberInput(input) {
    input.value = input.value.replace(/[^0-9.]/g, '');
    // 防止输入多个小数点
    var parts = input.value.split('.');
    if (parts.length > 2) {
        input.value = parts[0] + '.' + parts.slice(1).join('');
    }
}
</script>
</body>
</html>
