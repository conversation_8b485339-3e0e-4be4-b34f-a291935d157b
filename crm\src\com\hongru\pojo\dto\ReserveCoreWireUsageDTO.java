package com.hongru.pojo.dto;

import com.hongru.entity.cost.ReserveCoreWireUsage;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class ReserveCoreWireUsageDTO {

    private PageInfo pageInfo;

    private List<ReserveCoreWireUsage> reserveCoreWireUsages;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<ReserveCoreWireUsage> getReserveCoreWireUsages() {
        return reserveCoreWireUsages;
    }

    public void setReserveCoreWireUsages(List<ReserveCoreWireUsage> reserveCoreWireUsages) {
        this.reserveCoreWireUsages = reserveCoreWireUsages;
    }

    public ReserveCoreWireUsageDTO(PageInfo pageInfo, List<ReserveCoreWireUsage> reserveCoreWireUsages) {
        this.pageInfo = pageInfo;
        this.reserveCoreWireUsages = reserveCoreWireUsages;
    }
}
