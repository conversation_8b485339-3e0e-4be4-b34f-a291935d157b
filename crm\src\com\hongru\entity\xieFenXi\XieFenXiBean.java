package com.hongru.entity.xieFenXi;

public class XieFenXiBean {

	protected String name;
	protected String date;
	protected Float outputproduct;//产量
	protected Float scrapAmount;//屑量
	protected String machineId;//设备号
	protected Float machineTime;//机械时间
	protected Integer diffDate;//(分钟)停止时间
	protected String machineStopReason;//停止原因也用来存储key值
	protected Integer breakNum;//断线次数

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public Float getOutputproduct() {
		return outputproduct;
	}

	public void setOutputproduct(Float outputproduct) {
		this.outputproduct = outputproduct;
	}

	public Float getScrapAmount() {
		return scrapAmount;
	}

	public void setScrapAmount(Float scrapAmount) {
		this.scrapAmount = scrapAmount;
	}

	public String getMachineId() {
		return machineId;
	}

	public void setMachineId(String machineId) {
		this.machineId = machineId;
	}

	public Float getMachineTime() {
		return machineTime;
	}

	public void setMachineTime(Float machineTime) {
		this.machineTime = machineTime;
	}

	public Integer getDiffDate() {
		return diffDate;
	}

	public void setDiffDate(Integer diffDate) {
		this.diffDate = diffDate;
	}

	public String getMachineStopReason() {
		return machineStopReason;
	}

	public void setMachineStopReason(String machineStopReason) {
		this.machineStopReason = machineStopReason;
	}

	public Integer getBreakNum() {
		return breakNum;
	}

	public void setBreakNum(Integer breakNum) {
		this.breakNum = breakNum;
	}
}