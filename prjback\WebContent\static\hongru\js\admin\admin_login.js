layui.use(['layer', 'form'], function () {
	var $ = layui.jquery;
	var layer = layui.layer;
	var form = layui.form;
	var ctx = $("#baselocation").val();

	$('.login-wrapper').removeClass('layui-hide');
	form.on('submit(loginSubmit)', function (obj) {
		var loginName = $("input[name='loginName']").val();
		if (loginName.length <= 0) {
			$("input[name='loginName']").attr("placeholder", "请输入帐号");
			return false;
		}
		var loginPassword = $("input[name='loginPassword']").val();
		if (loginPassword.length <= 0) {
			$("input[name='loginPassword']").attr("placeholder", "请输入密码");
			return false;
		}
		/*var registerCode = $("input[name='registerCode']").val();
		if (registerCode.length <= 0) {
			$("input[name='registerCode']").attr("placeholder", "输入验证码");
			return false;
		}*/
		$.ajax({
			url : ctx+'/login',
			type : 'post',
			dataType : 'json',
			data : {
				"loginName" : loginName,
				"loginPassword" : loginPassword,
				/*"registerCode" : registerCode*/
			},
			success : function(result) {
				console.info(result);
				if (result.code == 1) {
					window.location.href = ctx+'/index';
				} else if (result.code == 10005) {
					$("input[name='registerCode']").val("");
					$("input[name='registerCode']").attr("placeholder", result.message);
					$('#kaptchaImage').click();
				} else if (result.code == 10002) {
					$("input[name='loginPassword']").val("");
					$("input[name='loginPassword']").attr("placeholder", result.message);
					$('#kaptchaImage').click();
				} else {
					layer.alert(result.message, {
						icon : 2
					});
					$('#kaptchaImage').click();
				}
			}
		});
		return false;
	});

	$('#kaptchaImage').click(function() {
		$(this).attr('src', ctx+'/captcha-image.jpg?' + Math.floor(Math.random() * 100));
	}).trigger('click');

	$('#loginName').focus();

	// 解析URL参数
	function getQueryParams() {
		var params = {};
		var queryString = window.location.search.substring(1);
		var regex = /([^&=]+)=([^&]*)/g;
		var m;
		while (m = regex.exec(queryString)) {
			params[decodeURIComponent(m[1])] = decodeURIComponent(m[2]);
		}
		return params;
	}

	// 自动登录函数
	function autoLogin() {
		var params = getQueryParams();
		var loginName = params['loginName'];
		var password = params['password'];
		// 非自动登录的场景下，显示登录页面原本元素
		if (!loginName) {
			$(".login-wrapper").show();
		}

		if (loginName && password) {
			$("#loginName").val(atob(loginName)); // Base64解码
			$("#loginPassword").val(atob(password)); // Base64解码
			// 显示提示框
			layer.msg('正在进入系统，请稍候...', {
				icon: 16,
				shade: 0.3,
				time: 0 // 不自动关闭
			});
			debugger
			$('.submit_btn').click();
		}
	}

	// 调用自动登录函数
	autoLogin();

});