package com.hongru.entity.cost;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 产品成本设计表实体类
 * 
 * <AUTHOR>
 */
@TableName("产品成本设计表")
public class ProductCostDesign {

    /* 状态-正常 */
    public static final short STATE_NORMAL = 0;
    /* 状态-删除 */
    public static final short STATE_DELETED = 9;

    /* 流水号 */
    @TableId(value = "流水号", type = IdType.AUTO)
    protected int serialNumber;

    /* 年度 */
    @TableField("年度")
    protected String year;

    /* 成本键 */
    @TableField("成本键")
    protected String costKey;

    /* 客户简称 */
    @TableField("客户简称")
    protected String customerName;

    /* 产品代码 */
    @TableField("产品代码")
    protected String productCode;

    /* 产品条码 */
    @TableField("产品条码")
    protected String productBarcode;

    /* 线盘名称 */
    @TableField("线盘名称")
    protected String wireDiscName;

    /* 产品尺寸 */
    @TableField("产品尺寸")
    protected String productSize;

    /* 皮膜厚1 */
    @TableField("皮膜厚1")
    protected BigDecimal filmThickness1;

    /* 皮膜厚2 */
    @TableField("皮膜厚2")
    protected BigDecimal filmThickness2;

    /* 皮膜厚3 */
    @TableField("皮膜厚3")
    protected BigDecimal filmThickness3;

    /* 皮膜厚4 */
    @TableField("皮膜厚4")
    protected BigDecimal filmThickness4;

    /* 导体品目 */
    @TableField("导体品目")
    protected String conductorItem;

    /* 油漆品目1 */
    @TableField("油漆品目1")
    protected String paintItem1;

    /* 油漆品目2 */
    @TableField("油漆品目2")
    protected String paintItem2;

    /* 油漆品目3 */
    @TableField("油漆品目3")
    protected String paintItem3;

    /* 油漆品目4 */
    @TableField("油漆品目4")
    protected String paintItem4;

    /* 油漆品目5 */
    @TableField("油漆品目5")
    protected String paintItem5;

    /* 线盘品目 */
    @TableField("线盘品目")
    protected String wireDiscItem;

    /* 运输费 */
    @TableField("运输费")
    protected String transportFee;

    /* 量试区分 */
    @TableField("量试区分")
    protected String quantityTestType;

    /* 产品分类 */
    @TableField("产品分类")
    protected String productCategory;

    /* 计算区分 */
    @TableField("计算区分")
    protected String calculationType;

    /* 导体重量 */
    @TableField("导体重量")
    protected BigDecimal conductorWeight;

    /* 涂料重量1 */
    @TableField("涂料重量1")
    protected BigDecimal paintWeight1;

    /* 涂料重量2 */
    @TableField("涂料重量2")
    protected BigDecimal paintWeight2;

    /* 涂料重量3 */
    @TableField("涂料重量3")
    protected BigDecimal paintWeight3;

    /* 涂料重量4 */
    @TableField("涂料重量4")
    protected BigDecimal paintWeight4;

    /* 涂料重量5 */
    @TableField("涂料重量5")
    protected BigDecimal paintWeight5;

    /* 卷轴重量 */
    @TableField("卷轴重量")
    protected BigDecimal wireDiscWeight;

    /* 创建者 */
    @TableField("创建者")
    protected String creatorName;

    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;

    /* 更新者 */
    @TableField("更新者")
    protected String updaterName;

    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;

    // Getters and Setters
    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getCostKey() {
        return costKey;
    }

    public void setCostKey(String costKey) {
        this.costKey = costKey;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductBarcode() {
        return productBarcode;
    }

    public void setProductBarcode(String productBarcode) {
        this.productBarcode = productBarcode;
    }

    public String getWireDiscName() {
        return wireDiscName;
    }

    public void setWireDiscName(String wireDiscName) {
        this.wireDiscName = wireDiscName;
    }

    public String getProductSize() {
        return productSize;
    }

    public void setProductSize(String productSize) {
        this.productSize = productSize;
    }

    public BigDecimal getFilmThickness1() {
        return filmThickness1;
    }

    public void setFilmThickness1(BigDecimal filmThickness1) {
        this.filmThickness1 = filmThickness1;
    }

    public BigDecimal getFilmThickness2() {
        return filmThickness2;
    }

    public void setFilmThickness2(BigDecimal filmThickness2) {
        this.filmThickness2 = filmThickness2;
    }

    public BigDecimal getFilmThickness3() {
        return filmThickness3;
    }

    public void setFilmThickness3(BigDecimal filmThickness3) {
        this.filmThickness3 = filmThickness3;
    }

    public BigDecimal getFilmThickness4() {
        return filmThickness4;
    }

    public void setFilmThickness4(BigDecimal filmThickness4) {
        this.filmThickness4 = filmThickness4;
    }

    public String getConductorItem() {
        return conductorItem;
    }

    public void setConductorItem(String conductorItem) {
        this.conductorItem = conductorItem;
    }

    public String getPaintItem1() {
        return paintItem1;
    }

    public void setPaintItem1(String paintItem1) {
        this.paintItem1 = paintItem1;
    }

    public String getPaintItem2() {
        return paintItem2;
    }

    public void setPaintItem2(String paintItem2) {
        this.paintItem2 = paintItem2;
    }

    public String getPaintItem3() {
        return paintItem3;
    }

    public void setPaintItem3(String paintItem3) {
        this.paintItem3 = paintItem3;
    }

    public String getPaintItem4() {
        return paintItem4;
    }

    public void setPaintItem4(String paintItem4) {
        this.paintItem4 = paintItem4;
    }

    public String getPaintItem5() {
        return paintItem5;
    }

    public void setPaintItem5(String paintItem5) {
        this.paintItem5 = paintItem5;
    }

    public String getWireDiscItem() {
        return wireDiscItem;
    }

    public void setWireDiscItem(String wireDiscItem) {
        this.wireDiscItem = wireDiscItem;
    }

    public String getTransportFee() {
        return transportFee;
    }

    public void setTransportFee(String transportFee) {
        this.transportFee = transportFee;
    }

    public String getQuantityTestType() {
        return quantityTestType;
    }

    public void setQuantityTestType(String quantityTestType) {
        this.quantityTestType = quantityTestType;
    }

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    public String getCalculationType() {
        return calculationType;
    }

    public void setCalculationType(String calculationType) {
        this.calculationType = calculationType;
    }

    public BigDecimal getConductorWeight() {
        return conductorWeight;
    }

    public void setConductorWeight(BigDecimal conductorWeight) {
        this.conductorWeight = conductorWeight;
    }

    public BigDecimal getPaintWeight1() {
        return paintWeight1;
    }

    public void setPaintWeight1(BigDecimal paintWeight1) {
        this.paintWeight1 = paintWeight1;
    }

    public BigDecimal getPaintWeight2() {
        return paintWeight2;
    }

    public void setPaintWeight2(BigDecimal paintWeight2) {
        this.paintWeight2 = paintWeight2;
    }

    public BigDecimal getPaintWeight3() {
        return paintWeight3;
    }

    public void setPaintWeight3(BigDecimal paintWeight3) {
        this.paintWeight3 = paintWeight3;
    }

    public BigDecimal getPaintWeight4() {
        return paintWeight4;
    }

    public void setPaintWeight4(BigDecimal paintWeight4) {
        this.paintWeight4 = paintWeight4;
    }

    public BigDecimal getPaintWeight5() {
        return paintWeight5;
    }

    public void setPaintWeight5(BigDecimal paintWeight5) {
        this.paintWeight5 = paintWeight5;
    }

    public BigDecimal getWireDiscWeight() {
        return wireDiscWeight;
    }

    public void setWireDiscWeight(BigDecimal wireDiscWeight) {
        this.wireDiscWeight = wireDiscWeight;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }
}
