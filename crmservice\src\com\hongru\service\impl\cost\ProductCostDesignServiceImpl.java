package com.hongru.service.impl.cost;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.hongru.entity.cost.*;
import com.hongru.entity.pims.ProductDesignData;
import com.hongru.entity.sumitomo.Customer;
import com.hongru.entity.sumitomo.Product;
import com.hongru.mapper.cost.ProductCostDesignMapper;
import com.hongru.service.cost.IProductCostDesignService;
import com.hongru.support.page.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品成本设计服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class ProductCostDesignServiceImpl extends ServiceImpl<ProductCostDesignMapper, ProductCostDesign> 
        implements IProductCostDesignService {

    @Autowired
    private ProductCostDesignMapper productCostDesignMapper;

    @Override
    public ProductCostDesignDTO listProductCostDesignPage(String year, String customerName, String productCategory,
                                                         String productCode, PageInfo pageInfo) throws Exception {
        // 调用Mapper进行分页查询
        List<ProductCostDesign> productCostDesignList = productCostDesignMapper.listProductCostDesignPage(
            year, customerName, productCategory, productCode, pageInfo);

        // 查询总数
        int total = productCostDesignMapper.listProductCostDesignPageCount(
            year, customerName, productCategory, productCode);

        // 设置总数到分页信息
        pageInfo.setTotal(total);

        // 构建返回对象
        ProductCostDesignDTO productCostDesignDTO = new ProductCostDesignDTO();
        productCostDesignDTO.setProductCostDesignList(productCostDesignList);
        productCostDesignDTO.setPageInfo(pageInfo);

        return productCostDesignDTO;
    }

    @Override
    public ProductCostDesign getProductCostDesignById(Integer serialNumber) {
        return productCostDesignMapper.selectById(serialNumber);
    }

    @Override
    public int addProductCostDesign(ProductCostDesign productCostDesign) {
        return productCostDesignMapper.insert(productCostDesign);
    }

    @Override
    public int updateProductCostDesign(ProductCostDesign productCostDesign) {
        return productCostDesignMapper.updateById(productCostDesign);
    }

    @Override
    public int deleteProductCostDesign(Integer serialNumber) {
        return productCostDesignMapper.deleteById(serialNumber);
    }

    @Override
    public List<RawMaterialItem> getRawMaterialItems(String year, String materialType) {
        return productCostDesignMapper.getRawMaterialItems(year, materialType);
    }

    @Override
    public List<TransportUnitPrice> getTransportUnitPrices(String year) {
        return productCostDesignMapper.getTransportUnitPrices(year);
    }

    @Override
    public List<Customer> getCustomers() {
        return productCostDesignMapper.getCustomers();
    }

    @Override
    public List<Product> getProducts(String customerName, String productCategory) {
        return productCostDesignMapper.getProducts(customerName, productCategory);
    }

    @Override
    public ProductDesignData getProductDesignData(String productCode) {
        return productCostDesignMapper.getProductDesignData(productCode);
    }

    @Override
    public List<ProductManufacturingDesign> getProductManufacturingDesigns(String year, String productCode, 
            String productBarcode, String customerName) {
        return productCostDesignMapper.getProductManufacturingDesigns(year, productCode, productBarcode, customerName);
    }

    @Override
    public int addProductManufacturingDesign(ProductManufacturingDesign productManufacturingDesign) {
        return productCostDesignMapper.addProductManufacturingDesign(productManufacturingDesign);
    }

    @Override
    public int updateProductManufacturingDesign(ProductManufacturingDesign productManufacturingDesign) {
        return productCostDesignMapper.updateProductManufacturingDesign(productManufacturingDesign);
    }

    @Override
    public int addProductCost(ProductCost productCost) {
        return productCostDesignMapper.addProductCost(productCost);
    }

    @Override
    public int updateProductCost(ProductCost productCost) {
        return productCostDesignMapper.updateProductCost(productCost);
    }
}
