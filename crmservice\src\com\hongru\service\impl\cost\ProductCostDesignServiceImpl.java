package com.hongru.service.impl.cost;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.util.StringUtils;
import com.hongru.entity.cost.*;
import com.hongru.entity.sumitomo.Customer;
import com.hongru.entity.sumitomo.Product;
import com.hongru.entity.pims.ProductDesignData;
import com.hongru.mapper.cost.ProductCostDesignMapper;
import com.hongru.service.cost.IProductCostDesignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 产品成本设计服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class ProductCostDesignServiceImpl extends ServiceImpl<ProductCostDesignMapper, ProductCostDesign> 
        implements IProductCostDesignService {

    @Autowired
    private ProductCostDesignMapper productCostDesignMapper;

    @Override
    public HrPageResult<ProductCostDesign> listProductCostDesign(Map<String, Object> params) {
        String year = (String) params.get("year");
        String costKey = (String) params.get("costKey");
        String customerName = (String) params.get("customerName");
        String productCode = (String) params.get("productCode");
        
        Integer page = params.get("page") != null ? Integer.valueOf(params.get("page").toString()) : 1;
        Integer limit = params.get("limit") != null ? Integer.valueOf(params.get("limit").toString()) : 10;

        EntityWrapper<ProductCostDesign> wrapper = new EntityWrapper<>();
        
        if (StringUtils.isNotEmpty(year)) {
            wrapper.eq("年度", year);
        }
        if (StringUtils.isNotEmpty(costKey)) {
            wrapper.like("成本键", costKey);
        }
        if (StringUtils.isNotEmpty(customerName)) {
            wrapper.like("客户简称", customerName);
        }
        if (StringUtils.isNotEmpty(productCode)) {
            wrapper.like("产品代码", productCode);
        }

        Page<ProductCostDesign> pageInfo = new Page<>(page, limit);
        Page<ProductCostDesign> result = selectPage(pageInfo, wrapper);

        return new HrPageResult<>(result.getTotal(), result.getRecords());
    }

    @Override
    public ProductCostDesign getProductCostDesignById(Integer serialNumber) {
        return selectById(serialNumber);
    }

    @Override
    public int addProductCostDesign(ProductCostDesign productCostDesign) {
        return insert(productCostDesign) ? 1 : 0;
    }

    @Override
    public int updateProductCostDesign(ProductCostDesign productCostDesign) {
        return updateById(productCostDesign) ? 1 : 0;
    }

    @Override
    public int deleteProductCostDesign(Integer serialNumber) {
        return deleteById(serialNumber) ? 1 : 0;
    }

    @Override
    public List<RawMaterialItem> getRawMaterialItems(String year, String materialType) {
        return productCostDesignMapper.getRawMaterialItems(year, materialType);
    }

    @Override
    public List<TransportUnitPrice> getTransportUnitPrices(String year) {
        return productCostDesignMapper.getTransportUnitPrices(year);
    }

    @Override
    public List<Customer> getCustomers() {
        return productCostDesignMapper.getCustomers();
    }

    @Override
    public List<Product> getProducts(String customerName, String productCategory) {
        return productCostDesignMapper.getProducts(customerName, productCategory);
    }

    @Override
    public ProductDesignData getProductDesignData(String productCode) {
        return productCostDesignMapper.getProductDesignData(productCode);
    }

    @Override
    public List<ProductManufacturingDesign> getProductManufacturingDesigns(String year, String productCode, 
            String productBarcode, String customerName) {
        return productCostDesignMapper.getProductManufacturingDesigns(year, productCode, productBarcode, customerName);
    }

    @Override
    public int addProductManufacturingDesign(ProductManufacturingDesign productManufacturingDesign) {
        return productCostDesignMapper.addProductManufacturingDesign(productManufacturingDesign);
    }

    @Override
    public int updateProductManufacturingDesign(ProductManufacturingDesign productManufacturingDesign) {
        return productCostDesignMapper.updateProductManufacturingDesign(productManufacturingDesign);
    }

    @Override
    public int addProductCost(ProductCost productCost) {
        return productCostDesignMapper.addProductCost(productCost);
    }

    @Override
    public int updateProductCost(ProductCost productCost) {
        return productCostDesignMapper.updateProductCost(productCost);
    }
}
