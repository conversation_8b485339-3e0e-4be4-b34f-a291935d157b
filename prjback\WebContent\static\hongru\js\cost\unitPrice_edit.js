layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate
        ,layer = layui.layer
        ,table = layui.table
        ,element = layui.element
        ,dropdown = layui.dropdown
    var form = layui.form;
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year', //指定元素
        btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
    });
    //监听提交
    form.on('submit(formDemo)', function(data){
        var flag = true;

        var statId = $("#statId").val();
        var year = $("#year").val();
        var electricUnitPrice = $("#electricUnitPrice").val();
        var generalUnitPrice = $("#generalUnitPrice").val();
        var temporaryUnitPrice = $("#temporaryUnitPrice").val();
        var saveDamageUnitPrice = $("#saveDamageUnitPrice").val();
        var wrEMUnitPrice = $("#wrEMUnitPrice").val();
        var wrEFUnitPrice = $("#wrEFUnitPrice").val();
        var wrERUnitPrice = $("#wrERUnitPrice").val();
        var wrEHUnitPrice = $("#wrEHUnitPrice").val();
        var tranUnitPrice = $("#tranUnitPrice").val();
        var wireUnitPrice = $("#wireUnitPrice").val();
        var wireRecycling = $("#wireRecycling").val();
        var cuCrumbsUnitPrice = $("#cuCrumbsUnitPrice").val();
        var gasUnitPrice = $("#gasUnitPrice").val();
        var nitrogenUnitPrice = $("#nitrogenUnitPrice").val();

        if(flag){
            var index = layer.load(2, {
                shade: [0.1, '#fff']
            });
            var actionUrl = $("#submitForm").attr('action');
            $.ajax({
                url: actionUrl,
                type: 'post',
                data: {
                    "statId":statId,
                    "year":year,
                    "electricUnitPrice":electricUnitPrice,
                    "generalUnitPrice":generalUnitPrice,
                    "temporaryUnitPrice":temporaryUnitPrice,
                    "saveDamageUnitPrice":saveDamageUnitPrice,
                    "wrEMUnitPrice":wrEMUnitPrice,
                    "wrEFUnitPrice":wrEFUnitPrice,
                    "wrERUnitPrice":wrERUnitPrice,
                    "wrEHUnitPrice":wrEHUnitPrice,
                    "tranUnitPrice":tranUnitPrice,
                    "wireUnitPrice":wireUnitPrice,
                    "wireRecycling":wireRecycling,
                    "cuCrumbsUnitPrice":cuCrumbsUnitPrice,
                    "gasUnitPrice":gasUnitPrice,
                    "nitrogenUnitPrice":nitrogenUnitPrice
                },
                success: function (result) {
                    layer.closeAll();
                    if (result.code == 1) {
                        parent.layer.msg("操作成功!", {
                            shade: 0.5,
                            time: 1500
                        });
                        parent.search();
                        parent.layer.closeAll();
                    } else {
                        layer.alert(result.message);
                    }
                }
            });
        }
        return false;
    });
});