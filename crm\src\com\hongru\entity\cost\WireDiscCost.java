package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("线盘費用表")//CostPrice
public class WireDiscCost {
	/* MW */
	public static final String STATE_ZERO ="0";
	/* UF */
	public static final String STATE_ONE = "1";
	
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 年度 */
	protected String year;
	/* 线盘型号 */
	protected String wireDiscType;
	/* 单重 */
	protected BigDecimal singleWeight;
	/* 采购单价 */
	protected BigDecimal buyPrice;
	/* 线盘原价 */
	protected BigDecimal wireDiscPrice;
	/* 线盘单价 */
	protected BigDecimal wireDiscUnitPrice;
	/* 标准回收率 */
	protected BigDecimal standardRate;
	/* 回收率实绩 */
	protected BigDecimal recoveryRate;
	/* 回收费用实绩 */
	protected BigDecimal recoveryCost;
	/* 报废率实绩 */
	protected BigDecimal scrapRate;
	/* 入库重量 */
	protected BigDecimal storageWeight;
	/* 线盘个数 */
	protected int wireDiscNum;
	/* 区分 */
	protected String distinguish;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	@TableField(exist = false)
	/* 成本编码 */
	protected String costCode;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}
	
	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getWireDiscType() {
		return wireDiscType;
	}

	public void setWireDiscType(String wireDiscType) {
		this.wireDiscType = wireDiscType;
	}

	public BigDecimal getSingleWeight() {
		return singleWeight;
	}

	public void setSingleWeight(BigDecimal singleWeight) {
		this.singleWeight = singleWeight;
	}

	public BigDecimal getBuyPrice() {
		return buyPrice;
	}

	public void setBuyPrice(BigDecimal buyPrice) {
		this.buyPrice = buyPrice;
	}

	public BigDecimal getWireDiscPrice() {
		return wireDiscPrice;
	}

	public void setWireDiscPrice(BigDecimal wireDiscPrice) {
		this.wireDiscPrice = wireDiscPrice;
	}

	public BigDecimal getWireDiscUnitPrice() {
		return wireDiscUnitPrice;
	}

	public void setWireDiscUnitPrice(BigDecimal wireDiscUnitPrice) {
		this.wireDiscUnitPrice = wireDiscUnitPrice;
	}

	public BigDecimal getStandardRate() {
		return standardRate;
	}

	public void setStandardRate(BigDecimal standardRate) {
		this.standardRate = standardRate;
	}

	public BigDecimal getRecoveryRate() {
		return recoveryRate;
	}

	public void setRecoveryRate(BigDecimal recoveryRate) {
		this.recoveryRate = recoveryRate;
	}

	public BigDecimal getRecoveryCost() {
		return recoveryCost;
	}

	public void setRecoveryCost(BigDecimal recoveryCost) {
		this.recoveryCost = recoveryCost;
	}

	public BigDecimal getScrapRate() {
		return scrapRate;
	}

	public void setScrapRate(BigDecimal scrapRate) {
		this.scrapRate = scrapRate;
	}

	public BigDecimal getStorageWeight() {
		return storageWeight;
	}

	public void setStorageWeight(BigDecimal storageWeight) {
		this.storageWeight = storageWeight;
	}

	public int getWireDiscNum() {
		return wireDiscNum;
	}

	public void setWireDiscNum(int wireDiscNum) {
		this.wireDiscNum = wireDiscNum;
	}

	public String getDistinguish() {
		return distinguish;
	}

	public void setDistinguish(String distinguish) {
		this.distinguish = distinguish;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public String getCostCode() {
		return costCode;
	}

	public void setCostCode(String costCode) {
		this.costCode = costCode;
	}
}