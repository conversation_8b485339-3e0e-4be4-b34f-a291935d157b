package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("铜供应商表")//CostPrice
public class CuSupplier {

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int supplierId;
	/* 供应商编码 */
	protected String supplierCode;
	/* 供应商名称 */
	protected String supplierName;
	/* 国内供应商 */
	protected short supplierFrom;

	public int getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(int supplierId) {
		this.supplierId = supplierId;
	}

	public String getSupplierCode() {
		return supplierCode;
	}

	public void setSupplierCode(String supplierCode) {
		this.supplierCode = supplierCode;
	}

	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}

	public short getSupplierFrom() {
		return supplierFrom;
	}

	public void setSupplierFrom(short supplierFrom) {
		this.supplierFrom = supplierFrom;
	}
}