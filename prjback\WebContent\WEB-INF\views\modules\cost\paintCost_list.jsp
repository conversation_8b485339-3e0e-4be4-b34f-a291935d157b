<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>油漆费用列表</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md2">
                                <label class="layui-form-label">年月:</label>
                                <div class="layui-input-block">
                                    <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="" />
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label" style="width: 100px"><span class="star">*</span>社内油漆名:</label>
                                <div class="layui-input-block" style="margin-left: 130px">
                                    <select class="layui-select" id="insidePaintName" name="insidePaintName" lay-verify="required" lay-search="true" required>
                                        <option value="">请选择</option>
                                        <c:forEach items="${insidePaintNameList}" var="insidePaintNameList">
                                            <option value="${insidePaintNameList}" >${insidePaintNameList}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md2">
                                <label class="layui-form-label">使用区分:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="paintUseCode" name="paintUseCode" lay-verify="required" lay-search="true" required>
                                        <option value="">请选择</option>
                                        <option value="EM">EM</option>
                                        <option value="EF">EF</option>
                                        <option value="ER01">ER01</option>
                                        <option value="ER02">ER02</option>
                                        <option value="ER03">ER03</option>
                                        <option value="ER04">ER04</option>
                                        <option value="ER05">ER05</option>
                                        <option value="ER09">ER09</option>
                                        <option value="UF">UF</option>
                                        <option value="样品">样品</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md1 hr-div-btn">
                                <input type="hidden" id="isSearch" name="isSearch" value="0" />
                                <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="demo" lay-filter="test"></table>
        <!-- 自定义表中工具栏 -->
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>删除</a>
        </script>
        <!-- 自定义头部工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="import"><i class="layui-icon layui-icon-add-1"></i>导入</button>
                <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete"><i class="layui-icon layui-icon-delete"></i>删除</button>
            </div>
        </script>
    </div>
</div>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/paintCost_list.js?time=8"></script>
</myfooter>
</body>
</html>