.lock-screen-wrapper {
    color: #ffffff;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 50px 60px 0 60px;
    background-color: #000000;
    background-image: url("../images/lockscreen/bg-screen.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}

.lock-screen-time {
    font-size: 88px;
}

.lock-screen-date {
    font-size: 24px;
    padding: 0 0 0 13px;
}

.lock-screen-form {
    position: absolute;
    left: 0;
    right: 0;
    top: 65%;
    width: 100%;
    text-align: center;
}

.lock-screen-psw {
    color: #ffffff;
    font-size: 22px;
    width: 0;
    height: 0;
    line-height: 40px;
    border-radius: 40px;
    border: 2px solid transparent;
    background-color: transparent;
    box-sizing: border-box;
    vertical-align: middle;
    -webkit-transition: all .3s;
    transition: all .3s;
    text-align: center;
}

.lock-screen-psw::-webkit-input-placeholder {
    color: #F6F6F6;
    font-size: 16px;
}

.lock-screen-psw::-moz-placeholder {
    color: #F6F6F6;
    font-size: 16px;
}

.lock-screen-psw::-ms-input-placeholder {
    color: #F6F6F6;
    font-size: 16px;
}

.lock-screen-enter {
    width: 65px;
    height: 65px;
    line-height: 1;
    font-size: 28px;
    padding-top: 18px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    text-align: center;
    display: inline-block;
    box-sizing: border-box;
    vertical-align: middle;
    transition: all .3s;
    cursor: pointer;
}

.lock-screen-enter:hover {
    background-color: rgba(255, 255, 255, .3);
}

.lock-screen-form.show-psw .lock-screen-psw {
    height: 40px;
    width: 180px;
    padding: 0 5px;
    margin-right: 10px;
    border-color: #ffffff;
    background-color: rgba(255, 255, 255, .3);
}

.lock-screen-form.show-psw .lock-screen-enter {
    width: 40px;
    height: 40px;
    font-size: 20px;
    padding-top: 8px;
}

.lock-screen-form.show-back .lock-screen-enter:before {
    content: "\e603";
}

.lock-screen-tip {
    color: red;
    width: 230px;
    padding: 0 3px;
    font-size: 14px;
    text-align: left;
    box-sizing: border-box;
    display: none;
}

.lock-screen-form.show-psw .lock-screen-tip {
    display: inline-block;
}

.lock-screen-tool {
    width: 20px;
    position: absolute;
    right: 20px;
    bottom: 20px;
}

.lock-screen-tool .lock-screen-tool-item {
    position: relative;
    width: 20px;
    height: 20px;
    line-height: 20px;
    margin-top: 15px;
    cursor: pointer;
}

.lock-screen-tool .lock-screen-tool-item:hover .layui-icon {
    color: #ffffff;
}

.lock-screen-tool .layui-icon {
    font-size: 20px;
}

.lock-screen-tool .lock-screen-tool-item .lock-screen-tool-tip {
    position: absolute;
    top: 50%;
    right: 100%;
    height: 24px;
    line-height: 24px;
    width: 60px;
    width: max-content;
    text-align: center;
    margin-right: 10px;
    margin-top: -12px;
    font-size: 12px;
    padding: 0 8px;
    color: #eee;
    border-radius: 3px;
    background-color: rgba(255, 255, 255, .3);
    word-break: break-all;
    display: none;
}

.lock-screen-tool .lock-screen-tool-item:hover .lock-screen-tool-tip {
    display: inline-block;
}

.lock-screen-tool .lock-screen-tool-item .lock-screen-tool-tip:before {
    content: "";
    border: 4px solid transparent;
    border-left-color: rgba(255, 255, 255, .3);
    position: absolute;
    right: -8px;
    top: 50%;
    margin-top: -4px;
}