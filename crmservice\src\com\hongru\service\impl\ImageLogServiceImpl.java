package com.hongru.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.hongru.entity.ImageLog;
import com.hongru.mapper.ImageLogMapper;
import com.hongru.service.IImageLogService;

/**
 * 
*    
* 类名称：ImageLogServiceImpl   
* 类描述：ImageLogServiceImpl / 云存储图片记录表 业务逻辑层接口实现   
* 创建人：hongru   
* 创建时间：2017年7月11日 下午8:15:57   
*
 */
@Service
public class ImageLogServiceImpl extends ServiceImpl<ImageLogMapper, ImageLog> implements IImageLogService {
	
}
