<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/gas/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md6">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="${gasPriceCost.yearMonth}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md6">
                        <label class="layui-form-label"><span class="star">*</span>部门:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="department" name="department" required>
                                <option value="">${gasPriceCost.departmentName}</option>
                                <c:forEach items="${departmentList}" var="department">
                                    <option value="${department.midCode}" >${department.midCode} - ${department.midDepartment}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md6">
                        <label class="layui-form-label"><span class="star">*</span>用量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="gasCost" name="gasCost" value="${gasPriceCost.gasCost}" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md6">
                        <label class="layui-form-label"><span class="star">*</span>金额:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="gasPrice" name="gasPrice" value="${gasPriceCost.gasPrice}" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="costId" name="costId" value="${gasPriceCost.costId}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/gasCostPrice_add.js?time=2"></script>
</myfooter>
</body>
</html>
