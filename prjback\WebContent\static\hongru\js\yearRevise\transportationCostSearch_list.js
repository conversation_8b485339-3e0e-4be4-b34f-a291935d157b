
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    laydate.render({
        elem: '#time'
        ,type: 'month'
        ,range: '~'
        ,format: 'yyyy-MM'
      });

    //执行一个 table 实例
    var url = baselocation+'/yearRevise/transportationCostSearch/list';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '列表'
        ,page: false //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'area',title: '地区',align:'center', width:100,templet: function (d) {
	                if(d.area ==1){
	                    return "自提";
	                }else if(d.area ==2){
	                    return "华南";
	                }else if(d.area ==3){
	                    return "华东";
	                }else if(d.area ==4){
	                    return "华北";
	                }else if(d.area ==5){
	                    return "其他";
	                }else if(d.area ==6){
	                    return "UF国内";
	                }else if(d.area ==7){
	                    return "UF出口";
	                }
	            }
            }
            ,{field: 'outQuantity',title: '出库量',align:'center', width:180}
            ,{field: 'transportationCost',title: '运输费金额',align:'center', width:180}
            ,{field: 'transportationCostUnit',title: '运输费单价',align:'center', width:180}  
            ,{field: 'salesQuantity',title: '销售量',align:'center', width:180}
            ,{field: 'recycleTransportationCost',title: '回收运输费金额',align:'center', width:180}
            ,{field: 'recycleTransportationCostUnit',title: '回收运输费单价',align:'center', width:180}  
            ,{field: 'recycleTransportationCostAvgUnit',title: '回收运输费平均单价',align:'center', width:180}  
            ,{field: 'comprehensiveFreight',title: '综合运费(含回收)',align:'center', width:180}  
        ]]
    });
});

function search() {
	var time=$("#time").val();
	if(time == null || time.trim() == ''){
		layer.alert("请选择日期范围！");
		return;
	}else{
	    $("#isSearch").val(1);
	    //var temp = $("#formSearch").serializeJsonObject();
	    var timeArray = time.split('\~').map(s => s.trim());
        var temp = {
            isSearch: 1,
            yearMonthStart: timeArray[0],
            yearMonthEnd: timeArray[1]
        };
	    console.info(temp);
	    //执行重载
	    layui.table.reload('demo', {
	    	where: temp
	    }, 'data');
	}
}