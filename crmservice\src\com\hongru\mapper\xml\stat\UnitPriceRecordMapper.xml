<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.stat.UnitPriceRecordMapper">
    <sql id="unitPriceRecord_sql">
		uni.[流水号] AS statId,uni.[年度] AS year,
		uni.[电力单价] AS electricUnitPrice,uni.[一般社员单价] AS generalUnitPrice,uni.[临时工单价] AS temporaryUnitPrice,
		uni.[保全单价] AS saveDamageUnitPrice,uni.[WR加工费EM] AS wrEMUnitPrice,uni.[WR加工费EF] AS wrEFUnitPrice,
		uni.[WR加工费ER] AS wrERUnitPrice,uni.[WR加工费EH] AS wrEHUnitPrice,uni.[运费单价] AS tranUnitPrice,
		uni.[线盘单价] AS wireUnitPrice,uni.[线盘回收率] AS wireRecycling,uni.[铜屑单价] AS cuCrumbsUnitPrice,
		uni.[天然气单价] AS gasUnitPrice,uni.[窒素单价] AS nitrogenUnitPrice
	</sql>
	
	<select id="selectUnitPriceRecordByYearMonth" resultType="com.hongru.entity.stat.UnitPriceRecord">
		SELECT
		<include refid="unitPriceRecord_sql"/>
		FROM [CostPrice].[dbo].[预定单价表] uni
		<where>
			<if test="year != null and year != ''">
                AND uni.[年度] = #{year}
       		</if>
       </where>
	</select>

	<select id="selectByCostId" resultType="com.hongru.entity.stat.UnitPriceRecord">
		SELECT
		<include refid="unitPriceRecord_sql"/>
		FROM [CostPrice].[dbo].[预定单价表] uni
		<where>
			1 = 1
			<if test="statId != null">
				AND uni.[流水号] = #{statId}
			</if>
		</where>
	</select>

	<select id="listUnitPriceRecordPage" resultType="com.hongru.entity.stat.UnitPriceRecord">
		SELECT
		<include refid="unitPriceRecord_sql"/>
		FROM [CostPrice].[dbo].[预定单价表] uni 
		<where>
			<if test="year != null and year != ''">
                AND uni.[年度] = #{year}
       		</if>
       </where>		
		ORDER BY uni.[年度] desc
	</select>
	
	<insert id="insertUnitPriceRecord" parameterType="com.hongru.entity.stat.Proportion">
		INSERT INTO [CostPrice].[dbo].[预定单价表]
		(
		[年度],
		[电力单价],
		[一般社员单价],
		[临时工单价],
		[保全单价],
		[WR加工费EM],
		[WR加工费EF],
		[WR加工费ER],
		[WR加工费EH],
		[运费单价],
		[线盘单价],
		[线盘回收率],
		[铜屑单价],
		[天然气单价],
		[窒素单价]
		)VALUES(
		#{unitPriceRecord.year},
		#{unitPriceRecord.electricUnitPrice},
		#{unitPriceRecord.generalUnitPrice},
		#{unitPriceRecord.temporaryUnitPrice},
		#{unitPriceRecord.saveDamageUnitPrice},
		#{unitPriceRecord.wrEMUnitPrice},
		#{unitPriceRecord.wrEFUnitPrice},
		#{unitPriceRecord.wrERUnitPrice},
		#{unitPriceRecord.wrEHUnitPrice},
		#{unitPriceRecord.tranUnitPrice},
		#{unitPriceRecord.wireUnitPrice},
		#{unitPriceRecord.wireRecycling},
		#{unitPriceRecord.cuCrumbsUnitPrice},
		#{unitPriceRecord.gasUnitPrice},
		#{unitPriceRecord.nitrogenUnitPrice}
		)
	</insert>
	
	<update id="updateUnitPriceRecord">
		UPDATE [CostPrice].[dbo].[预定单价表]
		<set>
			<if test="unitPriceRecord.year != null and unitPriceRecord.year != ''">
				[年度] = #{unitPriceRecord.year},
			</if>
			<if test="unitPriceRecord.electricUnitPrice != null and unitPriceRecord.electricUnitPrice != ''">
				[电力单价] = #{unitPriceRecord.electricUnitPrice},
			</if>
			<if test="unitPriceRecord.generalUnitPrice != null and unitPriceRecord.generalUnitPrice != ''">
				[一般社员单价] = #{unitPriceRecord.generalUnitPrice},
			</if>
			<if test="unitPriceRecord.temporaryUnitPrice != null and unitPriceRecord.temporaryUnitPrice != ''">
				[临时工单价] = #{unitPriceRecord.temporaryUnitPrice},
			</if>
			<if test="unitPriceRecord.saveDamageUnitPrice != null and unitPriceRecord.saveDamageUnitPrice != ''">
				[保全单价] = #{unitPriceRecord.saveDamageUnitPrice},
			</if>
			<if test="unitPriceRecord.wrEMUnitPrice != null and unitPriceRecord.wrEMUnitPrice != ''">
				[WR加工费EM] = #{unitPriceRecord.wrEMUnitPrice},
			</if>
			<if test="unitPriceRecord.wrEFUnitPrice != null and unitPriceRecord.wrEFUnitPrice != ''">
				[WR加工费EF] = #{unitPriceRecord.wrEFUnitPrice},
			</if>
			<if test="unitPriceRecord.wrERUnitPrice != null and unitPriceRecord.wrERUnitPrice != ''">
				[WR加工费ER] = #{unitPriceRecord.wrERUnitPrice},
			</if>
			<if test="unitPriceRecord.wrEHUnitPrice != null and unitPriceRecord.wrEHUnitPrice != ''">
				[WR加工费EH] = #{unitPriceRecord.wrEHUnitPrice},
			</if>
			<if test="unitPriceRecord.tranUnitPrice != null and unitPriceRecord.tranUnitPrice != ''">
				[运费单价] = #{unitPriceRecord.tranUnitPrice},
			</if>
			<if test="unitPriceRecord.wireUnitPrice != null and unitPriceRecord.wireUnitPrice != ''">
				[线盘单价] = #{unitPriceRecord.wireUnitPrice},
			</if>
			<if test="unitPriceRecord.wireRecycling != null and unitPriceRecord.wireRecycling != ''">
				[线盘回收率] = #{unitPriceRecord.wireRecycling},
			</if>
			<if test="unitPriceRecord.cuCrumbsUnitPrice != null and unitPriceRecord.cuCrumbsUnitPrice != ''">
				[铜屑单价] = #{unitPriceRecord.cuCrumbsUnitPrice},
			</if>
			<if test="unitPriceRecord.gasUnitPrice != null and unitPriceRecord.gasUnitPrice != ''">
				[天然气单价] = #{unitPriceRecord.gasUnitPrice},
			</if>
			<if test="unitPriceRecord.nitrogenUnitPrice != null and unitPriceRecord.nitrogenUnitPrice != ''">
				[窒素单价] = #{unitPriceRecord.nitrogenUnitPrice},
			</if>
		</set>
		<where>
			<if test="unitPriceRecord.statId != null">
				AND [流水号] = #{unitPriceRecord.statId}
			</if>
		</where>
	</update>

	<select id="selectUnitPriceRecordByYear" resultType="com.hongru.entity.stat.UnitPriceRecord">
		SELECT
		<include refid="unitPriceRecord_sql"/>
		FROM [CostPrice].[dbo].[预定单价表] uni
		WHERE uni.[年度] = #{year}
		ORDER BY uni.[年度] DESC
	</select>

	<delete id="deleteUnitPriceRecordByYear">
		DELETE FROM [CostPrice].[dbo].[预定单价表]
		WHERE [年度] = #{year}
	</delete>

	<insert id="batchInsertUnitPriceRecord" parameterType="java.util.List">
		INSERT INTO [CostPrice].[dbo].[预定单价表]
		(
		[年度],
		[电力单价],
		[一般社员单价],
		[临时工单价],
		[保全单价],
		[WR加工费EM],
		[WR加工费EF],
		[WR加工费ER],
		[WR加工费EH],
		[运费单价],
		[线盘单价],
		[线盘回收率],
		[铜屑单价],
		[天然气单价],
		[窒素单价]
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		(
		#{item.year},
		#{item.electricUnitPrice},
		#{item.generalUnitPrice},
		#{item.temporaryUnitPrice},
		#{item.saveDamageUnitPrice},
		#{item.wrEMUnitPrice},
		#{item.wrEFUnitPrice},
		#{item.wrERUnitPrice},
		#{item.wrEHUnitPrice},
		#{item.tranUnitPrice},
		#{item.wireUnitPrice},
		#{item.wireRecycling},
		#{item.cuCrumbsUnitPrice},
		#{item.gasUnitPrice},
		#{item.nitrogenUnitPrice}
		)
		</foreach>
	</insert>
</mapper>