layui.config({
	base: baselocationsta+'/common/layui/'
}).extend({
	iconPicker: 'iconPicker/iconPicker'
});
layui.use(['iconPicker','upload','element','layer','form','laydate'], function(){
	var form = layui.form;
	var $ = layui.jquery
		,upload = layui.upload
		,element = layui.element
		,layer = layui.layer;
	var laydate = layui.laydate;
	var iconPicker = layui.iconPicker;
	iconPicker.render({
		elem: '#iconPicker',
		type: 'fontClass',
		search: true,
		page: false,
		limit: 12,
		click: function (data) {
			console.log(data);
			$("#iconPicker").val(data.icon);
		},
		success: function(d) {
			console.log(d);
		}
	});
	form.on('submit(formDemo)', function(data){
		var index = layer.load(2,{
			shade:[0.1,'#fff']
		});
		var actionUrl = $("#submitForm").attr('action');
		$.ajax({
			url : actionUrl,
			type : 'post',
			data : $('#submitForm').serialize(),
			success : function(result) {
				layer.closeAll();
				if(result.code == 1){
					parent.layer.msg("操作成功!", {
						shade : 0.3,
						time : 1500
					}, function() {
						window.parent.location.reload();
						parent.layer.closeAll();
					});
				}else{
					layer.alert(result.message);
				}
			}
		});
		return false;
	});

});

function closeAll(){
	parent.layer.closeAll();
}