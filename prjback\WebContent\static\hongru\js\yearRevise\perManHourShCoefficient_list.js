
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    laydate.render({
        elem: '#time'
        ,type: 'month'
        ,range: '~'
        ,format: 'yyyy-MM'
      });

    //执行一个 table 实例
    var url = baselocation+'/yearRevise/perManHourShCoefficient/list';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '列表'
        ,page: false //关闭分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            ,{field: 'machineType',title: '机械类别',align:'center', width:150} 
            ,{field: 'departmentCode',title: '部门',align:'center', width:150} 
            ,{field: 'workType',title: '人件工种',align:'center', width:150}
            ,{field: 'workHour',title: '人件工时',align:'center', width:150}
            ,{field: 'machineTimePreAvg',title: '机械时间予定',align:'center', width:200}
            ,{field: 'personManHourSH',title: '人件工时SH系数',align:'center', width:200}
        ]]
    });
    
  //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                	// 不需要分页
                   where: temp
                }, 'data');
                break;
    		case 'toExport':
    			var time = $("#time").val();
    			var departmentCode = $("#departmentCode").val();
    			$("#timeForExcell").val(time);
    			$("#departmentCode").val(departmentCode);
    			$("#formExcell").submit();
    		break;
        };
    });

});


function search() {
	var time=$("#time").val();
	if(time == null || time.trim() == ''){
		layer.alert("请选择日期范围！");
		return;
	}else{
	    $("#isSearch").val(1);
	    var temp = $("#formSearch").serializeJsonObject();
	    console.info(temp);
	    //执行重载
	    layui.table.reload('demo', {
	       where: temp
	    }, 'data');
	}
}
// MW、UF一览明细选择
function toPage(orders) {
    if(orders == 1){
        window.location.href = baselocation+'/yearRevise/perManHourShCoefficient/list/view';
    }else if(orders == 2){
        window.location.href = baselocation+'/yearRevise/perManHourShCoefficientUF/list/view';
    }
}

//excell导出
function toExport(){
	var time=$("#time").val();
	var departmentCode = $("#departmentCode").val();
	$("#timeForExcell").val(time);
	$("#departmentCode").val(departmentCode);
	$("#formExcell").submit();
}
