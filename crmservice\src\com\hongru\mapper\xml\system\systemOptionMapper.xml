<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.system.SystemOptionMapper">
	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.hongru.entity.system.SystemOption">
		<id column="systemOptionId" property="systemOptionId" />
		<result column="cnName" property="cnName" />
		<result column="enName" property="enName" />
		<result column="content" property="content" />
		<result column="createdTimestamp" property="createdTimestamp" />
		
		<result column="creatorId" property="creatorId" />
		<result column="creatorName" property="creatorName" />
		<result column="lastModified" property="lastModified" />
		<result column="lastModifierId" property="lastModifierId" />
		<result column="lastModifierName" property="lastModifierName" />
	</resultMap>
	
	<update id="updateSystemOptionByEnName" parameterType="com.hongru.entity.system.SystemOption">
		update hr_system_option set content = #{content },lastModifierId = #{lastModifierId },lastModified = #{lastModified },
		lastModifierName = #{lastModifierName }, cnName = #{cnName } WHERE enName = #{enName }
	</update>
	
	<select id="getSystemOptionByEnName" resultType="com.hongru.entity.system.SystemOption">
		select * from hr_system_option where enName = #{enName }
	</select>
</mapper>