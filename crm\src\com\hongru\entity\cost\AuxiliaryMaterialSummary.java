package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("辅材費用汇总表")//AuxiliaryMaterialSummary
public class AuxiliaryMaterialSummary {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 状态 */
	protected short state;
	/* 导入标识 */
	protected int importId;
	/* 年月 */
	protected String yearMonth;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 性质 */
	protected String nature;
	/* 摘要 */
	protected String abstracts;
	/* 金额 */
	protected BigDecimal amount;
	/* EM */
	protected BigDecimal em;
	/* EF */
	protected BigDecimal ef;
	/* UF */
	protected BigDecimal uf;
	/* ER */
	protected BigDecimal er;
	/* EH */
	protected BigDecimal eh;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}

	public short getState() {
		return state;
	}

	public void setState(short state) {
		this.state = state;
	}

	public int getImportId() {
		return importId;
	}

	public void setImportId(int importId) {
		this.importId = importId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public String getNature() {
		return nature;
	}

	public void setNature(String nature) {
		this.nature = nature;
	}

	public String getAbstracts() {
		return abstracts;
	}

	public void setAbstracts(String abstracts) {
		this.abstracts = abstracts;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getEm() {
		return em;
	}

	public void setEm(BigDecimal em) {
		this.em = em;
	}

	public BigDecimal getEf() {
		return ef;
	}

	public void setEf(BigDecimal ef) {
		this.ef = ef;
	}

	public BigDecimal getUf() {
		return uf;
	}

	public void setUf(BigDecimal uf) {
		this.uf = uf;
	}

	public BigDecimal getEr() {
		return er;
	}

	public void setEr(BigDecimal er) {
		this.er = er;
	}

	public BigDecimal getEh() {
		return eh;
	}

	public void setEh(BigDecimal eh) {
		this.eh = eh;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
}