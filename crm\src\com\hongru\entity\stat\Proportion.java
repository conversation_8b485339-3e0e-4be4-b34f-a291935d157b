package com.hongru.entity.stat;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("比例表")//RepairFee
public class Proportion {

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int statId;
	/* 年度 */
	protected String year;
	/* 部门 */
	protected String departmentCode;
	/* EM */
	protected BigDecimal em;
	/* EF */
	protected BigDecimal ef;
	/* UF */
	protected BigDecimal uf;
	/* ER */
	protected BigDecimal er;
	/* EH */
	protected BigDecimal eh;

	public int getStatId() {
		return statId;
	}

	public void setStatId(int statId) {
		this.statId = statId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}

	public BigDecimal getEm() {
		return em;
	}

	public void setEm(BigDecimal em) {
		this.em = em;
	}

	public BigDecimal getEf() {
		return ef;
	}

	public void setEf(BigDecimal ef) {
		this.ef = ef;
	}

	public BigDecimal getUf() {
		return uf;
	}

	public void setUf(BigDecimal uf) {
		this.uf = uf;
	}

	public BigDecimal getEr() {
		return er;
	}

	public void setEr(BigDecimal er) {
		this.er = er;
	}

	public BigDecimal getEh() {
		return eh;
	}

	public void setEh(BigDecimal eh) {
		this.eh = eh;
	}
}