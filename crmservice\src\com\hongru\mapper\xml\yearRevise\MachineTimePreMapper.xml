<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.yearRevise.MachineTimePreMapper">
 	
 	<select id="listMachineTimePreBeanForMW" resultType="com.hongru.entity.yearRevise.MachineTimePreBean">
		SELECT
		[部门] AS department, SUM([SMCH]) / #{monthInterval} AS machineTimePreAvg
		FROM [CostPrice].[dbo].[预定直接部门回收计算表]
		WHERE [区分] NOT IN ('6')  AND [年月] &gt;=#{timeStartStr} AND [年月] &lt;= #{timeEndStr}
		GROUP BY [部门] 
	</select>
	
	<select id="listMachineTimePreBeanForUF" resultType="com.hongru.entity.yearRevise.MachineTimePreBean">
		SELECT
		[部门] AS department, SUM([SMCH]) / #{monthInterval} AS machineTimePreAvg, SUM([SMCH]) / #{monthInterval} AS newYearMachineTimePre
		FROM [CostPrice].[dbo].[预定直接部门回收计算表]
		WHERE  [区分] IN ('6')  AND [年月] &gt;=#{timeStartStr} AND [年月] &lt;= #{timeEndStr}
		GROUP BY [部门] 
	</select>
</mapper>