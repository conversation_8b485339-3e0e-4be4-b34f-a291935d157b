package com.hongru.common.util;

import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;

public class TestUtil{

    public static void main(String[] args) throws Exception{

        inserSheetData2007("1","1","10.5");
    }

    //向已知表中插入数据，累计追加
    //写入前先判断表是否存在，表中是否有数据
    private static void inserSheetData(String hang ,String lie,String shuju) throws Exception {

        String filePath = "D:\\1.xls";
        FileInputStream fs=new FileInputStream(filePath);

        //使用POI提供的方法得到excel的信息
        POIFSFileSystem fileSystem = new POIFSFileSystem(fs);
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileSystem);
        //获取到工作表，因为一个excel可能有多个工作表
        HSSFSheet InsertSheet=hssfWorkbook.getSheetAt(1);
        //获取第一行（excel中的行默认从0开始，所以这就是为什么，一个excel必须有字段列头），即，字段列头，便于赋值
        HSSFRow row=InsertSheet.getRow(1);
        //分别得到最后一行的行号，和一条记录的最后一个单元格
        System.out.println("最后一行的行 "+InsertSheet.getLastRowNum());

        //向文件中写入数据
        FileOutputStream out=new FileOutputStream(filePath);

//        //表示你想在那个行+1插入数据    比如6就在第7行
//        //int lastRowNum = InsertSheet.getLastRowNum()+1;
//        int lastRowNum = 6;
//
//        //在指定行后追加数据
//        row=InsertSheet.createRow((short)(lastRowNum));
//
//        //设置第一个（从0开始）单元格的数据
//        row.createCell(1).setCellValue("xiaoming");//列+1   1则在第2列插入
//        row.createCell(2).setCellValue(24);
//        row.createCell(3).setCellValue("nan");
//        row.createCell(4).setCellValue("nanjing");


        //自动设置的
        String[] hangs = hang.split(",");
        String[] lies = lie.split(",");
        String[] shujus = shuju.split(",");
        for (int i = 0; i <hangs.length ; i++) {
            for (int j = 0; j <lies.length ; j++) {

                int a =Integer.parseInt(hangs[i]);
                int b =Integer.parseInt(lies[j]);
                Float c =Float.parseFloat(shujus[j]);
                //在指定行后追加数据
                //row=InsertSheet.createRow((short)(a)); //createRow会删除掉整行数据
                row=InsertSheet.getRow((short)(a));//只更新本单元格
                if(row == null){
                    row = InsertSheet.createRow(a);
                }
                //设置第一个（从0开始）单元格的数据
                 row.createCell(b).setCellValue(c);//列+1   1则在第2列插入
//                row.createCell(b).setCellValue("hahh");//列+1   1则在第2列插入

                System.out.println(a+b+c);
            }
        }
        InsertSheet.setForceFormulaRecalculation(true);
        //后面使用它来执行计算公式 核心代码
//        FormulaEvaluator evaluator = hssfWorkbook.getCreationHelper().createFormulaEvaluator();
//        CellReference cellReference = new CellReference("C1");
//        Row row = InsertSheet.getRow(cellReference.getRow());
//        Cell cell = row.getCell(cellReference.getCol());
//        CellValue cellValue = evaluator.evaluate(cell);
//        System.out.println(cellValue.getNumberValue());

        out.flush();
        hssfWorkbook.write(out);
        out.close();

        System.out.println("物理列数="+row.getPhysicalNumberOfCells()+" 实际列数="+row.getLastCellNum());
    }

    //向已知表中插入数据，累计追加
    //写入前先判断表是否存在，表中是否有数据
    private static void inserSheetData2007(String hang ,String lie,String shuju) throws Exception {

        String filePath = "D:\\报表4.xlsx";
        FileInputStream fs=new FileInputStream(filePath);

        //使用POI提供的方法得到excel的信息
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook(fs);
        //获取到工作表，因为一个excel可能有多个工作表
        XSSFSheet InsertSheet=hssfWorkbook.getSheetAt(1);
        //获取第一行（excel中的行默认从0开始，所以这就是为什么，一个excel必须有字段列头），即，字段列头，便于赋值
        XSSFRow row=InsertSheet.getRow(1);
        //分别得到最后一行的行号，和一条记录的最后一个单元格
        System.out.println("最后一行的行 "+InsertSheet.getLastRowNum());

        //向文件中写入数据
        FileOutputStream out=new FileOutputStream(filePath);

//        //表示你想在那个行+1插入数据    比如6就在第7行
//        //int lastRowNum = InsertSheet.getLastRowNum()+1;
//        int lastRowNum = 6;
//
//        //在指定行后追加数据
//        row=InsertSheet.createRow((short)(lastRowNum));
//
//        //设置第一个（从0开始）单元格的数据
//        row.createCell(1).setCellValue("xiaoming");//列+1   1则在第2列插入
//        row.createCell(2).setCellValue(24);
//        row.createCell(3).setCellValue("nan");
//        row.createCell(4).setCellValue("nanjing");


        //自动设置的
        String[] hangs = hang.split(",");
        String[] lies = lie.split(",");
        String[] shujus = shuju.split(",");
        for (int i = 0; i <hangs.length ; i++) {
            for (int j = 0; j <lies.length ; j++) {

                int a =Integer.parseInt(hangs[i]);
                int b =Integer.parseInt(lies[j]);
                Float c =Float.parseFloat(shujus[j]);
                //在指定行后追加数据
                //row=InsertSheet.createRow((short)(a)); //createRow会删除掉整行数据
                row=InsertSheet.getRow((short)(a));//只更新本单元格
                if(row == null){
                    row = InsertSheet.createRow(a);
                }
                //设置第一个（从0开始）单元格的数据
                row.createCell(b).setCellValue(c);//列+1   1则在第2列插入
//                row.createCell(b).setCellValue("hahh");//列+1   1则在第2列插入

                System.out.println(a+b+c);
            }
        }
        InsertSheet.setForceFormulaRecalculation(true);
        //后面使用它来执行计算公式 核心代码
//        FormulaEvaluator evaluator = hssfWorkbook.getCreationHelper().createFormulaEvaluator();
//        CellReference cellReference = new CellReference("C1");
//        Row row = InsertSheet.getRow(cellReference.getRow());
//        Cell cell = row.getCell(cellReference.getCol());
//        CellValue cellValue = evaluator.evaluate(cell);
//        System.out.println(cellValue.getNumberValue());

        out.flush();
        hssfWorkbook.write(out);
        out.close();

        System.out.println("物理列数="+row.getPhysicalNumberOfCells()+" 实际列数="+row.getLastCellNum());
    }

}

