package com.hongru.mapper.yearRevise;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.yearRevise.MachineTimePreBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MachineTimePreMapper extends BaseMapper<MachineTimePreBean> {

	 List<MachineTimePreBean> listMachineTimePreBeanForMW(@Param("monthInterval") int monthInterval,  @Param("timeStartStr") String timeStartStr,  @Param("timeEndStr") String timeEndStr);
	 List<MachineTimePreBean> listMachineTimePreBeanForUF(@Param("monthInterval") int monthInterval,  @Param("timeStartStr") String timeStartStr,  @Param("timeEndStr") String timeEndStr);
	
	
    
   
}
