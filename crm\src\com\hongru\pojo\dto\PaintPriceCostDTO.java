package com.hongru.pojo.dto;

import com.hongru.entity.cost.PaintPriceCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class PaintPriceCostDTO {

    private PageInfo pageInfo;

    private List<PaintPriceCost> paintPriceCostList;

    public PaintPriceCostDTO(PageInfo pageInfo, List<PaintPriceCost> paintPriceCostList) {
        super();
        this.pageInfo = pageInfo;
        this.paintPriceCostList = paintPriceCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<PaintPriceCost> getPaintPriceCostList() {
        return paintPriceCostList;
    }

    public void setPaintPriceCostList(List<PaintPriceCost> paintPriceCostList) {
        this.paintPriceCostList = paintPriceCostList;
    }
}
