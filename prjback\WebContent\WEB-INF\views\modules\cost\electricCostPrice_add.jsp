<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>电费使用添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/electric/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value=""/>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>电费计算方式:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="calculateTag" name="calculateTag" lay-verify="required" required>
                                <option value="">请选择</option>
                                <option value="1">不分时电费</option>
                                <option value="2">分时电费（峰）</option>
                                <option value="3">分时电费（平）</option>
                                <option value="4">分时电费（谷）</option>
                                <option value="5">分时电费（尖峰）</option>
                                <option value="6">分时电费（深谷）</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>单价:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eleSinglePrice" name="eleSinglePrice" value="0" onKeyUp="amountV2(this)" onBlur="overFormatV2(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>用量(KW/H):</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eleCost" name="eleCost" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                      <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>单价(实际):</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eleSinglePriceAct" name="eleSinglePriceAct" value="0" onKeyUp="amountV2(this)" onBlur="overFormatV2(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>用量(实际):</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eleCostAct" name="eleCostAct" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/electricCostPrice_add.js?time=6"></script>
</myfooter>
</body>
</html>
