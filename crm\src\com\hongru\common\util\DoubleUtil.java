package com.hongru.common.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class DoubleUtil {

	/***
	 * 
	 * @param price
	 * @param code
	 * @return
	 * @throws Exception
	 */
	public static int DoubleForInt(double price,double code)throws Exception{
		BigDecimal pricebd = new BigDecimal(Double.toString(price));
		BigDecimal b2 = new BigDecimal(Double.toString(code));
		int priceInt = pricebd.multiply(b2).intValue();
		return priceInt;
	} 
	
	/***
	 * 
	 *@param v1 被除数
     * @param v2 除数
     * @param scale 表示表示需要精确到小数点以后几位。
     * @return 两个参数的商 
	 * @return
	 */
	public static double IntForDouble(double v1,double v2,int scale){
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.divide(b2,scale,BigDecimal.ROUND_HALF_UP).doubleValue();
    } 
	
	/**
     * 提供精确的小数位四舍五入处理。
     * @param v 需要四舍五入的数字
     * @param scale 小数点后保留几位
     * @return 四舍五入后的结果
     */
    public static double round(double v,int scale){
        BigDecimal b = new BigDecimal(Double.toString(v));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one,scale,BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
    * 提供精确的小数位向下取整处理。
    * @param v 需要向下取整的数字
    * @param scale 小数点后保留几位
    * @throws
    * <AUTHOR>
    * @create 2022/12/5 14:55
    * @return 向下取整的结果
    */
    public static double roundV2(double v,int scale){
        BigDecimal b = new BigDecimal(Double.toString(v));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one,scale,BigDecimal.ROUND_DOWN).doubleValue();
    }

    /**
     * 提供精确加法计算的add方法
     * @param value1 被加数
     * @param value2 加数
     * @return 两个参数的和
     */
    public static double add(double value1,double value2){
	    BigDecimal b1 = new BigDecimal(Double.valueOf(value1));
	    BigDecimal b2 = new BigDecimal(Double.valueOf(value2));
	    return b1.add(b2).doubleValue();
    }

    /**
     * 提供精确减法运算的sub方法
     * @param value1 被减数
     * @param value2 减数
     * @return 两个参数的差
     */
    public static double sub(double value1,double value2){
	    BigDecimal b1 = new BigDecimal(Double.toString(value1));
	    BigDecimal b2 = new BigDecimal(Double.toString(value2));
	    return b1.subtract(b2).doubleValue();
    }

    /**
     * 提供精确乘法运算的mul方法
     * @param value1 被乘数
     * @param value2 乘数
     * @return 两个参数的积
     */
    public static double mul(double value1,double value2){
	    BigDecimal b1 = new BigDecimal(Double.toString(value1));
	    BigDecimal b2 = new BigDecimal(Double.toString(value2));
	    return b1.multiply(b2).doubleValue();
    }
    
//    public static void main(String[] args) {
//    	int currentPrice = 30000;
//    	int originalPrice = 37600;
//    	int totalPrice = 1500;
//    	double discount2 = DoubleUtil.IntForDouble(currentPrice, originalPrice,4);
//    	System.out.println("discount2 double is " + discount2);
//		double basePrice = DoubleUtil.mul(totalPrice, discount2);
//		System.out.println("basePrice double is " + basePrice);
//		System.out.println("basePrice int is " + (int)basePrice);
//		
//		double earnPrecent = 0.85;
//		double flowAmount = DoubleUtil.mul(basePrice, earnPrecent);
//		System.out.println("flowAmount double is" + flowAmount);
//	}

    /**
     * 提供精确的除法运算方法div
     * @param value1 被除数
     * @param value2 除数
     * @param scale 精确范围
     * @return 两个参数的商
     * @throws IllegalAccessException
     */
    public static double div(double value1,double value2,int scale) throws IllegalAccessException{
	    //如果精确范围小于0，抛出异常信息
	    if(scale<0){
	      throw new IllegalAccessException("精确度不能小于0");
	    }
	    BigDecimal b1 = new BigDecimal(Double.toString(value1));
	    BigDecimal b2 = new BigDecimal(Double.toString(value2));
	    return b1.divide(b2, scale).doubleValue();
    }
    
    /**
     * 把小数转换成百分数
     * @param result
     * @return
     * <AUTHOR>
     * @create 2016年11月8日
     */
    public static String getBaiFenShu(double result){
        DecimalFormat df = new DecimalFormat("0.00%");
        String r = df.format(result);
        return r;
    }
    
    /**
     * 分转成元
     * @param amount
     * @return
     * <AUTHOR>
     * @create 2019年12月18日 下午1:10:18
     */
    public static  String centToDollarForString(Integer amount){
        if (amount == null) {
            return "0.00";
        } else {
            BigDecimal amount1 = new BigDecimal(amount.toString());
            amount1 = amount1.divide(new BigDecimal(100));
            DecimalFormat df = new DecimalFormat("#0.00");
            return df.format(amount1);
        }
    }
    public static void main(String[] a) {
    	System.out.println((int)mul(0.01, 0.01));
    }

    /**
     * 方法描述：将double转换成带有2位小数的字符串,21.3299转成21.32,21转成21.00,0.1转成0.10 .</br> 备 注:
     * .</br> 创 建 人: zhaogang .</br> 创建日期: 2011-9-16 下午3:54:45 .</br>
     *
     * @param value
     * @return .</br>
     */
    public static String covertDoubleWith2Decimal(Double value)
    {
        if(Double.isNaN(value)) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat("########.##");// 指定转换的格式
        String strVal = df.format(value);// 将double类型的值转换为String类型
        return strVal;
    }
}
