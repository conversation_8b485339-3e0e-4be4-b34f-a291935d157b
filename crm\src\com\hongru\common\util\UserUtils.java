package com.hongru.common.util;

import java.io.File;
import java.util.Date;

import com.hongru.common.enums.WebSiteFileBelongEnum;

/**
 * 
* 类名称：UserUtils   
* 类描述：UserUtils工具类：提供一些用户操作的方法      
* 创建人：hongru   
* 创建时间：2017年4月2日 下午2:46:39   
*
 */
public class UserUtils {

	/** 用户编号后缀位数 */
	private static final int SUFFIX_NUMBER = 2;

	private UserUtils() {
		throw new AssertionError();
	}

	/** 获得用户编号 */
	public static Long getUserNumber() {
		String prefixNumber = Long.toString(new Date().getTime());
		String suffixNumber = RandomUtils.number(SUFFIX_NUMBER);
		String userNumber = prefixNumber + suffixNumber;
		return Long.valueOf(userNumber);
	}

	/** 获得系统默认的头像 */
	public static String getPicImg() {
		// 系统默认头像名
		String picImg = "";
		// 系统 默认头像路径
		StringBuilder picImgUrl = new StringBuilder();
		picImgUrl.append(WebSiteFileBelongEnum.DEFAULT.getBelong());
		picImgUrl.append(File.separator);
		picImgUrl.append(WebSiteFileBelongEnum.AVATAR.getBelong());
		picImgUrl.append(File.separator);
		picImgUrl.append(picImg);

		// 将绝对路径"\"替换成"/"
		return picImgUrl.toString().replaceAll("\\\\", "/");
	}
}
