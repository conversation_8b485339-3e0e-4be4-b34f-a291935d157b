<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>成本参数复制</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <form id="copyForm" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">源年度:</label>
                                <div class="layui-input-block">
                                    <input type="text" class="layui-input" id="fromYear" name="fromYear" placeholder="请选择源年度" />
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">目标年度:</label>
                                <div class="layui-input-block">
                                    <input type="text" class="layui-input" id="toYear" name="toYear" placeholder="请选择目标年度" />
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md1 hr-div-btn">
                                <button type="button" id="copyBtn" class="layui-btn layui-bg-blue">
                                    <i class="layui-icon layui-icon-upload"></i>开始复制
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-panel">
                    <div style="padding: 15px;">
                        <h4><i class="layui-icon layui-icon-tips"></i> 复制说明</h4>
                        <p>此功能将复制以下5个表的数据从源年度到目标年度：</p>
                        <ul>
                            <li>1. 费用项目单价表</li>
                            <li>2. 原料项目表</li>
                            <li>3. 导体单价明细表（铜加工費用明细表）</li>
                            <li>4. 油漆单价明细表（油漆价格表）</li>
                            <li>5. 线盘单价明细表（线盘费用表）</li>
                        </ul>
                        <p style="color: red;"><strong>注意：</strong>复制操作会先删除目标年度的现有数据，然后复制源年度的数据。请谨慎操作！</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
layui.use(['form', 'layer', 'laydate'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;

    // 源年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#fromYear',
        btns: ['clear','confirm']
    });

    // 目标年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#toYear',
        btns: ['clear','confirm']
    });

    // 复制按钮点击事件
    $('#copyBtn').click(function() {
        var fromYear = $('#fromYear').val();
        var toYear = $('#toYear').val();

        // 参数验证
        if (!fromYear) {
            layer.msg('请选择源年度', {icon: 2});
            return;
        }
        if (!toYear) {
            layer.msg('请选择目标年度', {icon: 2});
            return;
        }
        if (fromYear === toYear) {
            layer.msg('源年度和目标年度不能相同', {icon: 2});
            return;
        }

        // 确认对话框
        layer.confirm('确定要将' + fromYear + '年度的成本参数复制到' + toYear + '年度吗？<br><br><span style="color:red;">注意：此操作会删除目标年度的现有数据！</span>', {
            icon: 3,
            title: '确认复制'
        }, function(index) {
            // 显示加载中
            var loadingIndex = layer.load(1, {
                shade: [0.1, '#fff']
            });

            // 禁用按钮
            $('#copyBtn').prop('disabled', true).html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 复制中...');

            // 执行复制
            $.ajax({
                url: baselocation + '/yearParamSet/copyCostParameters/copy',
                type: 'POST',
                data: {
                    fromYear: fromYear,
                    toYear: toYear
                },
                success: function(result) {
                    layer.close(loadingIndex);
                    if (result.code === 1) {
                        layer.msg(result.msg || '复制成功', {icon: 1}, function() {
                            // 重置表单
                            $('#fromYear').val('');
                            $('#toYear').val('');
                        });
                    } else {
                        layer.msg(result.msg || '复制失败', {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    layer.close(loadingIndex);
                    layer.msg('复制失败：' + error, {icon: 2});
                },
                complete: function() {
                    // 恢复按钮
                    $('#copyBtn').prop('disabled', false).html('<i class="layui-icon layui-icon-upload"></i>开始复制');
                }
            });

            layer.close(index);
        });
    });
});
</script>
</body>
</html>
