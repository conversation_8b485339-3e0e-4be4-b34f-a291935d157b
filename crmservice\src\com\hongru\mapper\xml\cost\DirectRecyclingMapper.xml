<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.DirectRecyclingMapper">
    <sql id="directRecycling_sql">
		di.[流水号] AS costId,di.[导入标识] AS importId,di.[年月] AS yearMonth,di.[年] AS year,di.[月] AS month,
		di.[区分] AS directCode,di.[部门] AS departmentCode,di.[SMCH] AS sMCHNum,di.[操作工人件费SH] AS operatorCostSH,
		di.[操作工人件费费用] AS operatorCost,di.[劳务工人件费SH] AS laborCostSH,di.[劳务工人件费费用] AS laborCost,
		di.[电费SH] AS electricCostSH,di.[电费费用] AS electricCost,di.[煤气费SH] AS gasCostSH,di.[煤气费费用] AS gasCost,
		di.[水费SH] AS waterCostSH,di.[水费费用] AS waterCost,di.[氮气费SH] AS nitrogenCostSH,di.[氮气费费用] AS nitrogenCost,
		di.[保全操作工人件费SH] AS securityCostSH,di.[保全操作工人件费费用] AS securityCost,di.[补修费SH] AS repairCostSH,
		di.[补修费费用] AS repairCost,di.[辅材费SH] AS materialCostSH,di.[辅材费费用] AS materialCost,
		di.[创建人标识] AS creatorId,di.[创建人姓名] AS creatorName,di.[创建时间] AS createdTime
	</sql>

	<insert id="insertDirectRecycling" parameterType="com.hongru.entity.cost.DirectRecycling">
		INSERT INTO [CostPrice].[dbo].[预定直接部门回收计算表]
		(
		[导入标识],
		[年月],
		[年],
		[月],
		[区分],
		[部门],
		[SMCH],
		[操作工人件费SH],
		[操作工人件费费用],
		[劳务工人件费SH],
		[劳务工人件费费用],
		[电费SH],
		[电费费用],
		[煤气费SH],
		[煤气费费用],
		[水费SH],
		[水费费用],
		[氮气费SH],
		[氮气费费用],
		[保全操作工人件费SH],
		[保全操作工人件费费用],
		[补修费SH],
		[补修费费用],
		[辅材费SH],
		[辅材费费用],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{directRecycling.importId},
		#{directRecycling.yearMonth},
		#{directRecycling.year},
		#{directRecycling.month},
		#{directRecycling.directCode},
		#{directRecycling.departmentCode},
		#{directRecycling.sMCHNum},
		#{directRecycling.operatorCostSH},
		#{directRecycling.operatorCost},
		#{directRecycling.laborCostSH},
		#{directRecycling.laborCost},
		#{directRecycling.electricCostSH},
		#{directRecycling.electricCost},
		#{directRecycling.gasCostSH},
		#{directRecycling.gasCost},
		#{directRecycling.waterCostSH},
		#{directRecycling.waterCost},
		#{directRecycling.nitrogenCostSH},
		#{directRecycling.nitrogenCost},
		#{directRecycling.securityCostSH},
		#{directRecycling.securityCost},
		#{directRecycling.repairCostSH},
		#{directRecycling.repairCost},
		#{directRecycling.materialCostSH},
		#{directRecycling.materialCost},
		#{directRecycling.creatorId},
		#{directRecycling.creatorName},
		#{directRecycling.createdTime}
		)
	</insert>

	<delete id="deleteDirectRecyclingByCostId">
		DELETE FROM [CostPrice].[dbo].[预定直接部门回收计算表] WHERE [流水号] = #{costId}
	</delete>
	
	<select id="listDirectRecyclingByYearMonth" resultType="com.hongru.entity.cost.DirectRecycling">
		SELECT
		<include refid="directRecycling_sql"/>
		FROM [CostPrice].[dbo].[预定直接部门回收计算表] di
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND di.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<select id="listDirectRecyclingPage" resultType="com.hongru.entity.cost.DirectRecycling">
		SELECT
		<include refid="directRecycling_sql"/>
		FROM [CostPrice].[dbo].[预定直接部门回收计算表] di
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND di.[年月] = #{yearMonth}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY di.[年月] DESC,di.[流水号] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listDirectRecyclingPageCount" resultType="integer">
		SELECT
		count(1)
		FROM [CostPrice].[dbo].[预定直接部门回收计算表]
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND [年月] = #{yearMonth}
			</if>
		</where>
	</select>
	

		<select id="listDirectRecyclingForMW" resultType="com.hongru.entity.cost.DirectRecycling">
		SELECT
		  [部门] AS departmentCode, SUM([操作工人件费SH])/ #{monthInterval} AS operatorCostSH, SUM([劳务工人件费SH])/ #{monthInterval}  AS laborCostSH,
		  SUM([保全操作工人件费SH])/ #{monthInterval}  AS securityCostSH, SUM([补修费费用])/#{monthInterval} AS repairCost, SUM([辅材费费用])/#{monthInterval} AS materialCost 
		FROM [CostPrice].[dbo].[预定直接部门回收计算表]
		<where>
			  区分 != '6' 
		    <if test="departArr != null and departArr != ''">
                AND [部门]  IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>    
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
		</where>
		 GROUP BY [部门]
	</select>
	
	<select id="listDirectRecyclingByDateRange" resultType="com.hongru.entity.cost.DirectRecycling">
		SELECT
		  [部门] AS departmentCode, SUM([操作工人件费SH])/ #{monthInterval} AS operatorCostSH, SUM([劳务工人件费SH])/ #{monthInterval}  AS laborCostSH,
		  SUM([保全操作工人件费SH])/ #{monthInterval}  AS securityCostSH, SUM([补修费费用])/#{monthInterval} AS repairCost, SUM([辅材费费用])/#{monthInterval} AS materialCost 
		FROM [CostPrice].[dbo].[预定直接部门回收计算表]
		<where>
			<if test="flag == 0">
				AND [区分] !=6
			</if>
			<if test="flag == 1">
				AND [区分] =6
			</if>
		    <if test="departArr != null and departArr != ''">
                AND [部门]  IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>    
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
		</where>
		 GROUP BY [部门]
	</select>
	
	<select id="listDirectRecyclingForUF" resultType="com.hongru.entity.yearRevise.NewYearEleCoeffucientBean">
		SELECT
		 部门 AS department, SUM([SMCH]) / #{monthInterval} AS machineTimePreAvg, SUM([电费SH]) / #{monthInterval} AS electricityPreAvg
		 , SUM([氮气费SH]) / #{monthInterval} AS nitrogenPreAvg
		FROM [CostPrice].[dbo].[预定直接部门回收计算表]
		<where>
			 区分 = '6' 
		    <if test="departArr != null and departArr != ''">
                AND [部门]  NOT IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>    
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
		</where>
		 GROUP BY [部门]
	</select>
	<select id="listDirectRecyclingUFByDateRange" resultType="com.hongru.entity.cost.DirectRecycling">
		SELECT
		  [部门] AS departmentCode, SUM([操作工人件费SH])/ #{monthInterval} AS operatorCostSH, SUM([劳务工人件费SH])/ #{monthInterval}  AS laborCostSH,
		  SUM([保全操作工人件费SH])/ #{monthInterval}  AS securityCostSH, SUM([补修费费用])/#{monthInterval} AS repairCost, SUM([辅材费费用])/#{monthInterval} AS materialCost 
		FROM [CostPrice].[dbo].[预定直接部门回收计算表]
		<where>
			 区分 = '6' 
		    <if test="departArr != null and departArr != ''">
                AND [部门]  NOT IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>    
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
		</where>
		 GROUP BY [部门]
	</select>	
</mapper>