<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.ConfigMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.hongru.entity.Config">
		<id column="config_id" property="configId" />
		<result column="config_key" property="configKey" />
		<result column="config_value" property="configValue" />
		<result column="config_name" property="configName" />
		<result column="remark" property="remark" />
		<result column="create_time" property="createTime" />
		<result column="create_by" property="createBy" />
		<result column="update_time" property="updateTime" />
		<result column="update_by" property="updateBy" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        config_id AS configId, config_key AS configKey, config_value AS configValue, config_name AS configName, remark, create_time AS createTime, create_by AS createBy, update_time AS updateTime, update_by AS updateBy
    </sql>

</mapper>
