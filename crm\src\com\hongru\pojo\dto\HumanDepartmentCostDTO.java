package com.hongru.pojo.dto;

import com.hongru.entity.cost.HumanDepartmentCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class HumanDepartmentCostDTO {

    private PageInfo pageInfo;

    private List<HumanDepartmentCost> humanDepartmentCostList;

    public HumanDepartmentCostDTO(PageInfo pageInfo, List<HumanDepartmentCost> humanDepartmentCostList) {
        super();
        this.pageInfo = pageInfo;
        this.humanDepartmentCostList = humanDepartmentCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<HumanDepartmentCost> getHumanDepartmentCostList() {
        return humanDepartmentCostList;
    }

    public void setHumanDepartmentCostList(List<HumanDepartmentCost> humanDepartmentCostList) {
        this.humanDepartmentCostList = humanDepartmentCostList;
    }
}
