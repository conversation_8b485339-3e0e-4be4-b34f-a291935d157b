<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ page import="com.hongru.common.util.Constants" %>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <style>
    /*设置数据表表头字体*/
    .layui-table th .layui-table-cell{
      text-align: center;
    }
  </style>
</head>
<body >
  <div class="ibox-content">
    <div class="layui-collapse">
      <div class="layui-colla-item">
        <h2 class="layui-colla-title">查询条件（单击此处展开/收起筛选条件）</h2>
        <div class="layui-colla-content">
          <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
            <div class="layui-form-item">
              <div class="layui-inline layui-col-md4">
                <label class="layui-form-label">搜索条件：</label>
                <div class="layui-input-block">
                  <input class="layui-input" type="text" style="display: inline;" placeholder="搜索条件" name="searchStr" />
                </div>
              </div>
              <div class="layui-inline layui-col-md4"></div>
              <div class="layui-inline layui-col-md4 hr-div-btn">
                <button type="button" id="btn_query" onclick="search();" class="layui-btn"><i class="layui-icon layui-icon-search"></i>检索</button>
                <button type="reset" class="layui-btn layui-btn-primary" id="btn-resert"><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <table class="layui-hide" id="demo" lay-filter="test"></table>
    <script type="text/html" id="barDemo">
      <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
      <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>删除</a>
      <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="userList"><i class="layui-icon layui-icon-search"></i>管理员列表</a>
    </script>
    <script type="text/html" id="toolbarDemo">
      <div class="layui-btn-container">
        <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="toAdd"><i class="layui-icon layui-icon-add-1"></i>添加</button>
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="shanChu"><i class="layui-icon layui-icon-delete"></i>删除</button>
      </div>
    </script>
  </div>
<myfooter>
  <script src="${ctxsta}/hongru/js/admin/admin_role_list.js?timer=9"></script>
</myfooter>
</body>
</html>