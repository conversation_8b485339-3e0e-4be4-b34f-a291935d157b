<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report2" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="733faca4-61d5-4358-8562-156073881700">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table" fontName="黑体" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="0.0"/>
			<bottomPen lineWidth="0.0"/>
			<rightPen lineWidth="0.0"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="1cc3fc2c-d680-4140-b039-e065279cc02c">
		<parameter name="customerName" class="java.lang.String"/>
		<field name="batchNo" class="java.lang.String"/>
		<field name="serialNo" class="java.lang.String"/>
		<field name="weightStr" class="java.lang.String"/>
	</subDataset>
	<parameter name="parameter1" class="java.util.List"/>
	<field name="model" class="java.lang.String"/>
	<field name="size" class="java.lang.String"/>
	<field name="palletNo" class="java.lang.String"/>
	<field name="totalReels" class="java.lang.String"/>
	<field name="totalWeights" class="java.lang.String"/>
	<field name="customerName" class="java.lang.String"/>
	<field name="stockOutList" class="java.util.List"/>
	<group name="palletNoGroup" isStartNewPage="true">
		<groupExpression><![CDATA[$F{palletNo}]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="31"/>
	</title>
	<pageHeader>
		<band height="31"/>
	</pageHeader>
	<detail>
		<band height="252">
			<staticText>
				<reportElement uuid="938738ce-cc42-41f1-8855-5944b6e4db04" x="13" y="0" width="100" height="20"/>
				<textElement>
					<font fontName="宋体" size="16" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CASE MARK]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="adeea489-f756-4aeb-88af-f56c13419da1" x="0" y="23" width="168" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[  PART NAME]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="6c6ecc19-7900-4076-b3ca-071a85caa61f" x="0" y="43" width="168" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[  SIZE]]></text>
			</staticText>
			<textField>
				<reportElement uuid="37c86608-192e-4637-940e-46eadb4d5ce2" x="168" y="43" width="184" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  "+$F{size}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="e387f1b4-1230-47ef-a35d-b8a15db39142" x="352" y="43" width="203" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[mm]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="0734d521-6d93-4cf2-9cdd-0e3e73b9edfa" x="0" y="63" width="168" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[  PALLET NO.]]></text>
			</staticText>
			<textField>
				<reportElement uuid="b20f3e15-a77d-4f94-94ea-5f6c0eb73a54" x="168" y="63" width="387" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  "+$F{palletNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="e1ab1f0f-94d2-41d9-b111-23773f84cf61" x="0" y="83" width="168" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[  TOTAL REELS]]></text>
			</staticText>
			<textField>
				<reportElement uuid="2db91ac4-5c6e-4dda-9aec-6bababd46570" x="168" y="83" width="184" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  "+$F{totalReels}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="c6e11aef-eb55-4746-af24-a50cd499b50a" x="352" y="83" width="203" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[reels]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="67394165-f7bd-4fd5-b069-0715623067c3" x="0" y="103" width="168" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[  TOTAL QUANTITY]]></text>
			</staticText>
			<textField>
				<reportElement uuid="7692bcd0-a215-4557-925b-5b2660ddaa67" x="168" y="103" width="184" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  "+$F{totalWeights}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="7a2b78a0-54b9-4d86-a61d-ae01d9d0b42c" x="352" y="103" width="203" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[kg]]></text>
			</staticText>
			<componentElement>
				<reportElement uuid="40632edf-3ec0-4ea3-a9f8-0e7c91e63ff8" key="table" style="table" stretchType="RelativeToBandHeight" x="0" y="123" width="555" height="121"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="Table Dataset 1" uuid="02d0fa02-a3b8-4766-9800-e9f4b3735097">
						<datasetParameter name="customerName">
							<datasetParameterExpression><![CDATA[$F{customerName}]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{stockOutList})]]></dataSourceExpression>
					</datasetRun>
					<jr:columnGroup width="555" uuid="5630e6b1-7506-464b-87af-1a4011d32f22">
						<jr:tableFooter height="30" rowSpan="1">
							<textField>
								<reportElement uuid="42c7d503-17fe-425f-954f-799b7aa3e2ef" x="0" y="0" width="555" height="30"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="16" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{customerName}]]></textFieldExpression>
							</textField>
						</jr:tableFooter>
						<jr:column width="90" uuid="850faed8-ac21-43bf-a1c7-bd1179f40c24">
							<jr:tableHeader height="30" rowSpan="1">
								<staticText>
									<reportElement uuid="3e53a708-838a-4c48-953b-d29bf440922f" x="0" y="0" width="90" height="30"/>
									<box>
										<topPen lineWidth="1.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement>
										<font fontName="宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[]]></text>
								</staticText>
							</jr:tableHeader>
							<jr:detailCell height="25" rowSpan="1">
								<staticText>
									<reportElement uuid="008332c7-06a6-41ad-b74f-8684a52e1850" x="0" y="0" width="90" height="25"/>
									<box>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement>
										<font fontName="宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[]]></text>
								</staticText>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="2a97964a-67d8-400e-ba4f-c80b230185a2">
							<jr:tableHeader height="30" rowSpan="1">
								<staticText>
									<reportElement uuid="490dec40-c669-44a0-8521-9406722bff67" x="0" y="0" width="90" height="30"/>
									<box>
										<topPen lineWidth="1.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement>
										<font fontName="宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[]]></text>
								</staticText>
							</jr:tableHeader>
							<jr:detailCell height="25" rowSpan="1">
								<staticText>
									<reportElement uuid="a7d4f2e4-9c3c-4d75-9e6f-4503c68e7193" x="0" y="0" width="90" height="25"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement>
										<font fontName="宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[]]></text>
								</staticText>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="239cd801-5089-40c8-b5f9-8f3a9eab62f5">
							<jr:tableHeader height="30" rowSpan="1">
								<staticText>
									<reportElement uuid="1ac32576-7e34-4b7c-b90a-6c2a88e5d227" x="0" y="0" width="90" height="30"/>
									<box>
										<topPen lineWidth="1.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[LOT No]]></text>
								</staticText>
							</jr:tableHeader>
							<jr:detailCell height="25" rowSpan="1">
								<textField>
									<reportElement uuid="70a28d30-f514-402a-8e62-8e4f5d3440e6" x="0" y="0" width="90" height="25"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{batchNo}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="6ce07e0d-d4bb-454c-979f-630ab3ca74f1">
							<jr:tableHeader height="30" rowSpan="1">
								<staticText>
									<reportElement uuid="53335146-e469-4279-b7e4-16e87c08214d" x="0" y="0" width="90" height="30"/>
									<box>
										<topPen lineWidth="1.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[Serial]]></text>
								</staticText>
							</jr:tableHeader>
							<jr:detailCell height="25" rowSpan="1">
								<textField>
									<reportElement uuid="f2bf1bfd-26b5-45a5-b535-957f912d9b87" x="0" y="0" width="90" height="25"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{serialNo}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="6073b606-a842-4923-9233-8454c93c2f57">
							<jr:tableHeader height="30" rowSpan="1">
								<staticText>
									<reportElement uuid="40e1b3e5-698c-4702-ad40-a9a029ce2c74" x="0" y="0" width="90" height="30"/>
									<box>
										<topPen lineWidth="1.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[NET WEIGHT]]></text>
								</staticText>
							</jr:tableHeader>
							<jr:detailCell height="25" rowSpan="1">
								<textField>
									<reportElement uuid="4536d991-3f47-441e-9796-8ba0cd5a0d71" x="0" y="0" width="90" height="25"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{weightStr}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="105" uuid="3151758a-46ff-4d90-bde6-da699ca0844a">
							<jr:tableHeader height="30" rowSpan="1">
								<staticText>
									<reportElement uuid="f11d7709-3c1a-4079-9943-8f7a16b8734e" x="0" y="0" width="105" height="30"/>
									<box>
										<topPen lineWidth="1.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="1.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[SMALL QTY]]></text>
								</staticText>
							</jr:tableHeader>
							<jr:detailCell height="25" rowSpan="1">
								<staticText>
									<reportElement uuid="82bcb8de-4024-4d51-abe9-a10974440f33" x="0" y="0" width="105" height="25"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="1.0"/>
									</box>
									<textElement>
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[]]></text>
								</staticText>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="77749937-42dc-4581-8b5a-5da1cb42e584" x="168" y="23" width="387" height="20"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="宋体" size="14" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  "+$F{model}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="33"/>
	</pageFooter>
</jasperReport>
