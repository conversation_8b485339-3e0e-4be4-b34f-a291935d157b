<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report1" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="47bd7318-3552-4d9b-a4b5-70f11b3866ba">
	<property name="ireport.zoom" value="1.1000000000000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="14d1bf7c-8998-42be-9c45-1cd91c59806c">
		<field name="palletNo" class="java.lang.String"/>
		<field name="batchNoStr" class="java.lang.String"/>
		<field name="weightStr" class="java.lang.String"/>
		<field name="coil" class="java.lang.String"/>
		<field name="minStockInTime" class="java.lang.String"/>
		<field name="place" class="java.lang.String"/>
		<field name="mark" class="java.lang.String"/>
		<field name="fewNum" class="java.lang.String"/>
	</subDataset>
	<parameter name="productCode" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="totalWeight" class="java.lang.String"/>
	<parameter name="customerName" class="java.lang.String"/>
	<parameter name="palletNum" class="java.lang.String"/>
	<parameter name="printDate" class="java.lang.String"/>
	<parameter name="qiCheKeHu" class="java.lang.String"/>
	<field name="stockList" class="java.util.List"/>
	<pageHeader>
		<band height="89">
			<staticText>
				<reportElement uuid="bd66da3a-2bfd-446e-ae37-e37cc659d0f9" x="23" y="31" width="74" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[客户名称：]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="83d215e7-037d-4c00-b1b7-7ac3532f2f46" x="190" y="0" width="190" height="31"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="20" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[库存查询]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="919f2c42-4ffb-4316-b18a-7517b37c184e" x="87" y="31" width="335" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="16" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{customerName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="1a14bf6b-ce5d-4a95-8147-cf08f596c733" x="23" y="51" width="74" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[产品代码：]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="15a42413-7815-4069-922d-033e74c6863e" x="87" y="51" width="274" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="16" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{productCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="36d880fc-b824-4270-8008-d04f5a131c76" x="87" y="69" width="172" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="16" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{totalWeight}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="b96828ea-4410-40d5-aa5d-3a19067269d8" x="23" y="69" width="74" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[总计重量：]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="42eff72b-acf4-4f62-a0fd-8decbc5954a0" x="425" y="69" width="130" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{palletNum}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="f4309991-fa8d-4e75-bf9b-459c1d4a1715" x="361" y="69" width="32" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[木托数：]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="80936695-ad06-46f5-a023-a960bdd6c08d" x="425" y="31" width="130" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="16" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{qiCheKeHu}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="105" splitType="Stretch">
			<componentElement>
				<reportElement uuid="23c0a6e6-b896-416a-944f-e34aa444284a" key="table 2" stretchType="RelativeToBandHeight" x="0" y="10" width="555" height="56"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="Table Dataset 1" uuid="7292598a-8301-45b8-9a3e-2e23277f9e6c">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{stockList})]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="14" uuid="6cc0ced8-b4d9-4f20-8429-7993ea6c2512">
						<jr:columnHeader height="17" rowSpan="1"/>
						<jr:detailCell height="20" rowSpan="1">
							<rectangle>
								<reportElement uuid="ef7fee74-68cf-425f-b324-8da2f18c5d7f" stretchType="RelativeToTallestObject" x="2" y="6" width="9" height="10"/>
							</rectangle>
						</jr:detailCell>
					</jr:column>
					<jr:column width="70" uuid="a2fcabfd-f423-4839-ad49-91076fbbb607">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="9c04ebf7-6d8d-48a7-b3af-f9437b868bc3" stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[木托号]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="2616f072-a87a-4b19-8a07-bf15c6162296" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{palletNo}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="119" uuid="4a1c02fd-1d45-40fd-8b44-412d9ec669a2">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="f18cf006-02b4-48d1-9578-791c04aecded" stretchType="RelativeToBandHeight" x="0" y="0" width="119" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[Lot]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="ec9dcd44-3591-444c-941b-eee85fd05b4f" stretchType="RelativeToTallestObject" x="0" y="0" width="119" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{batchNoStr}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="70" uuid="21413d65-39c0-4e62-852e-d506778be51f">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="2a63eea5-618d-4517-a261-caaae7681fcb" stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[重量]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="3427646b-f148-487f-a702-b85ebcb312b4" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{weightStr}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:columnGroup width="40" uuid="5a5a7f29-fd46-4ad6-a675-8f30cae9449e">
						<jr:column width="40" uuid="bbce0b5b-d49b-4622-b2fc-2b2811c78711">
							<jr:columnHeader height="17" rowSpan="1">
								<staticText>
									<reportElement uuid="89c9debd-6f98-4218-979f-236a1de4da68" stretchType="RelativeToBandHeight" x="0" y="0" width="40" height="17"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<text><![CDATA[线盘]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
									<reportElement uuid="ba132e96-0608-46f1-b847-2173d6640296" stretchType="RelativeToTallestObject" x="0" y="0" width="40" height="20"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{coil}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
					<jr:column width="32" uuid="6a2f3049-82fe-4abb-a5a2-d8bc1b1d4876">
						<jr:tableHeader height="0" rowSpan="1"/>
						<jr:tableFooter height="0" rowSpan="1"/>
						<jr:columnHeader height="17" rowSpan="1"/>
						<jr:columnFooter height="0" rowSpan="1"/>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="0800ef25-4bca-492e-8e4f-676a1f4a2687" stretchType="RelativeToTallestObject" x="0" y="0" width="32" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{fewNum}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="70" uuid="5a647fe0-234c-44bc-a75a-bd71ec67e573">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="d07c82a8-bc1f-4e5a-a755-b7432e5fe7b2" stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[原因]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="f4f6e6f1-8165-4554-b71e-db063fd0e96a" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{mark}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="70" uuid="37096d62-1216-44a1-a30b-dd3fa123293c">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="d6c65342-fc5c-47ae-941d-a8e58726d56c" stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[入库日期]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="b0ea424c-064a-4693-abea-674a352a02c6" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{minStockInTime}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="70" uuid="75616733-fe28-459a-b79b-ed8775cb29eb">
						<jr:columnHeader height="17" rowSpan="1">
							<staticText>
								<reportElement uuid="889862a8-c210-49b5-83a2-e40822f43e65" stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="17"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[地点]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
								<reportElement uuid="8b7dae40-db0e-4e58-90d4-f73020fbf352" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{place}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<pageFooter>
		<band height="21">
			<textField>
				<reportElement uuid="6913f7d8-9086-4844-886a-04df271ca78a" x="409" y="1" width="100" height="20"/>
				<textElement>
					<font fontName="宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="acd1c687-b888-4b85-8d06-d79d76bed608" x="51" y="1" width="109" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{printDate}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
