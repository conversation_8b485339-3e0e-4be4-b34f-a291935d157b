
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    laydate.render({
        elem: '#time'
        ,type: 'month'
        ,range: '~'
        ,format: 'yyyy-MM'
      });
    
    // 日期：开始日期
//	var timeStart = laydate.render({
//		elem: '#timeStartStr', //指定元素
//		type: 'month',
//		btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
//		max:'${timeEndStr}',
//		done: function(value, date, endDate) {
//			timeEnd.config.min = {
//				year: date.year,
//				month: date.month - 1,
//			}
//		}
//	});
	   // 日期：结束日期
//	var timeEnd = laydate.render({
//		elem: '#timeEndStr', //指定元素
//		type: 'month',
//		btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
//		min:'${timeStartStr}',
//		done: function(value, date, endDate) {
//			if(date.year != undefined){
//				timeStart.config.max = {
//					year: date.year,
//					month: date.month,
//				}
//			}else{
//				timeEnd.config.min='1900-1-1';
//				timeStart.config.max=timeEnd.config.max;
//			}
//		}
//	});
//	if('${timeStartStr}' == '' || '${timeEndStr}' == ''){
//		timeEnd.config.min='1900-1-1';
//		timeStart.config.max=timeEnd.config.max;
//	}

    //执行一个 table 实例
    var url = baselocation+'/yearRevise/repairAndAuxiliaryShCoefficient/list';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '列表'
        ,page: false //关闭分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            ,{field: 'machineType',title: '机械类别',align:'center', width:150} 
            ,{field: 'department',title: '部门',align:'center', width:150} 
            ,{field: 'expenseItem',title: '费用项目',align:'center', width:150}
            ,{field: 'moneyPre',title: '予定分配金额',align:'center', width:150}
            ,{field: 'moneyAct',title: '实际分配金额',align:'center', width:180}
            ,{field: 'machineTimePreAvg',title: '机械时间予定',align:'center', width:200}
            ,{field: 'repairAndAuxiliarySH',title: '补辅材分配额SH系数',align:'center', width:200}
        ]]
    });
    
  //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                	// 不需要分页
                   where: temp
                }, 'data');
                break;
    		case 'toExport':
    			var time = $("#time").val();
    			$("#timeForExcell").val(time);
    			$("#formExcell").submit();
    		break;
        };
    });

});


function search() {
	var time=$("#time").val();
	if(time == null || time.trim() == '' ){
		layer.alert("请选择日期范围！");
		return;
	}else{
	    $("#isSearch").val(1);
	    var temp = $("#formSearch").serializeJsonObject();
	    console.info(temp);
	    //执行重载
	    layui.table.reload('demo', {
	       where: temp
	    }, 'data');
	}
}
// MW、UF一览明细选择
function toPage(orders) {
    if(orders == 1){
        window.location.href = baselocation+'/yearRevise/repairAndAuxiliaryShCoefficient/list/view';
    }else if(orders == 2){
        window.location.href = baselocation+'/yearRevise/repairAndAuxiliaryShCoefficientUF/list/view';
    }
}

//excell导出
function toExport(){
	var time = $("#time").val();
	$("#timeForExcell").val(time);
	$("#formExcell").submit();
}
