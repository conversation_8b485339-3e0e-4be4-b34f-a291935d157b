package com.hongru.service.impl.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.hongru.common.util.DateUtils;
import com.hongru.entity.system.SystemOption;
import com.hongru.mapper.system.SystemOptionMapper;
import com.hongru.service.system.ISystemOptionService;

@Service
public class SystemOptionServiceImpl extends ServiceImpl<SystemOptionMapper, SystemOption> implements ISystemOptionService{
	@Autowired
	private SystemOptionMapper systemOptionMapper;
	/**
	 * 新增系统参数
	 * @param systemOption
	 * @return
	 * <AUTHOR> 
	 * @create 2018年2月23日
	 */
	@Override
	public Integer insertSystemOption(SystemOption systemOption) {
		systemOption.setCreatedTimestamp(DateUtils.getCurrentTime());
		systemOption.setLastModified(DateUtils.getCurrentTime());
		systemOption.setLastModifierId(systemOption.getCreatorId());
		systemOption.setLastModifierName(systemOption.getCreatorName());
		return systemOptionMapper.insert(systemOption);
	}

	/**
	 * 根据 enName新增
	 * @param enName
	 * @return
	 * <AUTHOR> 
	 * @create 2018年2月23日
	 */
	@Override
	public SystemOption getSystemOptionByEnName(String enName) {
		return systemOptionMapper.getSystemOptionByEnName(enName);
	}

	/**
	 * 根据enName更新内容
	 * @param enName
	 * <AUTHOR> 
	 * @create 2018年2月23日
	 */
	@Override
	public void updateSystemOptionByEnName(SystemOption systemOption) {
		systemOption.setLastModified(DateUtils.getCurrentTime());
		systemOptionMapper.updateSystemOptionByEnName(systemOption);
	}

}
