package com.hongru.controller.administrator;

import com.google.code.kaptcha.Constants;
import com.google.code.kaptcha.Producer;
import com.hongru.base.BaseController;
import com.hongru.common.constant.UserReturnCode;
import com.hongru.common.enums.StatusEnum;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.EasyUsernameToken;
import com.hongru.common.util.ServletUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.admin.UserLoginLog;
import com.hongru.service.admin.IUserService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.DisabledAccountException;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.util.Date;

/**
 * 
* 类名称：AdministratorLoginController   
* 类描述：管理员登录表示层控制器      
* 创建人：hongru   
* 创建时间：2017年4月1日 上午1:23:59   
*
 */
@Controller
public class AdministratorLoginController extends BaseController{
	
	@Autowired
	private Producer captchaProducer;
	@Autowired
	private IUserService userService;

	/**
	 * GET 登录
	 * @param model
	 * @return
	 */
	@GetMapping(value = "/login")
	public String getLoginPage(Model model,HttpServletRequest request,String jbNo){
		if(!StringUtil.isStringEmpty(jbNo)){
			jbNo = StringUtil.decryptionText(jbNo);
			if(!StringUtil.isStringEmpty(jbNo)){
				try {
					//通过工号进入
					User user = userService.getUserByJobNumber(jbNo);
					if(user !=null && user.getStatus() != StatusEnum.FREEZE.getStatus()){
						//免密登录
						EasyUsernameToken token = new EasyUsernameToken(user.getLoginName());
						Subject currentUser = SecurityUtils.getSubject();
						currentUser.login(token);
						UserLoginLog userLoginLog = new UserLoginLog(new Date(), ServletUtils.getIpAddr(),
								SingletonLoginUtils.getUserId(), ServletUtils.getUserOperatingSystem(),
								ServletUtils.getUserBrowser());
						userService.updateLogById(SingletonLoginUtils.getUserId(), userLoginLog);
						return redirectTo("/index");
					}
				} catch (Exception e) {
					e.printStackTrace();
					logger.error("异常信息：", e);
				}
			}
		}
		return "/modules/admin/admin_login";
	}
	
	/**
	 * POST 登录
	 * @param loginName
	 * @param loginPassword
	 * @return
	 */
	@PostMapping(value = "/login")
	@ResponseBody
	public Object login(@RequestParam("loginName") String loginName,
			@RequestParam("loginPassword") String loginPassword/*, @RequestParam("registerCode") String registerCode*/) {
		/*if (!SingletonLoginUtils.validate(registerCode)) {
			return new HrResult(UserReturnCode.REGISTER_CODE_ERROR);
		}*/
		
		Subject currentUser = SecurityUtils.getSubject();
//		UsernamePasswordToken token = new UsernamePasswordToken(loginName, loginPassword);
//		token.setRememberMe(false);// 默认不记住密码
		EasyUsernameToken token = new EasyUsernameToken(loginName, loginPassword, false);//带密码登录
		try{
			currentUser.login(token);
			UserLoginLog userLoginLog = new UserLoginLog(new Date(), ServletUtils.getIpAddr(),
					SingletonLoginUtils.getUserId(), ServletUtils.getUserOperatingSystem(),
					ServletUtils.getUserBrowser());
			Integer count = userService.updateLogById(SingletonLoginUtils.getUserId(), userLoginLog);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} catch (UnknownAccountException e) {
			logger.error(UserReturnCode.USER_NOT_EXIST.getMessage(), e);
			return new HrResult(UserReturnCode.USER_NOT_EXIST);
		} catch (DisabledAccountException e) {
			logger.error(UserReturnCode.USER_SUSPEND.getMessage(), e);
			return new HrResult(UserReturnCode.USER_SUSPEND);
		} catch (IncorrectCredentialsException e) {
			logger.error(UserReturnCode.WRONG_PASSWORD.getMessage(), e);
			return new HrResult(UserReturnCode.WRONG_PASSWORD);
		} catch (ExcessiveAttemptsException e) {
			logger.error(UserReturnCode.ACCOUNT_LOCK.getMessage(), e);
			return new HrResult(UserReturnCode.ACCOUNT_LOCK);
		}catch (RuntimeException e) {
			logger.error(CommonReturnCode.UNKNOWN_ERROR.getMessage(), e);
			return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
		}
	}

	/**
	 * GET 退出登录
	 * @return
	 */
	@GetMapping(value = "/logout")
	public String logout() {
		Subject subject = SecurityUtils.getSubject();
		subject.logout();
		return redirectTo("/login");
	}
	
	/**
	 * GET 验证码
	 */
	@RequestMapping(value = "/captcha-image")
	public ModelAndView getKaptchaImage(HttpServletRequest request, HttpServletResponse response) throws Exception {
		// 禁止服务器端缓存
		response.setDateHeader("Expires", 0);
		response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
		response.addHeader("Cache-Control", "post-check=0, pre-check=0");
		// 设置标准 HTTP/1.0 不缓存图片  
		response.setHeader("Pragma", "no-cache");
		response.setContentType("image/jpeg");
		String capText = captchaProducer.createText();
		request.getSession().setAttribute(Constants.KAPTCHA_SESSION_KEY, capText);
		BufferedImage bi = captchaProducer.createImage(capText);
		ServletOutputStream out = response.getOutputStream();
		ImageIO.write(bi, "jpg", out);
		try {
			out.flush();
		} finally {
			out.close();
		}
		return null;
	}
}
