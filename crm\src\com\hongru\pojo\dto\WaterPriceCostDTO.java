package com.hongru.pojo.dto;

import com.hongru.entity.cost.WaterPriceCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class WaterPriceCostDTO {

    private PageInfo pageInfo;

    private List<WaterPriceCost> waterPriceCostList;

    public WaterPriceCostDTO(PageInfo pageInfo, List<WaterPriceCost> waterPriceCostList) {
        super();
        this.pageInfo = pageInfo;
        this.waterPriceCostList = waterPriceCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<WaterPriceCost> getWaterPriceCostList() {
        return waterPriceCostList;
    }

    public void setWaterPriceCostList(List<WaterPriceCost> waterPriceCostList) {
        this.waterPriceCostList = waterPriceCostList;
    }
}
