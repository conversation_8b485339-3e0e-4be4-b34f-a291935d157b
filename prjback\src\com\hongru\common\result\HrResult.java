package com.hongru.common.result;

import com.hongru.base.BaseResult;
import com.hongru.common.exception.ReturnCode;

/**
 * 
* 类名称：HrResult   
* 类描述：HrResult 后台管理系统返回结果类      
* 创建人：hongru   
* 创建时间：2017年4月1日 上午4:31:09   
*
 */
public class HrResult extends BaseResult {

	private static final long serialVersionUID = 1L;

	public HrResult(ReturnCode returnCode) {
		super(returnCode.getCode(), returnCode.getMessage());
	}
	
	public HrResult(ReturnCode returnCode, Object data) {
		super(returnCode.getCode(), returnCode.getMessage(), data);
	}
	
	public HrResult(Integer code, String message) {
		super(code, message);
	}
}
