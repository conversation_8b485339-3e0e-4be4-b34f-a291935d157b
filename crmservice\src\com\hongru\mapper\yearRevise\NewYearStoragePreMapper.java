package com.hongru.mapper.yearRevise;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.yearRevise.NewYearStoragePre;
import com.hongru.support.page.PageInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NewYearStoragePreMapper extends BaseMapper<NewYearStoragePre> {

	void insertNewYearStoragePre(@Param("newYearStoragePre") NewYearStoragePre newYearStoragePre);
	
	NewYearStoragePre selectNewYearStoragePreByYear(@Param("year") String year);
	
	 List<NewYearStoragePre> listNewYearStoragePrePage(@Param("year") String year,  @Param("pageInfo") PageInfo pageInfo);
	
	int listNewYearStoragePreCount(@Param("year") String year);
	
    void updateNewYearStoragePreInfo(@Param("newYearStoragePre") NewYearStoragePre newYearStoragePre);
    
    void deleteNewYearStoragePreByYear(@Param("year") String year);
    
   
}
