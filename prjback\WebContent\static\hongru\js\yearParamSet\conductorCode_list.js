layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
        ,form = layui.form;

    //执行一个 table 实例
    var url = baselocation+'/yearParamSet/conductorCode/listPage';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '导体成本编码列表'
        ,page: true //开启分页
        ,where:{isSearch: 0} // 初始状态不加载数据
        ,toolbar: '#toolbarDemo' //开启工具栏
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'orderNo', title: '',width:50,fixed: 'left',type:"numbers",align:'center'}
            ,{field: 'conductorName',title: '导体名称',align:'center', width:200}
            ,{field: 'costCode',title: '成本编码',align:'center', width:150}
            ,{field: 'creatorName',title: '创建人',align:'center', width:120}
            ,{field: 'createdTime',title: '创建时间',align:'center', width:160}
            ,{field: 'lastModifierName',title: '最后修改人',align:'center', width:120}
            ,{field: 'lastModifiedTime',title: '最后修改时间',align:'center', width:160}
            ,{title: '操作',minWidth:150, align:'center',fixed: 'right', toolbar: '#barDemo',width: 150}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                toAdd();
                break;
            case 'refresh':
                search();
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){
        var data = obj.data;
        if(obj.event === 'edit'){
            toEdit(data.codeId);
        } else if(obj.event === 'remove'){
            layer.confirm('确定删除该导体成本编码吗？', function(index){
                $.ajax({
                    url: baselocation + '/yearParamSet/conductorCode/delete',
                    type: 'POST',
                    data: {codeId: data.codeId},
                    success: function(result) {
                        if(result.code == 1) {
                            layer.msg('删除成功', {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(result.message, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('系统异常', {icon: 2});
                    }
                });
                layer.close(index);
            });
        }
    });

    // 搜索
    function search() {
        var temp = $("#formSearch").serializeJsonObject();
        temp.isSearch = 1; // 添加检索标识
        layui.table.reload('demo', {
            page: {
                curr: 1
            }
            ,where: temp
        }, 'data');
    }

    // 新增
    function toAdd(){
        var url = baselocation+'/yearParamSet/conductorCode/add/view';
        layer.open({
            type: 2,
            title: '新增导体成本编码',
            shadeClose: true,
            shade: 0.8,
            area: ['600px', '400px'],
            content: url,
            end: function(){
                search();
            }
        });
    }

    // 编辑
    function toEdit(codeId){
        var url = baselocation+'/yearParamSet/conductorCode/edit/view?codeId='+codeId;
        layer.open({
            type: 2,
            title: '编辑导体成本编码',
            shadeClose: true,
            shade: 0.8,
            area: ['600px', '400px'],
            content: url,
            end: function(){
                search();
            }
        });
    }

    // 将search函数暴露到全局
    window.search = search;
});
