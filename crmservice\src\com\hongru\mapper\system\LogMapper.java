package com.hongru.mapper.system;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.system.Log;
import com.hongru.support.page.PageInfo;

/**
 * 
* 类名称：LogMapper   
* 类描述：LogMapper / 日志记录表 数据访问层接口      
* 创建人：hongru   
* 创建时间：2017-10-25 上午10:25:26   
*
 */
public interface LogMapper extends BaseMapper<Log> {
	
	/**
	 * 根据分页信息/搜索内容查找日志记录列表
	 * @param pageInfo 分页信息
	 * @param search 搜索内容
	 * @return
	 */
	List<Log> listByPage(@Param("pageInfo") PageInfo pageInfo, @Param("search") String search);
	Integer listByPageCount(@Param("search") String search);

	/**
	* 根据参数删除日志
	* @throws
	* <AUTHOR>
	* @create 2022/1/14 16:12
	* @return
	*/
    void deleteLogByParam(@Param("logIds")Integer[] logIds);

	/**
	* 根据参数获取日志列表
	* @throws
	* <AUTHOR>
	* @create 2022/1/14 16:12
	* @return
	*/
	List<Log> listLogByParam(@Param("logIds")Integer[] logIds);
}