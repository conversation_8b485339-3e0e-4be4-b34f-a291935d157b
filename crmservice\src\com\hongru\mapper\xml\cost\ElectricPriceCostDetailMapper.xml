<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.ElectricPriceCostDetailMapper">
    <sql id="electricPriceCostDetail_sql">
		eled.[流水号] AS costId,eled.[年月] AS yearMonth,
		eled.[部门] AS department,eled.[用电量] AS eleCost,eled.[年] AS year,eled.[月] AS month,
		eled.[创建人标识] AS creatorId,eled.[创建人姓名] AS creatorName,eled.[创建时间] AS createdTime,eled.[导入标识] AS importId
	</sql>

	<insert id="insertelectricPriceCostDetail" parameterType="com.hongru.entity.cost.ElectricPriceCostDetail">
		INSERT INTO [CostPrice].[dbo].[电力使用明细表]
		(
		[导入标识],
		[年月],
		[部门],
		[用电量],
		[年],
		[月],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[最后修改人标识],
		[最后修改人姓名],
		[最后修改时间]
		)VALUES(
		#{electricPriceCostDetail.importId},
		#{electricPriceCostDetail.yearMonth},
		#{electricPriceCostDetail.department},
		#{electricPriceCostDetail.eleCost},
		#{electricPriceCostDetail.year},
		#{electricPriceCostDetail.month},
		#{electricPriceCostDetail.creatorId},
		#{electricPriceCostDetail.creatorName},
		#{electricPriceCostDetail.createdTime},
		#{electricPriceCostDetail.lastModifierId},
		#{electricPriceCostDetail.lastModifierName},
		#{electricPriceCostDetail.lastModifiedTime}
		)
	</insert>

	<select id="listCostPricePage" resultType="com.hongru.entity.cost.ElectricPriceCostDetail">
		SELECT
		<include refid="electricPriceCostDetail_sql"/>
		FROM [CostPrice].[dbo].[电力使用明细表] eled
		<where>
			1 = 1
			<if test="yearMonth != null and yearMonth != ''">
				AND eled.[年月] = #{yearMonth}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY eled.[年月] DESC, eled.[部门]
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listCostPricePageCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[电力使用明细表] eled
		<where>
			1 = 1
			<if test="yearMonth != null and yearMonth != ''">
				AND eled.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<delete id="deleteByCostId">
		DELETE FROM [CostPrice].[dbo].[电力使用明细表] WHERE [流水号] = #{costId}
	</delete>

	<select id="listElectricPriceCostDetailByYearMonth" resultType="com.hongru.entity.cost.ElectricPriceCostDetail">
		SELECT
		<include refid="electricPriceCostDetail_sql"/>
		FROM [CostPrice].[dbo].[电力使用明细表] eled
		<where>
			1 = 1
			<if test="yearMonth != null and yearMonth != ''">
				AND eled.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<update id="updateState">
		UPDATE [CostPrice].[dbo].[电力使用明细表]
		SET [状态] = #{state}
		WHERE [流水号] = #{costId}
	</update>
	
	<select id="selectElectricActAvgByDepart" resultType="com.hongru.entity.yearRevise.NewYearEleCoeffucientBean">
		SELECT
		 [部门] AS department, AVG([用电量]) AS  electricityActAvg
		FROM [CostPrice].[dbo].[电力使用明细表]
		<where>
		    <if test="departArr != null and departArr != ''">
                AND [部门]  IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>    
			<if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
		</where>
		 GROUP BY [部门]
	</select>
</mapper>