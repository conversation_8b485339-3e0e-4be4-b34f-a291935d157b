package com.hongru.entity.yearRevise;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;

import java.math.BigDecimal;

@TableName("年度予定入库量表")//CostPrice
public class NewYearStoragePre {
	/* 年度 */
	@TableId(value="年度")
	protected String year;
	/* EM年度予定入库量 */
	protected BigDecimal emNewYearStoragePre;
	/* EF年度予定入库量 */
	protected BigDecimal efNewYearStoragePre;
	/* EF09年度予定入库量 */
	protected BigDecimal ef09NewYearStoragePre;
	/* ER年度予定入库量 */
	protected BigDecimal erNewYearStoragePre;
	/* EH年度予定入库量 */
	protected BigDecimal ehNewYearStoragePre;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 最后修改人标识 */
	protected int lastModifierId;
	/* 最后修改人姓名 */
	protected String lastModifierName;
	/* 最后修改时间 */
	protected String lastModifiedTime;
	
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public BigDecimal getEmNewYearStoragePre() {
		return emNewYearStoragePre;
	}
	public void setEmNewYearStoragePre(BigDecimal emNewYearStoragePre) {
		this.emNewYearStoragePre = emNewYearStoragePre;
	}
	public BigDecimal getEfNewYearStoragePre() {
		return efNewYearStoragePre;
	}
	public void setEfNewYearStoragePre(BigDecimal efNewYearStoragePre) {
		this.efNewYearStoragePre = efNewYearStoragePre;
	}
	public BigDecimal getEf09NewYearStoragePre() {
		return ef09NewYearStoragePre;
	}
	public void setEf09NewYearStoragePre(BigDecimal ef09NewYearStoragePre) {
		this.ef09NewYearStoragePre = ef09NewYearStoragePre;
	}
	public BigDecimal getErNewYearStoragePre() {
		return erNewYearStoragePre;
	}
	public void setErNewYearStoragePre(BigDecimal erNewYearStoragePre) {
		this.erNewYearStoragePre = erNewYearStoragePre;
	}
	public BigDecimal getEhNewYearStoragePre() {
		return ehNewYearStoragePre;
	}
	public void setEhNewYearStoragePre(BigDecimal ehNewYearStoragePre) {
		this.ehNewYearStoragePre = ehNewYearStoragePre;
	}
	public int getCreatorId() {
		return creatorId;
	}
	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
	public int getLastModifierId() {
		return lastModifierId;
	}
	public void setLastModifierId(int lastModifierId) {
		this.lastModifierId = lastModifierId;
	}
	public String getLastModifierName() {
		return lastModifierName;
	}
	public void setLastModifierName(String lastModifierName) {
		this.lastModifierName = lastModifierName;
	}
	public String getLastModifiedTime() {
		return lastModifiedTime;
	}
	public void setLastModifiedTime(String lastModifiedTime) {
		this.lastModifiedTime = lastModifiedTime;
	}
}