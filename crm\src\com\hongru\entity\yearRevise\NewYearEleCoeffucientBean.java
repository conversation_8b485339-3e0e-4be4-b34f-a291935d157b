package com.hongru.entity.yearRevise;

import java.math.BigDecimal;

/**
* 新年度予定电量系数查询返回结果实体类
* <AUTHOR>
* @create 2024/01/10 09:55
*/
public class NewYearEleCoeffucientBean {
	/* 部门 */
	private String  department;
	/* 电量实绩平均 */
	private BigDecimal electricityActAvg;
	/* 电量予定平均 */
	private BigDecimal electricityPreAvg;
	/* 氮气予定平均 */
	private BigDecimal nitrogenPreAvg;
	/* 实绩比例 */
	private BigDecimal proportionAct;
	/* 予定比例 */
	private BigDecimal proportionPre;
	/* 间接分摊 */
	private BigDecimal indirectAllocation;
	/* 实绩总电量 */
	private BigDecimal electricityActTotal;
	/* 实绩电量分配 */
	private BigDecimal eleActAllocation;
	/* 实绩氮气分配 */
	private BigDecimal nitrogenActAllocation;
	/* 机械时间予定 */
	private BigDecimal machineTimePreAvg;
	/* 新年度机械稼动UP率 */
	protected BigDecimal newYearMecUpRate;
	/* 新年度予定电量 */
	protected BigDecimal newYearElectricityPre;
	/* 新年度机械时间 */
	protected String newYearMechanicalTime;
	/* 新年度予定电量系数 */
	protected String newYearElectricityPreSh;
	/* 新年度予定SYS系数 */
	protected String newYearPreCoefficient;
	
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public BigDecimal getElectricityActAvg() {
		return electricityActAvg;
	}
	public void setElectricityActAvg(BigDecimal electricityActAvg) {
		this.electricityActAvg = electricityActAvg;
	}
	public BigDecimal getElectricityPreAvg() {
		return electricityPreAvg;
	}
	public BigDecimal getNitrogenPreAvg() {
		return nitrogenPreAvg;
	}
	public void setNitrogenPreAvg(BigDecimal nitrogenPreAvg) {
		this.nitrogenPreAvg = nitrogenPreAvg;
	}
	public void setElectricityPreAvg(BigDecimal electricityPreAvg) {
		this.electricityPreAvg = electricityPreAvg;
	}
	public BigDecimal getProportionAct() {
		return proportionAct;
	}
	public void setProportionAct(BigDecimal proportionAct) {
		this.proportionAct = proportionAct;
	}
	public BigDecimal getProportionPre() {
		return proportionPre;
	}
	public void setProportionPre(BigDecimal proportionPre) {
		this.proportionPre = proportionPre;
	}
	public BigDecimal getIndirectAllocation() {
		return indirectAllocation;
	}
	public void setIndirectAllocation(BigDecimal indirectAllocation) {
		this.indirectAllocation = indirectAllocation;
	}
	public BigDecimal getElectricityActTotal() {
		return electricityActTotal;
	}
	public void setElectricityActTotal(BigDecimal electricityActTotal) {
		this.electricityActTotal = electricityActTotal;
	}
	public BigDecimal getEleActAllocation() {
		return eleActAllocation;
	}
	public void setEleActAllocation(BigDecimal eleActAllocation) {
		this.eleActAllocation = eleActAllocation;
	}
	public BigDecimal getNitrogenActAllocation() {
		return nitrogenActAllocation;
	}
	public void setNitrogenActAllocation(BigDecimal nitrogenActAllocation) {
		this.nitrogenActAllocation = nitrogenActAllocation;
	}
	public BigDecimal getMachineTimePreAvg() {
		return machineTimePreAvg;
	}
	public void setMachineTimePreAvg(BigDecimal machineTimePreAvg) {
		this.machineTimePreAvg = machineTimePreAvg;
	}
	public BigDecimal getNewYearMecUpRate() {
		return newYearMecUpRate;
	}
	public void setNewYearMecUpRate(BigDecimal newYearMecUpRate) {
		this.newYearMecUpRate = newYearMecUpRate;
	}
	public BigDecimal getNewYearElectricityPre() {
		return newYearElectricityPre;
	}
	public void setNewYearElectricityPre(BigDecimal newYearElectricityPre) {
		this.newYearElectricityPre = newYearElectricityPre;
	}
	public String getNewYearMechanicalTime() {
		return newYearMechanicalTime;
	}
	public void setNewYearMechanicalTime(String newYearMechanicalTime) {
		this.newYearMechanicalTime = newYearMechanicalTime;
	}
	public String getNewYearElectricityPreSh() {
		return newYearElectricityPreSh;
	}
	public void setNewYearElectricityPreSh(String newYearElectricityPreSh) {
		this.newYearElectricityPreSh = newYearElectricityPreSh;
	}
	public String getNewYearPreCoefficient() {
		return newYearPreCoefficient;
	}
	public void setNewYearPreCoefficient(String newYearPreCoefficient) {
		this.newYearPreCoefficient = newYearPreCoefficient;
	}
}
