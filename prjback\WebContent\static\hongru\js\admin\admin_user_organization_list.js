layui.config({
	base: baselocationsta+'/common/layui/'
});

layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
	var laydate = layui.laydate
		,layer = layui.layer
		,table = layui.table
		,element = layui.element
	var form = layui.form;

	var url = baselocation+'/administrator/organization/'+$("#organizationId").val()+'/lists';
	table.render({
		elem: '#demo'
		,height: 'full-70'
		,url: url
		,parseData:function(res){
			return {
				"code": 0,
				"msg": '',
				"count": res.total,
				"data": res.rows
			};
		}
		,method:'post'
		,title: '管理员列表'
		,page: true
		,limits: [10,20,50,100]
		,where:$("#formSearch").serializeJsonObject()
		,toolbar: '#toolbarDemo'
		,defaultToolbar: ['filter']
		,totalRow: false
		,cols: [[
			{field: 'userName',width:100,title: '昵称',align:'center',
				templet: function(d){
					return '<a href="javascript:void(0)" onclick="layer_show(\'' + d.userName + '\',\'' + baselocation + '/administrator/list/' + d.userId + '\',\'500\',\'425\')">' + d.userName + '</a>';
				}
			}
			,{field: 'realName',width:120,title: '姓名',align:'center'}
			,{field: 'telephone',width:120,title: '手机',align:'center'}
			,{field: 'email',width:200,title: '邮箱',align:'center'}
			,{field: 'organizationName',width:120,title: '部门',align:'center'}
			,{field: 'createTime',width:180,title: '注册时间',align:'center',
				templet: function(d){
					return new Date(d.createTime).format("yyyy-MM-dd HH:mm:ss");
				}
			}
			,{field: 'lastLoginTime',width:180,title: '最后登录时间',align:'center',
				templet: function(d){
					return new Date(d.lastLoginTime).format("yyyy-MM-dd HH:mm:ss");
				}
			}
			,{field: 'lastLoginIp',width:120,title: '登录IP',align:'center'}
			,{field: 'status',width:80,title: '状态',align:'center',
				templet: function(d){
					if (d.status == 1) {
						return '<span class="label label-primary">正常</span>'
					} else if (d.status == 0) {
						return '<span class="label label-danger">冻结</span>'
					}
				}
			}
		]]
	});

	table.on('toolbar(test)', function(obj){
		var checkStatus = table.checkStatus(obj.config.id)
			,data = checkStatus.data;
		switch(obj.event){
		};
	});

	table.on('tool(test)', function(obj){
		var data = obj.data
			,layEvent = obj.event;
	});

	$("#btn-resert").on("click",function(){
		$('#searchForm input[type="text"]').each(function (i, j) {
			$(j).attr("value", "");
		})

		$('#searchForm select').each(function (i, j) {
			$(j).find("option:selected").attr("selected", false);
			$(j).find("option").first().attr("selected", true);
		})
		$("#cityId").html('<option value="" >全部</option>');
	})

});

function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}