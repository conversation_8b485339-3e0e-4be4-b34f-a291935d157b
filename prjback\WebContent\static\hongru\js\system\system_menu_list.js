layui.config({
    base: baselocationsta+'/common/layui/'
}).use(['treeTable','form'], function () {
    var $ = layui.jquery;
    var treeTable = layui.treeTable;
    $('body').removeClass('layui-hide');

    treeTable.render({
        elem: '#demoTreeTb',
        toolbar: 'default',
		openName: 'open',
		treeLinkage: false,
        tree: {
            iconIndex: 0,
			idName: 'menuId',
			pidName: 'parentId',
			isPidData: true
        },
        defaultToolbar: ['filter'],
        toolbar:'#tool',
        cols: [[
            {field: 'menuName', title: '菜单名称',width:200,
				templet: function(d){
					return '<i class="layui-icon '+d.icon+'"></i>'+d.menuName;
				}},
            {field: 'sort', title: '排序',width:100, align: 'center'},
            {field: 'status', title: '状态',width:100, align: 'center',
                templet: function(d){
                    if (d.status == 1) {
						var str="";
						str += '<form class="layui-form" action="">';
						str += '<input type="checkbox" name="status" lay-skin="switch" value="'+d.menuId+'" lay-text="显示|隐藏" checked lay-filter="statusFun">';
						str += '</form>';
                        return str;
                    } else if (d.status == 0) {
						var str="";
						str += '<form class="layui-form" action="">';
						str += '<input type="checkbox" name="status" lay-skin="switch" value="'+d.menuId+'" lay-text="显示|隐藏" lay-filter="statusFun">';
						str += '</form>';
						return str;
                    }
                }},
            {field: 'href', title: '链接地址',width:200},
            {field: 'permission', title: '权限标识',width:200},
			{title: '操作',minWidth:315,align:'left',fixed:'right',toolbar:'#tbBar'}
        ]],
        reqData: function (data, callback) {
            setTimeout(function () {
                var url = baselocation+'/system/menu/tree/';
                url += (data ? data.menuId : 0);
                $.get(url, function (res) {
                    callback(res.data);
                });
            }, 800);
        },
        style: 'margin-top:0;'
    });

    treeTable.on('tool(demoTreeTb)', function (obj) {
        var event = obj.event;
        var value = obj.data.menuId;
        if (event === 'del') {
        	layer.confirm('确认要删除该菜单吗？', {
        		btn : [ '确定', '取消' ]
        	}, function() {
        		$.ajax({
        			type : 'delete',
        			dataType : 'json',
        			url : baselocation + '/system/menu/' + value,
        			success : function(result) {
        				if (result.code == 1) {
        					layer.msg('该菜单删除成功!', {
        						icon : 1,
        						time : 1000
        					}, function() {
        						window.location.reload();
        					});
        				} else {
        					layer.alert(result.message, {
        						icon : 2
        					});
        				}
        			}
        		})
        	});
        } else if (event === 'edit') {
        	layer_show('编辑菜单',baselocation+'/system/menu/' + value + '/edit', document.body.clientWidth-10, document.body.clientHeight-10);
        }else if (event === 'add') {
        	layer_show('创建菜单',baselocation+'/system/menu/' + value + '/create',document.body.clientWidth-10, document.body.clientHeight-10);
        }else if (event === 'stop') {
        	layer.confirm('确认要隐藏该菜单吗？', {
        		btn : [ '确定', '取消' ]
        	}, function() {
        		$.ajax({
        			dataType : 'json',
        			type : 'put',
        			url : baselocation + '/system/menu/' + value + '/audit',
        			success : function(result) {
        				if (result.code == 1) {
        					layer.msg('该菜单隐藏成功!', {
        						icon : 5,
        						time : 1000
        					}, function() {
        						window.location.reload();
        					});
        				} else {
        					layer.alert(result.message, {
        						icon : 2
        					});
        				}
        			}
        		})
        	});
        }else if (event === 'start') {
        	layer.confirm('确认要显示该菜单吗？', {
        		btn : [ '确定', '取消' ]
        	}, function() {
        		$.ajax({
        			dataType : 'json',
        			type : 'put',
        			url : baselocation + '/system/menu/' + value + '/audit',
        			success : function(result) {
        				if (result.code == 1) {
        					layer.msg('该菜单显示成功!', {
        						icon : 6,
        						time : 1000
        					}, function() {
        						window.location.reload();
        					});
        				} else {
        					layer.alert(result.message, {
        						icon : 2
        					});
        				}
        			}
        		})
        	});
        }
    });

    treeTable.on('toolbar(demoTreeTb)', function (obj) {
        switch (obj.event) {
            case 'toAdd':
            	layer_show('创建菜单',baselocation+'/system/menu/1/create',document.body.clientWidth-10, document.body.clientHeight-10);
                break;
            case 'fresh':
            	window.location.reload();
                break;
        }
    });

});

layui.use(['form'], function(){
	var form = layui.form
		,layer = layui.layer
	form.on('switch(statusFun)', function(data){
		$.ajax({
			dataType : 'json',
			type : 'put',
			url : baselocation + '/system/menu/' + data.value + '/audit',
			success : function(result) {
				if (result.code == 1) {
					layer.msg('该菜单显示成功!', {
						icon : 6,
						time : 1000
					}, function() {
						window.location.reload();
					});
				} else {
					layer.alert(result.message, {
						icon : 2
					});
				}
			}
		})
	});
});