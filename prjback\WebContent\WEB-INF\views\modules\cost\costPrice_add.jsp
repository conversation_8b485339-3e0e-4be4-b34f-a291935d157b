<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>铜加工费用添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value=""/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>区分:</label>
                        <div class="layui-input-block">
                            <input type="radio" name="lineType" value="1" title="圆线" lay-filter="lineTypeCheck"/>
                            <input type="radio" name="lineType" value="2" title="平角线" lay-filter="lineTypeCheck"/>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>供应商:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="supplierId" name="supplierId" lay-verify="required" required>
                                <option value="">请选择</option>
                                <c:forEach items="${supplierList}" var="supplier">
                                    <option value="${supplier.supplierId }" >${supplier.supplierCode } - ${supplier.supplierName }</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>购入量(T):</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="purchasNum" name="purchasNum" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>加工费:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="procePrice" name="procePrice" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>铜单价:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="cuUnitPrice" value="0" name="cuUnitPrice" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">升水:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ascendingWater" name="ascendingWater" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">汇率:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="exchangeRate" name="exchangeRate" value="1" onKeyUp="amount3(this)" onBlur="overFormat3(this)"/>
                        </div>
                    </div>

                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/costPrice_addOrModify.js?time=4"></script>
</myfooter>
</body>
</html>
