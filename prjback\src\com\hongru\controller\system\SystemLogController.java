package com.hongru.controller.system;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.hongru.base.BaseController;
import com.hongru.base.BasePageDTO;
import com.hongru.common.poiExcelExport.PoiExcelExport;
import com.hongru.common.poiExcelExport.ServletUtil;
import com.hongru.common.poiExcelExport.bean.LogBean;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.system.Log;
import com.hongru.service.admin.IUserService;
import com.hongru.service.system.ILogService;
import com.hongru.support.page.PageInfo;

/**
 * 
* 类名称：SystemLogController   
* 类描述：日记记录表示层控制器         
* 创建人：hongru   
* 创建时间：2017-10-25 下午12:50:26   
*
 */
@Controller
@RequestMapping(value = "/system/log")
public class SystemLogController extends BaseController {
	
	
	@Autowired
	private ILogService logService;
	@Autowired
	private IUserService userService;
	/**
	 * GET 日记记录
	 * @return
	 */
	@RequiresPermissions("system:log:view")
	@GetMapping(value = "/view")
	public String list(Model model) {
		return "/modules/system/system_log_list";
	}

	/**
	 * GET 日记记录
	 * @return
	 */
	@RequiresPermissions("system:log:view")
	@PostMapping(value = "/")
	@ResponseBody
	public Object listUser(Integer page, Integer limit,String searchStr) {
		PageInfo pageInfo = new PageInfo(limit, page);
		BasePageDTO<Log> basePageDTO = logService.listByPage(pageInfo, searchStr);
		for(Log log : basePageDTO.getList()){
			User user = userService.getById(log.getUserId());
			if(user != null){
				log.setUserName(user.getUserName());
			}
		}
		return new HrPageResult(basePageDTO.getList(), basePageDTO.getPageInfo().getTotal());
	}

	/**
	* 批量导出日志
	* @throws
	* <AUTHOR>
	* @create 2022/1/14 16:35
	* @return
	*/
	@RequestMapping(value = "/export/forBatch")
	public void exportProjectList(String logIds, HttpServletRequest req, HttpServletResponse resp) throws Exception {
		List<Log> logs = new ArrayList<>();
		if(!StringUtil.isStringEmpty(logIds)) {
			String[] logIdStrArr = logIds.split(",");
			Integer[] logIdArr = new Integer[logIdStrArr.length];
			for (int i = 0; i < logIdStrArr.length; i++) {
				logIdArr[i] = Integer.parseInt(logIdStrArr[i]);
			}
			logs = logService.listLogByParam(logIdArr);
		}else{
			logs = logService.listLogByParam(null);
		}
		if(logs !=null && logs.size() >0) {
			List<LogBean> beanList = new ArrayList<LogBean>();
			for (Log log : logs) {
				LogBean bean = new LogBean();
				bean.setLogId(log.getLogId().intValue());
				User user = userService.getById(log.getUserId());
				if(user != null){
					bean.setUserName(log.getUserName());
				}else{
					bean.setUserName("");
				}
				bean.setCreateTimeStr(log.getCreateTime() + "");
				bean.setSpendTimeStr(log.getSpendTime() + "");
				bean.setUrl(log.getUrl());
				bean.setMethod(log.getMethod());
				bean.setUserAgent(log.getUserAgent());
				bean.setUserIp(log.getUserIp());
				beanList.add(bean);
			}
			String[] heads = {"编号","操作用户","操作时间","耗时","请求路径","请求类型","用户标识","IP地址"};
			String[] cols = {"logId","userName","createTimeStr","spendTimeStr","url","method","userAgent","userIp"};
			int[] numerics = {0};
			String fileName = "日志表_"+ DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),  "yyyyMMddHHmmss");
			ServletUtil su = new ServletUtil(fileName, req, resp);
			su.poiExcelServlet();
			PoiExcelExport<LogBean> pee = new PoiExcelExport<LogBean>(fileName, heads, cols, beanList, numerics, su.getOut());
			pee.exportExcel();
		}
	}

	/**
	* 批量删除日志
	* @throws
	* <AUTHOR>
	* @create 2022/1/14 16:14
	* @return
	*/
	@PostMapping(value = "/delete/forBatch")
	@ResponseBody
	public Object deleteLogForBatch(String logIds) throws Exception {
		if(!StringUtil.isStringEmpty(logIds)){
			String[] logIdStrArr = logIds.split(",");
			Integer[] logIdArr = new Integer[logIdStrArr.length];
			for(int i=0;i<logIdStrArr.length;i++){
				logIdArr[i] = Integer.parseInt(logIdStrArr[i]);
			}
			logService.removeLogByParam(logIdArr);
		}else{
			logService.removeLogByParam(null);
		}
		return new HrResult(CommonReturnCode.SUCCESS);
	}
}
