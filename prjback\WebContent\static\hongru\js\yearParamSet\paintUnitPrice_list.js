layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    //执行一个 table 实例
    var url = baselocation+'/yearParamSet/paintUnitPrice/listPage';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '油漆单价'
        ,page: true //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'orderNo', title: '',width:50,fixed: 'left',type:"numbers",align:'center'}
            ,{field: 'year',title: '年度',align:'center', width:100}
            ,{field: 'itemCode',title: '品目',align:'center', width:120}
            ,{field: 'itemName',title: '品目名',align:'center', width:200}
            ,{field: 'creatorName',title: '创建人姓名',align:'center', width:120}
            ,{field: 'createdTime',title: '创建时间',align:'center', width:180, templet: function(d){
                if(d.createdTime) {
                    return layui.util.toDateString(new Date(d.createdTime), 'yyyy-MM-dd HH:mm:ss');
                }
                return '';
            }}
            ,{field: 'updaterName',title: '更新人姓名',align:'center', width:120}
            ,{field: 'updatedTime',title: '更新时间',align:'center', width:180, templet: function(d){
                if(d.updatedTime) {
                    return layui.util.toDateString(new Date(d.updatedTime), 'yyyy-MM-dd HH:mm:ss');
                }
                return '';
            }}
            ,{title: '操作', width:180, align:'center', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(demo)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        switch(obj.event){
            case 'toAdd':
                toAdd();
                break;
            case 'refresh':
                search();
                break;
        };
    });

    //监听行工具事件
    table.on('tool(demo)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            toDetail(data.serialNumber);
        } else if(obj.event === 'edit'){
            toEdit(data.serialNumber);
        }
    });

    // 新增
    function toAdd(){
        var url = baselocation+'/yearParamSet/paintUnitPrice/add/view';
        layer.open({
            type: 2,
            title: '新增油漆单价',
            shadeClose: true,
            shade: 0.8,
            area: ['900px', '700px'],
            content: url,
            end: function(){
                search();
            }
        });
    }

    // 详情
    function toDetail(serialNumber){
        var url = baselocation+'/yearParamSet/paintUnitPrice/detail/view?serialNumber='+serialNumber;
        layer.open({
            type: 2,
            title: '油漆单价详情',
            shadeClose: true,
            shade: 0.8,
            area: ['900px', '700px'],
            content: url
        });
    }

    // 编辑
    function toEdit(serialNumber){
        var url = baselocation+'/yearParamSet/paintUnitPrice/edit/view?serialNumber='+serialNumber;
        layer.open({
            type: 2,
            title: '编辑油漆单价',
            shadeClose: true,
            shade: 0.8,
            area: ['900px', '700px'],
            content: url,
            end: function(){
                search();
            }
        });
    }

    // 查询
    function search(){
        $("#isSearch").val("1");
        table.reload('demo', {
            where: $("#formSearch").serializeJsonObject()
        });
    }

    // 全局函数
    window.search = search;
});

// 序列化表单为JSON对象
$.fn.serializeJsonObject = function() {
    var o = {};
    var a = this.serializeArray();
    $.each(a, function() {
        if (o[this.name] !== undefined) {
            if (!o[this.name].push) {
                o[this.name] = [o[this.name]];
            }
            o[this.name].push(this.value || '');
        } else {
            o[this.name] = this.value || '';
        }
    });
    return o;
};
