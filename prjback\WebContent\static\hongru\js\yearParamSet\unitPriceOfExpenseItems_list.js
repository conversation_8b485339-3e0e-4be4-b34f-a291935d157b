layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    //执行一个 table 实例
    var url = baselocation+'/yearParamSet/unitPriceOfExpenseItems/listPage';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '费用项目单价表'
        ,page: true //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'orderNo', title: '',width:50,fixed: 'left',type:"numbers",align:'center'}
            ,{field: 'year',title: '年度',align:'center', width:100}
            ,{field: 'expenseItemName',title: '费用项目名称',align:'center', width:150}
            ,{field: 'expenseItemUnitPrice',title: '费用项目单价',align:'center', width:120}
            ,{field: 'ratio',title: '比例',align:'center', width:120}
            ,{field: 'creatorName',title: '创建人姓名',align:'center', width:120}
            ,{field: 'createdTime',title: '创建时间',align:'center', width:180}
            ,{field: 'updaterName',title: '更新人姓名',align:'center', width:120}
            ,{field: 'updatedTime',title: '更新时间',align:'center', width:180}
            ,{title: '操作', width:120, align:'center', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(demo)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                add();
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
        };
    });

    //监听行工具事件
    table.on('tool(demo)', function(obj){
        var data = obj.data;
        if(obj.event === 'edit'){
            edit(data.serialNumber);
        }
    });

});

function search() {
    $("#isSearch").val(1);
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    //执行重载
    layui.table.reload('demo', {
       where: temp
    }, 'data');
}

function reset() {
    $("#year").val('');
    $("#isSearch").val(0);
    //执行重载，显示空数据
    layui.table.reload('demo', {
       where: {isSearch: 0}
    }, 'data');
}

function add() {
    layer.open({
        type: 2,
        title: '新增费用项目单价',
        shadeClose: true,
        shade: 0.8,
        area: ['800px', '400px'],
        content: baselocation + '/yearParamSet/unitPriceOfExpenseItems/add/view',
        end: function(){
            search();
        }
    });
}

function edit(serialNumber) {
    layer.open({
        type: 2,
        title: '编辑费用项目单价',
        shadeClose: true,
        shade: 0.8,
        area: ['800px', '400px'],
        content: baselocation + '/yearParamSet/unitPriceOfExpenseItems/edit/view?serialNumber=' + serialNumber,
        end: function(){
            search();
        }
    });
}
