<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.admin.UserRoleMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.hongru.entity.admin.UserRole">
		<id column="user_role_id" property="userRoleId" />
		<result column="role_id" property="roleId" />
		<result column="user_id" property="userId" />
		<result column="create_time" property="createTime" />
		<result column="create_by" property="createBy" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_role_id AS userRoleId, role_id AS roleId, user_id AS userId, create_time AS createTime, create_by AS createBy
    </sql>
    
    <!-- 插入用户角色记录 -->
    <insert id="insertUserRoles" parameterType="java.util.List">
    	InSERT INTO hr_admin_user_role(
   			role_id, 
    		user_id, 
    		create_time, 
    		create_by
    	)VALUES
    	<foreach collection="userRoles" item="userRole" separator=",">
    	(
	     	#{userRole.roleId},
	     	#{userRole.userId},
	        #{userRole.createTime}, 
	        #{userRole.createBy}    		
    	)
    	</foreach>
    </insert>   
    
    <!-- 根据管理员ID查找角色ID列表 -->
    <select id="listByUserId" resultType="com.hongru.entity.admin.Role">
    	SELECT
	        r.role_id AS roleId, 
	        role_name AS roleName, 
	        role_sign AS roleSign, 
	        is_system AS isSystem
    	FROM
    		hr_admin_user_role u
    	LEFT JOIN hr_admin_role r ON u.role_id = r.role_id
    	<where>
    		1=1
    		<if test="userId != null">
    			AND user_id = #{userId}
    		</if>
    		<if test="status != null">
    			AND status = #{status}
    		</if>
    	</where>
    </select>
    
    <!-- 根据角色ID查找管理员列表 -->
	<sql id="listByRoleId_where">
		<if test="roleId != null">
			AND r.role_id = #{roleId}
		</if>
		<if test="search != null">
			AND (
			u.user_name LIKE  #{search} 
			OR u.real_name LIKE  #{search} 
			OR u.telephone LIKE  #{search} 
			OR u.email LIKE  #{search} 
			OR o.organization_name LIKE  #{search} 
			)
		</if>
	</sql>
    <select id="listByRoleId" resultType="com.hongru.pojo.vo.UserVO">
     	SELECT
	    	u.user_id AS userId, 
	    	u.organization_id AS organizationId, 
	    	o.organization_name AS organizationName,
			u.login_name AS loginName,
			u.user_name AS userName,
			u.real_name AS realName,
			u.sex,
			u.age,
			u.pic_img AS picImg,
	    	u.status,
			u.email,
			u.telephone,
			u.last_login_time AS lastLoginTime,
			u.last_login_ip AS lastLoginIp,
			u.create_time AS createTime,
			u.create_by AS createBy,
			u.update_time AS updateTime,
			u.update_by AS updateBy
	    FROM
	    	hr_admin_user_role r
	    LEFT JOIN hr_admin_user u ON r.user_id = u.user_id
	    LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
		<where>
			<include refid="listByRoleId_where"></include>
		</where>
		ORDER BY u.user_id DESC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
    </select>
	<select id="listByRoleIdCount" resultType="integer">
		SELECT
			COUNT(1)
		FROM
			hr_admin_user_role r
			LEFT JOIN hr_admin_user u ON r.user_id = u.user_id
			LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
		<where>
			<include refid="listByRoleId_where"></include>
		</where>
	</select>
</mapper>
