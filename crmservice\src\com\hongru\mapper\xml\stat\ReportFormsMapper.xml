<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.stat.ReportFormsMapper">

    <sql id="reportForms_Sql">
		r.[流水号] AS reportFormsId,r.[状态] AS state,r.[报表类型] AS reportType,r.[标题] AS title,r.[年] AS year
		,r.[年月] AS yearAndMonth,r.[文件路径] AS filePath,r.[创建人标识] AS creatorId,r.[创建人姓名] AS creatorName,r.[创建时间] AS createdTime
	</sql>

    <sql id="reportForms_where">
        <if test="reportType != null">
            AND r.[报表类型] = #{reportType}
        </if>
        <if test="title != null and title != ''">
            AND r.[标题] = #{title}
        </if>
        <if test="year != null and year != ''">
            AND r.[年] = #{year}
        </if>
        <if test="yearAndMonth != null and yearAndMonth != ''">
            AND r.[年月] = #{yearAndMonth}
        </if>
    </sql>

    <insert id="insertReportForms" parameterType="com.hongru.entity.stat.ReportForms" useGeneratedKeys="true" keyProperty="reportFormsId">
        INSERT INTO [CostPrice].[dbo].[报表]
		(
		[状态],
		[报表类型],
		[标题],
		[年],
		[年月],
		[文件路径],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{reportForms.state},
		#{reportForms.reportType},
		#{reportForms.title},
		#{reportForms.year},
		#{reportForms.yearAndMonth},
		#{reportForms.filePath},
		#{reportForms.creatorId},
		#{reportForms.creatorName},
		#{reportForms.createdTime}
		)
    </insert>

    <select id="listReportFormsByPage" resultType="com.hongru.entity.stat.ReportForms">
        SELECT
        <include refid="reportForms_Sql"/>
        FROM [CostPrice].[dbo].[报表] r
        <where>
            <include refid="reportForms_where"/>
        </where>
        <if test="pageInfo.sort != null">
            ORDER BY ${pageInfo.sort} ${pageInfo.order}
        </if>
        <if test="pageInfo.sort == null">
            ORDER BY r.[流水号] DESC
        </if>
        <if test="pageInfo.limit != null">
            OFFSET ${pageInfo.offset} ROWS
            FETCH NEXT ${pageInfo.limit} ROWS ONLY
        </if>
    </select>
    <select id="listReportFormsByPageCount" resultType="integer">
        SELECT
        COUNT(1)
        FROM [CostPrice].[dbo].[报表] r
        <where>
            <include refid="reportForms_where"/>
        </where>
    </select>

	<delete id="removeReportForms">
		DELETE FROM [CostPrice].[dbo].[报表] WHERE [流水号] = #{reportFormsId}
	</delete>
</mapper>