package com.hongru.pojo.dto;

import com.hongru.entity.cost.PaintPrice;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class PaintPriceDTO {

    private PageInfo pageInfo;

    private List<PaintPrice> paintPriceList;

    public PaintPriceDTO(PageInfo pageInfo, List<PaintPrice> paintPriceList) {
        super();
        this.pageInfo = pageInfo;
        this.paintPriceList = paintPriceList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<PaintPrice> getPaintPriceList() {
        return paintPriceList;
    }

    public void setPaintPriceList(List<PaintPrice> paintPriceList) {
        this.paintPriceList = paintPriceList;
    }
}
