<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.ExeclImportMapper">

    <insert id="insertExcelImpot" parameterType="com.hongru.entity.cost.ExcelImport" useGeneratedKeys="true" keyProperty="importId" >
        INSERT INTO [CostPrice].[dbo].[文件导入表]
        (
		[年],
		[月],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[年月]
		)VALUES(
		#{excelImport.year},
		#{excelImport.month},
		#{excelImport.creatorId},
		#{excelImport.creatorName},
		#{excelImport.createdTime},
		#{excelImport.yearMonth}
		)
    </insert>

</mapper>