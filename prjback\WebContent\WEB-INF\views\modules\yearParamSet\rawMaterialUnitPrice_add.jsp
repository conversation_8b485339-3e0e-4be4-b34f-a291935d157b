<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>新增${materialTypeName}单价</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="formAdd" class="layui-form" method="post" action="">
            <!-- 基础信息 -->
            <fieldset class="layui-elem-field layui-field-title">
                <legend>基础信息</legend>
            </fieldset>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年度<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="rawMaterialItem.year" id="year" lay-verify="required" placeholder="请选择年度" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">品目<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="rawMaterialItem.itemCode" id="itemCode" lay-verify="required" placeholder="请输入品目" autocomplete="off" class="layui-input" maxlength="3">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">品目名<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="rawMaterialItem.itemName" id="itemName" lay-verify="required" placeholder="请输入品目名" autocomplete="off" class="layui-input" maxlength="12">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">原料类型:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${materialTypeName}" readonly class="layui-input layui-disabled">
                        <input type="hidden" name="rawMaterialItem.materialType" id="materialType" value="${materialType}">
                    </div>
                </div>
            </div>

            <!-- 导体明细信息 -->
            <div id="conductorDetail" style="display: none;">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>导体单价明细</legend>
                </fieldset>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关税率<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="conductorUnitPriceDetail.tariffRate" placeholder="请输入关税率" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">采购单价<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="conductorUnitPriceDetail.purchaseUnitPrice" placeholder="请输入采购单价" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">溢价<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="conductorUnitPriceDetail.premium" placeholder="请输入溢价" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">附随费用<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="conductorUnitPriceDetail.incidentalExpenses" placeholder="请输入附随费用" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">屑铜原料单价<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="conductorUnitPriceDetail.scrapCopperUnitPrice" placeholder="请输入屑铜原料单价" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">比重<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="conductorUnitPriceDetail.specificGravity" placeholder="请输入比重" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 油漆明细信息 -->
            <div id="paintDetail" style="display: none;">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>油漆单价明细</legend>
                </fieldset>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">关税率<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="paintUnitPriceDetail.tariffRate" placeholder="请输入关税率" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">采购单价<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="paintUnitPriceDetail.purchaseUnitPrice" placeholder="请输入采购单价" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">油漆主体<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="paintUnitPriceDetail.paintBody" placeholder="请输入油漆主体" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">油漆单位长重量<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="paintUnitPriceDetail.paintUnitLengthWeight" placeholder="请输入油漆单位长重量" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">油漆需求率<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="paintUnitPriceDetail.paintDemandRate" placeholder="请输入油漆需求率" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">备注:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="paintUnitPriceDetail.remark" placeholder="请输入备注" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 线盘明细信息 -->
            <div id="wireDiscDetail" style="display: none;">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>线盘单价明细</legend>
                </fieldset>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">原料单价<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="wireDiscUnitPriceDetail.rawMaterialUnitPrice" placeholder="请输入原料单价" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">每卷标准重量<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="wireDiscUnitPriceDetail.standardWeightPerRoll" placeholder="请输入每卷标准重量" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">捆包费<span style="color: red">*</span>:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="wireDiscUnitPriceDetail.bundlingFee" placeholder="请输入捆包费" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formAdd">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="cancel();">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'layer', 'laydate'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    // 页面加载时根据预设的原料类型显示对应的明细区域
    var materialType = $('#materialType').val();
    if(materialType) {
        showDetailByMaterialType(materialType);
    }

    // 显示对应的明细区域
    function showDetailByMaterialType(materialType) {
        // 隐藏所有明细区域
        $('#conductorDetail').hide();
        $('#paintDetail').hide();
        $('#wireDiscDetail').hide();

        // 根据选择显示对应的明细区域
        if(materialType === '01') {
            $('#conductorDetail').show();
        } else if(materialType === '02') {
            $('#paintDetail').show();
        } else if(materialType === '03') {
            $('#wireDiscDetail').show();
        }
    }

    //监听提交
    form.on('submit(formAdd)', function(data){
        // 获取原料类型
        var materialType = data.field['rawMaterialItem.materialType'];

        // 根据原料区分验证对应的明细字段
        if(materialType === '01') {
            // 导体验证
            if(!data.field['conductorUnitPriceDetail.tariffRate'] ||
               !data.field['conductorUnitPriceDetail.purchaseUnitPrice'] ||
               !data.field['conductorUnitPriceDetail.premium'] ||
               !data.field['conductorUnitPriceDetail.incidentalExpenses'] ||
               !data.field['conductorUnitPriceDetail.scrapCopperUnitPrice'] ||
               !data.field['conductorUnitPriceDetail.specificGravity']) {
                layer.msg('请填写完整的导体单价明细信息', {icon: 2});
                return false;
            }
        } else if(materialType === '02') {
            // 油漆验证
            if(!data.field['paintUnitPriceDetail.tariffRate'] ||
               !data.field['paintUnitPriceDetail.purchaseUnitPrice'] ||
               !data.field['paintUnitPriceDetail.paintBody'] ||
               !data.field['paintUnitPriceDetail.paintUnitLengthWeight'] ||
               !data.field['paintUnitPriceDetail.paintDemandRate']) {
                layer.msg('请填写完整的油漆单价明细信息', {icon: 2});
                return false;
            }
        } else if(materialType === '03') {
            // 线盘验证
            if(!data.field['wireDiscUnitPriceDetail.rawMaterialUnitPrice'] ||
               !data.field['wireDiscUnitPriceDetail.standardWeightPerRoll'] ||
               !data.field['wireDiscUnitPriceDetail.bundlingFee']) {
                layer.msg('请填写完整的线盘单价明细信息', {icon: 2});
                return false;
            }
        }

        $.ajax({
            url: baselocation + '/costParameters/unitPriceOfRawMaterials/add',
            type: 'POST',
            data: data.field,
            success: function(result) {
                if(result.code == 1) {
                    layer.msg('新增成功', {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统异常', {icon: 2});
            }
        });
        return false;
    });
});

// 取消按钮
function cancel() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>
<style>
    .layui-form-label {
        width: 120px;
    }
</style>
</body>
</html>
