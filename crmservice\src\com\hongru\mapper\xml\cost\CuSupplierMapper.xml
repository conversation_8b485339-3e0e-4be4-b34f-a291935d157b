<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.CuSupplierMapper">
    <sql id="cuSupplier_sql">
		cs.[流水号] AS supplierId,cs.[供应商编码] AS supplierCode,cs.[供应商名称] AS supplierName,
		cs.[国内供应商] AS supplierFrom
	</sql>


    <select id="listCuSupplier" resultType="com.hongru.entity.cost.CuSupplier">
        SELECT
            <include refid="cuSupplier_sql"/>
        FROM [CostPrice].[dbo].[铜供应商表] cs
        ORDER BY cs.[供应商编码] ASC,cs.[流水号] DESC
    </select>

    <select id="selectSupplierById" resultType="com.hongru.entity.cost.CuSupplier">
        SELECT
        <include refid="cuSupplier_sql"/>
        FROM [CostPrice].[dbo].[铜供应商表] cs
        WHERE cs.[流水号] = #{supplierId}
        ORDER BY cs.[供应商编码] ASC,cs.[流水号] DESC
    </select>
</mapper>