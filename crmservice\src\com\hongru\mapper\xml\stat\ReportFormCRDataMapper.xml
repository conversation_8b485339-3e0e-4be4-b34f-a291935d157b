<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.stat.ReportFormCRDataMapper">

    <sql id="reportFormCRData_Sql">
		r.[流水号] AS reportFormsCRDataId,r.[报表流水号] AS reportFormsId,r.[数据类型] AS dataType,r.[年月] AS yearAndMonth,r.[部门] AS department
		,r.[计划予定] AS jiHuaYuDing,r.[计划实际] AS jiHuaShiJi,r.[实绩予定] AS shiJiYuDing,r.[实绩实际] AS shiJiShiJi,r.[实际入库量] AS inStore
		,r.[预计入库量] AS yujiInStore,r.[单价] AS singlePrice
	</sql>

    <sql id="reportFormCRData_where">
        <if test="dataType != null">
            AND r.[数据类型] = #{dataType}
        </if>
        <if test="department != null and department != ''">
            AND r.[部门] = #{department}
        </if>
        <if test="yearMonth != null and yearMonth != ''">
            AND r.[年月] = #{yearMonth}
        </if>
    </sql>

    <insert id="insertReportFormCRData" parameterType="com.hongru.entity.stat.ReportFormCRData" useGeneratedKeys="true" keyProperty="reportFormsCRDataId">
        INSERT INTO [CostPrice].[dbo].[报表_CR计划实际]
		(
		[报表流水号],
		[数据类型],
		[年月],
		[部门],
		[计划予定],
		[计划实际],
		[实绩予定],
		[实绩实际],
		[实际入库量],
		[单价],
		[预计入库量]
		)VALUES(
		#{reportFormCRData.reportFormsId},
		#{reportFormCRData.dataType},
		#{reportFormCRData.yearAndMonth},
		#{reportFormCRData.department},
		#{reportFormCRData.jiHuaYuDing},
		#{reportFormCRData.jiHuaShiJi},
		#{reportFormCRData.shiJiYuDing},
		#{reportFormCRData.shiJiShiJi},
		#{reportFormCRData.inStore},
		#{reportFormCRData.singlePrice},
		#{reportFormCRData.yujiInStore}
		)
    </insert>

    <select id="selectReportFormCRData" resultType="com.hongru.entity.stat.ReportFormCRData">
        SELECT
        <include refid="reportFormCRData_Sql"/>
        FROM
        ( SELECT * , ROW_NUMBER ( ) OVER ( PARTITION BY 数据类型,年月,部门 ORDER BY 流水号 DESC ) AS COUNT FROM [报表_CR计划实际] ) r
        <where>
			r.count = 1
			<if test="dataType != null">
				AND r.[数据类型] = #{dataType}
			</if>
			<if test="department != null and department != ''">
				AND r.[部门] = #{department}
			</if>
			<if test="yearMonth != null and yearMonth != ''">
				AND r.[年月] = #{yearMonth}
			</if>
		</where>
        ORDER BY
        r.[数据类型] ASC
    </select>

</mapper>