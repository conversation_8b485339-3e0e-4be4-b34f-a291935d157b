<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.BookingBundlingMapper">
    <sql id="bookingBundling_sql">
		bo.[流水号] AS costId,bo.[导入标识] AS importId,bo.[年月] AS yearMonth,bo.[年] AS year,bo.[月] AS month,
		bo.[区分] AS bookingCode,bo.[捆包方法] AS bundlingMethod,bo.[捆包费] AS bundlingCost,
		bo.[创建人标识] AS creatorId,bo.[创建人姓名] AS creatorName,bo.[创建时间] AS createdTime
	</sql>

	<insert id="insertBookingBundling" parameterType="com.hongru.entity.cost.BookingBundling">
		INSERT INTO [CostPrice].[dbo].[预定捆包费表]
		(
		[导入标识],
		[年月],
		[年],
		[月],
		[区分],
		[捆包方法],
		[捆包费],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{bookingBundling.importId},
		#{bookingBundling.yearMonth},
		#{bookingBundling.year},
		#{bookingBundling.month},
		#{bookingBundling.bookingCode},
		#{bookingBundling.bundlingMethod},
		#{bookingBundling.bundlingCost},
		#{bookingBundling.creatorId},
		#{bookingBundling.creatorName},
		#{bookingBundling.createdTime}
		)
	</insert>

	<delete id="deleteBookingBundlingByCostId">
		DELETE FROM [CostPrice].[dbo].[预定捆包费表] WHERE [流水号] = #{costId}
	</delete>
	
	<select id="listBookingBundlingByYearMonth" resultType="com.hongru.entity.cost.BookingBundling">
		SELECT
		<include refid="bookingBundling_sql"/>
		FROM [CostPrice].[dbo].[预定捆包费表] bo
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND bo.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<select id="listBookingBundlingByParam" resultType="com.hongru.entity.cost.BookingBundling">
		SELECT
		<include refid="bookingBundling_sql"/>
		FROM [CostPrice].[dbo].[预定捆包费表] bo
		<where>
			<if test="timeMin != null and timeMin != ''">
				AND CONVERT ( DATETIME, bo.[年月] + '-01 00:00:00' ) &gt;= #{timeMin}
			</if>
			<if test="timeMax != null and timeMax != ''">
				AND CONVERT ( DATETIME, bo.[年月] + '-01 00:00:00' ) &lt;= #{timeMax}
			</if>
			<if test="yearMonth != null and yearMonth != ''">
				AND bo.[年月] = #{yearMonth}
			</if>
			<if test="year != null">
				AND bo.[年] = #{year}
			</if>
			<if test="month != null">
				AND bo.[月] = #{month}
			</if>
			<if test="bookingCodeStrArr != null and bookingCodeStrArr != ''">
				AND bo.[区分] IN
				<foreach collection="bookingCodeStrArr" item="bookingCode" open="(" close=")" separator=",">
					#{bookingCode}
				</foreach>
			</if>
		</where>
	</select>

	<select id="listBookingBundlingPage" resultType="com.hongru.entity.cost.BookingBundling">
		SELECT
		<include refid="bookingBundling_sql"/>
		FROM [CostPrice].[dbo].[预定捆包费表] bo
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND bo.[年月] = #{yearMonth}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY bo.[年月] DESC,bo.[流水号] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listBookingBundlingPageCount" resultType="integer">
		SELECT count(1)
		FROM [CostPrice].[dbo].[预定捆包费表] bo
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND bo.[年月] = #{yearMonth}
			</if>
		</where>
	</select>
</mapper>