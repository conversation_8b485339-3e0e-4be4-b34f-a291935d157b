
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    laydate.render({
        elem: '#time'
        ,type: 'month'
        ,range: '~'
        ,format: 'yyyy-MM'
      });

    //执行一个 table 实例
    var url = baselocation+'/yearRevise/newYearNitrogenCoefficientUF/list';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '列表'
        ,page: true //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
        	 ,{field: 'department',title: '部门',align:'center', width:100}
            ,{field: 'nitrogenPreAvg',title: '予定氮气平均',align:'center', width:200}
            ,{field: 'proportionPre',title: '予定比例',align:'center', width:150}
            ,{field: 'nitrogenActAllocation',title: '实绩氮气分配',align:'center', width:150}
            ,{field: 'machineTimePreAvg',title: '机械时间实绩',align:'center', width:200} 
            ,{field: 'newYearPreCoefficient',title: '新年度予定SYS系数',align:'center', width:200} 
        ]]
    });
});

function search() {
	var time=$("#time").val();
	if(time == null || time.trim() == ''){
		layer.alert("请选择日期范围！");
		return;
	}else{
	    $("#isSearch").val(1);
	    var temp = $("#formSearch").serializeJsonObject();
	    console.info(temp);
	    //执行重载
	    layui.table.reload('demo', {
	        page: {
	            curr: 1 //重新从第 1 页开始${ctx}
	        }
	        ,where: temp
	    }, 'data');
	}

}
// MW、UF一览明细选择
function toPage(orders) {
    if(orders == 1){
        window.location.href = baselocation+'/yearRevise/powerShCoefficient/list/view';
    }else if(orders == 2){
        window.location.href = baselocation+'/yearRevise/newYearEleCoefficient/list/view';
    }else if(orders == 3){
        window.location.href = baselocation+'/yearRevise/newYearEleCoefficientUF/list/view';
	}else if(orders == 4){
		window.location.href = baselocation+'/yearRevise/newYearNitrogenCoefficientUF/list/view';
	}
}
