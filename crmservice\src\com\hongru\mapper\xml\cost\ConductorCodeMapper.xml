<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.ConductorCodeMapper">
    <sql id="conductorCode_sql">
        cc.[流水号] AS codeId,cc.[状态] AS state,cc.[导体名称] AS conductorName,cc.[成本编码] AS costCode,
        cc.[创建人姓名] AS creatorName,cc.[创建时间] AS createdTime,
        cc.[最后修改人姓名] AS lastModifierName,cc.[最后修改时间] AS lastModifiedTime
    </sql>

    <select id="listConductorCodeByParam" resultType="com.hongru.entity.cost.ConductorCode">
        SELECT
        <include refid="conductorCode_sql"/>
        FROM [CostPrice].[dbo].[导体成本编码表] cc
        <where>
            <if test="state != null">
                AND cc.[状态] = #{state}
            </if>
        </where>
    </select>

    <select id="selectConductorCodeByParam" resultType="com.hongru.entity.cost.ConductorCode">
        SELECT
        <include refid="conductorCode_sql"/>
        FROM [CostPrice].[dbo].[导体成本编码表] cc
        <where>
            <if test="conductorName != null">
                AND cc.[导体名称] = #{conductorName}
            </if>
        </where>
    </select>

    <select id="listConductorCodePage" resultType="com.hongru.entity.cost.ConductorCode">
        SELECT
        <include refid="conductorCode_sql"/>
        FROM [CostPrice].[dbo].[导体成本编码表] cc
        <where>
            cc.[状态] != 9
            <if test="conductorName != null and conductorName != ''">
                AND cc.[导体名称] LIKE '%' + #{conductorName} + '%'
            </if>
        </where>
        ORDER BY cc.[流水号] DESC
        OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
    </select>

    <select id="listConductorCodePageCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM [CostPrice].[dbo].[导体成本编码表] cc
        <where>
            cc.[状态] != 9
            <if test="conductorName != null and conductorName != ''">
                AND cc.[导体名称] LIKE '%' + #{conductorName} + '%'
            </if>
        </where>
    </select>

    <insert id="insertConductorCode" parameterType="com.hongru.entity.cost.ConductorCode" useGeneratedKeys="true" keyProperty="conductorCode.codeId">
        INSERT INTO [CostPrice].[dbo].[导体成本编码表]
        (
            [状态],
            [导体名称],
            [成本编码],
            [创建人姓名],
            [创建时间],
            [最后修改人姓名],
            [最后修改时间]
        )VALUES(
            #{conductorCode.state},
            #{conductorCode.conductorName},
            #{conductorCode.costCode},
            #{conductorCode.creatorName},
            #{conductorCode.createdTime},
            #{conductorCode.lastModifierName},
            #{conductorCode.lastModifiedTime}
        )
    </insert>

    <update id="updateConductorCode" parameterType="com.hongru.entity.cost.ConductorCode">
        UPDATE [CostPrice].[dbo].[导体成本编码表]
        SET
            [导体名称] = #{conductorCode.conductorName},
            [最后修改人姓名] = #{conductorCode.lastModifierName},
            [最后修改时间] = #{conductorCode.lastModifiedTime}
        WHERE [流水号] = #{conductorCode.codeId}
    </update>

    <select id="selectConductorCodeById" resultType="com.hongru.entity.cost.ConductorCode">
        SELECT
        <include refid="conductorCode_sql"/>
        FROM [CostPrice].[dbo].[导体成本编码表] cc
        WHERE cc.[流水号] = #{codeId} AND cc.[状态] != 9
    </select>

    <select id="selectMaxCostCode" resultType="java.lang.String">
        SELECT TOP 1 cc.[成本编码]
        FROM [CostPrice].[dbo].[导体成本编码表] cc
        WHERE cc.[成本编码] LIKE 'C1%' AND cc.[状态] != 9
        ORDER BY cc.[成本编码] DESC
    </select>

</mapper>
