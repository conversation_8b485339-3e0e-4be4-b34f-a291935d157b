<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="palletLable" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="832" leftMargin="10" rightMargin="0" topMargin="10" bottomMargin="0" isIgnorePagination="true" uuid="2d78ba35-291b-4276-84d0-be2fd62d3581">
	<property name="ireport.zoom" value="1.100000000000012"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="mutoNo" class="java.lang.String"/>
	<parameter name="model" class="java.lang.String"/>
	<parameter name="size" class="java.lang.String"/>
	<parameter name="netWeight" class="java.lang.String"/>
	<parameter name="reel" class="java.lang.String"/>
	<parameter name="lot" class="java.lang.String"/>
	<parameter name="zhuYouPic" class="java.lang.String"/>
	<parameter name="contextPath" class="java.lang.String"/>
	<parameter name="mutoNo2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="partNo" class="java.lang.String"/>
	<parameter name="customerName" class="java.lang.String"/>
	<parameter name="customerAddress" class="java.lang.String"/>
	<parameter name="certificateNo" class="java.lang.String"/>
	<parameter name="bzyq" class="java.lang.String"/>
	<parameter name="qrCodeContent" class="java.lang.String"/>
	<detail>
		<band height="585">
			<staticText>
				<reportElement uuid="3635d346-3d8d-4e21-964e-0f3f04f6234f" x="0" y="94" width="166" height="74"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="55" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="false"/>
				</textElement>
				<text><![CDATA[木托号]]></text>
			</staticText>
			<line>
				<reportElement uuid="dd132130-04a8-48a3-8673-18af6f1bc442" x="213" y="167" width="610" height="1"/>
			</line>
			<line>
				<reportElement uuid="51de4cc9-f175-46c0-a127-4942756ecc08" x="213" y="227" width="610" height="1"/>
			</line>
			<staticText>
				<reportElement uuid="85fbae0a-8a5d-46de-8fc1-f7c2acd13594" x="0" y="155" width="166" height="73"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="55" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="false"/>
				</textElement>
				<text><![CDATA[型号]]></text>
			</staticText>
			<line>
				<reportElement uuid="fa4bf4c7-4368-4ff5-bb4f-f5f619b7b941" x="213" y="286" width="610" height="1"/>
			</line>
			<staticText>
				<reportElement uuid="fd636022-76c9-406a-961d-cc6dacfcaead" x="0" y="213" width="166" height="75"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="55" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[尺寸]]></text>
			</staticText>
			<line>
				<reportElement uuid="284b6b5f-35df-4537-8bcf-d48b4498337d" x="213" y="346" width="610" height="1"/>
			</line>
			<staticText>
				<reportElement uuid="d0f813b5-90bf-4f72-ae85-829b75807e37" x="0" y="275" width="166" height="72"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="55" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[净重]]></text>
			</staticText>
			<line>
				<reportElement uuid="4c7306dc-22cc-458e-890d-66e3c265940e" x="213" y="406" width="610" height="1"/>
			</line>
			<staticText>
				<reportElement uuid="f5922d93-8ec7-4140-bf19-a7a62240d98f" x="0" y="335" width="166" height="72"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="55" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[线盘]]></text>
			</staticText>
			<rectangle>
				<reportElement uuid="5f3d344b-0a4d-47cc-9ed3-d8ca2e23170a" x="549" y="74" width="194" height="48"/>
			</rectangle>
			<staticText>
				<reportElement uuid="d2a65e40-e394-4a2c-bf77-9ba9b1cbb806" x="550" y="69" width="206" height="51"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="36" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="false"/>
				</textElement>
				<text><![CDATA[RoHS对应品]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="25dfbacb-f794-470a-acbd-763329b04ae0" x="213" y="67" width="619" height="101"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="88" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mutoNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="ae5d7c82-f2ea-41ed-b36c-bc834902f21a" x="213" y="168" width="619" height="60"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="52" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{model}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="debc4b47-4af8-462e-a034-57b9218a586c" x="213" y="227" width="619" height="60"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="52" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{size}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="6eb5ed76-320e-40d9-ad4d-274fb610fcd0" x="213" y="286" width="619" height="60"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="52" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{netWeight}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="741bb37f-c6ab-48be-a7fb-45788aac7f65" x="213" y="347" width="619" height="60"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="52" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{reel}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="6ba7c9c7-3749-43b5-b1f4-8e63870e2356" x="0" y="463" width="123" height="50"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="40" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[批号:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="13f295f3-2a0a-40b7-9d0b-1c3dc8daa765" x="110" y="463" width="722" height="50"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="40" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{lot}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="d0c00a9e-3af1-4f31-a5e8-daf3ac4ec5c9" x="377" y="536" width="236" height="26"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="16" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[住友电工运泰克(无锡)有限公司]]></text>
			</staticText>
			<image>
				<reportElement uuid="d57eb111-f38b-4058-bcd8-5323da532684" x="358" y="542" width="20" height="20"/>
				<imageExpression><![CDATA[$P{contextPath}+$P{zhuYouPic}]]></imageExpression>
			</image>
			<image>
				<reportElement uuid="de2b585f-2aad-4533-81be-a5a77de5e1ae" x="213" y="536" width="127" height="26"/>
				<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage( new com.google.zxing.oned.Code128Writer().encode( $P{mutoNo2},com.google.zxing.BarcodeFormat.CODE_128,127,26) )]]></imageExpression>
			</image>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="ad1a8e40-8a00-4d67-9daa-26a5ca6cbafd" x="0" y="413" width="832" height="50"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="40" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{partNo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="c5436852-8071-440a-81a2-8c087123b0a4" x="0" y="33" width="832" height="34"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="28" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{customerAddress}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="f517f982-da98-42d1-92d9-2575affc65d8" x="0" y="0" width="832" height="34"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="28" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{customerName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="b881785f-c114-4c6d-abf5-671205462b9d" stretchType="RelativeToBandHeight" x="613" y="513" width="180" height="48"/>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="宋体" size="20" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{bzyq}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="a95e259a-eaa4-449a-b502-0fdacde3519c" x="0" y="540" width="213" height="26"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="宋体" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{certificateNo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
