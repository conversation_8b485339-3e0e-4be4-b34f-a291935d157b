package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

/**
 * 产品制造设计表实体类
 * 
 * <AUTHOR>
 */
@TableName("产品制造设计表")
public class ProductManufacturingDesign {

    /* 状态-正常 */
    public static final short STATE_NORMAL = 0;
    /* 状态-删除 */
    public static final short STATE_DELETED = 9;

    /* 流水号 */
    @TableId(value = "流水号", type = IdType.AUTO)
    protected int serialNumber;

    /* 年度 */
    @TableField("年度")
    protected String year;

    /* 条码 */
    @TableField("条码")
    protected String barcode;

    /* 客户简称 */
    @TableField("客户简称")
    protected String customerName;

    /* 产品代码 */
    @TableField("产品代码")
    protected String productCode;

    /* 产品条码 */
    @TableField("产品条码")
    protected String productBarcode;

    /* 线盘名称 */
    @TableField("线盘名称")
    protected String wireDiscName;

    /* 产品尺寸 */
    @TableField("产品尺寸")
    protected String productSize;

    /* 皮膜厚1 */
    @TableField("皮膜厚1")
    protected BigDecimal filmThickness1;

    /* 皮膜厚2 */
    @TableField("皮膜厚2")
    protected BigDecimal filmThickness2;

    /* 皮膜厚3 */
    @TableField("皮膜厚3")
    protected BigDecimal filmThickness3;

    /* 皮膜厚4 */
    @TableField("皮膜厚4")
    protected BigDecimal filmThickness4;

    /* 导体原料 */
    @TableField("导体原料")
    protected String conductorMaterial;

    /* 涂料1 */
    @TableField("涂料1")
    protected String paint1;

    /* 涂料2 */
    @TableField("涂料2")
    protected String paint2;

    /* 涂料3 */
    @TableField("涂料3")
    protected String paint3;

    /* 涂料4 */
    @TableField("涂料4")
    protected String paint4;

    /* 涂料5 */
    @TableField("涂料5")
    protected String paint5;

    /* 卷轴原料 */
    @TableField("卷轴原料")
    protected String wireDiscMaterial;

    /* 运输费 */
    @TableField("运输费")
    protected String transportFee;

    /* 量试区分 */
    @TableField("量试区分")
    protected String quantityTestType;

    /* 产品分类 */
    @TableField("产品分类")
    protected String productCategory;

    /* 计算区分 */
    @TableField("计算区分")
    protected String calculationType;

    /* 导体重量 */
    @TableField("导体重量")
    protected BigDecimal conductorWeight;

    /* 涂料重量1 */
    @TableField("涂料重量1")
    protected BigDecimal paintWeight1;

    /* 涂料重量2 */
    @TableField("涂料重量2")
    protected BigDecimal paintWeight2;

    /* 涂料重量3 */
    @TableField("涂料重量3")
    protected BigDecimal paintWeight3;

    /* 涂料重量4 */
    @TableField("涂料重量4")
    protected BigDecimal paintWeight4;

    /* 涂料重量5 */
    @TableField("涂料重量5")
    protected BigDecimal paintWeight5;

    /* 卷轴重量 */
    @TableField("卷轴重量")
    protected BigDecimal wireDiscWeight;

    /* 创建者 */
    @TableField("创建者")
    protected String creatorName;

    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;

    /* 更新者 */
    @TableField("更新者")
    protected String updaterName;

    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;

    // Getters and Setters
    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductBarcode() {
        return productBarcode;
    }

    public void setProductBarcode(String productBarcode) {
        this.productBarcode = productBarcode;
    }

    public String getWireDiscName() {
        return wireDiscName;
    }

    public void setWireDiscName(String wireDiscName) {
        this.wireDiscName = wireDiscName;
    }

    public String getProductSize() {
        return productSize;
    }

    public void setProductSize(String productSize) {
        this.productSize = productSize;
    }

    public BigDecimal getFilmThickness1() {
        return filmThickness1;
    }

    public void setFilmThickness1(BigDecimal filmThickness1) {
        this.filmThickness1 = filmThickness1;
    }

    public BigDecimal getFilmThickness2() {
        return filmThickness2;
    }

    public void setFilmThickness2(BigDecimal filmThickness2) {
        this.filmThickness2 = filmThickness2;
    }

    public BigDecimal getFilmThickness3() {
        return filmThickness3;
    }

    public void setFilmThickness3(BigDecimal filmThickness3) {
        this.filmThickness3 = filmThickness3;
    }

    public BigDecimal getFilmThickness4() {
        return filmThickness4;
    }

    public void setFilmThickness4(BigDecimal filmThickness4) {
        this.filmThickness4 = filmThickness4;
    }

    public String getConductorMaterial() {
        return conductorMaterial;
    }

    public void setConductorMaterial(String conductorMaterial) {
        this.conductorMaterial = conductorMaterial;
    }

    public String getPaint1() {
        return paint1;
    }

    public void setPaint1(String paint1) {
        this.paint1 = paint1;
    }

    public String getPaint2() {
        return paint2;
    }

    public void setPaint2(String paint2) {
        this.paint2 = paint2;
    }

    public String getPaint3() {
        return paint3;
    }

    public void setPaint3(String paint3) {
        this.paint3 = paint3;
    }

    public String getPaint4() {
        return paint4;
    }

    public void setPaint4(String paint4) {
        this.paint4 = paint4;
    }

    public String getPaint5() {
        return paint5;
    }

    public void setPaint5(String paint5) {
        this.paint5 = paint5;
    }

    public String getWireDiscMaterial() {
        return wireDiscMaterial;
    }

    public void setWireDiscMaterial(String wireDiscMaterial) {
        this.wireDiscMaterial = wireDiscMaterial;
    }

    public String getTransportFee() {
        return transportFee;
    }

    public void setTransportFee(String transportFee) {
        this.transportFee = transportFee;
    }

    public String getQuantityTestType() {
        return quantityTestType;
    }

    public void setQuantityTestType(String quantityTestType) {
        this.quantityTestType = quantityTestType;
    }

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    public String getCalculationType() {
        return calculationType;
    }

    public void setCalculationType(String calculationType) {
        this.calculationType = calculationType;
    }

    public BigDecimal getConductorWeight() {
        return conductorWeight;
    }

    public void setConductorWeight(BigDecimal conductorWeight) {
        this.conductorWeight = conductorWeight;
    }

    public BigDecimal getPaintWeight1() {
        return paintWeight1;
    }

    public void setPaintWeight1(BigDecimal paintWeight1) {
        this.paintWeight1 = paintWeight1;
    }

    public BigDecimal getPaintWeight2() {
        return paintWeight2;
    }

    public void setPaintWeight2(BigDecimal paintWeight2) {
        this.paintWeight2 = paintWeight2;
    }

    public BigDecimal getPaintWeight3() {
        return paintWeight3;
    }

    public void setPaintWeight3(BigDecimal paintWeight3) {
        this.paintWeight3 = paintWeight3;
    }

    public BigDecimal getPaintWeight4() {
        return paintWeight4;
    }

    public void setPaintWeight4(BigDecimal paintWeight4) {
        this.paintWeight4 = paintWeight4;
    }

    public BigDecimal getPaintWeight5() {
        return paintWeight5;
    }

    public void setPaintWeight5(BigDecimal paintWeight5) {
        this.paintWeight5 = paintWeight5;
    }

    public BigDecimal getWireDiscWeight() {
        return wireDiscWeight;
    }

    public void setWireDiscWeight(BigDecimal wireDiscWeight) {
        this.wireDiscWeight = wireDiscWeight;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }
}
