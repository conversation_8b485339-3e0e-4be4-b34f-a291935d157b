package com.hongru.common.util.wx;

/**
 * 小程序登录凭证校验返回实体类
 * <AUTHOR>
 * @create 2018年4月17日 下午2:59:06
 * @version 1.0
 */
public class Jscode2SessionReturn {
	/*用户唯一标识*/
	private String openid;
	/*会话密钥*/
	private String session_key;	//只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。
	/*用户在开放平台的唯一标识符*/
	private String unionid;
	/*错误码*/
	private String errcode;		
	/*错误信息*/
	private String errmsg;		
	
	public String getOpenid() {
		return openid;
	}
	public void setOpenid(String openid) {
		this.openid = openid;
	}
	public String getSession_key() {
		return session_key;
	}
	public void setSession_key(String session_key) {
		this.session_key = session_key;
	}
	public String getUnionid() {
		return unionid;
	}
	public void setUnionid(String unionid) {
		this.unionid = unionid;
	}
	public String getErrcode() {
		return errcode;
	}
	public void setErrcode(String errcode) {
		this.errcode = errcode;
	}
	public String getErrmsg() {
		return errmsg;
	}
	public void setErrmsg(String errmsg) {
		this.errmsg = errmsg;
	}
	
	
}
