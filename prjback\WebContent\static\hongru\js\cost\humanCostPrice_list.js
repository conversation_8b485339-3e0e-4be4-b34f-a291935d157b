
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作

    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#yearMonth', //指定元素
        btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
    });

    //执行一个 table 实例
    var url = baselocation+'/costPrice/humanCostPrice/listPage';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '人件费用列表'
        ,page: true //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'orderNo', title: '',width:50,fixed: 'left',type:"numbers",align:'center'}
            ,{field: 'yearMonth',title: '年月',align:'center', width:150}
            ,{field: 'humanPriceCode',title: '人件费区分',align:'center', width:150}
            ,{field: 'humanPrice',title: '人件费',align:'center', width:150}
            ,{title: '操作',minWidth:150, align:'left',fixed: 'right', toolbar: '#barDemo',width: 100}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                layer_show('添加', baselocation+"/costPrice/humanCostPrice/add/view", 750, document.body.clientHeight-100)
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值
        if(layEvent === 'remove'){
            layer.confirm('确认要删除吗？', {
                btn : [ '确定', '取消' ] //按钮
            }, function() {
                $.ajax({
                    type : 'post',
                    dataType : 'json',
                    data: {"costId":data.costId,"state":9},
                    url : baselocation + '/costPrice/humanCostPrice/remove',
                    success : function(result) {
                        if (result.code == 1) {
                            layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {
                                search();
                                layer.closeAll();
                            });
                        } else {
                            layer.alert(result.message, {
                                icon : 2
                            });
                        }
                    }
                })
            });
        }
    });
});

function search() {
    var arr = new Array();
    $("input:checkbox[name='processIds']:checked").each(function(i){
        arr[i] = $(this).val();
    });
    $("#processIdStr").val(arr.join(","));
    $("#isSearch").val(1);
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    //执行重载
    layui.table.reload('demo', {
        page: {
            curr: 1 //重新从第 1 页开始${ctx}
        }
        ,where: temp
    }, 'data');
}
