<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.xieFenXi.ScrapReasonDetailMapper">

    <select id="selectScrapReason" resultType="com.hongru.entity.xieFenXi.ScrapReasonDetail">
        select
            s.[代码] as scrapReasonId,
            s.[子项代码] as scrapReasonDetailId,
            s.[屑原因子项] as scrapReasonDetail,
            s.[现场显示] as sceneDisplay,
            r.[屑原因] as code
        FROM
        [屑分析].[dbo].[屑原因子项表] s left join [屑分析].[dbo].[屑原因表] r on s.[代码]=r.[代码]
    </select>

</mapper>