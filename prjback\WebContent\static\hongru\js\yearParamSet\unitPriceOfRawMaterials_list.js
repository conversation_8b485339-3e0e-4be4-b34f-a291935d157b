layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
        ,form = layui.form //表单

    // 当前选择的原料类型
    var currentMaterialType = '';
    var materialTypeNames = {
        '01': '导体',
        '02': '油漆',
        '03': '线盘'
    };

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    // 监听原料类型选择
    form.on('select(materialTypeSelect)', function(data){
        // 这里不自动切换，等用户点击切换按钮
    });

    // 模式切换函数
    window.switchMaterialType = function(){
        var selectedType = $('#materialTypeSelect').val();
        if(!selectedType) {
            layer.msg('请先选择原料类型', {icon: 2});
            return;
        }

        currentMaterialType = selectedType;
        $('#currentMaterialType').val(selectedType);

        // 显示查询面板和表格面板
        $('#searchPanel').show();
        $('#tablePanel').show();
        $('#tipPanel').hide();

        // 更新工具栏按钮文字
        $('#materialTypeName').text(materialTypeNames[selectedType]);

        // 初始化表格
        initTable();

        layer.msg('已切换到' + materialTypeNames[selectedType] + '模式', {icon: 1});
    }

    // 初始化表格
    function initTable(){
        var url = baselocation+'/yearParamSet/unitPriceOfRawMaterials/listPage';
        table.render({
            elem: '#demo'
            ,height: 'full-250'
            ,url: url //数据接口
            ,parseData:function(res){ //res 即为原始返回的数据
                return {
                    "code": 0, //解析接口状态
                    "msg": '', //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.rows //解析数据列表
                };
            }
            ,method:'post'
            ,title: materialTypeNames[currentMaterialType] + '单价'
            ,page: true //开启分页
            ,where:$("#formSearch").serializeJsonObject()
            ,toolbar: '#toolbarDemo' //开启工具栏
            ,defaultToolbar: ['filter']
            ,totalRow: false //开启合计行
            ,cols: getTableColumns()
        });
    }

    // 根据原料类型获取表格列配置
    function getTableColumns(){
        var baseCols = [
            {field: 'orderNo', title: '',width:50,fixed: 'left',type:"numbers",align:'center'}
            ,{field: 'year',title: '年度',align:'center', width:100}
            ,{field: 'itemCode',title: '品目',align:'center', width:120}
            ,{field: 'itemName',title: '品目名',align:'center', width:200}
            ,{field: 'creatorName',title: '创建人姓名',align:'center', width:120}
            ,{field: 'createdTime',title: '创建时间',align:'center', width:180, templet: function(d){
                if(d.createdTime) {
                    return layui.util.toDateString(new Date(d.createdTime), 'yyyy-MM-dd HH:mm:ss');
                }
                return '';
            }}
            ,{field: 'updaterName',title: '更新人姓名',align:'center', width:120}
            ,{field: 'updatedTime',title: '更新时间',align:'center', width:180, templet: function(d){
                if(d.updatedTime) {
                    return layui.util.toDateString(new Date(d.updatedTime), 'yyyy-MM-dd HH:mm:ss');
                }
                return '';
            }}
            ,{title: '操作', width:180, align:'center', toolbar: '#barDemo'}
        ];

        return [baseCols];
    }

    //监听头工具栏事件
    table.on('toolbar(demo)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        switch(obj.event){
            case 'toAdd':
                toAdd();
                break;
            case 'refresh':
                search();
                break;
        };
    });

    //监听行工具事件
    table.on('tool(demo)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            toDetail(data.serialNumber);
        } else if(obj.event === 'edit'){
            toEdit(data.serialNumber);
        }
    });

    // 新增
    function toAdd(){
        if(!currentMaterialType) {
            layer.msg('请先选择原料类型', {icon: 2});
            return;
        }

        var url = baselocation+'/yearParamSet/unitPriceOfRawMaterials/add/view?materialType=' + currentMaterialType;
        var title = '新增' + materialTypeNames[currentMaterialType] + '单价';

        layer.open({
            type: 2,
            title: title,
            shadeClose: true,
            shade: 0.8,
            area: ['900px', '700px'],
            content: url,
            end: function(){
                search();
            }
        });
    }

    // 详情
    function toDetail(serialNumber){
        var url = baselocation+'/yearParamSet/unitPriceOfRawMaterials/detail/view?serialNumber='+serialNumber;
        layer.open({
            type: 2,
            title: '原料单价详情',
            shadeClose: true,
            shade: 0.8,
            area: ['900px', '700px'],
            content: url
        });
    }

    // 编辑
    function toEdit(serialNumber){
        var url = baselocation+'/yearParamSet/unitPriceOfRawMaterials/edit/view?serialNumber='+serialNumber;
        layer.open({
            type: 2,
            title: '编辑原料单价',
            shadeClose: true,
            shade: 0.8,
            area: ['800px', '600px'],
            content: url,
            end: function(){
                search();
            }
        });
    }

    // 搜索
    window.search = function(){
        $("#isSearch").val("1");
        table.reload('demo', {
            where: $("#formSearch").serializeJsonObject()
        });
    }
});
