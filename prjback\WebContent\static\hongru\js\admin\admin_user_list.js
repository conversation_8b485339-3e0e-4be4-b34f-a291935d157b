layui.config({
    base: baselocationsta+'/common/layui/'
});

layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate
        ,layer = layui.layer
        ,table = layui.table
        ,element = layui.element
    var form = layui.form;

    var url = baselocation+'/administrator/list/json';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url
        ,parseData:function(res){
            return {
                "code": 0,
                "msg": '',
                "count": res.total,
                "data": res.rows
            };
        }
        ,method:'post'
        ,title: '管理员列表'
        ,page: true
        ,limits: [10,20,50,100]
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo'
        ,defaultToolbar: ['filter']
        ,totalRow: false
        ,cols: [[
            {type: 'checkbox', fixed: 'left'}
            ,{field: 'zizeng', title: '', width:50, fixed:'left', type:"numbers", align:'center'}
            ,{field: 'loginName',width:120,title: '登录名',align:'center'}
            ,{field: 'realName',width:120,title: '姓名',align:'center'}
            ,{field: 'status',width:80,title: '状态',align:'center',
                templet: function(d){
                    if (d.status == 1) {
                        var str="";
                        str += '<form class="layui-form" action="">';
                        str += '<input type="checkbox" name="status" lay-skin="switch" value="'+d.userId+'" lay-text="正常|冻结" checked lay-filter="statusFun">';
                        str += '</form>';
                        return str;
                    } else if (d.status == 0) {
                        var str="";
                        str += '<form class="layui-form" action="">';
                        str += '<input type="checkbox" name="status" lay-skin="switch" value="'+d.userId+'" lay-text="正常|冻结" lay-filter="statusFun">';
                        str += '</form>';
                        return str;
                    }
                }
            }
            ,{field: 'lastLoginTime',width:180,title: '最后登录时间',align:'center',
                templet: function(d){
                    if(d.lastLoginTime !=null){
                        return new Date(d.lastLoginTime).FormatV2("yyyy-MM-dd HH:mm:ss");
                    }else{
                        return "";
                    }
                }
            }
            ,{field: 'roleName',width:120,title: '角色',align:'center'}
            ,{field: 'organizationName',width:120,title: '部门',align:'center'}
            ,{field: 'telephone',width:120,title: '手机',align:'center'}
            ,{field: 'email',width:200,title: '邮箱',align:'center'}
            ,{title: '操作',minWidth:315,align:'left',fixed:'right',toolbar:'#barDemo'}
        ]]
    });

    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data;
        switch(obj.event){
            case 'toAdd':
                layer_show('创建管理员',baselocation+'/administrator/list/create/view',document.body.clientWidth-10, document.body.clientHeight-10)
                break;
            case 'refresh':
                search();
                break;
            case 'bathDelete':
                if(data.length == 0){
                    layer.alert("请选择要删除的数据！");
                    return;
                }else{
                    var userIds = "";
                    for(var i=0; i < data.length; i++){
                        if(userIds==""){
                            userIds = data[i].userId;
                        }else{
                            userIds+= ","+data[i].userId;
                        }
                    }
                    var index = layer.load(2,{
                        shade:[0.1,'#fff']
                    });
                    $.ajax({
                        type : 'post',
                        dataType : 'json',
                        data: {"userIds":userIds},
                        url : baselocation + '/administrator/list/delete/forBatch',
                        success : function(result) {
                            layer.closeAll();
                            if (result.code == 1) {
                                layer.msg("操作成功!", {
                                    shade : 0.3,
                                    time : 1500
                                }, function() {
                                    layui.table.reload('demo');
                                });
                            } else {
                                layer.alert(result.message, {
                                    icon : 2
                                });
                            }
                        }
                    })
                }
                break;
        };
    });

    table.on('tool(test)', function(obj){
        var data = obj.data
            ,layEvent = obj.event;
        if(layEvent === 'freeze'){
            status_stop(data.userId);
        }else if(layEvent === 'normal'){
            status_start(data.userId);
        }else if(layEvent === 'edit'){
            layer_show(data.userName, baselocation + '/administrator/list/' + data.userId + '/edit/view', document.body.clientWidth-10, document.body.clientHeight-10)
        }else if(layEvent === 'remove'){
            admin_delete(data.userId);
        }else if(layEvent === 'loglist'){
            layer_show(data.userName + '登录日志', baselocation + '/administrator/list/' + data.userId + '/log', document.body.clientWidth-10, document.body.clientHeight-10)
        }else if(layEvent==='modifyPwd'){
            var title =data.userName+ "-修改密码";
            var html='';
            html+='<div class="ibox-content" style="height: 120px;">';
            html+='     <form class="layui-form layui-form-pane" action="" method="post" id="modifyPwdForm">';
            html+='         <input type="hidden" name="userId" value="'+data.userId+'"';
            html+='         <table style="width:100%;">';
            html+='             <tr>';
            html+='                 <td>';
            html+='                     <div class="layui-form-item">';
            html+='                         <label class="layui-form-label">密码</label>';
            html+='                         <div class="layui-input-inline">';
            html+='                             <input type="text" class="layui-input" autocomplete="off" id="password" name="password" maxlength="20">';
            html+='                         </div>';
            html+='                     </div>';
            html+='                 </td>';
            html+='             </tr>';
            html+='             <tr>';
            html+='                 <td>';
            html+='                     <div class="layui-form-item">';
            html+='                         <div class="layui-input-block" style="text-align: center;margin-left: 0px;padding-top: 20px" >';
            html+='                             <button type="button" class="layui-btn" onclick="modifyPwd()">修改</button>';
            html+='                         </div>';
            html+='                     </div>';
            html+='                 </td>';
            html+='             </tr>';
            html+='          </table>';
            html+='     </form>';
            html+='</div>';
            layer.open({
                type: 1
                ,title: title
                ,maxmin: false
                ,shadeClose: false
                ,area : ["350px" , "200px"]
                ,content: html
            });
        }
    });

    $("#btn-resert").on("click",function(){
        $('#searchForm input[type="text"]').each(function (i, j) {
            $(j).attr("value", "");
        })

        $('#searchForm select').each(function (i, j) {
            $(j).find("option:selected").attr("selected", false);
            $(j).find("option").first().attr("selected", true);
        })
        $("#cityId").html('<option value="" >全部</option>');
    })

    form.on('switch(statusFun)', function(data){
        var index = layer.load(2,{
            shade:[0.1,'#fff']
        });
        $.ajax({
            dataType : 'json',
            type : 'put',
            url : baselocation + '/administrator/list/' + data.value + '/audit',
            success : function(result) {
                layer.closeAll();
                if (result.code == 1) {
                    layer.msg('操作成功!', {
                        icon : 1,
                        time : 1000
                    }, function() {
                        layui.table.reload('demo');
                    });
                } else {
                    layer.alert(result.message, {
                        icon : 2
                    });
                }
            }
        })
    });
});

function search() {
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    layui.table.reload('demo', {
        page: {
            curr: 1
        }
        ,where: temp
    }, 'data');
}

function status_stop(value) {
    layer.confirm('确认要冻结该管理员吗？', {
        btn : [ '确定', '取消' ]
    }, function() {
        $.ajax({
            dataType : 'json',
            type : 'put',
            url : baselocation + '/administrator/list/' + value + '/audit',
            success : function(result) {
                if (result.code == 1) {
                    layer.msg("操作成功", {
                        time : 1000
                    });
                    layui.table.reload('demo');
                } else {
                    layer.alert(result.message, {
                        icon : 2
                    });
                }
            }
        })
    });
}

function status_start(value) {
    layer.confirm('确认要启用该管理员吗？', {
        btn : [ '确定', '取消' ]
    }, function() {
        $.ajax({
            dataType : 'json',
            type : 'put',
            url : baselocation + '/administrator/list/' + value + '/audit',
            success : function(result) {
                if (result.code == 1) {
                    layer.msg("操作成功", {
                        time : 1000
                    });
                    layui.table.reload('demo');
                } else {
                    layer.alert(result.message, {
                        icon : 2
                    });
                }
            }
        })
    });
}

function admin_delete(value) {
    layer.confirm('确认要删除该管理员吗？', {
        btn : [ '确定', '取消' ]
    }, function() {
        $.ajax({
            type : 'delete',
            dataType : 'json',
            url : baselocation + '/administrator/list/' + value,
            success : function(result) {
                if (result.code == 1) {
                    layer.msg("操作成功", {
                        time : 1000
                    });
                    layui.table.reload('demo');
                } else {
                    layer.alert(result.message, {
                        icon : 2
                    });
                }
            }
        })
    });
}

function modifyPwd(){
    var password = $("#password").val();
    if(password == null || password ==''){
        layer.alert("请填写密码！");
        return;
    }
    var index = layer.load(2,{
        shade:[0.1,'#fff']
    });
    $.ajax({
        url : baselocation+'/administrator/info/reset/psw',
        type : 'post',
        data : $('#modifyPwdForm').serialize(),
        success : function(result) {
            layer.close(index);
            if(result.code == 1){
                layer.msg("操作成功!", {
                    shade : 0.3,
                    time : 1500
                }, function() {
                    layer.closeAll();
                    layui.table.reload('demo');
                });
            }else{
                layer.alert(result.message);
            }
        }
    });
}