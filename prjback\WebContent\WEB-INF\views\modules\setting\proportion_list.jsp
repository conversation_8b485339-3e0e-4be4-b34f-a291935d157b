<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>人件比例列表</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show" >
                    <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                          <div class="layui-form-item">
                            <div class="layui-inline layui-col-md2">
                                <label class="layui-form-label" >年度:</label>
	                        	<div class="layui-input-block">
	                           		<input type="text" class="layui-input" id="year"  name="year" value=""   required=""  lay-verify="required"/>
                        		</div>
                            </div>
                            <div class="layui-inline layui-col-md4"></div>
                            <div class="layui-inline layui-col-md4 hr-div-btn">
                              	 <input type="hidden" id="isSearch" name="isSearch" value="0" />
                                <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="demo" lay-filter="test"></table>
        <!-- 自定义表中工具栏 -->
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs layui-bg-green" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
            <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>删除</a>
        </script>
        <!-- 自定义头部工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="toAdd"><i class="layui-icon layui-icon-add-1"></i>新增</button>
            </div>
        </script>
    </div>
</div>
<myfooter>
    <script src="${ctxsta}/hongru/js/setting/proportion_list.js?time=1"></script>
</myfooter>
</body>
</html>
