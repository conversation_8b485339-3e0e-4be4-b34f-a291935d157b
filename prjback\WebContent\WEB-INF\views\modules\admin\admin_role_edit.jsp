<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <title>编辑角色</title>
  <link rel="stylesheet" href="${ctxsta}/common/ztree/css/metroStyle/metroStyle.css" />
</head>
<body >
<form class="layui-form hr-form-add" id="submitForm" action="${ctx}/administrator/role/${role.roleId}/edit" method="post">
  <div class="layui-fluid hr-layui-fluid">
  <div class="layui-card">
    <div class="layui-card-body">
      <div class="layui-form-item layui-row">
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span class="star">*</span>角色名称：</label>
          <div class="layui-input-block">
            <input type="text" id="roleName" name="roleName" required=""
                   lay-verify="required" placeholder="请输入角色名称" class="layui-input" value="${role.roleName}"/>
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span  class="star">*</span>状态：</label>
          <div class="layui-input-block">
            <input type="radio" name="status" value="1" title="开启" ${role.status eq '1' ? 'checked':''}>
            <input type="radio" name="status" value="0" title="冻结" ${role.status eq '0' ? 'checked':''}>
          </div>
        </div>
        <div class="layui-inline layui-col-md12">
          <label class="layui-form-label">备注：</label>
          <div class="layui-input-block">
            <textarea class="layui-textarea" style="resize: none;" name="remarks" maxlength="220">${role.remarks}</textarea>
          </div>
        </div>
        <div class="layui-inline layui-col-md12">
          <label class="layui-form-label"><span  class="star">*</span>权限：</label>
          <div class="layui-input-block">
            <div id="ztree" class="ztree"></div>
          </div>
        </div>
      </div>
      <div class="layui-form-item layui-row">
        <div class="layui-inline layui-col-md12">
          <label class="layui-form-label"></label>
          <div class="layui-input-block">
            <button type="button" class="layui-btn" lay-submit lay-filter="formDemo"><i class="layui-icon layui-icon-ok"></i>保存</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();"><i class="layui-icon layui-icon-close"></i>取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</form>
<myfooter>
  <script src="${ctxsta}/common/ztree/js/jquery.ztree.all.min.js"></script>
  <script type="text/javascript">
    var treedata = '${menus}';
    var ztreeObject;
    var setting = {
      data : {
        simpleData : {
          enable : true,
          idKey : "menuId",
          pIdKey : "parentId",
          rootPId : 0
        },
        key : {
          name : 'menuName',
          title : 'menuName'
        }
      },
      check : {
        enable : true,
        nocheckInherit : true
      }
    };
    $(function() {
      treedata = eval('(' + treedata + ')');
      ztreeObject = $.fn.zTree.init($("#ztree"), setting, treedata);
      ztreeObject.expandAll(true);
    })
  </script>
  <script src="${ctxsta}/hongru/js/admin/admin_role_addOrModify.js?timer = 2"></script>
</myfooter>
</body>
</html>
