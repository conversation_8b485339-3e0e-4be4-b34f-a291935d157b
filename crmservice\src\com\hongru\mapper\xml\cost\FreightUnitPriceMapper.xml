<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.FreightUnitPriceMapper">
    
    <sql id="freightUnitPrice_sql">
        fup.[流水号] AS serialNumber,
        fup.[年度] AS year,
        fup.[地区] AS area,
        fup.[运费] AS freight,
        fup.[创建人姓名] AS creatorName,
        fup.[创建时间] AS createdTime,
        fup.[更新人姓名] AS updaterName,
        fup.[更新时间] AS updatedTime
    </sql>

    <select id="listFreightUnitPricePage" resultType="com.hongru.entity.cost.FreightUnitPrice">
        SELECT
        <include refid="freightUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[运费单价表] fup
        <where>
            <if test="year != null and year != ''">
                AND fup.[年度] = #{year}
            </if>
        </where>
        ORDER BY fup.[地区]
        <if test="pageInfo != null and pageInfo.limit != null">
            OFFSET #{pageInfo.offset} ROWS
            FETCH NEXT #{pageInfo.limit} ROWS ONLY
        </if>
    </select>

    <select id="listFreightUnitPricePageCount" resultType="int">
        SELECT COUNT(1)
        FROM [CostPrice].[dbo].[运费单价表] fup
        <where>
            <if test="year != null and year != ''">
                AND fup.[年度] = #{year}
            </if>
        </where>
    </select>

    <select id="selectBySerialNumber" resultType="com.hongru.entity.cost.FreightUnitPrice">
        SELECT
        <include refid="freightUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[运费单价表] fup
        WHERE fup.[流水号] = #{serialNumber}
    </select>

    <insert id="insertFreightUnitPrice" parameterType="com.hongru.entity.cost.FreightUnitPrice">
        INSERT INTO [CostPrice].[dbo].[运费单价表]
        (
            [年度],
            [地区],
            [运费],
            [创建人姓名],
            [创建时间],
            [更新人姓名],
            [更新时间]
        )
        VALUES
        (
            #{freightUnitPrice.year},
            #{freightUnitPrice.area},
            #{freightUnitPrice.freight},
            #{freightUnitPrice.creatorName},
            #{freightUnitPrice.createdTime},
            #{freightUnitPrice.updaterName},
            #{freightUnitPrice.updatedTime}
        )
    </insert>

    <update id="updateFreightUnitPrice" parameterType="com.hongru.entity.cost.FreightUnitPrice">
        UPDATE [CostPrice].[dbo].[运费单价表]
        SET
            [地区] = #{freightUnitPrice.area},
            [运费] = #{freightUnitPrice.freight},
            [更新人姓名] = #{freightUnitPrice.updaterName},
            [更新时间] = #{freightUnitPrice.updatedTime}
        WHERE [流水号] = #{freightUnitPrice.serialNumber}
    </update>

    <delete id="deleteByYear">
        DELETE FROM [CostPrice].[dbo].[运费单价表] 
        WHERE [年度] = #{year}
    </delete>

    <select id="selectFreightUnitPriceByYear" resultType="com.hongru.entity.cost.FreightUnitPrice">
        SELECT
        <include refid="freightUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[运费单价表] fup
        WHERE fup.[年度] = #{year}
        ORDER BY fup.[地区]
    </select>

</mapper>
