<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.system.LogMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.hongru.entity.system.Log">
		<id column="log_id" property="logId" />
		<result column="user_id" property="userId" />
		<result column="create_time" property="createTime" />
		<result column="spend_time" property="spendTime" />
		<result column="method" property="method" />
		<result column="user_agent" property="userAgent" />
		<result column="user_ip" property="userIp" />
		<result column="opt_content" property="optContent" />
		<result column="url" property="url" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        log_id AS logId, user_id AS userId, create_time AS createTime, spend_time AS spendTime, method, user_agent AS userAgent, user_ip AS userIp, opt_content AS optContent, url
    </sql>

	<delete id="deleteLogByParam">
		DELETE
		FROM
		hr_system_log
		<where>
			<if test="logIds != null">
				AND log_id in
				<foreach collection="logIds" index="index" item="logId" open="(" separator="," close=")">
					#{logId }
				</foreach>
			</if>
		</where>
	</delete>

    <!-- 根据分页信息/搜索内容查找日志记录列表 -->
	<sql id="listByPage_where">
		<if test="search != null">
			AND (
			method LIKE  #{search}
			OR user_agent LIKE  #{search}
			OR user_ip LIKE  #{search}
			OR opt_content LIKE  #{search}
			OR url LIKE  #{search}
			)
		</if>
	</sql>
    <select id="listByPage" resultType="com.hongru.entity.system.Log">
    	SELECT
      		<include refid="Base_Column_List" />
      	FROM
      		hr_system_log
		<where>
			<include refid="listByPage_where"></include>
		</where>
		ORDER BY create_time DESC,log_id DESC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listLogByParam" resultType="com.hongru.entity.system.Log">
		SELECT
		<include refid="Base_Column_List" />
		FROM
		hr_system_log
		<where>
			<if test="logIds != null">
				AND log_id in
				<foreach collection="logIds" index="index" item="logId" open="(" separator="," close=")">
					#{logId }
				</foreach>
			</if>
		</where>
	</select>
	<select id="listByPageCount" resultType="integer">
		SELECT
			COUNT(1)
		FROM
			hr_system_log
		<where>
			<include refid="listByPage_where"></include>
		</where>
	</select>
</mapper>
