<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ page import="com.hongru.common.util.Constants" %>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <style>
    /*设置数据表表头字体*/
    .layui-table th .layui-table-cell{
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="ibox-content">
    <div class="layui-collapse">
      <div class="layui-colla-item">
        <h2 class="layui-colla-title">查询条件（单击此处展开/收起筛选条件）</h2>
        <div class="layui-colla-content">
          <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
            <div class="layui-form-item">
              <div class="layui-inline layui-col-md4">
                <label class="layui-form-label">搜索条件：</label>
                <div class="layui-input-block">
                  <input class="layui-input" type="text" style="display: inline;" placeholder="搜索条件" name="searchStr" />
                </div>
              </div>
              <div class="layui-inline layui-col-md4"></div>
              <div class="layui-inline layui-col-md4 hr-div-btn">
                <button type="button" id="btn_query" onclick="search();" class="layui-btn"><i class="layui-icon layui-icon-search"></i>检索</button>
                <button type="reset" class="layui-btn layui-btn-primary" id="btn-resert"><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <table class="layui-hide" id="demo" lay-filter="test"></table>

    <script type="text/html" id="barDemo">
    </script>

    <script type="text/html" id="toolbarDemo">
      <div class="layui-btn-container">
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
        <button class="layui-btn layui-btn-sm" lay-event="daoChuXuanZhong"><i class="layui-icon layui-icon-download-circle"></i>导出选中</button>
        <button class="layui-btn layui-btn-sm" lay-event="daoChuQuanBu"><i class="layui-icon layui-icon-download-circle"></i>导出全部</button>
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="shanChuXuanZhong"><i class="layui-icon layui-icon-delete"></i>删除选中</button>
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="shanChuQuanBu"><i class="layui-icon layui-icon-delete"></i>删除全部</button>
      </div>
    </script>
  </div>
<myfooter>
  <script src="${ctxsta}/hongru/js/system/system_log_list.js?time=4"></script>
</myfooter>
</body>
</html>