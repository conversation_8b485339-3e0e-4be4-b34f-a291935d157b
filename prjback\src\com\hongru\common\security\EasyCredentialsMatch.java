package com.hongru.common.security;

import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.credential.HashedCredentialsMatcher;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheManager;

import java.util.concurrent.atomic.AtomicInteger;

/**
* 自定义登录认证方案
* <AUTHOR>
* @create 2023/5/30 17:30
*/
//参考地址：https://blog.csdn.net/weixin_40816738/article/details/120904486
public class EasyCredentialsMatch extends HashedCredentialsMatcher {
	private Cache<String, AtomicInteger> passwordRetryCache;
	public EasyCredentialsMatch(CacheManager cacheManager) {
		passwordRetryCache = cacheManager.getCache("passwordRetryCache");
	}

	/**
	 * 重写方法
	 * 区分 密码和非密码登录
	 * 此次无需记录登录次数 详情看SysPasswordService
	 */
	@Override
	public boolean doCredentialsMatch(AuthenticationToken token, AuthenticationInfo info) {
		EasyUsernameToken easyUsernameToken = (EasyUsernameToken) token;

		//免密登录,不验证密码
		if (ShiroApproveLoginType.NOPASSWD.equals(easyUsernameToken.getType())) {
			return true;
		}

		//密码登录
//		Object tokenHashedCredentials = hashProvidedCredentials(token, info);
//		Object accountCredentials = getCredentials(info);
//		return equals(tokenHashedCredentials, accountCredentials);

		//原RetryLimitHashedCredentialsMatcher中密码验证的逻辑
		String username = (String) token.getPrincipal();
		// retry count + 1
		AtomicInteger retryCount = passwordRetryCache.get(username);
		if (retryCount == null) {
			retryCount = new AtomicInteger(0);
			passwordRetryCache.put(username, retryCount);
		}
//		if (retryCount.incrementAndGet() > 5) {
//			// if retry count > 5 throw
//			throw new ExcessiveAttemptsException();
//		}

		boolean matches = super.doCredentialsMatch(token, info);
		if (matches) {
			// clear retry count
			passwordRetryCache.remove(username);
		}
		return matches;
	}
}