package com.hongru.entity.stat;

public class StatFlag {

	/* dhFlag */
	protected Boolean dhFlag;
	/* dmFlag */
	protected Boolean dmFlag;
	/* emFlag */
	protected Boolean emFlag;
	/* efFlag */
	protected Boolean efFlag;
	/* ef09Flag */
	protected Boolean ef09Flag;
	/* erFlag */
	protected Boolean erFlag;
	/* ehFlag */
	protected Boolean ehFlag;
	/* ufFlag */
	protected Boolean ufFlag;

	public Boolean getDhFlag() {
		return dhFlag;
	}

	public void setDhFlag(Boolean dhFlag) {
		this.dhFlag = dhFlag;
	}

	public Boolean getDmFlag() {
		return dmFlag;
	}

	public void setDmFlag(Boolean dmFlag) {
		this.dmFlag = dmFlag;
	}

	public Boolean getEmFlag() {
		return emFlag;
	}

	public void setEmFlag(Boolean emFlag) {
		this.emFlag = emFlag;
	}

	public Boolean getEfFlag() {
		return efFlag;
	}

	public void setEfFlag(Boolean efFlag) {
		this.efFlag = efFlag;
	}

	public Boolean getEf09Flag() {
		return ef09Flag;
	}

	public void setEf09Flag(Boolean ef09Flag) {
		this.ef09Flag = ef09Flag;
	}

	public Boolean getErFlag() {
		return erFlag;
	}

	public void setErFlag(Boolean erFlag) {
		this.erFlag = erFlag;
	}

	public Boolean getEhFlag() {
		return ehFlag;
	}

	public void setEhFlag(Boolean ehFlag) {
		this.ehFlag = ehFlag;
	}

	public Boolean getUfFlag() {
		return ufFlag;
	}

	public void setUfFlag(Boolean ufFlag) {
		this.ufFlag = ufFlag;
	}

	public StatFlag(Boolean dhFlag, Boolean dmFlag, Boolean emFlag, Boolean efFlag, Boolean ef09Flag, Boolean erFlag, Boolean ehFlag, Boolean ufFlag) {
		this.dhFlag = dhFlag;
		this.dmFlag = dmFlag;
		this.emFlag = emFlag;
		this.efFlag = efFlag;
		this.ef09Flag = ef09Flag;
		this.erFlag = erFlag;
		this.ehFlag = ehFlag;
		this.ufFlag = ufFlag;
	}

	@Override
	public String toString() {
		return "StatFlag{" +
				"dhFlag=" + dhFlag +
				", dmFlag=" + dmFlag +
				", emFlag=" + emFlag +
				", efFlag=" + efFlag +
				", ef09Flag=" + ef09Flag +
				", erFlag=" + erFlag +
				", ehFlag=" + ehFlag +
				", ufFlag=" + ufFlag +
				'}';
	}
}