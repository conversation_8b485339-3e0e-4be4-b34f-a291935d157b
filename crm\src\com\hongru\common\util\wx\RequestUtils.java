package com.hongru.common.util.wx;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.util.WebUtils;

public class RequestUtils
{
	public static Cookie getCookie(HttpServletRequest request, String cookieName)
	{
		return WebUtils.getCookie(request, cookieName);
	}
	
	public static void removeCookie(HttpServletResponse response, String cookieName)
	{
		Cookie cookie = new Cookie(cookieName, null);
		cookie.setMaxAge(30*24*60*60);
		cookie.setPath("/");
		response.addCookie(cookie);
	}
	
	public static String getRemoteAddr(HttpServletRequest request)
	{
		
		//对于nginx 适用
		String ip = request.getHeader("x-real-ip");
		
		
		if(ip == null || ip.equals(""))
		{
			ip = request.getRemoteAddr();
		}
		return ip ;
	}
	
	/*zhaochunjiao 2012-09-20 新增以下代码  start*/
	 /**
    *  新增 Cookie
    *
    *  @param  response
    *  @param  name
    *            Cookie 的名字
    *  @param  value
    *            Cookie 的值
    *  @param  maxAge
    *            Cookie 的存活时间
    */
    public static void  addCookie(HttpServletResponse response, String name,
          String value,  int  maxAge) {
      Cookie cookie =  new  Cookie(name, value);
       if  (maxAge > 0) { 
       	cookie.setMaxAge(maxAge);
       }
       
       cookie.setPath("/");
       //  新增到客户端
      response.addCookie(cookie);
   }
    
    /**
    *  取出硬盘中所有的 Cookie
    *
    *  @param  request
    *  @return
    */    
   public static  Map<String, Cookie> getAllCookies(HttpServletRequest request) {
      Map<String, Cookie> cookie_map =  new  HashMap<String, Cookie>();
      Cookie[] cookies = request.getCookies();
       // 如果存在 cookie, 就存入 Map
       if (cookies!= null ){
           for  ( int  i = 0; i < cookies.length ; i++) {
             cookie_map.put(cookies[i].getName(), cookies[i]);
          }
      }
       return  cookie_map;
   }
    
    /**
    *  在 Cookie 中通过 Cookie 名称获得Session中的 SessionId
    *  @param  request
    *  @param  name
    *  @return
    */
    public static  String getSessionIdByNameInCookie(HttpServletRequest request,String name){
      Map<String, Cookie> cookie_map=getAllCookies (request);
       if (cookie_map.containsKey(name)){
          Cookie cookie = cookie_map.get(name);
           return  cookie.getValue();
      }
       return null ;
   }
    
    public static String getClientIP(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (null == ip || 0 == ip.length() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (null == ip || 0 == ip.length() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (null == ip || 0 == ip.length() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("X-Real-IP");
		}
		if (null == ip || 0 == ip.length() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}
    
   /*zhaochunjiao 2012-09-20 新增以下代码  end*/
}
