package com.hongru.entity.stat;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
* 类名称：BoundQuantity
* 类描述：出入库量
* 创建人：hongru   
* 创建时间：2017年4月1日 下午4:30:31   
*
 */
@SuppressWarnings("serial")
@TableName("入库表")//CostPrice
public class BoundQuantity implements Serializable{

	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int statId;
	/* 年月 */
	protected String yearMonth;
	/** EM入库量 */
	private BigDecimal emInboundQuantity;
	/** EF入库量 */
	private BigDecimal efInboundQuantity;
	/** EF09入库量 */
	private BigDecimal ef09InboundQuantity;
	/** ER入库量 */
	private BigDecimal erInboundQuantity;
	/** EH日立外入库量 */
	private BigDecimal ehInboundQuantity1;
	/** EH入库量 */
	private BigDecimal ehInboundQuantity2;
	/** UF太线入库量 */
	private BigDecimal uftxInboundQuantity;
	/** UF细线入库量 */
	private BigDecimal ufxxInboundQuantity;
	/** EM出库量 */
	private BigDecimal emOutboundQuantity;
	/** EF出库量 */
	private BigDecimal efOutboundQuantity;
	/** EF09出库量 */
	private BigDecimal ef09OutboundQuantity;
	/** ER出库量 */
	private BigDecimal erOutboundQuantity;
	/** EH日立外出库量 */
	private BigDecimal ehOutboundQuantity1;
	/** EH出库量 */
	private BigDecimal ehOutboundQuantity2;
	/** UF太线出库量 */
	private BigDecimal uftxOutboundQuantity;
	/** UF细线出库量 */
	private BigDecimal ufxxOutboundQuantity;


	public BoundQuantity(int statId, String yearMonth, BigDecimal emInboundQuantity, BigDecimal efInboundQuantity, BigDecimal ef09InboundQuantity, BigDecimal erInboundQuantity, BigDecimal ehInboundQuantity1, BigDecimal ehInboundQuantity2, BigDecimal uftxInboundQuantity, BigDecimal ufxxInboundQuantity, BigDecimal emOutboundQuantity, BigDecimal efOutboundQuantity, BigDecimal ef09OutboundQuantity, BigDecimal erOutboundQuantity, BigDecimal ehOutboundQuantity1, BigDecimal ehOutboundQuantity2, BigDecimal uftxOutboundQuantity, BigDecimal ufxxOutboundQuantity) {
		this.statId = statId;
		this.yearMonth = yearMonth;
		this.emInboundQuantity = emInboundQuantity;
		this.efInboundQuantity = efInboundQuantity;
		this.ef09InboundQuantity = ef09InboundQuantity;
		this.erInboundQuantity = erInboundQuantity;
		this.ehInboundQuantity1 = ehInboundQuantity1;
		this.ehInboundQuantity2 = ehInboundQuantity2;
		this.uftxInboundQuantity = uftxInboundQuantity;
		this.ufxxInboundQuantity = ufxxInboundQuantity;
		this.emOutboundQuantity = emOutboundQuantity;
		this.efOutboundQuantity = efOutboundQuantity;
		this.ef09OutboundQuantity = ef09OutboundQuantity;
		this.erOutboundQuantity = erOutboundQuantity;
		this.ehOutboundQuantity1 = ehOutboundQuantity1;
		this.ehOutboundQuantity2 = ehOutboundQuantity2;
		this.uftxOutboundQuantity = uftxOutboundQuantity;
		this.ufxxOutboundQuantity = ufxxOutboundQuantity;
	}

	public int getStatId() {
		return statId;
	}

	public void setStatId(int statId) {
		this.statId = statId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public BigDecimal getEmInboundQuantity() {
		return emInboundQuantity;
	}

	public void setEmInboundQuantity(BigDecimal emInboundQuantity) {
		this.emInboundQuantity = emInboundQuantity;
	}

	public BigDecimal getEfInboundQuantity() {
		return efInboundQuantity;
	}

	public void setEfInboundQuantity(BigDecimal efInboundQuantity) {
		this.efInboundQuantity = efInboundQuantity;
	}

	public BigDecimal getEf09InboundQuantity() {
		return ef09InboundQuantity;
	}

	public void setEf09InboundQuantity(BigDecimal ef09InboundQuantity) {
		this.ef09InboundQuantity = ef09InboundQuantity;
	}

	public BigDecimal getErInboundQuantity() {
		return erInboundQuantity;
	}

	public void setErInboundQuantity(BigDecimal erInboundQuantity) {
		this.erInboundQuantity = erInboundQuantity;
	}

	public BigDecimal getEhInboundQuantity1() {
		return ehInboundQuantity1;
	}

	public void setEhInboundQuantity1(BigDecimal ehInboundQuantity1) {
		this.ehInboundQuantity1 = ehInboundQuantity1;
	}

	public BigDecimal getEhInboundQuantity2() {
		return ehInboundQuantity2;
	}

	public void setEhInboundQuantity2(BigDecimal ehInboundQuantity2) {
		this.ehInboundQuantity2 = ehInboundQuantity2;
	}

	public BigDecimal getUftxInboundQuantity() {
		return uftxInboundQuantity;
	}

	public void setUftxInboundQuantity(BigDecimal uftxInboundQuantity) {
		this.uftxInboundQuantity = uftxInboundQuantity;
	}

	public BigDecimal getUfxxInboundQuantity() {
		return ufxxInboundQuantity;
	}

	public void setUfxxInboundQuantity(BigDecimal ufxxInboundQuantity) {
		this.ufxxInboundQuantity = ufxxInboundQuantity;
	}

	public BigDecimal getEmOutboundQuantity() {
		return emOutboundQuantity;
	}

	public void setEmOutboundQuantity(BigDecimal emOutboundQuantity) {
		this.emOutboundQuantity = emOutboundQuantity;
	}

	public BigDecimal getEfOutboundQuantity() {
		return efOutboundQuantity;
	}

	public void setEfOutboundQuantity(BigDecimal efOutboundQuantity) {
		this.efOutboundQuantity = efOutboundQuantity;
	}

	public BigDecimal getEf09OutboundQuantity() {
		return ef09OutboundQuantity;
	}

	public void setEf09OutboundQuantity(BigDecimal ef09OutboundQuantity) {
		this.ef09OutboundQuantity = ef09OutboundQuantity;
	}

	public BigDecimal getErOutboundQuantity() {
		return erOutboundQuantity;
	}

	public void setErOutboundQuantity(BigDecimal erOutboundQuantity) {
		this.erOutboundQuantity = erOutboundQuantity;
	}

	public BigDecimal getEhOutboundQuantity1() {
		return ehOutboundQuantity1;
	}

	public void setEhOutboundQuantity1(BigDecimal ehOutboundQuantity1) {
		this.ehOutboundQuantity1 = ehOutboundQuantity1;
	}

	public BigDecimal getEhOutboundQuantity2() {
		return ehOutboundQuantity2;
	}

	public void setEhOutboundQuantity2(BigDecimal ehOutboundQuantity2) {
		this.ehOutboundQuantity2 = ehOutboundQuantity2;
	}

	public BigDecimal getUftxOutboundQuantity() {
		return uftxOutboundQuantity;
	}

	public void setUftxOutboundQuantity(BigDecimal uftxOutboundQuantity) {
		this.uftxOutboundQuantity = uftxOutboundQuantity;
	}

	public BigDecimal getUfxxOutboundQuantity() {
		return ufxxOutboundQuantity;
	}

	public void setUfxxOutboundQuantity(BigDecimal ufxxOutboundQuantity) {
		this.ufxxOutboundQuantity = ufxxOutboundQuantity;
	}
}
