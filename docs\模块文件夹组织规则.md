# 模块文件夹组织规则

## 概述
按照模块功能创建不同的文件夹存放相关画面，确保代码结构清晰、易于维护。

## 文件夹结构规则

### 1. cost - 月度相关画面
**用途：** 存放月度相关的功能画面
**包含内容：**
- 月度成本计算
- 月度数据录入
- 月度成本分析
- 其他月度相关功能

**文件路径：**
- JSP: `prjback/WebContent/WEB-INF/views/modules/cost/`
- JS: `prjback/WebContent/static/hongru/js/cost/`
- Controller: `prjback/src/com/hongru/controller/cost/`

### 2. setting - 月度部门设定相关画面
**用途：** 存放月度部门设定等相关的功能画面
**包含内容：**
- 部门设定
- 月度配置管理
- 部门参数设置
- 月度基础数据维护

**文件路径：**
- JSP: `prjback/WebContent/WEB-INF/views/modules/setting/`
- JS: `prjback/WebContent/static/hongru/js/setting/`
- Controller: `prjback/src/com/hongru/controller/setting/`

### 3. stat - 月度报表
**用途：** 存放月度报表相关画面
**包含内容：**
- 月度统计报表
- 数据分析图表
- 报表导出功能
- 统计数据查询

**文件路径：**
- JSP: `prjback/WebContent/WEB-INF/views/modules/stat/`
- JS: `prjback/WebContent/static/hongru/js/stat/`
- Controller: `prjback/src/com/hongru/controller/stat/`

### 4. yearRevise - 年度查询相关画面
**用途：** 存放年度查询的相关画面
**包含内容：**
- 年度数据查询
- 年度数据修订
- 历史数据查看
- 年度数据对比

**文件路径：**
- JSP: `prjback/WebContent/WEB-INF/views/modules/yearRevise/`
- JS: `prjback/WebContent/static/hongru/js/yearRevise/`
- Controller: `prjback/src/com/hongru/controller/yearRevise/`

### 5. yearCostCalc - 成本计算相关画面
**用途：** 成本计算相关画面
**包含内容：**
- 年度成本计算
- 成本核算逻辑
- 计算结果展示
- 成本计算参数设置

**文件路径：**
- JSP: `prjback/WebContent/WEB-INF/views/modules/yearCostCalc/`
- JS: `prjback/WebContent/static/hongru/js/yearCostCalc/`
- Controller: `prjback/src/com/hongru/controller/yearCostCalc/`

### 6. yearCostDes - 成本设计相关画面
**用途：** 成本设计相关画面
**包含内容：**
- 成本结构设计
- 成本模型配置
- 成本方案制定
- 成本设计模板

**文件路径：**
- JSP: `prjback/WebContent/WEB-INF/views/modules/yearCostDes/`
- JS: `prjback/WebContent/static/hongru/js/yearCostDes/`
- Controller: `prjback/src/com/hongru/controller/yearCostDes/`

### 7. yearParamSet - 成本参数设定相关画面
**用途：** 成本参数设定相关画面
**包含内容：**
- 部门单价设定
- 费用项目单价设定
- 原料单价设定（导体单价、油漆单价、线盘单价）
- 运费单价设定
- 成本参数复制功能

**文件路径：**
- JSP: `prjback/WebContent/WEB-INF/views/modules/yearParamSet/`
- JS: `prjback/WebContent/static/hongru/js/yearParamSet/`
- Controller: `prjback/src/com/hongru/controller/yearParamSet/`

## 命名规范

### 文件命名规范
1. **JSP文件：** `功能名_操作类型.jsp`
   - 例：`departmentalUnitPrice_list.jsp`, `departmentalUnitPrice_add.jsp`

2. **JS文件：** `功能名_操作类型.js`
   - 例：`departmentalUnitPrice_list.js`, `departmentalUnitPrice_add.js`

3. **Controller类：** `模块名Controller.java`
   - 例：`YearParamSetController.java`, `CostController.java`

### URL路径规范
- 基础路径：`/模块名/功能名/操作/类型`
- 例：`/yearParamSet/departmentalUnitPrice/list/view`
- 例：`/yearParamSet/departmentalUnitPrice/add`

## 迁移指导原则

### 1. 文件迁移步骤
1. 创建目标文件夹结构
2. 移动JSP文件到对应的views/modules/模块名/
3. 移动JS文件到对应的static/hongru/js/模块名/
4. 移动或创建Controller到对应的controller/模块名/
5. 更新所有文件中的路径引用
6. 更新菜单配置中的href路径
7. 测试功能是否正常

### 2. 路径更新清单
- [ ] JSP文件中的JS引用路径
- [ ] JS文件中的AJAX请求路径
- [ ] JSP文件中的表单提交路径
- [ ] Controller中的视图返回路径
- [ ] Controller中的RequestMapping路径
- [ ] 菜单配置中的href路径

### 3. 版本控制
- 移动JS文件后，更新版本号避免缓存问题
- 例：`?v=2` 或 `?v=${randomVersion}`

## 注意事项

1. **保持一致性：** 所有相关文件都应该放在同一个模块文件夹下
2. **路径同步：** 移动文件后必须同步更新所有引用路径
3. **功能测试：** 移动完成后必须测试所有功能是否正常
4. **文档更新：** 及时更新相关文档和注释
5. **团队沟通：** 大规模文件移动前应与团队成员沟通

## 示例

### yearParamSet模块示例
```
prjback/
├── WebContent/
│   ├── WEB-INF/views/modules/yearParamSet/
│   │   ├── departmentalUnitPrice_list.jsp
│   │   ├── departmentalUnitPrice_add.jsp
│   │   ├── departmentalUnitPrice_edit.jsp
│   │   └── ...
│   └── static/hongru/js/yearParamSet/
│       ├── departmentalUnitPrice_list.js
│       └── ...
└── src/com/hongru/controller/yearParamSet/
    └── YearParamSetController.java
```

---

**创建日期：** 2025-01-20  
**最后更新：** 2025-01-20  
**适用版本：** 所有版本
