package com.hongru.entity.stat;

import com.baomidou.mybatisplus.annotations.TableName;

import java.math.BigDecimal;

@TableName("报表数据")//CostPrice
public class ReportFormsData {
	// 判断缺少数据用
	protected String flag;
	
	protected BoundQuantity boundQuantity;

	/******************************************电力费******************************************/
	/* 预定电力DH */
	protected BigDecimal dhDianLiPre = new BigDecimal(0);
	/* 预定电力DM */
	protected BigDecimal dmDianLiPre = new BigDecimal(0);
	/* 预定电力EM */
	protected BigDecimal emDianLiPre = new BigDecimal(0);
	/* 预定电力EF */
	protected BigDecimal efDianLiPre = new BigDecimal(0);
	/* 预定电力EF09 */
	protected BigDecimal ef09DianLiPre = new BigDecimal(0);
	/* 预定电力ER */
	protected BigDecimal erDianLiPre = new BigDecimal(0);
	/* 预定电力EH */
	protected BigDecimal ehDianLiPre = new BigDecimal(0);
	/* 预定电力UF */
	protected BigDecimal ufDianLiPre = new BigDecimal(0);

	/* EM预定电力费 */
	protected BigDecimal emDianLiPreAmount = new BigDecimal(0);
	/* EF预定电力费 */
	protected BigDecimal efDianLiPreAmount = new BigDecimal(0);
	/* EF09预定电力费 */
	protected BigDecimal ef09DianLiPreAmount = new BigDecimal(0);
	/* ER预定电力费 */
	protected BigDecimal erDianLiPreAmount = new BigDecimal(0);
	/* EH预定电力费 */
	protected BigDecimal ehDianLiPreAmount = new BigDecimal(0);
	/* UF预定电力费 */
	protected BigDecimal ufDianLiPreAmount = new BigDecimal(0);

	/* MW预定费 */
	protected BigDecimal amountMWDianLiPre = new BigDecimal(0);
	/* EH预定费 */
	protected BigDecimal amountEHDianLiPre = new BigDecimal(0);
	/* UF预定费 */
	protected BigDecimal amountUFDianLiPre = new BigDecimal(0);


	/* 实际电力合计 */
	protected BigDecimal actDianLiAmount = new BigDecimal(0);
	/* 实际电费合计 */
	protected BigDecimal actDianFeiAmount = new BigDecimal(0);
	/* 水费合计 */
	protected BigDecimal actWaterCostAmount = new BigDecimal(0);

	/* 实际电力DH */
	protected BigDecimal dhDianLiAct = new BigDecimal(0);
	/* 实际电力DM */
	protected BigDecimal dmDianLiAct = new BigDecimal(0);
	/* 实际电力EM */
	protected BigDecimal emDianLiAct = new BigDecimal(0);
	/* 实际电力EF */
	protected BigDecimal efDianLiAct = new BigDecimal(0);
	/* 实际电力EF09 */
	protected BigDecimal ef09DianLiAct = new BigDecimal(0);
	/* 实际电力ER */
	protected BigDecimal erDianLiAct = new BigDecimal(0);
	/* 实际电力EH */
	protected BigDecimal ehDianLiAct = new BigDecimal(0);
	/* 实际电力UF */
	protected BigDecimal ufDianLiAct = new BigDecimal(0);

	/* EM实际电力费 */
	protected BigDecimal emDianLiActAmount = new BigDecimal(0);
	/* EF实际电力费 */
	protected BigDecimal efDianLiActAmount = new BigDecimal(0);
	/* EF09实际电力费 */
	protected BigDecimal ef09DianLiActAmount = new BigDecimal(0);
	/* ER实际电力费 */
	protected BigDecimal erDianLiActAmount = new BigDecimal(0);
	/* EH实际电力费 */
	protected BigDecimal ehDianLiActAmount = new BigDecimal(0);
	/* UF实际电力费 */
	protected BigDecimal ufDianLiActAmount = new BigDecimal(0);

	/* MW实际费 */
	protected BigDecimal amountMWDianLiAct = new BigDecimal(0);
	/* EH实际费 */
	protected BigDecimal amountEHDianLiAct = new BigDecimal(0);
	/* UF实际费 */
	protected BigDecimal amountUFDianLiAct = new BigDecimal(0);

	/******************************************一般社员人件费******************************************/
	/* 预定直接EM工时 */
	protected BigDecimal zjEmSheYuanPre = new BigDecimal(0);
	/* 预定直接EH工时 */
	protected BigDecimal zjEfSheYuanPre = new BigDecimal(0);
	/* 预定直接EF工时 */
	protected BigDecimal zjEf09SheYuanPre = new BigDecimal(0);
	/* 预定直接ER工时 */
	protected BigDecimal zjErSheYuanPre = new BigDecimal(0);
	/* 预定直接EH工时 */
	protected BigDecimal zjEhSheYuanPre = new BigDecimal(0);
	/* 预定直接UF工时 */
	protected BigDecimal zjUfSheYuanPre = new BigDecimal(0);

	/* 预定辅助EM工时 */
	protected BigDecimal fzEmSheYuanPre = new BigDecimal(0);
	/* 预定辅助EH工时 */
	protected BigDecimal fzEfSheYuanPre = new BigDecimal(0);
	/* 预定辅助EF工时 */
	protected BigDecimal fzEf09SheYuanPre = new BigDecimal(0);
	/* 预定辅助ER工时 */
	protected BigDecimal fzErSheYuanPre = new BigDecimal(0);
	/* 预定辅助EH工时 */
	protected BigDecimal fzEhSheYuanPre = new BigDecimal(0);
	/* 预定辅助UF工时 */
	protected BigDecimal fzUfSheYuanPre = new BigDecimal(0);

	/* 预定直接EM费用 */
	protected BigDecimal zjEmSheYuanPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEfSheYuanPreAmount = new BigDecimal(0);
	/* 预定直接EF费用 */
	protected BigDecimal zjEf09SheYuanPreAmount = new BigDecimal(0);
	/* 预定直接ER费用 */
	protected BigDecimal zjErSheYuanPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEhSheYuanPreAmount = new BigDecimal(0);
	/* 预定直接UF费用 */
	protected BigDecimal zjUfSheYuanPreAmount = new BigDecimal(0);

	/* 预定辅助EM费用 */
	protected BigDecimal fzEmSheYuanPreAmount = new BigDecimal(0);
	/* 预定辅助EH费用 */
	protected BigDecimal fzEfSheYuanPreAmount = new BigDecimal(0);
	/* 预定辅助EF费用 */
	protected BigDecimal fzEf09SheYuanPreAmount = new BigDecimal(0);
	/* 预定辅助ER费用 */
	protected BigDecimal fzErSheYuanPreAmount = new BigDecimal(0);
	/* 预定辅助EH费用 */
	protected BigDecimal fzEhSheYuanPreAmount = new BigDecimal(0);
	/* 预定辅助UF费用 */
	protected BigDecimal fzUfSheYuanPreAmount = new BigDecimal(0);

	/* MW预定费 */
	protected BigDecimal amountMWSheYuanPre = new BigDecimal(0);
	/* EH预定费 */
	protected BigDecimal amountEHSheYuanPre = new BigDecimal(0);
	/* UF预定费 */
	protected BigDecimal amountUFSheYuanPre = new BigDecimal(0);


	/* 实际直接EM工时 */
	protected BigDecimal zjEmSheYuanAct = new BigDecimal(0);
	/* 实际直接EH工时 */
	protected BigDecimal zjEfSheYuanAct = new BigDecimal(0);
	/* 实际直接EF工时 */
	protected BigDecimal zjEf09SheYuanAct = new BigDecimal(0);
	/* 实际直接ER工时 */
	protected BigDecimal zjErSheYuanAct = new BigDecimal(0);
	/* 实际直接EH工时 */
	protected BigDecimal zjEhSheYuanAct = new BigDecimal(0);
	/* 实际直接UF工时 */
	protected BigDecimal zjUfSheYuanAct = new BigDecimal(0);

	/* 实际辅助EM工时 */
	protected BigDecimal fzEmSheYuanAct = new BigDecimal(0);
	/* 实际辅助EH工时 */
	protected BigDecimal fzEfSheYuanAct = new BigDecimal(0);
	/* 实际辅助EF工时 */
	protected BigDecimal fzEf09SheYuanAct = new BigDecimal(0);
	/* 实际辅助ER工时 */
	protected BigDecimal fzErSheYuanAct = new BigDecimal(0);
	/* 实际辅助EH工时 */
	protected BigDecimal fzEhSheYuanAct = new BigDecimal(0);
	/* 实际辅助UF工时 */
	protected BigDecimal fzUfSheYuanAct = new BigDecimal(0);

	/* 实际辅助EM费用 */
	protected BigDecimal emSheYuanActAmount = new BigDecimal(0);
	/* 实际辅助EH费用 */
	protected BigDecimal efSheYuanActAmount = new BigDecimal(0);
	/* 实际辅助EF费用 */
	protected BigDecimal ef09SheYuanActAmount = new BigDecimal(0);
	/* 实际辅助ER费用 */
	protected BigDecimal erSheYuanActAmount = new BigDecimal(0);
	/* 实际辅助EH费用 */
	protected BigDecimal ehSheYuanActAmount = new BigDecimal(0);
	/* 实际辅助UF费用 */
	protected BigDecimal ufSheYuanActAmount = new BigDecimal(0);

	/* MW实际费 */
	protected BigDecimal amountMWSheYuanAct = new BigDecimal(0);
	/* EH实际费 */
	protected BigDecimal amountEHSheYuanAct = new BigDecimal(0);
	/* UF实际费 */
	protected BigDecimal amountUFSheYuanAct = new BigDecimal(0);
	/* 实际上传费用 */
	protected BigDecimal actUploadCostSheYuan = new BigDecimal(0);

	/******************************************临时（劳务）工人件费******************************************/
	/* 预定直接EM工时 */
	protected BigDecimal zjEmLinShiPre = new BigDecimal(0);
	/* 预定直接EF工时 */
	protected BigDecimal zjEfLinShiPre = new BigDecimal(0);
	/* 预定直接EF09工时 */
	protected BigDecimal zjEf09LinShiPre = new BigDecimal(0);
	/* 预定直接ER工时 */
	protected BigDecimal zjErLinShiPre = new BigDecimal(0);
	/* 预定直接EH工时 */
	protected BigDecimal zjEhLinShiPre = new BigDecimal(0);
	/* 预定直接UF工时 */
	protected BigDecimal zjUfLinShiPre = new BigDecimal(0);

	/* 预定辅助EM工时 */
	protected BigDecimal fzEmLinShiPre = new BigDecimal(0);
	/* 预定辅助EH工时 */
	protected BigDecimal fzEfLinShiPre = new BigDecimal(0);
	/* 预定辅助EF工时 */
	protected BigDecimal fzEf09LinShiPre = new BigDecimal(0);
	/* 预定辅助ER工时 */
	protected BigDecimal fzErLinShiPre = new BigDecimal(0);
	/* 预定辅助EH工时 */
	protected BigDecimal fzEhLinShiPre = new BigDecimal(0);
	/* 预定辅助UF工时 */
	protected BigDecimal fzUfLinShiPre = new BigDecimal(0);

	/* 预定直接EM费用 */
	protected BigDecimal zjEmLinShiPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEfLinShiPreAmount = new BigDecimal(0);
	/* 预定直接EF费用 */
	protected BigDecimal zjEf09LinShiPreAmount = new BigDecimal(0);
	/* 预定直接ER费用 */
	protected BigDecimal zjErLinShiPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEhLinShiPreAmount = new BigDecimal(0);
	/* 预定直接UF费用 */
	protected BigDecimal zjUfLinShiPreAmount = new BigDecimal(0);

	/* 预定辅助EM费用 */
	protected BigDecimal fzEmLinShiPreAmount = new BigDecimal(0);
	/* 预定辅助EH费用 */
	protected BigDecimal fzEfLinShiPreAmount = new BigDecimal(0);
	/* 预定辅助EF费用 */
	protected BigDecimal fzEf09LinShiPreAmount = new BigDecimal(0);
	/* 预定辅助ER费用 */
	protected BigDecimal fzErLinShiPreAmount = new BigDecimal(0);
	/* 预定辅助EH费用 */
	protected BigDecimal fzEhLinShiPreAmount = new BigDecimal(0);
	/* 预定辅助UF费用 */
	protected BigDecimal fzUfLinShiPreAmount = new BigDecimal(0);

	/* MW预定费 */
	protected BigDecimal amountMWLinShiPre = new BigDecimal(0);
	/* EH预定费 */
	protected BigDecimal amountEHLinShiPre = new BigDecimal(0);
	/* UF预定费 */
	protected BigDecimal amountUFLinShiPre = new BigDecimal(0);

	/* 实际直接EM工时 */
	protected BigDecimal zjEmLinShiAct = new BigDecimal(0);
	/* 实际直接EH工时 */
	protected BigDecimal zjEfLinShiAct = new BigDecimal(0);
	/* 实际直接EF工时 */
	protected BigDecimal zjEf09LinShiAct = new BigDecimal(0);
	/* 实际直接ER工时 */
	protected BigDecimal zjErLinShiAct = new BigDecimal(0);
	/* 实际直接EH工时 */
	protected BigDecimal zjEhLinShiAct = new BigDecimal(0);
	/* 实际直接UF工时 */
	protected BigDecimal zjUfLinShiAct = new BigDecimal(0);

	/* 实际辅助EM工时 */
	protected BigDecimal fzEmLinShiAct = new BigDecimal(0);
	/* 实际辅助EH工时 */
	protected BigDecimal fzEfLinShiAct = new BigDecimal(0);
	/* 实际辅助EF工时 */
	protected BigDecimal fzEf09LinShiAct = new BigDecimal(0);
	/* 实际辅助ER工时 */
	protected BigDecimal fzErLinShiAct = new BigDecimal(0);
	/* 实际辅助EH工时 */
	protected BigDecimal fzEhLinShiAct = new BigDecimal(0);
	/* 实际辅助UF工时 */
	protected BigDecimal fzUfLinShiAct = new BigDecimal(0);

	/* 实际直接EM费用 */
	protected BigDecimal emLaoWuActAmount = new BigDecimal(0);
	/* 实际直接EH费用 */
	protected BigDecimal efLaoWuActAmount = new BigDecimal(0);
	/* 实际直接EF费用 */
	protected BigDecimal ef09LaoWuActAmount = new BigDecimal(0);
	/* 实际直接ER费用 */
	protected BigDecimal erLaoWuActAmount = new BigDecimal(0);
	/* 实际直接EH费用 */
	protected BigDecimal ehLaoWuActAmount = new BigDecimal(0);
	/* 实际直接UF费用 */
	protected BigDecimal ufLaoWuActAmount = new BigDecimal(0);

	/* 实际辅助EM费用 */
	protected BigDecimal emWaiBaoActAmount = new BigDecimal(0);
	/* 实际辅助EH费用 */
	protected BigDecimal efWaiBaoActAmount = new BigDecimal(0);
	/* 实际辅助EF费用 */
	protected BigDecimal ef09WaiBaoActAmount = new BigDecimal(0);
	/* 实际辅助ER费用 */
	protected BigDecimal erWaiBaoActAmount = new BigDecimal(0);
	/* 实际辅助EH费用 */
	protected BigDecimal ehWaiBaoActAmount = new BigDecimal(0);
	/* 实际辅助UF费用 */
	protected BigDecimal ufWaiBaoActAmount = new BigDecimal(0);

	/* MW实际费 */
	protected BigDecimal amountMWLinShiAct = new BigDecimal(0);
	/* EH实际费 */
	protected BigDecimal amountEHLinShiAct = new BigDecimal(0);
	/* UF实际费 */
	protected BigDecimal amountUFLinShiAct = new BigDecimal(0);

	/******************************************保全人件费******************************************/
	/* 预定直接EM工时 */
	protected BigDecimal zjEmBaoQuanPre = new BigDecimal(0);
	/* 预定直接EH工时 */
	protected BigDecimal zjEfBaoQuanPre = new BigDecimal(0);
	/* 预定直接EF工时 */
	protected BigDecimal zjEf09BaoQuanPre = new BigDecimal(0);
	/* 预定直接ER工时 */
	protected BigDecimal zjErBaoQuanPre = new BigDecimal(0);
	/* 预定直接EH工时 */
	protected BigDecimal zjEhBaoQuanPre = new BigDecimal(0);
	/* 预定直接UF工时 */
	protected BigDecimal zjUfBaoQuanPre = new BigDecimal(0);

	/* 预定直接EM费用 */
	protected BigDecimal zjEmBaoQuanPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEfBaoQuanPreAmount = new BigDecimal(0);
	/* 预定直接EF费用 */
	protected BigDecimal zjEf09BaoQuanPreAmount = new BigDecimal(0);
	/* 预定直接ER费用 */
	protected BigDecimal zjErBaoQuanPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEhBaoQuanPreAmount = new BigDecimal(0);
	/* 预定直接UF费用 */
	protected BigDecimal zjUfBaoQuanPreAmount = new BigDecimal(0);

	/* MW预定费 */
	protected BigDecimal amountMWBaoQuanPre = new BigDecimal(0);
	/* EH预定费 */
	protected BigDecimal amountEHBaoQuanPre = new BigDecimal(0);
	/* UF预定费 */
	protected BigDecimal amountUFBaoQuanPre = new BigDecimal(0);



	/* 实际直接EM工时 */
	protected BigDecimal zjEmBaoQuanAct = new BigDecimal(0);
	/* 实际直接EH工时 */
	protected BigDecimal zjEfBaoQuanAct = new BigDecimal(0);
	/* 实际直接EF工时 */
	protected BigDecimal zjEf09BaoQuanAct = new BigDecimal(0);
	/* 实际直接ER工时 */
	protected BigDecimal zjErBaoQuanAct = new BigDecimal(0);
	/* 实际直接EH工时 */
	protected BigDecimal zjEhBaoQuanAct = new BigDecimal(0);
	/* 实际直接UF工时 */
	protected BigDecimal zjUfBaoQuanAct = new BigDecimal(0);

	/* 实际直接EM费用 */
	protected BigDecimal zjEmBaoQuanActAmount = new BigDecimal(0);
	/* 实际直接EH费用 */
	protected BigDecimal zjEfBaoQuanActAmount = new BigDecimal(0);
	/* 实际直接EF费用 */
	protected BigDecimal zjEf09BaoQuanActAmount = new BigDecimal(0);
	/* 实际直接ER费用 */
	protected BigDecimal zjErBaoQuanActAmount = new BigDecimal(0);
	/* 实际直接EH费用 */
	protected BigDecimal zjEhBaoQuanActAmount = new BigDecimal(0);
	/* 实际直接UF费用 */
	protected BigDecimal zjUfBaoQuanActAmount = new BigDecimal(0);

	/* MW实际费 */
	protected BigDecimal amountMWBaoQuanAct = new BigDecimal(0);
	/* EH实际费 */
	protected BigDecimal amountEHBaoQuanAct = new BigDecimal(0);
	/* UF实际费 */
	protected BigDecimal amountUFBaoQuanAct = new BigDecimal(0);

	/******************************************补修费******************************************/
	/* 预定直接EM费用 */
	protected BigDecimal zjEmBuXiuPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEfBuXiuPreAmount = new BigDecimal(0);
	/* 预定直接EF费用 */
	protected BigDecimal zjEf09BuXiuPreAmount = new BigDecimal(0);
	/* 预定直接ER费用 */
	protected BigDecimal zjErBuXiuPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEhBuXiuPreAmount = new BigDecimal(0);
	/* 预定直接UF费用 */
	protected BigDecimal zjUfBuXiuPreAmount = new BigDecimal(0);

	/* 预定辅助EM费用 */
	protected BigDecimal fzEmBuXiuPreAmount = new BigDecimal(0);
	/* 预定辅助EH费用 */
	protected BigDecimal fzEfBuXiuPreAmount = new BigDecimal(0);
	/* 预定辅助EF费用 */
	protected BigDecimal fzEf09BuXiuPreAmount = new BigDecimal(0);
	/* 预定辅助ER费用 */
	protected BigDecimal fzErBuXiuPreAmount = new BigDecimal(0);
	/* 预定辅助EH费用 */
	protected BigDecimal fzEhBuXiuPreAmount = new BigDecimal(0);
	/* 预定辅助UF费用 */
	protected BigDecimal fzUfBuXiuPreAmount = new BigDecimal(0);

	/* MW预定费 */
	protected BigDecimal amountMWBuXiuPre = new BigDecimal(0);
	/* EH预定费 */
	protected BigDecimal amountEHBuXiuPre = new BigDecimal(0);
	/* UF预定费 */
	protected BigDecimal amountUFBuXiuPre = new BigDecimal(0);


	/* 实际直接EM费用 */
	protected BigDecimal zjEmBuXiuActAmount = new BigDecimal(0);
	/* 实际直接EH费用 */
	protected BigDecimal zjEfBuXiuActAmount = new BigDecimal(0);
	/* 实际直接EF费用 */
	protected BigDecimal zjEf09BuXiuActAmount = new BigDecimal(0);
	/* 实际直接ER费用 */
	protected BigDecimal zjErBuXiuActAmount = new BigDecimal(0);
	/* 实际直接EH费用 */
	protected BigDecimal zjEhBuXiuActAmount = new BigDecimal(0);
	/* 实际直接UF费用 */
	protected BigDecimal zjUfBuXiuActAmount = new BigDecimal(0);

	/* 实际EM费用 */
	protected BigDecimal emBuXiuActAmount = new BigDecimal(0);
	/* 实际EH费用 */
	protected BigDecimal efBuXiuActAmount = new BigDecimal(0);
	/* 实际EF费用 */
	protected BigDecimal ef09BuXiuActAmount = new BigDecimal(0);
	/* 实际ER费用 */
	protected BigDecimal erBuXiuActAmount = new BigDecimal(0);
	/* 实际EH费用 */
	protected BigDecimal ehBuXiuActAmount = new BigDecimal(0);
	/* 实际UF费用 */
	protected BigDecimal ufBuXiuActAmount = new BigDecimal(0);

	/* MW实际费 */
	protected BigDecimal amountzjMWBuXiuAct = new BigDecimal(0);
	/* EH实际费 */
	protected BigDecimal amountzjEHBuXiuAct = new BigDecimal(0);
	/* UF实际费 */
	protected BigDecimal amountzjUFBuXiuAct = new BigDecimal(0);
	/* MW实际费 */
	protected BigDecimal amountMWBuXiuAct = new BigDecimal(0);
	/* EH实际费 */
	protected BigDecimal amountEHBuXiuAct = new BigDecimal(0);
	/* UF实际费 */
	protected BigDecimal amountUFBuXiuAct = new BigDecimal(0);

	/******************************************辅材费******************************************/
	/* 预定直接EM费用 */
	protected BigDecimal zjEmFuCaiPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEfFuCaiPreAmount = new BigDecimal(0);
	/* 预定直接EF费用 */
	protected BigDecimal zjEf09FuCaiPreAmount = new BigDecimal(0);
	/* 预定直接ER费用 */
	protected BigDecimal zjErFuCaiPreAmount = new BigDecimal(0);
	/* 预定直接EH费用 */
	protected BigDecimal zjEhFuCaiPreAmount = new BigDecimal(0);
	/* 预定直接UF费用 */
	protected BigDecimal zjUfFuCaiPreAmount = new BigDecimal(0);

	/* 预定辅助EM费用 */
	protected BigDecimal fzEmFuCaiPreAmount = new BigDecimal(0);
	/* 预定辅助EH费用 */
	protected BigDecimal fzEfFuCaiPreAmount = new BigDecimal(0);
	/* 预定辅助EF费用 */
	protected BigDecimal fzEf09FuCaiPreAmount = new BigDecimal(0);
	/* 预定辅助ER费用 */
	protected BigDecimal fzErFuCaiPreAmount = new BigDecimal(0);
	/* 预定辅助EH费用 */
	protected BigDecimal fzEhFuCaiPreAmount = new BigDecimal(0);
	/* 预定辅助UF费用 */
	protected BigDecimal fzUfFuCaiPreAmount = new BigDecimal(0);

	/* MW预定费 */
	protected BigDecimal amountMWFuCaiPre = new BigDecimal(0);
	/* EH预定费 */
	protected BigDecimal amountEHFuCaiPre = new BigDecimal(0);
	/* UF预定费 */
	protected BigDecimal amountUFFuCaiPre = new BigDecimal(0);

	/* 实际直接EM费用 */
	protected BigDecimal zjEmFuCaiActAmount = new BigDecimal(0);
	/* 实际直接EH费用 */
	protected BigDecimal zjEfFuCaiActAmount = new BigDecimal(0);
	/* 实际直接EF费用 */
	protected BigDecimal zjEf09FuCaiActAmount = new BigDecimal(0);
	/* 实际直接ER费用 */
	protected BigDecimal zjErFuCaiActAmount = new BigDecimal(0);
	/* 实际直接EH费用 */
	protected BigDecimal zjEhFuCaiActAmount = new BigDecimal(0);
	/* 实际直接UF费用 */
	protected BigDecimal zjUfFuCaiActAmount = new BigDecimal(0);

	/* 实际辅助EM费用 */
	protected BigDecimal emFuCaiActAmount = new BigDecimal(0);
	/* 实际辅助EH费用 */
	protected BigDecimal efFuCaiActAmount = new BigDecimal(0);
	/* 实际辅助EF费用 */
	protected BigDecimal ef09FuCaiActAmount = new BigDecimal(0);
	/* 实际辅助ER费用 */
	protected BigDecimal erFuCaiActAmount = new BigDecimal(0);
	/* 实际辅助EH费用 */
	protected BigDecimal ehFuCaiActAmount = new BigDecimal(0);
	/* 实际辅助UF费用 */
	protected BigDecimal ufFuCaiActAmount = new BigDecimal(0);

	/* MW实际费 */
	protected BigDecimal amountzjMWFuCaiAct = new BigDecimal(0);
	/* EH实际费 */
	protected BigDecimal amountzjEHFuCaiAct = new BigDecimal(0);
	/* UF实际费 */
	protected BigDecimal amountzjUFFuCaiAct = new BigDecimal(0);
	/* MW实际费 */
	protected BigDecimal amountMWFuCaiAct = new BigDecimal(0);
	/* EH实际费 */
	protected BigDecimal amountEHFuCaiAct = new BigDecimal(0);
	/* UF实际费 */
	protected BigDecimal amountUFFuCaiAct = new BigDecimal(0);

	/******************************************WR加工费******************************************/
	/* 预定WR加工EM */
	protected BigDecimal emWrPre = new BigDecimal(0);
	/* 预定WR加工EF */
	protected BigDecimal efWrPre = new BigDecimal(0);
	/* 预定WR加工EF09 */
	protected BigDecimal ef09WrPre = new BigDecimal(0);
	/* 预定WR加工ER */
	protected BigDecimal erWrPre = new BigDecimal(0);
	/* 预定WR加工EH */
	protected BigDecimal ehWrPre = new BigDecimal(0);
	/* 预定WR加工UF */
	protected BigDecimal ufWrPre = new BigDecimal(0);
	/* MW预定WR加工 */
	protected BigDecimal amountMWWrPre = new BigDecimal(0);
	/* EH预定WR加工 */
	protected BigDecimal amountEHWrPre = new BigDecimal(0);
	/* UF预定WR加工 */
	protected BigDecimal amountUFWrPre = new BigDecimal(0);

	/* 实际WR加工EM */
	protected BigDecimal emWrAct = new BigDecimal(0);
	/* 实际WR加工EF */
	protected BigDecimal efWrAct = new BigDecimal(0);
	/* 实际WR加工EF09 */
	protected BigDecimal ef09WrAct = new BigDecimal(0);
	/* 实际WR加工ER */
	protected BigDecimal erWrAct = new BigDecimal(0);
	/* 实际WR加工EH */
	protected BigDecimal ehWrAct = new BigDecimal(0);
	/* 实际WR加工UF */
	protected BigDecimal ufWrAct = new BigDecimal(0);
	/* MW实际WR加工 */
	protected BigDecimal amountMWWrAct = new BigDecimal(0);
	/* EH实际WR加工 */
	protected BigDecimal amountEHWrAct = new BigDecimal(0);
	/* UF实际WR加工 */
	protected BigDecimal amountUFWrAct = new BigDecimal(0);

	/******************************************传送费******************************************/
	/* 预定运费EM */
	protected BigDecimal emYunFeiPre = new BigDecimal(0);
	/* 预定运费EF */
	protected BigDecimal efYunFeiPre = new BigDecimal(0);
	/* 预定运费EF09 */
	protected BigDecimal ef09YunFeiPre = new BigDecimal(0);
	/* 预定运费ER */
	protected BigDecimal erYunFeiPre = new BigDecimal(0);
	/* 预定运费EH */
	protected BigDecimal ehYunFeiPre = new BigDecimal(0);
	/* 预定运费UF */
	protected BigDecimal ufYunFeiPre = new BigDecimal(0);
	/* MW预定运费 */
	protected BigDecimal amountMWYunFeiPre = new BigDecimal(0);
	/* EH预定运费 */
	protected BigDecimal amountEHYunFeiPre = new BigDecimal(0);
	/* UF预定运费 */
	protected BigDecimal amountUFYunFeiPre = new BigDecimal(0);

	/* 实际运费EM */
	protected BigDecimal emYunFeiAct = new BigDecimal(0);
	/* 实际运费EF */
	protected BigDecimal efYunFeiAct = new BigDecimal(0);
	/* 实际运费EF09 */
	protected BigDecimal ef09YunFeiAct = new BigDecimal(0);
	/* 实际运费ER */
	protected BigDecimal erYunFeiAct = new BigDecimal(0);
	/* 实际运费EH */
	protected BigDecimal ehYunFeiAct = new BigDecimal(0);
	/* 实际运费UF */
	protected BigDecimal ufYunFeiAct = new BigDecimal(0);
	/* MW实际运费 */
	protected BigDecimal amountMWYunFeiAct = new BigDecimal(0);
	/* EH实际运费 */
	protected BigDecimal amountEHYunFeiAct = new BigDecimal(0);
	/* UF实际运费 */
	protected BigDecimal amountUFYunFeiAct = new BigDecimal(0);

	/******************************************线盘回收费******************************************/
	/* 预定线盘回收EM */
	protected BigDecimal emXphsPre = new BigDecimal(0);
	/* 预定线盘回收EF */
	protected BigDecimal efXphsPre = new BigDecimal(0);
	/* 预定线盘回收EF09 */
	protected BigDecimal ef09XphsPre = new BigDecimal(0);
	/* 预定线盘回收ER */
	protected BigDecimal erXphsPre = new BigDecimal(0);
	/* 预定线盘回收EH */
	protected BigDecimal ehXphsPre = new BigDecimal(0);
	/* 预定线盘回收UF */
	protected BigDecimal ufXphsPre = new BigDecimal(0);
	/* MW预定费 */
	protected BigDecimal amountMWXphsPre = new BigDecimal(0);
	/* EH预定费 */
	protected BigDecimal amountEHXphsPre = new BigDecimal(0);
	/* UF预定费 */
	protected BigDecimal amountUFXphsPre = new BigDecimal(0);

	/* 实际线盘回收EM */
	protected BigDecimal emXphsAct = new BigDecimal(0);
	/* 实际线盘回收EF */
	protected BigDecimal efXphsAct = new BigDecimal(0);
	/* 实际线盘回收EF09 */
	protected BigDecimal ef09XphsAct = new BigDecimal(0);
	/* 实际线盘回收ER */
	protected BigDecimal erXphsAct = new BigDecimal(0);
	/* 实际线盘回收EH */
	protected BigDecimal ehXphsAct = new BigDecimal(0);
	/* 实际线盘回收UF */
	protected BigDecimal ufXphsAct = new BigDecimal(0);
	/* MW实际线盘回收 */
	protected BigDecimal amountMWXphsAct = new BigDecimal(0);
	/* EH实际线盘回收 */
	protected BigDecimal amountEHXphsAct = new BigDecimal(0);
	/* UF实际线盘回收 */
	protected BigDecimal amountUFXphsAct = new BigDecimal(0);

	/******************************************铜屑******************************************/
	/* 预定铜屑EM */
	protected BigDecimal emTongXiePre = new BigDecimal(0);
	/* 预定铜屑EF */
	protected BigDecimal efTongXiePre = new BigDecimal(0);
	/* 预定铜屑EF09 */
	protected BigDecimal ef09TongXiePre = new BigDecimal(0);
	/* 预定铜屑ER */
	protected BigDecimal erTongXiePre = new BigDecimal(0);
	/* 预定铜屑EH */
	protected BigDecimal ehTongXiePre = new BigDecimal(0);
	/* 预定铜屑UF */
	protected BigDecimal ufTongXiePre = new BigDecimal(0);
	/* MW预定铜屑 */
	protected BigDecimal amountMWTongXiePre = new BigDecimal(0);
	/* EH预定铜屑 */
	protected BigDecimal amountEHTongXiePre = new BigDecimal(0);
	/* UF预定铜屑 */
	protected BigDecimal amountUFTongXiePre = new BigDecimal(0);

	/* 实际铜屑EM */
	protected BigDecimal emTongXieAct = new BigDecimal(0);
	/* 实际铜屑EF */
	protected BigDecimal efTongXieAct = new BigDecimal(0);
	/* 实际铜屑EF09 */
	protected BigDecimal ef09TongXieAct = new BigDecimal(0);
	/* 实际铜屑ER */
	protected BigDecimal erTongXieAct = new BigDecimal(0);
	/* 实际铜屑EH */
	protected BigDecimal ehTongXieAct = new BigDecimal(0);
	/* 实际铜屑UF */
	protected BigDecimal ufTongXieAct = new BigDecimal(0);
	/* MW实际铜屑 */
	protected BigDecimal amountMWTongXieAct = new BigDecimal(0);
	/* EH实际铜屑 */
	protected BigDecimal amountEHTongXieAct = new BigDecimal(0);
	/* UF实际铜屑 */
	protected BigDecimal amountUFTongXieAct = new BigDecimal(0);

	/* 实际铜屑单价EM */
	protected BigDecimal emTongXieActPrice = new BigDecimal(0);
	/* 实际铜屑单价ER */
	protected BigDecimal erTongXieActPrice = new BigDecimal(0);
	/* 实际铜屑单价EH */
	protected BigDecimal ehTongXieActPrice = new BigDecimal(0);

	/******************************************油漆******************************************/
	/* 预定油漆EM */
	protected BigDecimal emYouQiPre = new BigDecimal(0);
	/* 预定油漆EF */
	protected BigDecimal efYouQiPre = new BigDecimal(0);
	/* 预定油漆EF09 */
	protected BigDecimal ef09YouQiPre = new BigDecimal(0);
	/* 预定油漆ER */
	protected BigDecimal erYouQiPre = new BigDecimal(0);
	/* 预定油漆EH */
	protected BigDecimal ehYouQiPre = new BigDecimal(0);
	/* 预定油漆UF */
	protected BigDecimal ufYouQiPre = new BigDecimal(0);

	/* 预定油漆EM */
	protected BigDecimal emYouQiPreAmount = new BigDecimal(0);
	/* 预定油漆EF */
	protected BigDecimal efYouQiPreAmount = new BigDecimal(0);
	/* 预定油漆EF09 */
	protected BigDecimal ef09YouQiPreAmount = new BigDecimal(0);
	/* 预定油漆ER */
	protected BigDecimal erYouQiPreAmount = new BigDecimal(0);
	/* 预定油漆EH */
	protected BigDecimal ehYouQiPreAmount = new BigDecimal(0);
	/* 预定油漆UF */
	protected BigDecimal ufYouQiPreAmount = new BigDecimal(0);

	/* MW预定油漆 */
	protected BigDecimal amountMWYouQiPre = new BigDecimal(0);
	/* EH预定油漆 */
	protected BigDecimal amountEHYouQiPre = new BigDecimal(0);
	/* UF预定油漆 */
	protected BigDecimal amountUFYouQiPre = new BigDecimal(0);

	/* 实际油漆EM */
	protected BigDecimal emYouQiAct = new BigDecimal(0);
	/* 实际油漆EF */
	protected BigDecimal efYouQiAct = new BigDecimal(0);
	/* 实际油漆EF09 */
	protected BigDecimal ef09YouQiAct = new BigDecimal(0);
	/* 实际油漆ER */
	protected BigDecimal erYouQiAct = new BigDecimal(0);
	/* 实际油漆EH */
	protected BigDecimal ehYouQiAct = new BigDecimal(0);
	/* 实际油漆UF */
	protected BigDecimal ufYouQiAct = new BigDecimal(0);

	/* 实际油漆EM */
	protected BigDecimal emYouQiActAmount = new BigDecimal(0);
	/* 实际油漆EF */
	protected BigDecimal efYouQiActAmount = new BigDecimal(0);
	/* 实际油漆EF09 */
	protected BigDecimal ef09YouQiActAmount = new BigDecimal(0);
	/* 实际油漆ER */
	protected BigDecimal erYouQiActAmount = new BigDecimal(0);
	/* 实际油漆EH */
	protected BigDecimal ehYouQiActAmount = new BigDecimal(0);
	/* 实际油漆UF */
	protected BigDecimal ufYouQiActAmount = new BigDecimal(0);

	/* MW实际油漆 */
	protected BigDecimal amountMWYouQiAct = new BigDecimal(0);
	/* EH实际油漆 */
	protected BigDecimal amountEHYouQiAct = new BigDecimal(0);
	/* UF实际油漆 */
	protected BigDecimal amountUFYouQiAct = new BigDecimal(0);

	/******************************************包装******************************************/
	/* 预定包装EM */
	protected BigDecimal emBaoZhuangPre = new BigDecimal(0);
	/* 预定包装EF */
	protected BigDecimal efBaoZhuangPre = new BigDecimal(0);
	/* 预定包装EF09 */
	protected BigDecimal ef09BaoZhuangPre = new BigDecimal(0);
	/* 预定包装ER */
	protected BigDecimal erBaoZhuangPre = new BigDecimal(0);
	/* 预定包装EH */
	protected BigDecimal ehBaoZhuangPre = new BigDecimal(0);
	/* 预定包装UF */
	protected BigDecimal ufBaoZhuangPre = new BigDecimal(0);
	/* MW预定费 */
	protected BigDecimal amountMWBaoZhuangPre = new BigDecimal(0);
	/* EH预定费 */
	protected BigDecimal amountEHBaoZhuangPre = new BigDecimal(0);
	/* UF预定费 */
	protected BigDecimal amountUFBaoZhuangPre = new BigDecimal(0);

	/* 实际包装EM */
	protected BigDecimal emBaoZhuangAct = new BigDecimal(0);
	/* 实际包装EF */
	protected BigDecimal efBaoZhuangAct = new BigDecimal(0);
	/* 实际包装EF09 */
	protected BigDecimal ef09BaoZhuangAct = new BigDecimal(0);
	/* 实际包装ER */
	protected BigDecimal erBaoZhuangAct = new BigDecimal(0);
	/* 实际包装EH */
	protected BigDecimal ehBaoZhuangAct = new BigDecimal(0);
	/* 实际包装UF */
	protected BigDecimal ufBaoZhuangAct = new BigDecimal(0);
	/* MW实际包装 */
	protected BigDecimal amountMWBaoZhuangAct = new BigDecimal(0);
	/* EH实际包装 */
	protected BigDecimal amountEHBaoZhuangAct = new BigDecimal(0);
	/* UF实际包装 */
	protected BigDecimal amountUFBaoZhuangAct = new BigDecimal(0);

	/******************************************线盘******************************************/
	/* 预定线盘EM */
	protected BigDecimal emXianPanPre = new BigDecimal(0);
	/* 预定线盘EF */
	protected BigDecimal efXianPanPre = new BigDecimal(0);
	/* 预定线盘EF09 */
	protected BigDecimal ef09XianPanPre = new BigDecimal(0);
	/* 预定线盘ER */
	protected BigDecimal erXianPanPre = new BigDecimal(0);
	/* 预定线盘EH */
	protected BigDecimal ehXianPanPre = new BigDecimal(0);
	/* 预定线盘UF */
	protected BigDecimal ufXianPanPre = new BigDecimal(0);
	/* MW预定线盘 */
	protected BigDecimal amountMWXianPanPre = new BigDecimal(0);
	/* EH预定线盘 */
	protected BigDecimal amountEHXianPanPre = new BigDecimal(0);
	/* UF预定线盘 */
	protected BigDecimal amountUFXianPanPre = new BigDecimal(0);

	/* 实际线盘EM */
	protected BigDecimal emXianPanAct = new BigDecimal(0);
	/* 实际线盘EF */
	protected BigDecimal efXianPanAct = new BigDecimal(0);
	/* 实际线盘EF09 */
	protected BigDecimal ef09XianPanAct = new BigDecimal(0);
	/* 实际线盘ER */
	protected BigDecimal erXianPanAct = new BigDecimal(0);
	/* 实际线盘EH */
	protected BigDecimal ehXianPanAct = new BigDecimal(0);
	/* 实际线盘UF */
	protected BigDecimal ufXianPanAct = new BigDecimal(0);
	/* MW实际线盘 */
	protected BigDecimal amountMWXianPanAct = new BigDecimal(0);
	/* EH实际线盘 */
	protected BigDecimal amountEHXianPanAct = new BigDecimal(0);
	/* UF实际线盘 */
	protected BigDecimal amountUFXianPanAct = new BigDecimal(0);

	/******************************************EH-天燃气費******************************************/
	/* 预定天然气量 */
	protected BigDecimal ehGasPreSH = new BigDecimal(0);
	/* 预定天然气费用 */
	protected BigDecimal ehGasPreCost = new BigDecimal(0);
	/* 预定天然气 */
	protected BigDecimal amountGasPreCost = new BigDecimal(0);

	/* 实际天然气量 */
	protected BigDecimal ehGasActSH = new BigDecimal(0);
	/* 实际天然气费用 */
	protected BigDecimal ehGasActCost = new BigDecimal(0);
	/* 实际天然气 */
	protected BigDecimal amountGasActCost = new BigDecimal(0);

	/******************************************UF-窒素費/氮气******************************************/
	/* 预定氮气量 */
	protected BigDecimal ehDanQiPreSH = new BigDecimal(0);
	/* 预定氮气费用 */
	protected BigDecimal ehDanQiPreCost = new BigDecimal(0);
	/* 预定氮气 */
	protected BigDecimal amountDanQiPreCost = new BigDecimal(0);

	/* 实际氮气量 */
	protected BigDecimal ehDanQiActSH = new BigDecimal(0);
	/* 实际氮气费用 */
	protected BigDecimal ehDanQiActCost = new BigDecimal(0);
	/* 实际氮气 */
	protected BigDecimal amountDanQiActCost = new BigDecimal(0);


	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public BigDecimal getDhDianLiPre() {
		return dhDianLiPre;
	}

	public void setDhDianLiPre(BigDecimal dhDianLiPre) {
		this.dhDianLiPre = dhDianLiPre;
	}

	public BigDecimal getDmDianLiPre() {
		return dmDianLiPre;
	}

	public void setDmDianLiPre(BigDecimal dmDianLiPre) {
		this.dmDianLiPre = dmDianLiPre;
	}

	public BigDecimal getEmDianLiPre() {
		return emDianLiPre;
	}

	public void setEmDianLiPre(BigDecimal emDianLiPre) {
		this.emDianLiPre = emDianLiPre;
	}

	public BigDecimal getEfDianLiPre() {
		return efDianLiPre;
	}

	public void setEfDianLiPre(BigDecimal efDianLiPre) {
		this.efDianLiPre = efDianLiPre;
	}

	public BigDecimal getEf09DianLiPre() {
		return ef09DianLiPre;
	}

	public void setEf09DianLiPre(BigDecimal ef09DianLiPre) {
		this.ef09DianLiPre = ef09DianLiPre;
	}

	public BigDecimal getErDianLiPre() {
		return erDianLiPre;
	}

	public void setErDianLiPre(BigDecimal erDianLiPre) {
		this.erDianLiPre = erDianLiPre;
	}

	public BigDecimal getEhDianLiPre() {
		return ehDianLiPre;
	}

	public void setEhDianLiPre(BigDecimal ehDianLiPre) {
		this.ehDianLiPre = ehDianLiPre;
	}

	public BigDecimal getUfDianLiPre() {
		return ufDianLiPre;
	}

	public void setUfDianLiPre(BigDecimal ufDianLiPre) {
		this.ufDianLiPre = ufDianLiPre;
	}

	public BigDecimal getEmDianLiPreAmount() {
		return emDianLiPreAmount;
	}

	public void setEmDianLiPreAmount(BigDecimal emDianLiPreAmount) {
		this.emDianLiPreAmount = emDianLiPreAmount;
	}

	public BigDecimal getEfDianLiPreAmount() {
		return efDianLiPreAmount;
	}

	public void setEfDianLiPreAmount(BigDecimal efDianLiPreAmount) {
		this.efDianLiPreAmount = efDianLiPreAmount;
	}

	public BigDecimal getEf09DianLiPreAmount() {
		return ef09DianLiPreAmount;
	}

	public void setEf09DianLiPreAmount(BigDecimal ef09DianLiPreAmount) {
		this.ef09DianLiPreAmount = ef09DianLiPreAmount;
	}

	public BigDecimal getErDianLiPreAmount() {
		return erDianLiPreAmount;
	}

	public void setErDianLiPreAmount(BigDecimal erDianLiPreAmount) {
		this.erDianLiPreAmount = erDianLiPreAmount;
	}

	public BigDecimal getEhDianLiPreAmount() {
		return ehDianLiPreAmount;
	}

	public void setEhDianLiPreAmount(BigDecimal ehDianLiPreAmount) {
		this.ehDianLiPreAmount = ehDianLiPreAmount;
	}

	public BigDecimal getUfDianLiPreAmount() {
		return ufDianLiPreAmount;
	}

	public void setUfDianLiPreAmount(BigDecimal ufDianLiPreAmount) {
		this.ufDianLiPreAmount = ufDianLiPreAmount;
	}

	public BigDecimal getAmountMWDianLiPre() {
		return amountMWDianLiPre;
	}

	public void setAmountMWDianLiPre(BigDecimal amountMWDianLiPre) {
		this.amountMWDianLiPre = amountMWDianLiPre;
	}

	public BigDecimal getAmountEHDianLiPre() {
		return amountEHDianLiPre;
	}

	public void setAmountEHDianLiPre(BigDecimal amountEHDianLiPre) {
		this.amountEHDianLiPre = amountEHDianLiPre;
	}

	public BigDecimal getAmountUFDianLiPre() {
		return amountUFDianLiPre;
	}

	public void setAmountUFDianLiPre(BigDecimal amountUFDianLiPre) {
		this.amountUFDianLiPre = amountUFDianLiPre;
	}

	public BigDecimal getActDianLiAmount() {
		return actDianLiAmount;
	}

	public void setActDianLiAmount(BigDecimal actDianLiAmount) {
		this.actDianLiAmount = actDianLiAmount;
	}

	public BigDecimal getActDianFeiAmount() {
		return actDianFeiAmount;
	}

	public void setActDianFeiAmount(BigDecimal actDianFeiAmount) {
		this.actDianFeiAmount = actDianFeiAmount;
	}

	public BigDecimal getActWaterCostAmount() {
		return actWaterCostAmount;
	}

	public void setActWaterCostAmount(BigDecimal actWaterCostAmount) {
		this.actWaterCostAmount = actWaterCostAmount;
	}

	public BigDecimal getDhDianLiAct() {
		return dhDianLiAct;
	}

	public void setDhDianLiAct(BigDecimal dhDianLiAct) {
		this.dhDianLiAct = dhDianLiAct;
	}

	public BigDecimal getDmDianLiAct() {
		return dmDianLiAct;
	}

	public void setDmDianLiAct(BigDecimal dmDianLiAct) {
		this.dmDianLiAct = dmDianLiAct;
	}

	public BigDecimal getEmDianLiAct() {
		return emDianLiAct;
	}

	public void setEmDianLiAct(BigDecimal emDianLiAct) {
		this.emDianLiAct = emDianLiAct;
	}

	public BigDecimal getEfDianLiAct() {
		return efDianLiAct;
	}

	public void setEfDianLiAct(BigDecimal efDianLiAct) {
		this.efDianLiAct = efDianLiAct;
	}

	public BigDecimal getEf09DianLiAct() {
		return ef09DianLiAct;
	}

	public void setEf09DianLiAct(BigDecimal ef09DianLiAct) {
		this.ef09DianLiAct = ef09DianLiAct;
	}

	public BigDecimal getErDianLiAct() {
		return erDianLiAct;
	}

	public void setErDianLiAct(BigDecimal erDianLiAct) {
		this.erDianLiAct = erDianLiAct;
	}

	public BigDecimal getEhDianLiAct() {
		return ehDianLiAct;
	}

	public void setEhDianLiAct(BigDecimal ehDianLiAct) {
		this.ehDianLiAct = ehDianLiAct;
	}

	public BigDecimal getUfDianLiAct() {
		return ufDianLiAct;
	}

	public void setUfDianLiAct(BigDecimal ufDianLiAct) {
		this.ufDianLiAct = ufDianLiAct;
	}

	public BigDecimal getEmDianLiActAmount() {
		return emDianLiActAmount;
	}

	public void setEmDianLiActAmount(BigDecimal emDianLiActAmount) {
		this.emDianLiActAmount = emDianLiActAmount;
	}

	public BigDecimal getEfDianLiActAmount() {
		return efDianLiActAmount;
	}

	public void setEfDianLiActAmount(BigDecimal efDianLiActAmount) {
		this.efDianLiActAmount = efDianLiActAmount;
	}

	public BigDecimal getEf09DianLiActAmount() {
		return ef09DianLiActAmount;
	}

	public void setEf09DianLiActAmount(BigDecimal ef09DianLiActAmount) {
		this.ef09DianLiActAmount = ef09DianLiActAmount;
	}

	public BigDecimal getErDianLiActAmount() {
		return erDianLiActAmount;
	}

	public void setErDianLiActAmount(BigDecimal erDianLiActAmount) {
		this.erDianLiActAmount = erDianLiActAmount;
	}

	public BigDecimal getEhDianLiActAmount() {
		return ehDianLiActAmount;
	}

	public void setEhDianLiActAmount(BigDecimal ehDianLiActAmount) {
		this.ehDianLiActAmount = ehDianLiActAmount;
	}

	public BigDecimal getUfDianLiActAmount() {
		return ufDianLiActAmount;
	}

	public void setUfDianLiActAmount(BigDecimal ufDianLiActAmount) {
		this.ufDianLiActAmount = ufDianLiActAmount;
	}

	public BigDecimal getAmountMWDianLiAct() {
		return amountMWDianLiAct;
	}

	public void setAmountMWDianLiAct(BigDecimal amountMWDianLiAct) {
		this.amountMWDianLiAct = amountMWDianLiAct;
	}

	public BigDecimal getAmountEHDianLiAct() {
		return amountEHDianLiAct;
	}

	public void setAmountEHDianLiAct(BigDecimal amountEHDianLiAct) {
		this.amountEHDianLiAct = amountEHDianLiAct;
	}

	public BigDecimal getAmountUFDianLiAct() {
		return amountUFDianLiAct;
	}

	public void setAmountUFDianLiAct(BigDecimal amountUFDianLiAct) {
		this.amountUFDianLiAct = amountUFDianLiAct;
	}

	public BigDecimal getZjEmSheYuanPre() {
		return zjEmSheYuanPre;
	}

	public void setZjEmSheYuanPre(BigDecimal zjEmSheYuanPre) {
		this.zjEmSheYuanPre = zjEmSheYuanPre;
	}

	public BigDecimal getZjEfSheYuanPre() {
		return zjEfSheYuanPre;
	}

	public void setZjEfSheYuanPre(BigDecimal zjEfSheYuanPre) {
		this.zjEfSheYuanPre = zjEfSheYuanPre;
	}

	public BigDecimal getZjEf09SheYuanPre() {
		return zjEf09SheYuanPre;
	}

	public void setZjEf09SheYuanPre(BigDecimal zjEf09SheYuanPre) {
		this.zjEf09SheYuanPre = zjEf09SheYuanPre;
	}

	public BigDecimal getZjErSheYuanPre() {
		return zjErSheYuanPre;
	}

	public void setZjErSheYuanPre(BigDecimal zjErSheYuanPre) {
		this.zjErSheYuanPre = zjErSheYuanPre;
	}

	public BigDecimal getZjEhSheYuanPre() {
		return zjEhSheYuanPre;
	}

	public void setZjEhSheYuanPre(BigDecimal zjEhSheYuanPre) {
		this.zjEhSheYuanPre = zjEhSheYuanPre;
	}

	public BigDecimal getZjUfSheYuanPre() {
		return zjUfSheYuanPre;
	}

	public void setZjUfSheYuanPre(BigDecimal zjUfSheYuanPre) {
		this.zjUfSheYuanPre = zjUfSheYuanPre;
	}

	public BigDecimal getFzEmSheYuanPre() {
		return fzEmSheYuanPre;
	}

	public void setFzEmSheYuanPre(BigDecimal fzEmSheYuanPre) {
		this.fzEmSheYuanPre = fzEmSheYuanPre;
	}

	public BigDecimal getFzEfSheYuanPre() {
		return fzEfSheYuanPre;
	}

	public void setFzEfSheYuanPre(BigDecimal fzEfSheYuanPre) {
		this.fzEfSheYuanPre = fzEfSheYuanPre;
	}

	public BigDecimal getFzEf09SheYuanPre() {
		return fzEf09SheYuanPre;
	}

	public void setFzEf09SheYuanPre(BigDecimal fzEf09SheYuanPre) {
		this.fzEf09SheYuanPre = fzEf09SheYuanPre;
	}

	public BigDecimal getFzErSheYuanPre() {
		return fzErSheYuanPre;
	}

	public void setFzErSheYuanPre(BigDecimal fzErSheYuanPre) {
		this.fzErSheYuanPre = fzErSheYuanPre;
	}

	public BigDecimal getFzEhSheYuanPre() {
		return fzEhSheYuanPre;
	}

	public void setFzEhSheYuanPre(BigDecimal fzEhSheYuanPre) {
		this.fzEhSheYuanPre = fzEhSheYuanPre;
	}

	public BigDecimal getFzUfSheYuanPre() {
		return fzUfSheYuanPre;
	}

	public void setFzUfSheYuanPre(BigDecimal fzUfSheYuanPre) {
		this.fzUfSheYuanPre = fzUfSheYuanPre;
	}

	public BigDecimal getZjEmSheYuanPreAmount() {
		return zjEmSheYuanPreAmount;
	}

	public void setZjEmSheYuanPreAmount(BigDecimal zjEmSheYuanPreAmount) {
		this.zjEmSheYuanPreAmount = zjEmSheYuanPreAmount;
	}

	public BigDecimal getZjEfSheYuanPreAmount() {
		return zjEfSheYuanPreAmount;
	}

	public void setZjEfSheYuanPreAmount(BigDecimal zjEfSheYuanPreAmount) {
		this.zjEfSheYuanPreAmount = zjEfSheYuanPreAmount;
	}

	public BigDecimal getZjEf09SheYuanPreAmount() {
		return zjEf09SheYuanPreAmount;
	}

	public void setZjEf09SheYuanPreAmount(BigDecimal zjEf09SheYuanPreAmount) {
		this.zjEf09SheYuanPreAmount = zjEf09SheYuanPreAmount;
	}

	public BigDecimal getZjErSheYuanPreAmount() {
		return zjErSheYuanPreAmount;
	}

	public void setZjErSheYuanPreAmount(BigDecimal zjErSheYuanPreAmount) {
		this.zjErSheYuanPreAmount = zjErSheYuanPreAmount;
	}

	public BigDecimal getZjEhSheYuanPreAmount() {
		return zjEhSheYuanPreAmount;
	}

	public void setZjEhSheYuanPreAmount(BigDecimal zjEhSheYuanPreAmount) {
		this.zjEhSheYuanPreAmount = zjEhSheYuanPreAmount;
	}

	public BigDecimal getZjUfSheYuanPreAmount() {
		return zjUfSheYuanPreAmount;
	}

	public void setZjUfSheYuanPreAmount(BigDecimal zjUfSheYuanPreAmount) {
		this.zjUfSheYuanPreAmount = zjUfSheYuanPreAmount;
	}

	public BigDecimal getFzEmSheYuanPreAmount() {
		return fzEmSheYuanPreAmount;
	}

	public void setFzEmSheYuanPreAmount(BigDecimal fzEmSheYuanPreAmount) {
		this.fzEmSheYuanPreAmount = fzEmSheYuanPreAmount;
	}

	public BigDecimal getFzEfSheYuanPreAmount() {
		return fzEfSheYuanPreAmount;
	}

	public void setFzEfSheYuanPreAmount(BigDecimal fzEfSheYuanPreAmount) {
		this.fzEfSheYuanPreAmount = fzEfSheYuanPreAmount;
	}

	public BigDecimal getFzEf09SheYuanPreAmount() {
		return fzEf09SheYuanPreAmount;
	}

	public void setFzEf09SheYuanPreAmount(BigDecimal fzEf09SheYuanPreAmount) {
		this.fzEf09SheYuanPreAmount = fzEf09SheYuanPreAmount;
	}

	public BigDecimal getFzErSheYuanPreAmount() {
		return fzErSheYuanPreAmount;
	}

	public void setFzErSheYuanPreAmount(BigDecimal fzErSheYuanPreAmount) {
		this.fzErSheYuanPreAmount = fzErSheYuanPreAmount;
	}

	public BigDecimal getFzEhSheYuanPreAmount() {
		return fzEhSheYuanPreAmount;
	}

	public void setFzEhSheYuanPreAmount(BigDecimal fzEhSheYuanPreAmount) {
		this.fzEhSheYuanPreAmount = fzEhSheYuanPreAmount;
	}

	public BigDecimal getFzUfSheYuanPreAmount() {
		return fzUfSheYuanPreAmount;
	}

	public void setFzUfSheYuanPreAmount(BigDecimal fzUfSheYuanPreAmount) {
		this.fzUfSheYuanPreAmount = fzUfSheYuanPreAmount;
	}

	public BigDecimal getAmountMWSheYuanPre() {
		return amountMWSheYuanPre;
	}

	public void setAmountMWSheYuanPre(BigDecimal amountMWSheYuanPre) {
		this.amountMWSheYuanPre = amountMWSheYuanPre;
	}

	public BigDecimal getAmountEHSheYuanPre() {
		return amountEHSheYuanPre;
	}

	public void setAmountEHSheYuanPre(BigDecimal amountEHSheYuanPre) {
		this.amountEHSheYuanPre = amountEHSheYuanPre;
	}

	public BigDecimal getAmountUFSheYuanPre() {
		return amountUFSheYuanPre;
	}

	public void setAmountUFSheYuanPre(BigDecimal amountUFSheYuanPre) {
		this.amountUFSheYuanPre = amountUFSheYuanPre;
	}

	public BigDecimal getZjEmSheYuanAct() {
		return zjEmSheYuanAct;
	}

	public void setZjEmSheYuanAct(BigDecimal zjEmSheYuanAct) {
		this.zjEmSheYuanAct = zjEmSheYuanAct;
	}

	public BigDecimal getZjEfSheYuanAct() {
		return zjEfSheYuanAct;
	}

	public void setZjEfSheYuanAct(BigDecimal zjEfSheYuanAct) {
		this.zjEfSheYuanAct = zjEfSheYuanAct;
	}

	public BigDecimal getZjEf09SheYuanAct() {
		return zjEf09SheYuanAct;
	}

	public void setZjEf09SheYuanAct(BigDecimal zjEf09SheYuanAct) {
		this.zjEf09SheYuanAct = zjEf09SheYuanAct;
	}

	public BigDecimal getZjErSheYuanAct() {
		return zjErSheYuanAct;
	}

	public void setZjErSheYuanAct(BigDecimal zjErSheYuanAct) {
		this.zjErSheYuanAct = zjErSheYuanAct;
	}

	public BigDecimal getZjEhSheYuanAct() {
		return zjEhSheYuanAct;
	}

	public void setZjEhSheYuanAct(BigDecimal zjEhSheYuanAct) {
		this.zjEhSheYuanAct = zjEhSheYuanAct;
	}

	public BigDecimal getZjUfSheYuanAct() {
		return zjUfSheYuanAct;
	}

	public void setZjUfSheYuanAct(BigDecimal zjUfSheYuanAct) {
		this.zjUfSheYuanAct = zjUfSheYuanAct;
	}

	public BigDecimal getFzEmSheYuanAct() {
		return fzEmSheYuanAct;
	}

	public void setFzEmSheYuanAct(BigDecimal fzEmSheYuanAct) {
		this.fzEmSheYuanAct = fzEmSheYuanAct;
	}

	public BigDecimal getFzEfSheYuanAct() {
		return fzEfSheYuanAct;
	}

	public void setFzEfSheYuanAct(BigDecimal fzEfSheYuanAct) {
		this.fzEfSheYuanAct = fzEfSheYuanAct;
	}

	public BigDecimal getFzEf09SheYuanAct() {
		return fzEf09SheYuanAct;
	}

	public void setFzEf09SheYuanAct(BigDecimal fzEf09SheYuanAct) {
		this.fzEf09SheYuanAct = fzEf09SheYuanAct;
	}

	public BigDecimal getFzErSheYuanAct() {
		return fzErSheYuanAct;
	}

	public void setFzErSheYuanAct(BigDecimal fzErSheYuanAct) {
		this.fzErSheYuanAct = fzErSheYuanAct;
	}

	public BigDecimal getFzEhSheYuanAct() {
		return fzEhSheYuanAct;
	}

	public void setFzEhSheYuanAct(BigDecimal fzEhSheYuanAct) {
		this.fzEhSheYuanAct = fzEhSheYuanAct;
	}

	public BigDecimal getFzUfSheYuanAct() {
		return fzUfSheYuanAct;
	}

	public void setFzUfSheYuanAct(BigDecimal fzUfSheYuanAct) {
		this.fzUfSheYuanAct = fzUfSheYuanAct;
	}

	public BigDecimal getEmSheYuanActAmount() {
		return emSheYuanActAmount;
	}

	public void setEmSheYuanActAmount(BigDecimal emSheYuanActAmount) {
		this.emSheYuanActAmount = emSheYuanActAmount;
	}

	public BigDecimal getEfSheYuanActAmount() {
		return efSheYuanActAmount;
	}

	public void setEfSheYuanActAmount(BigDecimal efSheYuanActAmount) {
		this.efSheYuanActAmount = efSheYuanActAmount;
	}

	public BigDecimal getEf09SheYuanActAmount() {
		return ef09SheYuanActAmount;
	}

	public void setEf09SheYuanActAmount(BigDecimal ef09SheYuanActAmount) {
		this.ef09SheYuanActAmount = ef09SheYuanActAmount;
	}

	public BigDecimal getErSheYuanActAmount() {
		return erSheYuanActAmount;
	}

	public void setErSheYuanActAmount(BigDecimal erSheYuanActAmount) {
		this.erSheYuanActAmount = erSheYuanActAmount;
	}

	public BigDecimal getEhSheYuanActAmount() {
		return ehSheYuanActAmount;
	}

	public void setEhSheYuanActAmount(BigDecimal ehSheYuanActAmount) {
		this.ehSheYuanActAmount = ehSheYuanActAmount;
	}

	public BigDecimal getUfSheYuanActAmount() {
		return ufSheYuanActAmount;
	}

	public void setUfSheYuanActAmount(BigDecimal ufSheYuanActAmount) {
		this.ufSheYuanActAmount = ufSheYuanActAmount;
	}

	public BigDecimal getAmountMWSheYuanAct() {
		return amountMWSheYuanAct;
	}

	public void setAmountMWSheYuanAct(BigDecimal amountMWSheYuanAct) {
		this.amountMWSheYuanAct = amountMWSheYuanAct;
	}

	public BigDecimal getAmountEHSheYuanAct() {
		return amountEHSheYuanAct;
	}

	public void setAmountEHSheYuanAct(BigDecimal amountEHSheYuanAct) {
		this.amountEHSheYuanAct = amountEHSheYuanAct;
	}

	public BigDecimal getAmountUFSheYuanAct() {
		return amountUFSheYuanAct;
	}

	public void setAmountUFSheYuanAct(BigDecimal amountUFSheYuanAct) {
		this.amountUFSheYuanAct = amountUFSheYuanAct;
	}

	public BigDecimal getZjEmLinShiPre() {
		return zjEmLinShiPre;
	}

	public void setZjEmLinShiPre(BigDecimal zjEmLinShiPre) {
		this.zjEmLinShiPre = zjEmLinShiPre;
	}

	public BigDecimal getZjEfLinShiPre() {
		return zjEfLinShiPre;
	}

	public void setZjEfLinShiPre(BigDecimal zjEfLinShiPre) {
		this.zjEfLinShiPre = zjEfLinShiPre;
	}

	public BigDecimal getZjEf09LinShiPre() {
		return zjEf09LinShiPre;
	}

	public void setZjEf09LinShiPre(BigDecimal zjEf09LinShiPre) {
		this.zjEf09LinShiPre = zjEf09LinShiPre;
	}

	public BigDecimal getZjErLinShiPre() {
		return zjErLinShiPre;
	}

	public void setZjErLinShiPre(BigDecimal zjErLinShiPre) {
		this.zjErLinShiPre = zjErLinShiPre;
	}

	public BigDecimal getZjEhLinShiPre() {
		return zjEhLinShiPre;
	}

	public void setZjEhLinShiPre(BigDecimal zjEhLinShiPre) {
		this.zjEhLinShiPre = zjEhLinShiPre;
	}

	public BigDecimal getZjUfLinShiPre() {
		return zjUfLinShiPre;
	}

	public void setZjUfLinShiPre(BigDecimal zjUfLinShiPre) {
		this.zjUfLinShiPre = zjUfLinShiPre;
	}

	public BigDecimal getFzEmLinShiPre() {
		return fzEmLinShiPre;
	}

	public void setFzEmLinShiPre(BigDecimal fzEmLinShiPre) {
		this.fzEmLinShiPre = fzEmLinShiPre;
	}

	public BigDecimal getFzEfLinShiPre() {
		return fzEfLinShiPre;
	}

	public void setFzEfLinShiPre(BigDecimal fzEfLinShiPre) {
		this.fzEfLinShiPre = fzEfLinShiPre;
	}

	public BigDecimal getFzEf09LinShiPre() {
		return fzEf09LinShiPre;
	}

	public void setFzEf09LinShiPre(BigDecimal fzEf09LinShiPre) {
		this.fzEf09LinShiPre = fzEf09LinShiPre;
	}

	public BigDecimal getFzErLinShiPre() {
		return fzErLinShiPre;
	}

	public void setFzErLinShiPre(BigDecimal fzErLinShiPre) {
		this.fzErLinShiPre = fzErLinShiPre;
	}

	public BigDecimal getFzEhLinShiPre() {
		return fzEhLinShiPre;
	}

	public void setFzEhLinShiPre(BigDecimal fzEhLinShiPre) {
		this.fzEhLinShiPre = fzEhLinShiPre;
	}

	public BigDecimal getFzUfLinShiPre() {
		return fzUfLinShiPre;
	}

	public void setFzUfLinShiPre(BigDecimal fzUfLinShiPre) {
		this.fzUfLinShiPre = fzUfLinShiPre;
	}

	public BigDecimal getZjEmLinShiPreAmount() {
		return zjEmLinShiPreAmount;
	}

	public void setZjEmLinShiPreAmount(BigDecimal zjEmLinShiPreAmount) {
		this.zjEmLinShiPreAmount = zjEmLinShiPreAmount;
	}

	public BigDecimal getZjEfLinShiPreAmount() {
		return zjEfLinShiPreAmount;
	}

	public void setZjEfLinShiPreAmount(BigDecimal zjEfLinShiPreAmount) {
		this.zjEfLinShiPreAmount = zjEfLinShiPreAmount;
	}

	public BigDecimal getZjEf09LinShiPreAmount() {
		return zjEf09LinShiPreAmount;
	}

	public void setZjEf09LinShiPreAmount(BigDecimal zjEf09LinShiPreAmount) {
		this.zjEf09LinShiPreAmount = zjEf09LinShiPreAmount;
	}

	public BigDecimal getZjErLinShiPreAmount() {
		return zjErLinShiPreAmount;
	}

	public void setZjErLinShiPreAmount(BigDecimal zjErLinShiPreAmount) {
		this.zjErLinShiPreAmount = zjErLinShiPreAmount;
	}

	public BigDecimal getZjEhLinShiPreAmount() {
		return zjEhLinShiPreAmount;
	}

	public void setZjEhLinShiPreAmount(BigDecimal zjEhLinShiPreAmount) {
		this.zjEhLinShiPreAmount = zjEhLinShiPreAmount;
	}

	public BigDecimal getZjUfLinShiPreAmount() {
		return zjUfLinShiPreAmount;
	}

	public void setZjUfLinShiPreAmount(BigDecimal zjUfLinShiPreAmount) {
		this.zjUfLinShiPreAmount = zjUfLinShiPreAmount;
	}

	public BigDecimal getFzEmLinShiPreAmount() {
		return fzEmLinShiPreAmount;
	}

	public void setFzEmLinShiPreAmount(BigDecimal fzEmLinShiPreAmount) {
		this.fzEmLinShiPreAmount = fzEmLinShiPreAmount;
	}

	public BigDecimal getFzEfLinShiPreAmount() {
		return fzEfLinShiPreAmount;
	}

	public void setFzEfLinShiPreAmount(BigDecimal fzEfLinShiPreAmount) {
		this.fzEfLinShiPreAmount = fzEfLinShiPreAmount;
	}

	public BigDecimal getFzEf09LinShiPreAmount() {
		return fzEf09LinShiPreAmount;
	}

	public void setFzEf09LinShiPreAmount(BigDecimal fzEf09LinShiPreAmount) {
		this.fzEf09LinShiPreAmount = fzEf09LinShiPreAmount;
	}

	public BigDecimal getFzErLinShiPreAmount() {
		return fzErLinShiPreAmount;
	}

	public void setFzErLinShiPreAmount(BigDecimal fzErLinShiPreAmount) {
		this.fzErLinShiPreAmount = fzErLinShiPreAmount;
	}

	public BigDecimal getFzEhLinShiPreAmount() {
		return fzEhLinShiPreAmount;
	}

	public void setFzEhLinShiPreAmount(BigDecimal fzEhLinShiPreAmount) {
		this.fzEhLinShiPreAmount = fzEhLinShiPreAmount;
	}

	public BigDecimal getFzUfLinShiPreAmount() {
		return fzUfLinShiPreAmount;
	}

	public void setFzUfLinShiPreAmount(BigDecimal fzUfLinShiPreAmount) {
		this.fzUfLinShiPreAmount = fzUfLinShiPreAmount;
	}

	public BigDecimal getAmountMWLinShiPre() {
		return amountMWLinShiPre;
	}

	public void setAmountMWLinShiPre(BigDecimal amountMWLinShiPre) {
		this.amountMWLinShiPre = amountMWLinShiPre;
	}

	public BigDecimal getAmountEHLinShiPre() {
		return amountEHLinShiPre;
	}

	public void setAmountEHLinShiPre(BigDecimal amountEHLinShiPre) {
		this.amountEHLinShiPre = amountEHLinShiPre;
	}

	public BigDecimal getAmountUFLinShiPre() {
		return amountUFLinShiPre;
	}

	public void setAmountUFLinShiPre(BigDecimal amountUFLinShiPre) {
		this.amountUFLinShiPre = amountUFLinShiPre;
	}

	public BigDecimal getZjEmLinShiAct() {
		return zjEmLinShiAct;
	}

	public void setZjEmLinShiAct(BigDecimal zjEmLinShiAct) {
		this.zjEmLinShiAct = zjEmLinShiAct;
	}

	public BigDecimal getZjEfLinShiAct() {
		return zjEfLinShiAct;
	}

	public void setZjEfLinShiAct(BigDecimal zjEfLinShiAct) {
		this.zjEfLinShiAct = zjEfLinShiAct;
	}

	public BigDecimal getZjEf09LinShiAct() {
		return zjEf09LinShiAct;
	}

	public void setZjEf09LinShiAct(BigDecimal zjEf09LinShiAct) {
		this.zjEf09LinShiAct = zjEf09LinShiAct;
	}

	public BigDecimal getZjErLinShiAct() {
		return zjErLinShiAct;
	}

	public void setZjErLinShiAct(BigDecimal zjErLinShiAct) {
		this.zjErLinShiAct = zjErLinShiAct;
	}

	public BigDecimal getZjEhLinShiAct() {
		return zjEhLinShiAct;
	}

	public void setZjEhLinShiAct(BigDecimal zjEhLinShiAct) {
		this.zjEhLinShiAct = zjEhLinShiAct;
	}

	public BigDecimal getZjUfLinShiAct() {
		return zjUfLinShiAct;
	}

	public void setZjUfLinShiAct(BigDecimal zjUfLinShiAct) {
		this.zjUfLinShiAct = zjUfLinShiAct;
	}

	public BigDecimal getFzEmLinShiAct() {
		return fzEmLinShiAct;
	}

	public void setFzEmLinShiAct(BigDecimal fzEmLinShiAct) {
		this.fzEmLinShiAct = fzEmLinShiAct;
	}

	public BigDecimal getFzEfLinShiAct() {
		return fzEfLinShiAct;
	}

	public void setFzEfLinShiAct(BigDecimal fzEfLinShiAct) {
		this.fzEfLinShiAct = fzEfLinShiAct;
	}

	public BigDecimal getFzEf09LinShiAct() {
		return fzEf09LinShiAct;
	}

	public void setFzEf09LinShiAct(BigDecimal fzEf09LinShiAct) {
		this.fzEf09LinShiAct = fzEf09LinShiAct;
	}

	public BigDecimal getFzErLinShiAct() {
		return fzErLinShiAct;
	}

	public void setFzErLinShiAct(BigDecimal fzErLinShiAct) {
		this.fzErLinShiAct = fzErLinShiAct;
	}

	public BigDecimal getFzEhLinShiAct() {
		return fzEhLinShiAct;
	}

	public void setFzEhLinShiAct(BigDecimal fzEhLinShiAct) {
		this.fzEhLinShiAct = fzEhLinShiAct;
	}

	public BigDecimal getFzUfLinShiAct() {
		return fzUfLinShiAct;
	}

	public void setFzUfLinShiAct(BigDecimal fzUfLinShiAct) {
		this.fzUfLinShiAct = fzUfLinShiAct;
	}

	public BigDecimal getEmLaoWuActAmount() {
		return emLaoWuActAmount;
	}

	public void setEmLaoWuActAmount(BigDecimal emLaoWuActAmount) {
		this.emLaoWuActAmount = emLaoWuActAmount;
	}

	public BigDecimal getEfLaoWuActAmount() {
		return efLaoWuActAmount;
	}

	public void setEfLaoWuActAmount(BigDecimal efLaoWuActAmount) {
		this.efLaoWuActAmount = efLaoWuActAmount;
	}

	public BigDecimal getEf09LaoWuActAmount() {
		return ef09LaoWuActAmount;
	}

	public void setEf09LaoWuActAmount(BigDecimal ef09LaoWuActAmount) {
		this.ef09LaoWuActAmount = ef09LaoWuActAmount;
	}

	public BigDecimal getErLaoWuActAmount() {
		return erLaoWuActAmount;
	}

	public void setErLaoWuActAmount(BigDecimal erLaoWuActAmount) {
		this.erLaoWuActAmount = erLaoWuActAmount;
	}

	public BigDecimal getEhLaoWuActAmount() {
		return ehLaoWuActAmount;
	}

	public void setEhLaoWuActAmount(BigDecimal ehLaoWuActAmount) {
		this.ehLaoWuActAmount = ehLaoWuActAmount;
	}

	public BigDecimal getUfLaoWuActAmount() {
		return ufLaoWuActAmount;
	}

	public void setUfLaoWuActAmount(BigDecimal ufLaoWuActAmount) {
		this.ufLaoWuActAmount = ufLaoWuActAmount;
	}

	public BigDecimal getEmWaiBaoActAmount() {
		return emWaiBaoActAmount;
	}

	public void setEmWaiBaoActAmount(BigDecimal emWaiBaoActAmount) {
		this.emWaiBaoActAmount = emWaiBaoActAmount;
	}

	public BigDecimal getEfWaiBaoActAmount() {
		return efWaiBaoActAmount;
	}

	public void setEfWaiBaoActAmount(BigDecimal efWaiBaoActAmount) {
		this.efWaiBaoActAmount = efWaiBaoActAmount;
	}

	public BigDecimal getEf09WaiBaoActAmount() {
		return ef09WaiBaoActAmount;
	}

	public void setEf09WaiBaoActAmount(BigDecimal ef09WaiBaoActAmount) {
		this.ef09WaiBaoActAmount = ef09WaiBaoActAmount;
	}

	public BigDecimal getErWaiBaoActAmount() {
		return erWaiBaoActAmount;
	}

	public void setErWaiBaoActAmount(BigDecimal erWaiBaoActAmount) {
		this.erWaiBaoActAmount = erWaiBaoActAmount;
	}

	public BigDecimal getEhWaiBaoActAmount() {
		return ehWaiBaoActAmount;
	}

	public void setEhWaiBaoActAmount(BigDecimal ehWaiBaoActAmount) {
		this.ehWaiBaoActAmount = ehWaiBaoActAmount;
	}

	public BigDecimal getUfWaiBaoActAmount() {
		return ufWaiBaoActAmount;
	}

	public void setUfWaiBaoActAmount(BigDecimal ufWaiBaoActAmount) {
		this.ufWaiBaoActAmount = ufWaiBaoActAmount;
	}

	public BigDecimal getAmountMWLinShiAct() {
		return amountMWLinShiAct;
	}

	public void setAmountMWLinShiAct(BigDecimal amountMWLinShiAct) {
		this.amountMWLinShiAct = amountMWLinShiAct;
	}

	public BigDecimal getAmountEHLinShiAct() {
		return amountEHLinShiAct;
	}

	public void setAmountEHLinShiAct(BigDecimal amountEHLinShiAct) {
		this.amountEHLinShiAct = amountEHLinShiAct;
	}

	public BigDecimal getAmountUFLinShiAct() {
		return amountUFLinShiAct;
	}

	public void setAmountUFLinShiAct(BigDecimal amountUFLinShiAct) {
		this.amountUFLinShiAct = amountUFLinShiAct;
	}

	public BigDecimal getZjEmBaoQuanPre() {
		return zjEmBaoQuanPre;
	}

	public void setZjEmBaoQuanPre(BigDecimal zjEmBaoQuanPre) {
		this.zjEmBaoQuanPre = zjEmBaoQuanPre;
	}

	public BigDecimal getZjEfBaoQuanPre() {
		return zjEfBaoQuanPre;
	}

	public void setZjEfBaoQuanPre(BigDecimal zjEfBaoQuanPre) {
		this.zjEfBaoQuanPre = zjEfBaoQuanPre;
	}

	public BigDecimal getZjEf09BaoQuanPre() {
		return zjEf09BaoQuanPre;
	}

	public void setZjEf09BaoQuanPre(BigDecimal zjEf09BaoQuanPre) {
		this.zjEf09BaoQuanPre = zjEf09BaoQuanPre;
	}

	public BigDecimal getZjErBaoQuanPre() {
		return zjErBaoQuanPre;
	}

	public void setZjErBaoQuanPre(BigDecimal zjErBaoQuanPre) {
		this.zjErBaoQuanPre = zjErBaoQuanPre;
	}

	public BigDecimal getZjEhBaoQuanPre() {
		return zjEhBaoQuanPre;
	}

	public void setZjEhBaoQuanPre(BigDecimal zjEhBaoQuanPre) {
		this.zjEhBaoQuanPre = zjEhBaoQuanPre;
	}

	public BigDecimal getZjUfBaoQuanPre() {
		return zjUfBaoQuanPre;
	}

	public void setZjUfBaoQuanPre(BigDecimal zjUfBaoQuanPre) {
		this.zjUfBaoQuanPre = zjUfBaoQuanPre;
	}

	public BigDecimal getZjEmBaoQuanPreAmount() {
		return zjEmBaoQuanPreAmount;
	}

	public void setZjEmBaoQuanPreAmount(BigDecimal zjEmBaoQuanPreAmount) {
		this.zjEmBaoQuanPreAmount = zjEmBaoQuanPreAmount;
	}

	public BigDecimal getZjEfBaoQuanPreAmount() {
		return zjEfBaoQuanPreAmount;
	}

	public void setZjEfBaoQuanPreAmount(BigDecimal zjEfBaoQuanPreAmount) {
		this.zjEfBaoQuanPreAmount = zjEfBaoQuanPreAmount;
	}

	public BigDecimal getZjEf09BaoQuanPreAmount() {
		return zjEf09BaoQuanPreAmount;
	}

	public void setZjEf09BaoQuanPreAmount(BigDecimal zjEf09BaoQuanPreAmount) {
		this.zjEf09BaoQuanPreAmount = zjEf09BaoQuanPreAmount;
	}

	public BigDecimal getZjErBaoQuanPreAmount() {
		return zjErBaoQuanPreAmount;
	}

	public void setZjErBaoQuanPreAmount(BigDecimal zjErBaoQuanPreAmount) {
		this.zjErBaoQuanPreAmount = zjErBaoQuanPreAmount;
	}

	public BigDecimal getZjEhBaoQuanPreAmount() {
		return zjEhBaoQuanPreAmount;
	}

	public void setZjEhBaoQuanPreAmount(BigDecimal zjEhBaoQuanPreAmount) {
		this.zjEhBaoQuanPreAmount = zjEhBaoQuanPreAmount;
	}

	public BigDecimal getZjUfBaoQuanPreAmount() {
		return zjUfBaoQuanPreAmount;
	}

	public void setZjUfBaoQuanPreAmount(BigDecimal zjUfBaoQuanPreAmount) {
		this.zjUfBaoQuanPreAmount = zjUfBaoQuanPreAmount;
	}

	public BigDecimal getAmountMWBaoQuanPre() {
		return amountMWBaoQuanPre;
	}

	public void setAmountMWBaoQuanPre(BigDecimal amountMWBaoQuanPre) {
		this.amountMWBaoQuanPre = amountMWBaoQuanPre;
	}

	public BigDecimal getAmountEHBaoQuanPre() {
		return amountEHBaoQuanPre;
	}

	public void setAmountEHBaoQuanPre(BigDecimal amountEHBaoQuanPre) {
		this.amountEHBaoQuanPre = amountEHBaoQuanPre;
	}

	public BigDecimal getAmountUFBaoQuanPre() {
		return amountUFBaoQuanPre;
	}

	public void setAmountUFBaoQuanPre(BigDecimal amountUFBaoQuanPre) {
		this.amountUFBaoQuanPre = amountUFBaoQuanPre;
	}

	public BigDecimal getZjEmBaoQuanAct() {
		return zjEmBaoQuanAct;
	}

	public void setZjEmBaoQuanAct(BigDecimal zjEmBaoQuanAct) {
		this.zjEmBaoQuanAct = zjEmBaoQuanAct;
	}

	public BigDecimal getZjEfBaoQuanAct() {
		return zjEfBaoQuanAct;
	}

	public void setZjEfBaoQuanAct(BigDecimal zjEfBaoQuanAct) {
		this.zjEfBaoQuanAct = zjEfBaoQuanAct;
	}

	public BigDecimal getZjEf09BaoQuanAct() {
		return zjEf09BaoQuanAct;
	}

	public void setZjEf09BaoQuanAct(BigDecimal zjEf09BaoQuanAct) {
		this.zjEf09BaoQuanAct = zjEf09BaoQuanAct;
	}

	public BigDecimal getZjErBaoQuanAct() {
		return zjErBaoQuanAct;
	}

	public void setZjErBaoQuanAct(BigDecimal zjErBaoQuanAct) {
		this.zjErBaoQuanAct = zjErBaoQuanAct;
	}

	public BigDecimal getZjEhBaoQuanAct() {
		return zjEhBaoQuanAct;
	}

	public void setZjEhBaoQuanAct(BigDecimal zjEhBaoQuanAct) {
		this.zjEhBaoQuanAct = zjEhBaoQuanAct;
	}

	public BigDecimal getZjUfBaoQuanAct() {
		return zjUfBaoQuanAct;
	}

	public void setZjUfBaoQuanAct(BigDecimal zjUfBaoQuanAct) {
		this.zjUfBaoQuanAct = zjUfBaoQuanAct;
	}

	public BigDecimal getZjEmBaoQuanActAmount() {
		return zjEmBaoQuanActAmount;
	}

	public void setZjEmBaoQuanActAmount(BigDecimal zjEmBaoQuanActAmount) {
		this.zjEmBaoQuanActAmount = zjEmBaoQuanActAmount;
	}

	public BigDecimal getZjEfBaoQuanActAmount() {
		return zjEfBaoQuanActAmount;
	}

	public void setZjEfBaoQuanActAmount(BigDecimal zjEfBaoQuanActAmount) {
		this.zjEfBaoQuanActAmount = zjEfBaoQuanActAmount;
	}

	public BigDecimal getZjEf09BaoQuanActAmount() {
		return zjEf09BaoQuanActAmount;
	}

	public void setZjEf09BaoQuanActAmount(BigDecimal zjEf09BaoQuanActAmount) {
		this.zjEf09BaoQuanActAmount = zjEf09BaoQuanActAmount;
	}

	public BigDecimal getZjErBaoQuanActAmount() {
		return zjErBaoQuanActAmount;
	}

	public void setZjErBaoQuanActAmount(BigDecimal zjErBaoQuanActAmount) {
		this.zjErBaoQuanActAmount = zjErBaoQuanActAmount;
	}

	public BigDecimal getZjEhBaoQuanActAmount() {
		return zjEhBaoQuanActAmount;
	}

	public void setZjEhBaoQuanActAmount(BigDecimal zjEhBaoQuanActAmount) {
		this.zjEhBaoQuanActAmount = zjEhBaoQuanActAmount;
	}

	public BigDecimal getZjUfBaoQuanActAmount() {
		return zjUfBaoQuanActAmount;
	}

	public void setZjUfBaoQuanActAmount(BigDecimal zjUfBaoQuanActAmount) {
		this.zjUfBaoQuanActAmount = zjUfBaoQuanActAmount;
	}

	public BigDecimal getAmountMWBaoQuanAct() {
		return amountMWBaoQuanAct;
	}

	public void setAmountMWBaoQuanAct(BigDecimal amountMWBaoQuanAct) {
		this.amountMWBaoQuanAct = amountMWBaoQuanAct;
	}

	public BigDecimal getAmountEHBaoQuanAct() {
		return amountEHBaoQuanAct;
	}

	public void setAmountEHBaoQuanAct(BigDecimal amountEHBaoQuanAct) {
		this.amountEHBaoQuanAct = amountEHBaoQuanAct;
	}

	public BigDecimal getAmountUFBaoQuanAct() {
		return amountUFBaoQuanAct;
	}

	public void setAmountUFBaoQuanAct(BigDecimal amountUFBaoQuanAct) {
		this.amountUFBaoQuanAct = amountUFBaoQuanAct;
	}

	public BigDecimal getZjEmBuXiuPreAmount() {
		return zjEmBuXiuPreAmount;
	}

	public void setZjEmBuXiuPreAmount(BigDecimal zjEmBuXiuPreAmount) {
		this.zjEmBuXiuPreAmount = zjEmBuXiuPreAmount;
	}

	public BigDecimal getZjEfBuXiuPreAmount() {
		return zjEfBuXiuPreAmount;
	}

	public void setZjEfBuXiuPreAmount(BigDecimal zjEfBuXiuPreAmount) {
		this.zjEfBuXiuPreAmount = zjEfBuXiuPreAmount;
	}

	public BigDecimal getZjEf09BuXiuPreAmount() {
		return zjEf09BuXiuPreAmount;
	}

	public void setZjEf09BuXiuPreAmount(BigDecimal zjEf09BuXiuPreAmount) {
		this.zjEf09BuXiuPreAmount = zjEf09BuXiuPreAmount;
	}

	public BigDecimal getZjErBuXiuPreAmount() {
		return zjErBuXiuPreAmount;
	}

	public void setZjErBuXiuPreAmount(BigDecimal zjErBuXiuPreAmount) {
		this.zjErBuXiuPreAmount = zjErBuXiuPreAmount;
	}

	public BigDecimal getZjEhBuXiuPreAmount() {
		return zjEhBuXiuPreAmount;
	}

	public void setZjEhBuXiuPreAmount(BigDecimal zjEhBuXiuPreAmount) {
		this.zjEhBuXiuPreAmount = zjEhBuXiuPreAmount;
	}

	public BigDecimal getZjUfBuXiuPreAmount() {
		return zjUfBuXiuPreAmount;
	}

	public void setZjUfBuXiuPreAmount(BigDecimal zjUfBuXiuPreAmount) {
		this.zjUfBuXiuPreAmount = zjUfBuXiuPreAmount;
	}

	public BigDecimal getFzEmBuXiuPreAmount() {
		return fzEmBuXiuPreAmount;
	}

	public void setFzEmBuXiuPreAmount(BigDecimal fzEmBuXiuPreAmount) {
		this.fzEmBuXiuPreAmount = fzEmBuXiuPreAmount;
	}

	public BigDecimal getFzEfBuXiuPreAmount() {
		return fzEfBuXiuPreAmount;
	}

	public void setFzEfBuXiuPreAmount(BigDecimal fzEfBuXiuPreAmount) {
		this.fzEfBuXiuPreAmount = fzEfBuXiuPreAmount;
	}

	public BigDecimal getFzEf09BuXiuPreAmount() {
		return fzEf09BuXiuPreAmount;
	}

	public void setFzEf09BuXiuPreAmount(BigDecimal fzEf09BuXiuPreAmount) {
		this.fzEf09BuXiuPreAmount = fzEf09BuXiuPreAmount;
	}

	public BigDecimal getFzErBuXiuPreAmount() {
		return fzErBuXiuPreAmount;
	}

	public void setFzErBuXiuPreAmount(BigDecimal fzErBuXiuPreAmount) {
		this.fzErBuXiuPreAmount = fzErBuXiuPreAmount;
	}

	public BigDecimal getFzEhBuXiuPreAmount() {
		return fzEhBuXiuPreAmount;
	}

	public void setFzEhBuXiuPreAmount(BigDecimal fzEhBuXiuPreAmount) {
		this.fzEhBuXiuPreAmount = fzEhBuXiuPreAmount;
	}

	public BigDecimal getFzUfBuXiuPreAmount() {
		return fzUfBuXiuPreAmount;
	}

	public void setFzUfBuXiuPreAmount(BigDecimal fzUfBuXiuPreAmount) {
		this.fzUfBuXiuPreAmount = fzUfBuXiuPreAmount;
	}

	public BigDecimal getAmountMWBuXiuPre() {
		return amountMWBuXiuPre;
	}

	public void setAmountMWBuXiuPre(BigDecimal amountMWBuXiuPre) {
		this.amountMWBuXiuPre = amountMWBuXiuPre;
	}

	public BigDecimal getAmountEHBuXiuPre() {
		return amountEHBuXiuPre;
	}

	public void setAmountEHBuXiuPre(BigDecimal amountEHBuXiuPre) {
		this.amountEHBuXiuPre = amountEHBuXiuPre;
	}

	public BigDecimal getAmountUFBuXiuPre() {
		return amountUFBuXiuPre;
	}

	public void setAmountUFBuXiuPre(BigDecimal amountUFBuXiuPre) {
		this.amountUFBuXiuPre = amountUFBuXiuPre;
	}

	public BigDecimal getZjEmBuXiuActAmount() {
		return zjEmBuXiuActAmount;
	}

	public void setZjEmBuXiuActAmount(BigDecimal zjEmBuXiuActAmount) {
		this.zjEmBuXiuActAmount = zjEmBuXiuActAmount;
	}

	public BigDecimal getZjEfBuXiuActAmount() {
		return zjEfBuXiuActAmount;
	}

	public void setZjEfBuXiuActAmount(BigDecimal zjEfBuXiuActAmount) {
		this.zjEfBuXiuActAmount = zjEfBuXiuActAmount;
	}

	public BigDecimal getZjEf09BuXiuActAmount() {
		return zjEf09BuXiuActAmount;
	}

	public void setZjEf09BuXiuActAmount(BigDecimal zjEf09BuXiuActAmount) {
		this.zjEf09BuXiuActAmount = zjEf09BuXiuActAmount;
	}

	public BigDecimal getZjErBuXiuActAmount() {
		return zjErBuXiuActAmount;
	}

	public void setZjErBuXiuActAmount(BigDecimal zjErBuXiuActAmount) {
		this.zjErBuXiuActAmount = zjErBuXiuActAmount;
	}

	public BigDecimal getZjEhBuXiuActAmount() {
		return zjEhBuXiuActAmount;
	}

	public void setZjEhBuXiuActAmount(BigDecimal zjEhBuXiuActAmount) {
		this.zjEhBuXiuActAmount = zjEhBuXiuActAmount;
	}

	public BigDecimal getZjUfBuXiuActAmount() {
		return zjUfBuXiuActAmount;
	}

	public void setZjUfBuXiuActAmount(BigDecimal zjUfBuXiuActAmount) {
		this.zjUfBuXiuActAmount = zjUfBuXiuActAmount;
	}



	public BigDecimal getAmountMWBuXiuAct() {
		return amountMWBuXiuAct;
	}

	public void setAmountMWBuXiuAct(BigDecimal amountMWBuXiuAct) {
		this.amountMWBuXiuAct = amountMWBuXiuAct;
	}

	public BigDecimal getAmountEHBuXiuAct() {
		return amountEHBuXiuAct;
	}

	public void setAmountEHBuXiuAct(BigDecimal amountEHBuXiuAct) {
		this.amountEHBuXiuAct = amountEHBuXiuAct;
	}

	public BigDecimal getAmountUFBuXiuAct() {
		return amountUFBuXiuAct;
	}

	public void setAmountUFBuXiuAct(BigDecimal amountUFBuXiuAct) {
		this.amountUFBuXiuAct = amountUFBuXiuAct;
	}

	public BigDecimal getZjEmFuCaiPreAmount() {
		return zjEmFuCaiPreAmount;
	}

	public void setZjEmFuCaiPreAmount(BigDecimal zjEmFuCaiPreAmount) {
		this.zjEmFuCaiPreAmount = zjEmFuCaiPreAmount;
	}

	public BigDecimal getZjEfFuCaiPreAmount() {
		return zjEfFuCaiPreAmount;
	}

	public void setZjEfFuCaiPreAmount(BigDecimal zjEfFuCaiPreAmount) {
		this.zjEfFuCaiPreAmount = zjEfFuCaiPreAmount;
	}

	public BigDecimal getZjEf09FuCaiPreAmount() {
		return zjEf09FuCaiPreAmount;
	}

	public void setZjEf09FuCaiPreAmount(BigDecimal zjEf09FuCaiPreAmount) {
		this.zjEf09FuCaiPreAmount = zjEf09FuCaiPreAmount;
	}

	public BigDecimal getZjErFuCaiPreAmount() {
		return zjErFuCaiPreAmount;
	}

	public void setZjErFuCaiPreAmount(BigDecimal zjErFuCaiPreAmount) {
		this.zjErFuCaiPreAmount = zjErFuCaiPreAmount;
	}

	public BigDecimal getZjEhFuCaiPreAmount() {
		return zjEhFuCaiPreAmount;
	}

	public void setZjEhFuCaiPreAmount(BigDecimal zjEhFuCaiPreAmount) {
		this.zjEhFuCaiPreAmount = zjEhFuCaiPreAmount;
	}

	public BigDecimal getZjUfFuCaiPreAmount() {
		return zjUfFuCaiPreAmount;
	}

	public void setZjUfFuCaiPreAmount(BigDecimal zjUfFuCaiPreAmount) {
		this.zjUfFuCaiPreAmount = zjUfFuCaiPreAmount;
	}

	public BigDecimal getFzEmFuCaiPreAmount() {
		return fzEmFuCaiPreAmount;
	}

	public void setFzEmFuCaiPreAmount(BigDecimal fzEmFuCaiPreAmount) {
		this.fzEmFuCaiPreAmount = fzEmFuCaiPreAmount;
	}

	public BigDecimal getFzEfFuCaiPreAmount() {
		return fzEfFuCaiPreAmount;
	}

	public void setFzEfFuCaiPreAmount(BigDecimal fzEfFuCaiPreAmount) {
		this.fzEfFuCaiPreAmount = fzEfFuCaiPreAmount;
	}

	public BigDecimal getFzEf09FuCaiPreAmount() {
		return fzEf09FuCaiPreAmount;
	}

	public void setFzEf09FuCaiPreAmount(BigDecimal fzEf09FuCaiPreAmount) {
		this.fzEf09FuCaiPreAmount = fzEf09FuCaiPreAmount;
	}

	public BigDecimal getFzErFuCaiPreAmount() {
		return fzErFuCaiPreAmount;
	}

	public void setFzErFuCaiPreAmount(BigDecimal fzErFuCaiPreAmount) {
		this.fzErFuCaiPreAmount = fzErFuCaiPreAmount;
	}

	public BigDecimal getFzEhFuCaiPreAmount() {
		return fzEhFuCaiPreAmount;
	}

	public void setFzEhFuCaiPreAmount(BigDecimal fzEhFuCaiPreAmount) {
		this.fzEhFuCaiPreAmount = fzEhFuCaiPreAmount;
	}

	public BigDecimal getFzUfFuCaiPreAmount() {
		return fzUfFuCaiPreAmount;
	}

	public void setFzUfFuCaiPreAmount(BigDecimal fzUfFuCaiPreAmount) {
		this.fzUfFuCaiPreAmount = fzUfFuCaiPreAmount;
	}

	public BigDecimal getAmountMWFuCaiPre() {
		return amountMWFuCaiPre;
	}

	public void setAmountMWFuCaiPre(BigDecimal amountMWFuCaiPre) {
		this.amountMWFuCaiPre = amountMWFuCaiPre;
	}

	public BigDecimal getAmountEHFuCaiPre() {
		return amountEHFuCaiPre;
	}

	public void setAmountEHFuCaiPre(BigDecimal amountEHFuCaiPre) {
		this.amountEHFuCaiPre = amountEHFuCaiPre;
	}

	public BigDecimal getAmountUFFuCaiPre() {
		return amountUFFuCaiPre;
	}

	public void setAmountUFFuCaiPre(BigDecimal amountUFFuCaiPre) {
		this.amountUFFuCaiPre = amountUFFuCaiPre;
	}

	public BigDecimal getZjEmFuCaiActAmount() {
		return zjEmFuCaiActAmount;
	}

	public void setZjEmFuCaiActAmount(BigDecimal zjEmFuCaiActAmount) {
		this.zjEmFuCaiActAmount = zjEmFuCaiActAmount;
	}

	public BigDecimal getZjEfFuCaiActAmount() {
		return zjEfFuCaiActAmount;
	}

	public void setZjEfFuCaiActAmount(BigDecimal zjEfFuCaiActAmount) {
		this.zjEfFuCaiActAmount = zjEfFuCaiActAmount;
	}

	public BigDecimal getZjEf09FuCaiActAmount() {
		return zjEf09FuCaiActAmount;
	}

	public void setZjEf09FuCaiActAmount(BigDecimal zjEf09FuCaiActAmount) {
		this.zjEf09FuCaiActAmount = zjEf09FuCaiActAmount;
	}

	public BigDecimal getZjErFuCaiActAmount() {
		return zjErFuCaiActAmount;
	}

	public void setZjErFuCaiActAmount(BigDecimal zjErFuCaiActAmount) {
		this.zjErFuCaiActAmount = zjErFuCaiActAmount;
	}

	public BigDecimal getZjEhFuCaiActAmount() {
		return zjEhFuCaiActAmount;
	}

	public void setZjEhFuCaiActAmount(BigDecimal zjEhFuCaiActAmount) {
		this.zjEhFuCaiActAmount = zjEhFuCaiActAmount;
	}

	public BigDecimal getZjUfFuCaiActAmount() {
		return zjUfFuCaiActAmount;
	}

	public void setZjUfFuCaiActAmount(BigDecimal zjUfFuCaiActAmount) {
		this.zjUfFuCaiActAmount = zjUfFuCaiActAmount;
	}

	public BigDecimal getEmBuXiuActAmount() {
		return emBuXiuActAmount;
	}

	public void setEmBuXiuActAmount(BigDecimal emBuXiuActAmount) {
		this.emBuXiuActAmount = emBuXiuActAmount;
	}

	public BigDecimal getEfBuXiuActAmount() {
		return efBuXiuActAmount;
	}

	public void setEfBuXiuActAmount(BigDecimal efBuXiuActAmount) {
		this.efBuXiuActAmount = efBuXiuActAmount;
	}

	public BigDecimal getEf09BuXiuActAmount() {
		return ef09BuXiuActAmount;
	}

	public void setEf09BuXiuActAmount(BigDecimal ef09BuXiuActAmount) {
		this.ef09BuXiuActAmount = ef09BuXiuActAmount;
	}

	public BigDecimal getErBuXiuActAmount() {
		return erBuXiuActAmount;
	}

	public void setErBuXiuActAmount(BigDecimal erBuXiuActAmount) {
		this.erBuXiuActAmount = erBuXiuActAmount;
	}

	public BigDecimal getEhBuXiuActAmount() {
		return ehBuXiuActAmount;
	}

	public void setEhBuXiuActAmount(BigDecimal ehBuXiuActAmount) {
		this.ehBuXiuActAmount = ehBuXiuActAmount;
	}

	public BigDecimal getUfBuXiuActAmount() {
		return ufBuXiuActAmount;
	}

	public void setUfBuXiuActAmount(BigDecimal ufBuXiuActAmount) {
		this.ufBuXiuActAmount = ufBuXiuActAmount;
	}

	public BigDecimal getAmountzjMWBuXiuAct() {
		return amountzjMWBuXiuAct;
	}

	public void setAmountzjMWBuXiuAct(BigDecimal amountzjMWBuXiuAct) {
		this.amountzjMWBuXiuAct = amountzjMWBuXiuAct;
	}

	public BigDecimal getAmountzjEHBuXiuAct() {
		return amountzjEHBuXiuAct;
	}

	public void setAmountzjEHBuXiuAct(BigDecimal amountzjEHBuXiuAct) {
		this.amountzjEHBuXiuAct = amountzjEHBuXiuAct;
	}

	public BigDecimal getAmountzjUFBuXiuAct() {
		return amountzjUFBuXiuAct;
	}

	public void setAmountzjUFBuXiuAct(BigDecimal amountzjUFBuXiuAct) {
		this.amountzjUFBuXiuAct = amountzjUFBuXiuAct;
	}

	public BigDecimal getEmFuCaiActAmount() {
		return emFuCaiActAmount;
	}

	public void setEmFuCaiActAmount(BigDecimal emFuCaiActAmount) {
		this.emFuCaiActAmount = emFuCaiActAmount;
	}

	public BigDecimal getEfFuCaiActAmount() {
		return efFuCaiActAmount;
	}

	public void setEfFuCaiActAmount(BigDecimal efFuCaiActAmount) {
		this.efFuCaiActAmount = efFuCaiActAmount;
	}

	public BigDecimal getEf09FuCaiActAmount() {
		return ef09FuCaiActAmount;
	}

	public void setEf09FuCaiActAmount(BigDecimal ef09FuCaiActAmount) {
		this.ef09FuCaiActAmount = ef09FuCaiActAmount;
	}

	public BigDecimal getErFuCaiActAmount() {
		return erFuCaiActAmount;
	}

	public void setErFuCaiActAmount(BigDecimal erFuCaiActAmount) {
		this.erFuCaiActAmount = erFuCaiActAmount;
	}

	public BigDecimal getEhFuCaiActAmount() {
		return ehFuCaiActAmount;
	}

	public void setEhFuCaiActAmount(BigDecimal ehFuCaiActAmount) {
		this.ehFuCaiActAmount = ehFuCaiActAmount;
	}

	public BigDecimal getUfFuCaiActAmount() {
		return ufFuCaiActAmount;
	}

	public void setUfFuCaiActAmount(BigDecimal ufFuCaiActAmount) {
		this.ufFuCaiActAmount = ufFuCaiActAmount;
	}

	public BigDecimal getAmountzjMWFuCaiAct() {
		return amountzjMWFuCaiAct;
	}

	public void setAmountzjMWFuCaiAct(BigDecimal amountzjMWFuCaiAct) {
		this.amountzjMWFuCaiAct = amountzjMWFuCaiAct;
	}

	public BigDecimal getAmountzjEHFuCaiAct() {
		return amountzjEHFuCaiAct;
	}

	public void setAmountzjEHFuCaiAct(BigDecimal amountzjEHFuCaiAct) {
		this.amountzjEHFuCaiAct = amountzjEHFuCaiAct;
	}

	public BigDecimal getAmountzjUFFuCaiAct() {
		return amountzjUFFuCaiAct;
	}

	public void setAmountzjUFFuCaiAct(BigDecimal amountzjUFFuCaiAct) {
		this.amountzjUFFuCaiAct = amountzjUFFuCaiAct;
	}

	public BigDecimal getAmountMWFuCaiAct() {
		return amountMWFuCaiAct;
	}

	public void setAmountMWFuCaiAct(BigDecimal amountMWFuCaiAct) {
		this.amountMWFuCaiAct = amountMWFuCaiAct;
	}

	public BigDecimal getAmountEHFuCaiAct() {
		return amountEHFuCaiAct;
	}

	public void setAmountEHFuCaiAct(BigDecimal amountEHFuCaiAct) {
		this.amountEHFuCaiAct = amountEHFuCaiAct;
	}

	public BigDecimal getAmountUFFuCaiAct() {
		return amountUFFuCaiAct;
	}

	public void setAmountUFFuCaiAct(BigDecimal amountUFFuCaiAct) {
		this.amountUFFuCaiAct = amountUFFuCaiAct;
	}

	public BigDecimal getEmWrPre() {
		return emWrPre;
	}

	public void setEmWrPre(BigDecimal emWrPre) {
		this.emWrPre = emWrPre;
	}

	public BigDecimal getEfWrPre() {
		return efWrPre;
	}

	public void setEfWrPre(BigDecimal efWrPre) {
		this.efWrPre = efWrPre;
	}

	public BigDecimal getEf09WrPre() {
		return ef09WrPre;
	}

	public void setEf09WrPre(BigDecimal ef09WrPre) {
		this.ef09WrPre = ef09WrPre;
	}

	public BigDecimal getErWrPre() {
		return erWrPre;
	}

	public void setErWrPre(BigDecimal erWrPre) {
		this.erWrPre = erWrPre;
	}

	public BigDecimal getEhWrPre() {
		return ehWrPre;
	}

	public void setEhWrPre(BigDecimal ehWrPre) {
		this.ehWrPre = ehWrPre;
	}

	public BigDecimal getUfWrPre() {
		return ufWrPre;
	}

	public void setUfWrPre(BigDecimal ufWrPre) {
		this.ufWrPre = ufWrPre;
	}

	public BigDecimal getAmountMWWrPre() {
		return amountMWWrPre;
	}

	public void setAmountMWWrPre(BigDecimal amountMWWrPre) {
		this.amountMWWrPre = amountMWWrPre;
	}

	public BigDecimal getAmountEHWrPre() {
		return amountEHWrPre;
	}

	public void setAmountEHWrPre(BigDecimal amountEHWrPre) {
		this.amountEHWrPre = amountEHWrPre;
	}

	public BigDecimal getAmountUFWrPre() {
		return amountUFWrPre;
	}

	public void setAmountUFWrPre(BigDecimal amountUFWrPre) {
		this.amountUFWrPre = amountUFWrPre;
	}

	public BigDecimal getEmWrAct() {
		return emWrAct;
	}

	public void setEmWrAct(BigDecimal emWrAct) {
		this.emWrAct = emWrAct;
	}

	public BigDecimal getEfWrAct() {
		return efWrAct;
	}

	public void setEfWrAct(BigDecimal efWrAct) {
		this.efWrAct = efWrAct;
	}

	public BigDecimal getEf09WrAct() {
		return ef09WrAct;
	}

	public void setEf09WrAct(BigDecimal ef09WrAct) {
		this.ef09WrAct = ef09WrAct;
	}

	public BigDecimal getErWrAct() {
		return erWrAct;
	}

	public void setErWrAct(BigDecimal erWrAct) {
		this.erWrAct = erWrAct;
	}

	public BigDecimal getEhWrAct() {
		return ehWrAct;
	}

	public void setEhWrAct(BigDecimal ehWrAct) {
		this.ehWrAct = ehWrAct;
	}

	public BigDecimal getUfWrAct() {
		return ufWrAct;
	}

	public void setUfWrAct(BigDecimal ufWrAct) {
		this.ufWrAct = ufWrAct;
	}

	public BigDecimal getAmountMWWrAct() {
		return amountMWWrAct;
	}

	public void setAmountMWWrAct(BigDecimal amountMWWrAct) {
		this.amountMWWrAct = amountMWWrAct;
	}

	public BigDecimal getAmountEHWrAct() {
		return amountEHWrAct;
	}

	public void setAmountEHWrAct(BigDecimal amountEHWrAct) {
		this.amountEHWrAct = amountEHWrAct;
	}

	public BigDecimal getAmountUFWrAct() {
		return amountUFWrAct;
	}

	public void setAmountUFWrAct(BigDecimal amountUFWrAct) {
		this.amountUFWrAct = amountUFWrAct;
	}

	public BigDecimal getEmYunFeiPre() {
		return emYunFeiPre;
	}

	public void setEmYunFeiPre(BigDecimal emYunFeiPre) {
		this.emYunFeiPre = emYunFeiPre;
	}

	public BigDecimal getEfYunFeiPre() {
		return efYunFeiPre;
	}

	public void setEfYunFeiPre(BigDecimal efYunFeiPre) {
		this.efYunFeiPre = efYunFeiPre;
	}

	public BigDecimal getEf09YunFeiPre() {
		return ef09YunFeiPre;
	}

	public void setEf09YunFeiPre(BigDecimal ef09YunFeiPre) {
		this.ef09YunFeiPre = ef09YunFeiPre;
	}

	public BigDecimal getErYunFeiPre() {
		return erYunFeiPre;
	}

	public void setErYunFeiPre(BigDecimal erYunFeiPre) {
		this.erYunFeiPre = erYunFeiPre;
	}

	public BigDecimal getEhYunFeiPre() {
		return ehYunFeiPre;
	}

	public void setEhYunFeiPre(BigDecimal ehYunFeiPre) {
		this.ehYunFeiPre = ehYunFeiPre;
	}

	public BigDecimal getUfYunFeiPre() {
		return ufYunFeiPre;
	}

	public void setUfYunFeiPre(BigDecimal ufYunFeiPre) {
		this.ufYunFeiPre = ufYunFeiPre;
	}

	public BigDecimal getAmountMWYunFeiPre() {
		return amountMWYunFeiPre;
	}

	public void setAmountMWYunFeiPre(BigDecimal amountMWYunFeiPre) {
		this.amountMWYunFeiPre = amountMWYunFeiPre;
	}

	public BigDecimal getAmountEHYunFeiPre() {
		return amountEHYunFeiPre;
	}

	public void setAmountEHYunFeiPre(BigDecimal amountEHYunFeiPre) {
		this.amountEHYunFeiPre = amountEHYunFeiPre;
	}

	public BigDecimal getAmountUFYunFeiPre() {
		return amountUFYunFeiPre;
	}

	public void setAmountUFYunFeiPre(BigDecimal amountUFYunFeiPre) {
		this.amountUFYunFeiPre = amountUFYunFeiPre;
	}

	public BigDecimal getEmYunFeiAct() {
		return emYunFeiAct;
	}

	public void setEmYunFeiAct(BigDecimal emYunFeiAct) {
		this.emYunFeiAct = emYunFeiAct;
	}

	public BigDecimal getEfYunFeiAct() {
		return efYunFeiAct;
	}

	public void setEfYunFeiAct(BigDecimal efYunFeiAct) {
		this.efYunFeiAct = efYunFeiAct;
	}

	public BigDecimal getEf09YunFeiAct() {
		return ef09YunFeiAct;
	}

	public void setEf09YunFeiAct(BigDecimal ef09YunFeiAct) {
		this.ef09YunFeiAct = ef09YunFeiAct;
	}

	public BigDecimal getErYunFeiAct() {
		return erYunFeiAct;
	}

	public void setErYunFeiAct(BigDecimal erYunFeiAct) {
		this.erYunFeiAct = erYunFeiAct;
	}

	public BigDecimal getEhYunFeiAct() {
		return ehYunFeiAct;
	}

	public void setEhYunFeiAct(BigDecimal ehYunFeiAct) {
		this.ehYunFeiAct = ehYunFeiAct;
	}

	public BigDecimal getUfYunFeiAct() {
		return ufYunFeiAct;
	}

	public void setUfYunFeiAct(BigDecimal ufYunFeiAct) {
		this.ufYunFeiAct = ufYunFeiAct;
	}

	public BigDecimal getAmountMWYunFeiAct() {
		return amountMWYunFeiAct;
	}

	public void setAmountMWYunFeiAct(BigDecimal amountMWYunFeiAct) {
		this.amountMWYunFeiAct = amountMWYunFeiAct;
	}

	public BigDecimal getAmountEHYunFeiAct() {
		return amountEHYunFeiAct;
	}

	public void setAmountEHYunFeiAct(BigDecimal amountEHYunFeiAct) {
		this.amountEHYunFeiAct = amountEHYunFeiAct;
	}

	public BigDecimal getAmountUFYunFeiAct() {
		return amountUFYunFeiAct;
	}

	public void setAmountUFYunFeiAct(BigDecimal amountUFYunFeiAct) {
		this.amountUFYunFeiAct = amountUFYunFeiAct;
	}

	public BigDecimal getEmXphsPre() {
		return emXphsPre;
	}

	public void setEmXphsPre(BigDecimal emXphsPre) {
		this.emXphsPre = emXphsPre;
	}

	public BigDecimal getEfXphsPre() {
		return efXphsPre;
	}

	public void setEfXphsPre(BigDecimal efXphsPre) {
		this.efXphsPre = efXphsPre;
	}

	public BigDecimal getEf09XphsPre() {
		return ef09XphsPre;
	}

	public void setEf09XphsPre(BigDecimal ef09XphsPre) {
		this.ef09XphsPre = ef09XphsPre;
	}

	public BigDecimal getErXphsPre() {
		return erXphsPre;
	}

	public void setErXphsPre(BigDecimal erXphsPre) {
		this.erXphsPre = erXphsPre;
	}

	public BigDecimal getEhXphsPre() {
		return ehXphsPre;
	}

	public void setEhXphsPre(BigDecimal ehXphsPre) {
		this.ehXphsPre = ehXphsPre;
	}

	public BigDecimal getUfXphsPre() {
		return ufXphsPre;
	}

	public void setUfXphsPre(BigDecimal ufXphsPre) {
		this.ufXphsPre = ufXphsPre;
	}

	public BigDecimal getAmountMWXphsPre() {
		return amountMWXphsPre;
	}

	public void setAmountMWXphsPre(BigDecimal amountMWXphsPre) {
		this.amountMWXphsPre = amountMWXphsPre;
	}

	public BigDecimal getAmountEHXphsPre() {
		return amountEHXphsPre;
	}

	public void setAmountEHXphsPre(BigDecimal amountEHXphsPre) {
		this.amountEHXphsPre = amountEHXphsPre;
	}

	public BigDecimal getAmountUFXphsPre() {
		return amountUFXphsPre;
	}

	public void setAmountUFXphsPre(BigDecimal amountUFXphsPre) {
		this.amountUFXphsPre = amountUFXphsPre;
	}

	public BigDecimal getEmXphsAct() {
		return emXphsAct;
	}

	public void setEmXphsAct(BigDecimal emXphsAct) {
		this.emXphsAct = emXphsAct;
	}

	public BigDecimal getEfXphsAct() {
		return efXphsAct;
	}

	public void setEfXphsAct(BigDecimal efXphsAct) {
		this.efXphsAct = efXphsAct;
	}

	public BigDecimal getEf09XphsAct() {
		return ef09XphsAct;
	}

	public void setEf09XphsAct(BigDecimal ef09XphsAct) {
		this.ef09XphsAct = ef09XphsAct;
	}

	public BigDecimal getErXphsAct() {
		return erXphsAct;
	}

	public void setErXphsAct(BigDecimal erXphsAct) {
		this.erXphsAct = erXphsAct;
	}

	public BigDecimal getEhXphsAct() {
		return ehXphsAct;
	}

	public void setEhXphsAct(BigDecimal ehXphsAct) {
		this.ehXphsAct = ehXphsAct;
	}

	public BigDecimal getUfXphsAct() {
		return ufXphsAct;
	}

	public void setUfXphsAct(BigDecimal ufXphsAct) {
		this.ufXphsAct = ufXphsAct;
	}

	public BigDecimal getAmountMWXphsAct() {
		return amountMWXphsAct;
	}

	public void setAmountMWXphsAct(BigDecimal amountMWXphsAct) {
		this.amountMWXphsAct = amountMWXphsAct;
	}

	public BigDecimal getAmountEHXphsAct() {
		return amountEHXphsAct;
	}

	public void setAmountEHXphsAct(BigDecimal amountEHXphsAct) {
		this.amountEHXphsAct = amountEHXphsAct;
	}

	public BigDecimal getAmountUFXphsAct() {
		return amountUFXphsAct;
	}

	public void setAmountUFXphsAct(BigDecimal amountUFXphsAct) {
		this.amountUFXphsAct = amountUFXphsAct;
	}

	public BigDecimal getEmTongXiePre() {
		return emTongXiePre;
	}

	public void setEmTongXiePre(BigDecimal emTongXiePre) {
		this.emTongXiePre = emTongXiePre;
	}

	public BigDecimal getEfTongXiePre() {
		return efTongXiePre;
	}

	public void setEfTongXiePre(BigDecimal efTongXiePre) {
		this.efTongXiePre = efTongXiePre;
	}

	public BigDecimal getEf09TongXiePre() {
		return ef09TongXiePre;
	}

	public void setEf09TongXiePre(BigDecimal ef09TongXiePre) {
		this.ef09TongXiePre = ef09TongXiePre;
	}

	public BigDecimal getErTongXiePre() {
		return erTongXiePre;
	}

	public void setErTongXiePre(BigDecimal erTongXiePre) {
		this.erTongXiePre = erTongXiePre;
	}

	public BigDecimal getEhTongXiePre() {
		return ehTongXiePre;
	}

	public void setEhTongXiePre(BigDecimal ehTongXiePre) {
		this.ehTongXiePre = ehTongXiePre;
	}

	public BigDecimal getUfTongXiePre() {
		return ufTongXiePre;
	}

	public void setUfTongXiePre(BigDecimal ufTongXiePre) {
		this.ufTongXiePre = ufTongXiePre;
	}

	public BigDecimal getAmountMWTongXiePre() {
		return amountMWTongXiePre;
	}

	public void setAmountMWTongXiePre(BigDecimal amountMWTongXiePre) {
		this.amountMWTongXiePre = amountMWTongXiePre;
	}

	public BigDecimal getAmountEHTongXiePre() {
		return amountEHTongXiePre;
	}

	public void setAmountEHTongXiePre(BigDecimal amountEHTongXiePre) {
		this.amountEHTongXiePre = amountEHTongXiePre;
	}

	public BigDecimal getAmountUFTongXiePre() {
		return amountUFTongXiePre;
	}

	public void setAmountUFTongXiePre(BigDecimal amountUFTongXiePre) {
		this.amountUFTongXiePre = amountUFTongXiePre;
	}

	public BigDecimal getEmTongXieAct() {
		return emTongXieAct;
	}

	public void setEmTongXieAct(BigDecimal emTongXieAct) {
		this.emTongXieAct = emTongXieAct;
	}

	public BigDecimal getEfTongXieAct() {
		return efTongXieAct;
	}

	public void setEfTongXieAct(BigDecimal efTongXieAct) {
		this.efTongXieAct = efTongXieAct;
	}

	public BigDecimal getEf09TongXieAct() {
		return ef09TongXieAct;
	}

	public void setEf09TongXieAct(BigDecimal ef09TongXieAct) {
		this.ef09TongXieAct = ef09TongXieAct;
	}

	public BigDecimal getErTongXieAct() {
		return erTongXieAct;
	}

	public void setErTongXieAct(BigDecimal erTongXieAct) {
		this.erTongXieAct = erTongXieAct;
	}

	public BigDecimal getEhTongXieAct() {
		return ehTongXieAct;
	}

	public void setEhTongXieAct(BigDecimal ehTongXieAct) {
		this.ehTongXieAct = ehTongXieAct;
	}

	public BigDecimal getUfTongXieAct() {
		return ufTongXieAct;
	}

	public void setUfTongXieAct(BigDecimal ufTongXieAct) {
		this.ufTongXieAct = ufTongXieAct;
	}

	public BigDecimal getAmountMWTongXieAct() {
		return amountMWTongXieAct;
	}

	public void setAmountMWTongXieAct(BigDecimal amountMWTongXieAct) {
		this.amountMWTongXieAct = amountMWTongXieAct;
	}

	public BigDecimal getAmountEHTongXieAct() {
		return amountEHTongXieAct;
	}

	public void setAmountEHTongXieAct(BigDecimal amountEHTongXieAct) {
		this.amountEHTongXieAct = amountEHTongXieAct;
	}

	public BigDecimal getAmountUFTongXieAct() {
		return amountUFTongXieAct;
	}

	public void setAmountUFTongXieAct(BigDecimal amountUFTongXieAct) {
		this.amountUFTongXieAct = amountUFTongXieAct;
	}

	public BigDecimal getEmYouQiPre() {
		return emYouQiPre;
	}

	public void setEmYouQiPre(BigDecimal emYouQiPre) {
		this.emYouQiPre = emYouQiPre;
	}

	public BigDecimal getEfYouQiPre() {
		return efYouQiPre;
	}

	public void setEfYouQiPre(BigDecimal efYouQiPre) {
		this.efYouQiPre = efYouQiPre;
	}

	public BigDecimal getEf09YouQiPre() {
		return ef09YouQiPre;
	}

	public void setEf09YouQiPre(BigDecimal ef09YouQiPre) {
		this.ef09YouQiPre = ef09YouQiPre;
	}

	public BigDecimal getErYouQiPre() {
		return erYouQiPre;
	}

	public void setErYouQiPre(BigDecimal erYouQiPre) {
		this.erYouQiPre = erYouQiPre;
	}

	public BigDecimal getEhYouQiPre() {
		return ehYouQiPre;
	}

	public void setEhYouQiPre(BigDecimal ehYouQiPre) {
		this.ehYouQiPre = ehYouQiPre;
	}

	public BigDecimal getUfYouQiPre() {
		return ufYouQiPre;
	}

	public void setUfYouQiPre(BigDecimal ufYouQiPre) {
		this.ufYouQiPre = ufYouQiPre;
	}

	public BigDecimal getEmYouQiPreAmount() {
		return emYouQiPreAmount;
	}

	public void setEmYouQiPreAmount(BigDecimal emYouQiPreAmount) {
		this.emYouQiPreAmount = emYouQiPreAmount;
	}

	public BigDecimal getEfYouQiPreAmount() {
		return efYouQiPreAmount;
	}

	public void setEfYouQiPreAmount(BigDecimal efYouQiPreAmount) {
		this.efYouQiPreAmount = efYouQiPreAmount;
	}

	public BigDecimal getEf09YouQiPreAmount() {
		return ef09YouQiPreAmount;
	}

	public void setEf09YouQiPreAmount(BigDecimal ef09YouQiPreAmount) {
		this.ef09YouQiPreAmount = ef09YouQiPreAmount;
	}

	public BigDecimal getErYouQiPreAmount() {
		return erYouQiPreAmount;
	}

	public void setErYouQiPreAmount(BigDecimal erYouQiPreAmount) {
		this.erYouQiPreAmount = erYouQiPreAmount;
	}

	public BigDecimal getEhYouQiPreAmount() {
		return ehYouQiPreAmount;
	}

	public void setEhYouQiPreAmount(BigDecimal ehYouQiPreAmount) {
		this.ehYouQiPreAmount = ehYouQiPreAmount;
	}

	public BigDecimal getUfYouQiPreAmount() {
		return ufYouQiPreAmount;
	}

	public void setUfYouQiPreAmount(BigDecimal ufYouQiPreAmount) {
		this.ufYouQiPreAmount = ufYouQiPreAmount;
	}

	public BigDecimal getAmountMWYouQiPre() {
		return amountMWYouQiPre;
	}

	public void setAmountMWYouQiPre(BigDecimal amountMWYouQiPre) {
		this.amountMWYouQiPre = amountMWYouQiPre;
	}

	public BigDecimal getAmountEHYouQiPre() {
		return amountEHYouQiPre;
	}

	public void setAmountEHYouQiPre(BigDecimal amountEHYouQiPre) {
		this.amountEHYouQiPre = amountEHYouQiPre;
	}

	public BigDecimal getAmountUFYouQiPre() {
		return amountUFYouQiPre;
	}

	public void setAmountUFYouQiPre(BigDecimal amountUFYouQiPre) {
		this.amountUFYouQiPre = amountUFYouQiPre;
	}

	public BigDecimal getEmYouQiAct() {
		return emYouQiAct;
	}

	public void setEmYouQiAct(BigDecimal emYouQiAct) {
		this.emYouQiAct = emYouQiAct;
	}

	public BigDecimal getEfYouQiAct() {
		return efYouQiAct;
	}

	public void setEfYouQiAct(BigDecimal efYouQiAct) {
		this.efYouQiAct = efYouQiAct;
	}

	public BigDecimal getEf09YouQiAct() {
		return ef09YouQiAct;
	}

	public void setEf09YouQiAct(BigDecimal ef09YouQiAct) {
		this.ef09YouQiAct = ef09YouQiAct;
	}

	public BigDecimal getErYouQiAct() {
		return erYouQiAct;
	}

	public void setErYouQiAct(BigDecimal erYouQiAct) {
		this.erYouQiAct = erYouQiAct;
	}

	public BigDecimal getEhYouQiAct() {
		return ehYouQiAct;
	}

	public void setEhYouQiAct(BigDecimal ehYouQiAct) {
		this.ehYouQiAct = ehYouQiAct;
	}

	public BigDecimal getUfYouQiAct() {
		return ufYouQiAct;
	}

	public void setUfYouQiAct(BigDecimal ufYouQiAct) {
		this.ufYouQiAct = ufYouQiAct;
	}

	public BigDecimal getEmYouQiActAmount() {
		return emYouQiActAmount;
	}

	public void setEmYouQiActAmount(BigDecimal emYouQiActAmount) {
		this.emYouQiActAmount = emYouQiActAmount;
	}

	public BigDecimal getEfYouQiActAmount() {
		return efYouQiActAmount;
	}

	public void setEfYouQiActAmount(BigDecimal efYouQiActAmount) {
		this.efYouQiActAmount = efYouQiActAmount;
	}

	public BigDecimal getEf09YouQiActAmount() {
		return ef09YouQiActAmount;
	}

	public void setEf09YouQiActAmount(BigDecimal ef09YouQiActAmount) {
		this.ef09YouQiActAmount = ef09YouQiActAmount;
	}

	public BigDecimal getErYouQiActAmount() {
		return erYouQiActAmount;
	}

	public void setErYouQiActAmount(BigDecimal erYouQiActAmount) {
		this.erYouQiActAmount = erYouQiActAmount;
	}

	public BigDecimal getEhYouQiActAmount() {
		return ehYouQiActAmount;
	}

	public void setEhYouQiActAmount(BigDecimal ehYouQiActAmount) {
		this.ehYouQiActAmount = ehYouQiActAmount;
	}

	public BigDecimal getUfYouQiActAmount() {
		return ufYouQiActAmount;
	}

	public void setUfYouQiActAmount(BigDecimal ufYouQiActAmount) {
		this.ufYouQiActAmount = ufYouQiActAmount;
	}

	public BigDecimal getAmountMWYouQiAct() {
		return amountMWYouQiAct;
	}

	public void setAmountMWYouQiAct(BigDecimal amountMWYouQiAct) {
		this.amountMWYouQiAct = amountMWYouQiAct;
	}

	public BigDecimal getAmountEHYouQiAct() {
		return amountEHYouQiAct;
	}

	public void setAmountEHYouQiAct(BigDecimal amountEHYouQiAct) {
		this.amountEHYouQiAct = amountEHYouQiAct;
	}

	public BigDecimal getAmountUFYouQiAct() {
		return amountUFYouQiAct;
	}

	public void setAmountUFYouQiAct(BigDecimal amountUFYouQiAct) {
		this.amountUFYouQiAct = amountUFYouQiAct;
	}

	public BigDecimal getEmBaoZhuangPre() {
		return emBaoZhuangPre;
	}

	public void setEmBaoZhuangPre(BigDecimal emBaoZhuangPre) {
		this.emBaoZhuangPre = emBaoZhuangPre;
	}

	public BigDecimal getEfBaoZhuangPre() {
		return efBaoZhuangPre;
	}

	public void setEfBaoZhuangPre(BigDecimal efBaoZhuangPre) {
		this.efBaoZhuangPre = efBaoZhuangPre;
	}

	public BigDecimal getEf09BaoZhuangPre() {
		return ef09BaoZhuangPre;
	}

	public void setEf09BaoZhuangPre(BigDecimal ef09BaoZhuangPre) {
		this.ef09BaoZhuangPre = ef09BaoZhuangPre;
	}

	public BigDecimal getErBaoZhuangPre() {
		return erBaoZhuangPre;
	}

	public void setErBaoZhuangPre(BigDecimal erBaoZhuangPre) {
		this.erBaoZhuangPre = erBaoZhuangPre;
	}

	public BigDecimal getEhBaoZhuangPre() {
		return ehBaoZhuangPre;
	}

	public void setEhBaoZhuangPre(BigDecimal ehBaoZhuangPre) {
		this.ehBaoZhuangPre = ehBaoZhuangPre;
	}

	public BigDecimal getUfBaoZhuangPre() {
		return ufBaoZhuangPre;
	}

	public void setUfBaoZhuangPre(BigDecimal ufBaoZhuangPre) {
		this.ufBaoZhuangPre = ufBaoZhuangPre;
	}

	public BigDecimal getAmountMWBaoZhuangPre() {
		return amountMWBaoZhuangPre;
	}

	public void setAmountMWBaoZhuangPre(BigDecimal amountMWBaoZhuangPre) {
		this.amountMWBaoZhuangPre = amountMWBaoZhuangPre;
	}

	public BigDecimal getAmountEHBaoZhuangPre() {
		return amountEHBaoZhuangPre;
	}

	public void setAmountEHBaoZhuangPre(BigDecimal amountEHBaoZhuangPre) {
		this.amountEHBaoZhuangPre = amountEHBaoZhuangPre;
	}

	public BigDecimal getAmountUFBaoZhuangPre() {
		return amountUFBaoZhuangPre;
	}

	public void setAmountUFBaoZhuangPre(BigDecimal amountUFBaoZhuangPre) {
		this.amountUFBaoZhuangPre = amountUFBaoZhuangPre;
	}

	public BigDecimal getEmBaoZhuangAct() {
		return emBaoZhuangAct;
	}

	public void setEmBaoZhuangAct(BigDecimal emBaoZhuangAct) {
		this.emBaoZhuangAct = emBaoZhuangAct;
	}

	public BigDecimal getEfBaoZhuangAct() {
		return efBaoZhuangAct;
	}

	public void setEfBaoZhuangAct(BigDecimal efBaoZhuangAct) {
		this.efBaoZhuangAct = efBaoZhuangAct;
	}

	public BigDecimal getEf09BaoZhuangAct() {
		return ef09BaoZhuangAct;
	}

	public void setEf09BaoZhuangAct(BigDecimal ef09BaoZhuangAct) {
		this.ef09BaoZhuangAct = ef09BaoZhuangAct;
	}

	public BigDecimal getErBaoZhuangAct() {
		return erBaoZhuangAct;
	}

	public void setErBaoZhuangAct(BigDecimal erBaoZhuangAct) {
		this.erBaoZhuangAct = erBaoZhuangAct;
	}

	public BigDecimal getEhBaoZhuangAct() {
		return ehBaoZhuangAct;
	}

	public void setEhBaoZhuangAct(BigDecimal ehBaoZhuangAct) {
		this.ehBaoZhuangAct = ehBaoZhuangAct;
	}

	public BigDecimal getUfBaoZhuangAct() {
		return ufBaoZhuangAct;
	}

	public void setUfBaoZhuangAct(BigDecimal ufBaoZhuangAct) {
		this.ufBaoZhuangAct = ufBaoZhuangAct;
	}

	public BigDecimal getAmountMWBaoZhuangAct() {
		return amountMWBaoZhuangAct;
	}

	public void setAmountMWBaoZhuangAct(BigDecimal amountMWBaoZhuangAct) {
		this.amountMWBaoZhuangAct = amountMWBaoZhuangAct;
	}

	public BigDecimal getAmountEHBaoZhuangAct() {
		return amountEHBaoZhuangAct;
	}

	public void setAmountEHBaoZhuangAct(BigDecimal amountEHBaoZhuangAct) {
		this.amountEHBaoZhuangAct = amountEHBaoZhuangAct;
	}

	public BigDecimal getAmountUFBaoZhuangAct() {
		return amountUFBaoZhuangAct;
	}

	public void setAmountUFBaoZhuangAct(BigDecimal amountUFBaoZhuangAct) {
		this.amountUFBaoZhuangAct = amountUFBaoZhuangAct;
	}

	public BigDecimal getEmXianPanPre() {
		return emXianPanPre;
	}

	public void setEmXianPanPre(BigDecimal emXianPanPre) {
		this.emXianPanPre = emXianPanPre;
	}

	public BigDecimal getEfXianPanPre() {
		return efXianPanPre;
	}

	public void setEfXianPanPre(BigDecimal efXianPanPre) {
		this.efXianPanPre = efXianPanPre;
	}

	public BigDecimal getEf09XianPanPre() {
		return ef09XianPanPre;
	}

	public void setEf09XianPanPre(BigDecimal ef09XianPanPre) {
		this.ef09XianPanPre = ef09XianPanPre;
	}

	public BigDecimal getErXianPanPre() {
		return erXianPanPre;
	}

	public void setErXianPanPre(BigDecimal erXianPanPre) {
		this.erXianPanPre = erXianPanPre;
	}

	public BigDecimal getEhXianPanPre() {
		return ehXianPanPre;
	}

	public void setEhXianPanPre(BigDecimal ehXianPanPre) {
		this.ehXianPanPre = ehXianPanPre;
	}

	public BigDecimal getUfXianPanPre() {
		return ufXianPanPre;
	}

	public void setUfXianPanPre(BigDecimal ufXianPanPre) {
		this.ufXianPanPre = ufXianPanPre;
	}

	public BigDecimal getAmountMWXianPanPre() {
		return amountMWXianPanPre;
	}

	public void setAmountMWXianPanPre(BigDecimal amountMWXianPanPre) {
		this.amountMWXianPanPre = amountMWXianPanPre;
	}

	public BigDecimal getAmountEHXianPanPre() {
		return amountEHXianPanPre;
	}

	public void setAmountEHXianPanPre(BigDecimal amountEHXianPanPre) {
		this.amountEHXianPanPre = amountEHXianPanPre;
	}

	public BigDecimal getAmountUFXianPanPre() {
		return amountUFXianPanPre;
	}

	public void setAmountUFXianPanPre(BigDecimal amountUFXianPanPre) {
		this.amountUFXianPanPre = amountUFXianPanPre;
	}

	public BigDecimal getEmXianPanAct() {
		return emXianPanAct;
	}

	public void setEmXianPanAct(BigDecimal emXianPanAct) {
		this.emXianPanAct = emXianPanAct;
	}

	public BigDecimal getEfXianPanAct() {
		return efXianPanAct;
	}

	public void setEfXianPanAct(BigDecimal efXianPanAct) {
		this.efXianPanAct = efXianPanAct;
	}

	public BigDecimal getEf09XianPanAct() {
		return ef09XianPanAct;
	}

	public void setEf09XianPanAct(BigDecimal ef09XianPanAct) {
		this.ef09XianPanAct = ef09XianPanAct;
	}

	public BigDecimal getErXianPanAct() {
		return erXianPanAct;
	}

	public void setErXianPanAct(BigDecimal erXianPanAct) {
		this.erXianPanAct = erXianPanAct;
	}

	public BigDecimal getEhXianPanAct() {
		return ehXianPanAct;
	}

	public void setEhXianPanAct(BigDecimal ehXianPanAct) {
		this.ehXianPanAct = ehXianPanAct;
	}

	public BigDecimal getUfXianPanAct() {
		return ufXianPanAct;
	}

	public void setUfXianPanAct(BigDecimal ufXianPanAct) {
		this.ufXianPanAct = ufXianPanAct;
	}

	public BigDecimal getAmountMWXianPanAct() {
		return amountMWXianPanAct;
	}

	public void setAmountMWXianPanAct(BigDecimal amountMWXianPanAct) {
		this.amountMWXianPanAct = amountMWXianPanAct;
	}

	public BigDecimal getAmountEHXianPanAct() {
		return amountEHXianPanAct;
	}

	public void setAmountEHXianPanAct(BigDecimal amountEHXianPanAct) {
		this.amountEHXianPanAct = amountEHXianPanAct;
	}

	public BigDecimal getAmountUFXianPanAct() {
		return amountUFXianPanAct;
	}

	public void setAmountUFXianPanAct(BigDecimal amountUFXianPanAct) {
		this.amountUFXianPanAct = amountUFXianPanAct;
	}

	public BigDecimal getEhGasPreSH() {
		return ehGasPreSH;
	}

	public void setEhGasPreSH(BigDecimal ehGasPreSH) {
		this.ehGasPreSH = ehGasPreSH;
	}

	public BigDecimal getEhGasPreCost() {
		return ehGasPreCost;
	}

	public void setEhGasPreCost(BigDecimal ehGasPreCost) {
		this.ehGasPreCost = ehGasPreCost;
	}

	public BigDecimal getAmountGasPreCost() {
		return amountGasPreCost;
	}

	public void setAmountGasPreCost(BigDecimal amountGasPreCost) {
		this.amountGasPreCost = amountGasPreCost;
	}

	public BigDecimal getEhGasActSH() {
		return ehGasActSH;
	}

	public void setEhGasActSH(BigDecimal ehGasActSH) {
		this.ehGasActSH = ehGasActSH;
	}

	public BigDecimal getEhGasActCost() {
		return ehGasActCost;
	}

	public void setEhGasActCost(BigDecimal ehGasActCost) {
		this.ehGasActCost = ehGasActCost;
	}

	public BigDecimal getAmountGasActCost() {
		return amountGasActCost;
	}

	public void setAmountGasActCost(BigDecimal amountGasActCost) {
		this.amountGasActCost = amountGasActCost;
	}

	public BigDecimal getEhDanQiPreSH() {
		return ehDanQiPreSH;
	}

	public void setEhDanQiPreSH(BigDecimal ehDanQiPreSH) {
		this.ehDanQiPreSH = ehDanQiPreSH;
	}

	public BigDecimal getEhDanQiPreCost() {
		return ehDanQiPreCost;
	}

	public void setEhDanQiPreCost(BigDecimal ehDanQiPreCost) {
		this.ehDanQiPreCost = ehDanQiPreCost;
	}

	public BigDecimal getAmountDanQiPreCost() {
		return amountDanQiPreCost;
	}

	public void setAmountDanQiPreCost(BigDecimal amountDanQiPreCost) {
		this.amountDanQiPreCost = amountDanQiPreCost;
	}

	public BigDecimal getEhDanQiActSH() {
		return ehDanQiActSH;
	}

	public void setEhDanQiActSH(BigDecimal ehDanQiActSH) {
		this.ehDanQiActSH = ehDanQiActSH;
	}

	public BigDecimal getEhDanQiActCost() {
		return ehDanQiActCost;
	}

	public void setEhDanQiActCost(BigDecimal ehDanQiActCost) {
		this.ehDanQiActCost = ehDanQiActCost;
	}

	public BigDecimal getAmountDanQiActCost() {
		return amountDanQiActCost;
	}

	public void setAmountDanQiActCost(BigDecimal amountDanQiActCost) {
		this.amountDanQiActCost = amountDanQiActCost;
	}

	public BigDecimal getActUploadCostSheYuan() {
		return actUploadCostSheYuan;
	}

	public void setActUploadCostSheYuan(BigDecimal actUploadCostSheYuan) {
		this.actUploadCostSheYuan = actUploadCostSheYuan;
	}

	public BoundQuantity getBoundQuantity() {
		return boundQuantity;
	}

	public void setBoundQuantity(BoundQuantity boundQuantity) {
		this.boundQuantity = boundQuantity;
	}

	public BigDecimal getEmTongXieActPrice() {
		return emTongXieActPrice;
	}

	public void setEmTongXieActPrice(BigDecimal emTongXieActPrice) {
		this.emTongXieActPrice = emTongXieActPrice;
	}

	public BigDecimal getErTongXieActPrice() {
		return erTongXieActPrice;
	}

	public void setErTongXieActPrice(BigDecimal erTongXieActPrice) {
		this.erTongXieActPrice = erTongXieActPrice;
	}

	public BigDecimal getEhTongXieActPrice() {
		return ehTongXieActPrice;
	}

	public void setEhTongXieActPrice(BigDecimal ehTongXieActPrice) {
		this.ehTongXieActPrice = ehTongXieActPrice;
	}
}