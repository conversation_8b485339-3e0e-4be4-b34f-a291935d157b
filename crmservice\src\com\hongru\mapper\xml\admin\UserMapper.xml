<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.admin.UserMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.hongru.entity.admin.User">
		<id column="user_id" property="userId" />
		<result column="organization_id" property="organizationId" />
		<result column="login_name" property="loginName" />
		<result column="login_password" property="loginPassword" />
		<result column="salt" property="salt" />
		<result column="user_name" property="userName" />
		<result column="real_name" property="realName" />
		<result column="sex" property="sex" />
		<result column="age" property="age" />
		<result column="pic_img" property="picImg" />
		<result column="status" property="status" />
		<result column="email" property="email" />
		<result column="telephone" property="telephone" />
		<result column="last_login_time" property="lastLoginTime" />
		<result column="last_login_ip" property="lastLoginIp" />
		<result column="create_time" property="createTime" />
		<result column="create_by" property="createBy" />
		<result column="update_time" property="updateTime" />
		<result column="update_by" property="updateBy" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id AS userId, organization_id AS organizationId, login_name AS loginName, login_password AS loginPassword, salt, user_name AS userName, real_name AS realName, sex, age, pic_img AS picImg, status, email, telephone, last_login_time AS lastLoginTime, last_login_ip AS lastLoginIp, create_time AS createTime, create_by AS createBy, update_time AS updateTime, update_by AS updateBy
    </sql>
    
    <!-- 根据管理员ID查找管理员信息 -->
    <select id="getUserById" resultType="com.hongru.pojo.vo.UserVO">
    	SELECT
	    	u.user_id AS userId,
	    	u.organization_id AS organizationId, 
	    	o.organization_name AS organizationName, 
	    	u.login_name AS loginName,
	    	u.user_name AS userName,
	    	u.real_name AS realName,
	    	u.sex,
	    	u.age,
	    	u.pic_img AS picImg,
	    	u.status,
	    	u.email,
	    	u.telephone,
	    	u.last_login_time AS lastLoginTime,
	    	u.last_login_ip AS lastLoginIp,
	    	u.create_time AS createTime, 
	    	u.create_by AS createBy, 
	    	u.update_time AS updateTime, 
	    	u.update_by AS updateBy,
	    	u.companyId AS companyId,
	    	u.salt
	    FROM
	    	hr_admin_user u
	    LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
	    WHERE
	    	user_id = #{userId}
    </select>
    
    <!-- 根据管理员信息查找管理员列表 -->
    <select id="listByUser" resultType="com.hongru.pojo.vo.UserVO">
    	SELECT
	    	user_id AS userId, 
	    	u.organization_id AS organizationId, 
	    	o.organization_name AS organizationName, 
	    	login_name AS loginName, 
	    	user_name AS userName, 
	    	real_name AS realName, 
	    	sex, 
	    	age, 
	    	pic_img AS picImg, 
	    	u.status,
	    	email, 
	    	telephone, 
	    	last_login_time AS lastLoginTime, 
	    	last_login_ip AS lastLoginIp, 
	    	u.create_time AS createTime, 
	    	u.create_by AS createBy, 
	    	u.update_time AS updateTime, 
	    	u.update_by AS updateBy
	    FROM
	    	hr_admin_user u
	    LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
    	<where>
    		1=1
    		<if test="telephone != null and telephone != ''">
    			AND telephone = #{telephone}
    		</if>
    		<if test="email != null and email != ''">
    			AND email = #{email}
    		</if>
    		<if test="organizationId != null">
    			AND u.organization_id = #{organizationId}
    		</if>      		   		 		
    	</where>
    </select>
    
    <!-- 根据分页信息/搜索内容查找管理员列表 -->
	<sql id="listByPage_where">
		<if test="organizationId != null">
			AND u.organization_id = #{organizationId}
		</if>
		<if test="roleIds != null">
			AND r.role_id IN
			<foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
				#{roleId}
			</foreach>
		</if>
		<if test="search != null">
			AND (
			u.user_name LIKE  #{search} 
			OR u.real_name LIKE  #{search} 
			OR u.telephone LIKE  #{search} 
			OR u.email LIKE  #{search} 
			OR o.organization_name LIKE  #{search} 
			)
		</if>
		<if test="realNameLike != null and realNameLike !=''">
			AND u.real_name LIKE  #{realNameLike} 
		</if>
		<if test="emailLike != null and emailLike !=''">
			AND u.email LIKE  #{emailLike} 
		</if>
		<if test="telephoneLike != null and telephoneLike !=''">
			AND u.telephone LIKE  #{telephoneLike} 
		</if>
	</sql>
	<select id="listByPage" resultType="com.hongru.pojo.vo.UserVO">
     	SELECT
			u.user_id AS userId,
	    	u.organization_id AS organizationId, 
	    	o.organization_name AS organizationName,
			u.login_name AS loginName,
			u.user_name AS userName,
			u.real_name AS realName,
			u.sex,
			u.age,
			u.pic_img AS picImg,
	    	u.status,
			u.email,
			u.telephone,
			u.last_login_time AS lastLoginTime,
			u.last_login_ip AS lastLoginIp,
	    	u.create_time AS createTime, 
	    	u.create_by AS createBy, 
	    	u.update_time AS updateTime, 
	    	u.update_by AS updateBy,
	    	u.companyId AS companyId
	    FROM
	    	hr_admin_user u
	    LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
		LEFT JOIN (
				SELECT a.user_id as user_id,max(b.role_id) as role_id
				FROM hr_admin_user a LEFT JOIN hr_admin_user_role b ON a.user_id = b.user_id GROUP BY a.user_id
		)r ON r.user_id = u.user_id
    	<where>
			<include refid="listByPage_where"></include>
    	</where>
		ORDER BY u.user_id DESC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	<select id="listByPageCount" resultType="integer">
		SELECT
			COUNT(1)
		FROM
			hr_admin_user u
		LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
		LEFT JOIN (
			SELECT a.user_id as user_id,max(b.role_id) as role_id
			FROM hr_admin_user a LEFT JOIN hr_admin_user_role b ON a.user_id = b.user_id GROUP BY a.user_id
		)r ON r.user_id = u.user_id
		<where>
			<include refid="listByPage_where"></include>
		</where>
	</select>

    <!-- 根据分页信息/搜索内容查找管理员列表 -->
	<sql id="listByOrganizationId_where">
		<if test="organizationId != null">
			u.organization_id = #{organizationId}
		</if>
		<if test="search != null">
			AND (
			u.user_name LIKE  #{search} 
			OR u.real_name LIKE  #{search} 
			OR u.telephone LIKE  #{search} 
			OR u.email LIKE  #{search} 
			OR o.organization_name LIKE  #{search} 
			)
		</if>
	</sql>
    <select id="listByOrganizationId" resultType="com.hongru.pojo.vo.UserVO">
     	SELECT
	    	user_id AS userId, 
	    	u.organization_id AS organizationId, 
	    	o.organization_name AS organizationName, 
	    	login_name AS loginName, 
	    	user_name AS userName, 
	    	real_name AS realName, 
	    	sex, 
	    	age, 
	    	pic_img AS picImg, 
	    	u.status,
	    	email, 
	    	telephone, 
	    	last_login_time AS lastLoginTime, 
	    	last_login_ip AS lastLoginIp, 
	    	u.create_time AS createTime, 
	    	u.create_by AS createBy, 
	    	u.update_time AS updateTime, 
	    	u.update_by AS updateBy
	    FROM
	    	hr_admin_user u
	    LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
    	<where>
			<where>
				<include refid="listByOrganizationId_where"></include>
			</where>
    	</where>
		ORDER BY u.user_id DESC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
    </select>
	<select id="listByOrganizationIdCount" resultType="integer">
		SELECT
			COUNT(1)
		FROM
			hr_admin_user u
		LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
		<where>
			<include refid="listByOrganizationId_where"></include>
		</where>
	</select>


    <!-- 根据部门ID重置部门ID -->
    <update id="updateOrganization">
    	UPDATE hr_admin_user
    	SET
    		organization_id = null
    	WHERE
    		organization_id = #{organizationId}
    </update>

	<update id="resetPwd">
    	UPDATE hr_admin_user
    	SET
    		login_password = #{newPassword}
    	WHERE
    		user_id = #{userId}
    </update>
    
    <select id="listByCompanyId" resultType="com.hongru.pojo.vo.UserVO">
     	SELECT
	    	user_id AS userId, 
	    	u.organization_id AS organizationId, 
	    	o.organization_name AS organizationName, 
	    	login_name AS loginName, 
	    	user_name AS userName, 
	    	real_name AS realName, 
	    	sex, 
	    	age, 
	    	pic_img AS picImg, 
	    	u.status,
	    	email, 
	    	telephone, 
	    	last_login_time AS lastLoginTime, 
	    	last_login_ip AS lastLoginIp, 
	    	u.create_time AS createTime, 
	    	u.create_by AS createBy, 
	    	u.update_time AS updateTime, 
	    	u.update_by AS updateBy
	    FROM
	    	hr_admin_user u
	    LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
    	<where>
    		u.companyId = #{companyId}
    	</where>   
    </select>
    <select id="getUserByJobNumber" resultType="com.hongru.entity.admin.User">
		SELECT
			TOP 1
	    	u.user_id AS userId,
	    	u.organization_id AS organizationId,
	    	o.organization_name AS organizationName,
	    	u.login_name AS loginName,
	    	u.user_name AS userName,
	    	u.real_name AS realName,
	    	u.sex,
	    	u.age,
	    	u.pic_img AS picImg,
	    	u.status,
	    	u.email,
	    	u.telephone,
	    	u.last_login_time AS lastLoginTime,
	    	u.last_login_ip AS lastLoginIp,
	    	u.create_time AS createTime,
	    	u.create_by AS createBy,
	    	u.update_time AS updateTime,
	    	u.update_by AS updateBy,
	    	u.companyId AS companyId,
	    	u.salt
	    FROM
	    	hr_admin_user u
	    LEFT JOIN hr_admin_organization o ON u.organization_id = o.organization_id
	    WHERE
	    	jobNumber = #{jobNumber}
		ORDER BY user_id DESC
	</select>
</mapper>
