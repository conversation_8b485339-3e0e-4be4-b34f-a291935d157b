package com.hongru.service.impl.stat;

import com.hongru.entity.stat.ReportForms;
import com.hongru.mapper.stat.ReportFormsMapper;
import com.hongru.pojo.dto.ReportFormsDTO;
import com.hongru.service.stat.IReportFormsService;
import com.hongru.support.page.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReportFormsServiceImpl implements IReportFormsService {
	@Autowired
	private ReportFormsMapper reportFormsMapper;

	/**
	* 添加报表
	* @throws
	* <AUTHOR>
	* @create 2023/9/18 15:49
	* @return
	*/
	@Override
	public int addReportForms(ReportForms reportForms) throws Exception {
		reportFormsMapper.insertReportForms(reportForms);
		return reportForms.getReportFormsId();
	}

	/**
	* 删除报表
	* @throws
	* <AUTHOR>
	* @create 2025/04/08 15:49
	* @return
	*/
	@Override
	public void removeReportForms(Integer reportFormsId) throws Exception {
		reportFormsMapper.removeReportForms(reportFormsId);
	}
	
	/**
	* 获取报表分页列表
	* @throws
	* <AUTHOR>
	* @create 2023/9/18 10:04
	* @return
	*/
	@Override
	public ReportFormsDTO listReportFormsByPage(Short reportType, String title, String year, String yearAndMonth, PageInfo pageInfo,String userName) throws Exception {
		List<ReportForms> reportFormsList = reportFormsMapper.listReportFormsByPage( reportType, title, year, yearAndMonth, pageInfo);

		for(ReportForms reportForms:reportFormsList){
			if(userName.equals(reportForms.getCreatorName())) {
				reportForms.setState((short) 1);
			}
		}
		Integer total = reportFormsMapper.listReportFormsByPageCount( reportType, title, year,yearAndMonth);
		pageInfo.setTotal(total);
		return new ReportFormsDTO(pageInfo,reportFormsList);
	}
}