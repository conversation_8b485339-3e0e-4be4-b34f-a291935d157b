<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.ProductCostDesignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hongru.entity.cost.ProductCostDesign">
        <id column="流水号" property="serialNumber" />
        <result column="年度" property="year" />
        <result column="成本键" property="costKey" />
        <result column="客户简称" property="customerName" />
        <result column="产品代码" property="productCode" />
        <result column="产品条码" property="productBarcode" />
        <result column="线盘名称" property="wireDiscName" />
        <result column="产品尺寸" property="productSize" />
        <result column="皮膜厚1" property="filmThickness1" />
        <result column="皮膜厚2" property="filmThickness2" />
        <result column="皮膜厚3" property="filmThickness3" />
        <result column="皮膜厚4" property="filmThickness4" />
        <result column="导体品目" property="conductorItem" />
        <result column="油漆品目1" property="paintItem1" />
        <result column="油漆品目2" property="paintItem2" />
        <result column="油漆品目3" property="paintItem3" />
        <result column="油漆品目4" property="paintItem4" />
        <result column="油漆品目5" property="paintItem5" />
        <result column="线盘品目" property="wireDiscItem" />
        <result column="运输费" property="transportFee" />
        <result column="量试区分" property="quantityTestType" />
        <result column="产品分类" property="productCategory" />
        <result column="计算区分" property="calculationType" />
        <result column="导体重量" property="conductorWeight" />
        <result column="涂料重量1" property="paintWeight1" />
        <result column="涂料重量2" property="paintWeight2" />
        <result column="涂料重量3" property="paintWeight3" />
        <result column="涂料重量4" property="paintWeight4" />
        <result column="涂料重量5" property="paintWeight5" />
        <result column="卷轴重量" property="wireDiscWeight" />
        <result column="创建者" property="creatorName" />
        <result column="创建时间" property="createdTime" />
        <result column="更新者" property="updaterName" />
        <result column="更新时间" property="updatedTime" />
    </resultMap>

    <!-- 原料项目单价映射结果 -->
    <resultMap id="RawMaterialItemResultMap" type="com.hongru.entity.cost.RawMaterialItem">
        <id column="流水号" property="serialNumber" />
        <result column="年度" property="year" />
        <result column="原料区分" property="materialType" />
        <result column="品目" property="itemCode" />
        <result column="品目名" property="itemName" />
        <result column="单价" property="unitPrice" />
    </resultMap>

    <!-- 运费单价映射结果 -->
    <resultMap id="TransportUnitPriceResultMap" type="com.hongru.entity.cost.TransportUnitPrice">
        <id column="流水号" property="serialNumber" />
        <result column="年度" property="year" />
        <result column="地区" property="region" />
        <result column="单价" property="unitPrice" />
    </resultMap>

    <!-- 客户映射结果 -->
    <resultMap id="CustomerResultMap" type="com.hongru.entity.sumitomo.Customer">
        <id column="客户代码" property="customerCode" />
        <result column="客户简称" property="customerName" />
        <result column="客户全称" property="customerFullName" />
    </resultMap>

    <!-- 产品映射结果 -->
    <resultMap id="ProductResultMap" type="com.hongru.entity.sumitomo.Product">
        <id column="产品代码" property="productCode" />
        <result column="产品信息" property="productInfo" />
        <result column="用户名" property="userName" />
        <result column="平角线尺寸" property="flatWireSize" />
    </resultMap>

    <!-- 产品设计数据映射结果 -->
    <resultMap id="ProductDesignDataResultMap" type="com.hongru.entity.pims.ProductDesignData">
        <id column="流水号" property="serialNumber" />
        <result column="产品代码" property="productCode" />
        <result column="条码" property="barcode" />
        <result column="标签尺寸名称" property="labelSizeName" />
        <result column="膜厚底1.STD" property="filmThickness1Std" />
        <result column="膜厚底2.STD" property="filmThickness2Std" />
        <result column="膜厚中.STD" property="filmThickness3Std" />
        <result column="膜厚上.STD" property="filmThickness4Std" />
        <result column="线盘名称" property="wireDiscName" />
        <result column="有效状态" property="validStatus" />
    </resultMap>

    <!-- 产品制造设计映射结果 -->
    <resultMap id="ProductManufacturingDesignResultMap" type="com.hongru.entity.cost.ProductManufacturingDesign">
        <id column="流水号" property="serialNumber" />
        <result column="年度" property="year" />
        <result column="条码" property="barcode" />
        <result column="客户简称" property="customerName" />
        <result column="产品代码" property="productCode" />
        <result column="产品条码" property="productBarcode" />
        <result column="线盘名称" property="wireDiscName" />
        <result column="产品尺寸" property="productSize" />
        <result column="皮膜厚1" property="filmThickness1" />
        <result column="皮膜厚2" property="filmThickness2" />
        <result column="皮膜厚3" property="filmThickness3" />
        <result column="皮膜厚4" property="filmThickness4" />
        <result column="导体原料" property="conductorMaterial" />
        <result column="涂料1" property="paint1" />
        <result column="涂料2" property="paint2" />
        <result column="涂料3" property="paint3" />
        <result column="涂料4" property="paint4" />
        <result column="涂料5" property="paint5" />
        <result column="卷轴原料" property="wireDiscMaterial" />
        <result column="运输费" property="transportFee" />
        <result column="量试区分" property="quantityTestType" />
        <result column="产品分类" property="productCategory" />
        <result column="计算区分" property="calculationType" />
        <result column="导体重量" property="conductorWeight" />
        <result column="涂料重量1" property="paintWeight1" />
        <result column="涂料重量2" property="paintWeight2" />
        <result column="涂料重量3" property="paintWeight3" />
        <result column="涂料重量4" property="paintWeight4" />
        <result column="涂料重量5" property="paintWeight5" />
        <result column="卷轴重量" property="wireDiscWeight" />
        <result column="创建者" property="creatorName" />
        <result column="创建时间" property="createdTime" />
        <result column="更新者" property="updaterName" />
        <result column="更新时间" property="updatedTime" />
    </resultMap>

    <!-- 产品成本映射结果 -->
    <resultMap id="ProductCostResultMap" type="com.hongru.entity.cost.ProductCost">
        <id column="流水号" property="serialNumber" />
        <result column="年度" property="year" />
        <result column="成本键" property="costKey" />
        <result column="客户简称" property="customerName" />
        <result column="产品代码" property="productCode" />
        <result column="产品条码" property="productBarcode" />
        <result column="线盘名称" property="wireDiscName" />
        <result column="尺寸" property="size" />
        <result column="产品分类" property="productCategory" />
        <result column="计算区分" property="calculationType" />
        <result column="成本计算状态" property="costCalculationStatus" />
        <result column="导体重量" property="conductorWeight" />
        <result column="涂料重量1" property="paintWeight1" />
        <result column="涂料重量2" property="paintWeight2" />
        <result column="涂料重量3" property="paintWeight3" />
        <result column="涂料重量4" property="paintWeight4" />
        <result column="涂料重量5" property="paintWeight5" />
        <result column="卷轴重量" property="wireDiscWeight" />
        <result column="创建者" property="creatorName" />
        <result column="创建时间" property="createdTime" />
        <result column="更新者" property="updaterName" />
        <result column="更新时间" property="updatedTime" />
    </resultMap>

    <!-- 根据年度和原料区分查询原料项目单价 -->
    <select id="getRawMaterialItems" resultMap="RawMaterialItemResultMap">
        SELECT 流水号, 年度, 原料区分, 品目, 品目名, 单价
        FROM [CostPrice].[dbo].[原料项目单价表]
        WHERE 年度 = #{year} AND 原料区分 = #{materialType}
        ORDER BY 品目
    </select>

    <!-- 根据年度查询运费单价 -->
    <select id="getTransportUnitPrices" resultMap="TransportUnitPriceResultMap">
        SELECT 流水号, 年度, 地区, 单价
        FROM [CostPrice].[dbo].[运费单价表]
        WHERE 年度 = #{year}
        ORDER BY 地区
    </select>

    <!-- 查询客户列表 -->
    <select id="getCustomers" resultMap="CustomerResultMap">
        SELECT 客户代码, 客户简称, 客户全称
        FROM [sumitomo].[dbo].[客户表]
        ORDER BY 客户代码
    </select>

    <!-- 根据客户简称和产品分类查询产品列表 -->
    <select id="getProducts" resultMap="ProductResultMap">
        SELECT 产品代码, 产品信息, 用户名, 平角线尺寸
        FROM [sumitomo].[dbo].[产品表]
        WHERE 用户名 = #{customerName}
        <if test="productCategory == '平角线'">
            AND 平角线尺寸 IS NOT NULL AND 平角线尺寸 > 0
        </if>
        ORDER BY 产品代码
    </select>

    <!-- 根据产品代码查询产品设计数据 -->
    <select id="getProductDesignData" resultMap="ProductDesignDataResultMap">
        SELECT 流水号, 产品代码, 条码, 标签尺寸名称, [膜厚底1.STD], [膜厚底2.STD], [膜厚中.STD], [膜厚上.STD], 线盘名称, 有效状态
        FROM [PIMS].[dbo].[产品设计数据表]
        WHERE 产品代码 = #{productCode} AND 有效状态 = '0'
        ORDER BY 流水号 DESC
    </select>

    <!-- 根据条件查询产品制造设计 -->
    <select id="getProductManufacturingDesigns" resultMap="ProductManufacturingDesignResultMap">
        SELECT *
        FROM [CostPrice].[dbo].[产品制造设计表]
        WHERE 年度 = #{year} AND 产品代码 = #{productCode} AND 产品条码 = #{productBarcode} AND 客户简称 = #{customerName}
        ORDER BY 流水号 DESC
    </select>

    <!-- 新增产品制造设计 -->
    <insert id="addProductManufacturingDesign" parameterType="com.hongru.entity.cost.ProductManufacturingDesign">
        INSERT INTO [CostPrice].[dbo].[产品制造设计表] (
            年度, 条码, 客户简称, 产品代码, 产品条码, 线盘名称, 产品尺寸, 皮膜厚1, 皮膜厚2, 皮膜厚3, 皮膜厚4,
            导体原料, 涂料1, 涂料2, 涂料3, 涂料4, 涂料5, 卷轴原料, 运输费, 量试区分, 产品分类, 计算区分,
            导体重量, 涂料重量1, 涂料重量2, 涂料重量3, 涂料重量4, 涂料重量5, 卷轴重量, 创建者, 创建时间
        ) VALUES (
            #{year}, #{barcode}, #{customerName}, #{productCode}, #{productBarcode}, #{wireDiscName}, #{productSize},
            #{filmThickness1}, #{filmThickness2}, #{filmThickness3}, #{filmThickness4}, #{conductorMaterial},
            #{paint1}, #{paint2}, #{paint3}, #{paint4}, #{paint5}, #{wireDiscMaterial}, #{transportFee},
            #{quantityTestType}, #{productCategory}, #{calculationType}, #{conductorWeight}, #{paintWeight1},
            #{paintWeight2}, #{paintWeight3}, #{paintWeight4}, #{paintWeight5}, #{wireDiscWeight},
            #{creatorName}, #{createdTime}
        )
    </insert>

    <!-- 更新产品制造设计 -->
    <update id="updateProductManufacturingDesign" parameterType="com.hongru.entity.cost.ProductManufacturingDesign">
        UPDATE [CostPrice].[dbo].[产品制造设计表]
        SET 客户简称 = #{customerName}, 产品代码 = #{productCode}, 产品条码 = #{productBarcode}, 线盘名称 = #{wireDiscName},
            产品尺寸 = #{productSize}, 皮膜厚1 = #{filmThickness1}, 皮膜厚2 = #{filmThickness2}, 皮膜厚3 = #{filmThickness3},
            皮膜厚4 = #{filmThickness4}, 导体原料 = #{conductorMaterial}, 涂料1 = #{paint1}, 涂料2 = #{paint2},
            涂料3 = #{paint3}, 涂料4 = #{paint4}, 涂料5 = #{paint5}, 卷轴原料 = #{wireDiscMaterial},
            运输费 = #{transportFee}, 量试区分 = #{quantityTestType}, 产品分类 = #{productCategory}, 计算区分 = #{calculationType},
            导体重量 = #{conductorWeight}, 涂料重量1 = #{paintWeight1}, 涂料重量2 = #{paintWeight2}, 涂料重量3 = #{paintWeight3},
            涂料重量4 = #{paintWeight4}, 涂料重量5 = #{paintWeight5}, 卷轴重量 = #{wireDiscWeight},
            更新者 = #{updaterName}, 更新时间 = #{updatedTime}
        WHERE 年度 = #{year} AND 条码 = #{barcode}
    </update>

    <!-- 新增产品成本 -->
    <insert id="addProductCost" parameterType="com.hongru.entity.cost.ProductCost">
        INSERT INTO [CostPrice].[dbo].[产品成本表] (
            年度, 成本键, 客户简称, 产品代码, 产品条码, 线盘名称, 尺寸, 产品分类, 计算区分, 成本计算状态,
            导体重量, 涂料重量1, 涂料重量2, 涂料重量3, 涂料重量4, 涂料重量5, 卷轴重量, 创建者, 创建时间
        ) VALUES (
            #{year}, #{costKey}, #{customerName}, #{productCode}, #{productBarcode}, #{wireDiscName}, #{size},
            #{productCategory}, #{calculationType}, #{costCalculationStatus}, #{conductorWeight}, #{paintWeight1},
            #{paintWeight2}, #{paintWeight3}, #{paintWeight4}, #{paintWeight5}, #{wireDiscWeight},
            #{creatorName}, #{createdTime}
        )
    </insert>

    <!-- 更新产品成本 -->
    <update id="updateProductCost" parameterType="com.hongru.entity.cost.ProductCost">
        UPDATE [CostPrice].[dbo].[产品成本表]
        SET 客户简称 = #{customerName}, 产品代码 = #{productCode}, 产品条码 = #{productBarcode}, 线盘名称 = #{wireDiscName},
            尺寸 = #{size}, 产品分类 = #{productCategory}, 计算区分 = #{calculationType}, 成本计算状态 = #{costCalculationStatus},
            导体重量 = #{conductorWeight}, 涂料重量1 = #{paintWeight1}, 涂料重量2 = #{paintWeight2}, 涂料重量3 = #{paintWeight3},
            涂料重量4 = #{paintWeight4}, 涂料重量5 = #{paintWeight5}, 卷轴重量 = #{wireDiscWeight},
            更新者 = #{updaterName}, 更新时间 = #{updatedTime}
        WHERE 年度 = #{year} AND 成本键 = #{costKey}
    </update>

</mapper>
