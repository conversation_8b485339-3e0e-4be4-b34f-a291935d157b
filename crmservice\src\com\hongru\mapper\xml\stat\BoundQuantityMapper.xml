<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.stat.BoundQuantityMapper">
    <sql id="BoundQuantity_sql">
		bo.[流水号] AS statId,bo.[年月] AS yearMonth,
		bo.[EM入库量] AS emInboundQuantity,bo.[EF入库量] AS efInboundQuantity,bo.[EF09入库量] AS ef09InboundQuantity,
		bo.[ER入库量] AS erInboundQuantity,bo.[EH日立外入库量] AS ehInboundQuantity1,bo.[EH入库量] AS ehInboundQuantity2,
		bo.[UF太线入库量] AS uftxInboundQuantity,bo.[UF细线入库量] AS ufxxInboundQuantity,
		bo.[EM出库量] AS emOutboundQuantity,bo.[EF出库量] AS efOutboundQuantity,bo.[EF09出库量] AS ef09OutboundQuantity,
		bo.[ER出库量] AS erOutboundQuantity,bo.[EH日立外出库量] AS ehOutboundQuantity1,bo.[EH出库量] AS ehOutboundQuantity2,
		bo.[UF太线出库量] AS uftxOutboundQuantity,bo.[UF细线出库量] AS ufxxOutboundQuantity
	</sql>
	
	<select id="getBoundQuantityById" resultType="com.hongru.entity.stat.BoundQuantity">
		SELECT
		<include refid="BoundQuantity_sql"/>
		FROM [CostPrice].[dbo].[入库表] bo
		<where>
			<if test="statId != null and statId != ''">
				AND bo.[流水号] = #{statId}
			</if>
		</where>
	</select>

	<select id="getBoundQuantityByYearMonth" resultType="com.hongru.entity.stat.BoundQuantity">
		SELECT
		<include refid="BoundQuantity_sql"/>		
<!-- 			bo.[流水号] AS statId,bo.[年月] AS yearMonth, -->
<!-- 			bo.[EM入库量] AS emInboundQuantity,(bo.[EF入库量]+bo.[EF09入库量]) AS efInboundQuantity,bo.[EF09入库量] AS ef09InboundQuantity, -->
<!-- 			bo.[ER入库量] AS erInboundQuantity,bo.[EH日立外入库量] AS ehInboundQuantity1,bo.[EH入库量] AS ehInboundQuantity2, -->
<!-- 			bo.[UF太线入库量] AS uftxInboundQuantity,bo.[UF细线入库量] AS ufxxInboundQuantity, -->
<!-- 			bo.[EM出库量] AS emOutboundQuantity,(bo.[EF出库量]+bo.[EF09出库量]) AS efOutboundQuantity,bo.[EF09出库量] AS ef09OutboundQuantity, -->
<!-- 			bo.[ER出库量] AS erOutboundQuantity,bo.[EH日立外出库量] AS ehOutboundQuantity1,bo.[EH出库量] AS ehOutboundQuantity2, -->
<!-- 			bo.[UF太线出库量] AS uftxOutboundQuantity,bo.[UF细线出库量] AS ufxxOutboundQuantity -->
		FROM [CostPrice].[dbo].[入库表] bo
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND bo.[年月] = #{yearMonth}
			</if>
		</where>
		order by bo.[流水号] desc
	</select>

	<select id="selectBoundQuantityList" resultType="com.hongru.entity.stat.BoundQuantity">
		SELECT
		<include refid="BoundQuantity_sql"/>
		FROM [CostPrice].[dbo].[入库表] bo
		<where>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			order by bo.[年月] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="selectBoundQuantityCount" resultType="integer">
		SELECT COUNT(1)
		FROM [CostPrice].[dbo].[入库表] bo
	</select>

	<insert id="insertBoundQuantity" parameterType="com.hongru.entity.stat.BoundQuantity">
		INSERT INTO [CostPrice].[dbo].[入库表]
		(
		[年月],
		[EM入库量],
		[EF入库量],
		[EF09入库量],
		[ER入库量],
		[EH日立外入库量],
		[EH入库量],
		[UF太线入库量],
		[UF细线入库量],
		[EM出库量],
		[EF出库量],
		[EF09出库量],
		[ER出库量],
		[EH日立外出库量],
		[EH出库量],
		[UF太线出库量],
		[UF细线出库量]
		)VALUES(
		#{boundQuantity.yearMonth},
		#{boundQuantity.emInboundQuantity},
		#{boundQuantity.efInboundQuantity},
		#{boundQuantity.ef09InboundQuantity},
		#{boundQuantity.erInboundQuantity},
		#{boundQuantity.ehInboundQuantity1},
		#{boundQuantity.ehInboundQuantity2},
		#{boundQuantity.uftxInboundQuantity},
		#{boundQuantity.ufxxInboundQuantity},
		#{boundQuantity.emOutboundQuantity},
		#{boundQuantity.efOutboundQuantity},
		#{boundQuantity.ef09OutboundQuantity},
		#{boundQuantity.erOutboundQuantity},
		#{boundQuantity.ehOutboundQuantity1},
		#{boundQuantity.ehOutboundQuantity2},
		#{boundQuantity.uftxOutboundQuantity},
		#{boundQuantity.ufxxOutboundQuantity}
		)
	</insert>

	<update id="updateBoundQuantity">
		UPDATE [CostPrice].[dbo].[入库表]
		<set>
			<if test="boundQuantity.yearMonth != null and boundQuantity.yearMonth != ''">
				[年月] = #{boundQuantity.yearMonth},
			</if>
			<if test="boundQuantity.emInboundQuantity != null">
				[EM入库量] = #{boundQuantity.emInboundQuantity},
			</if>
			<if test="boundQuantity.efInboundQuantity != null">
				[EF入库量] = #{boundQuantity.efInboundQuantity},
			</if>
			<if test="boundQuantity.ef09InboundQuantity != null">
				[EF09入库量] = #{boundQuantity.ef09InboundQuantity},
			</if>
			<if test="boundQuantity.erInboundQuantity != null">
				[ER入库量] = #{boundQuantity.erInboundQuantity},
			</if>
			<if test="boundQuantity.ehInboundQuantity1 != null">
				[EH日立外入库量] = #{boundQuantity.ehInboundQuantity1},
			</if>
			<if test="boundQuantity.ehInboundQuantity2 != null">
				[EH入库量] = #{boundQuantity.ehInboundQuantity2},
			</if>
			<if test="boundQuantity.uftxInboundQuantity != null">
				[UF太线入库量] = #{boundQuantity.uftxInboundQuantity},
			</if>
			<if test="boundQuantity.ufxxInboundQuantity != null">
				[UF细线入库量] = #{boundQuantity.ufxxInboundQuantity},
			</if>
			<if test="boundQuantity.emOutboundQuantity != null">
				[EM出库量] = #{boundQuantity.emOutboundQuantity},
			</if>
			<if test="boundQuantity.efOutboundQuantity != null">
				[EF出库量] = #{boundQuantity.efOutboundQuantity},
			</if>
			<if test="boundQuantity.ef09OutboundQuantity != null">
				[EF09出库量] = #{boundQuantity.ef09OutboundQuantity},
			</if>
			<if test="boundQuantity.erOutboundQuantity != null">
				[ER出库量] = #{boundQuantity.erOutboundQuantity},
			</if>
			<if test="boundQuantity.ehOutboundQuantity1 != null">
				[EH日立外出库量] = #{boundQuantity.ehOutboundQuantity1},
			</if>
			<if test="boundQuantity.ehOutboundQuantity2 != null">
				[EH出库量] = #{boundQuantity.ehOutboundQuantity2},
			</if>
			<if test="boundQuantity.uftxOutboundQuantity != null">
				[UF太线出库量] = #{boundQuantity.uftxOutboundQuantity},
			</if>
			<if test="boundQuantity.ufxxOutboundQuantity != null">
				[UF细线出库量] = #{boundQuantity.ufxxOutboundQuantity},
			</if>
		</set>
		<where>
			<if test="boundQuantity.statId != null">
				AND [流水号] = #{boundQuantity.statId}
			</if>
		</where>
	</update>

	<delete id="updateState">
		DELETE FROM [CostPrice].[dbo].[入库表]
		WHERE [流水号] = #{statId}
	</delete>
	
	<select id="selectStorageInActBeanForMWList" resultType="com.hongru.entity.yearRevise.StorageInActBean">
		SELECT
		SUM([EM入库量]) / #{monthInterval} AS storageInActEM, SUM([EF入库量]) / #{monthInterval} AS storageInActEF,
		SUM([EF09入库量]) / #{monthInterval} AS storageInActEF09, SUM([ER入库量]) / #{monthInterval} AS storageInActER,
		SUM([EH入库量]) / #{monthInterval} AS storageInActEH
		FROM  [CostPrice].[dbo].[入库表]
		WHERE [年月] &gt;=#{timeStartStr} AND [年月] &lt;= #{timeEndStr}
	</select>
	
</mapper>