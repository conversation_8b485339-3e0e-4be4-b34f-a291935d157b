<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/electric/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="${electricPriceCost.yearMonth}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>电费计算方式:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="calculateTag" name="calculateTag" required>
                                <option value="">${electricPriceCost.calculateTagStr}</option>
                                <option value="1">不分时电费</option>
                                <option value="2">分时电费（峰）</option>
                                <option value="3">分时电费（平）</option>
                                <option value="4">分时电费（谷）</option>
                                <option value="5">分时电费（尖峰）</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>单价:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eleSinglePrice" name="eleSinglePrice" value="${electricPriceCost.eleSinglePrice}" onKeyUp="amountV2(this)" onBlur="overFormatV2(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>用量(KW/H):</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eleCost" name="eleCost" value="${electricPriceCost.eleCost}" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                     <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>单价(实际):</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eleSinglePriceAct" name="eleSinglePriceAct" value="${electricPriceCost.eleSinglePriceAct}" onKeyUp="amountV2(this)" onBlur="overFormatV2(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>用量(实际):</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eleCostAct" name="eleCostAct" value="${electricPriceCost.eleCostAct}" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="costId" name="costId" value="${electricPriceCost.costId}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/electricCostPrice_add.js?time=1"></script>
</myfooter>
</body>
</html>
