package com.hongru.controller.yearParamSet;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.cost.*;
import com.hongru.entity.yearRevise.SummaryOfDepartmentalUnitPricesBean;
import com.hongru.pojo.dto.*;
import com.hongru.service.admin.IUserService;
import com.hongru.service.cost.ICostService;
import com.hongru.service.yearRevise.IYearReviseService;
import com.hongru.support.page.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 年度参数设定控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/yearParamSet")
public class YearParamSetController extends BaseController {

    @Autowired
    private ICostService costService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IYearReviseService yearReviseService;

    /*
     * =================================部门单价表======================================
     */
    /**
     * 部门单价表列表页面
     * 
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/departmentalUnitPrice/list/view")
    public String departmentalUnitPriceListView(Model model) throws Exception {
        return "/modules/yearParamSet/departmentalUnitPrice_list";
    }

    /**
     * 部门单价表检索
     *
     * @param isSearch    是否检索
     * @param year        年度
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param machineType 机械类别
     * @param factoryType 工场区分(MW/UF)
     * @param pageInfo    分页信息
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/departmentalUnitPrice/listPage")
    @ResponseBody
    public Object departmentalUnitPriceListPage(short isSearch, String year, String startDate, String endDate, String machineType, String factoryType,
            PageInfo pageInfo)
            throws Exception {
        if ("HZ".equals(factoryType)) {
            // 部门汇总查询
            List<SummaryOfDepartmentalUnitPricesBean> departmentCostTotalList = Optional.ofNullable(
                    costService.getDepartmentCostTotal(startDate, endDate)).orElse(new ArrayList<>());

            return new HrPageResult(departmentCostTotalList, departmentCostTotalList.size());
        } else if (isSearch == 1) {
            // 当isSearch=1时进行查询，允许条件为空（查询全部）
            DepartmentUnitPriceDTO departmentUnitPriceDTO = costService.listDepartmentUnitPricePage(year, machineType,
                    factoryType,
                    pageInfo);
            return new HrPageResult(departmentUnitPriceDTO.getDepartmentUnitPriceList(),
                    departmentUnitPriceDTO.getPageInfo().getTotal());
        } else {
            // 当isSearch=0时返回空结果
            return new HrPageResult(new ArrayList<DepartmentUnitPrice>(), 0);
        }
    }

    /**
     * 部门单价数据读取页面
     *
     * @param model
     * @param factoryType 工场区分
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/departmentalUnitPrice/dataImport/view")
    public String departmentalUnitPriceDataImportView(Model model, String factoryType) throws Exception {
        model.addAttribute("factoryType", factoryType);
        return "/modules/yearParamSet/departmentalUnitPrice_dataImport";
    }

    /**
     * 数据读取按钮 - 从年度改订部门单价汇总导入数据
     *
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param factoryType 工场区分
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/departmentalUnitPrice/importData")
    @ResponseBody
    public Object departmentalUnitPriceImportData(String startDate, String endDate, String factoryType)
            throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            //根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            int importCount = costService.importDepartmentUnitPriceFromSummary(startDate, endDate, factoryType, user.getUserName());
            return new HrResult(CommonReturnCode.SUCCESS, "成功导入" + importCount + "条数据");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("部门单价表数据导入异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
    }

    /**
     * 新增部门单价表页面
     *
     * @param model
     * @param factoryType 工场区分
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/departmentalUnitPrice/add/view")
    public String departmentalUnitPriceAddView(Model model, String factoryType) throws Exception {
        model.addAttribute("factoryType", factoryType);
        return "/modules/yearParamSet/departmentalUnitPrice_add";
    }

    /**
     * 新增部门单价表
     * 
     * @param departmentUnitPrice
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/departmentalUnitPrice/add")
    @ResponseBody
    public Object departmentalUnitPriceAdd(DepartmentUnitPrice departmentUnitPrice) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            // 根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            departmentUnitPrice.setCreatorName(user.getUserName());
            departmentUnitPrice.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            departmentUnitPrice.setUpdaterName(user.getUserName());
            departmentUnitPrice.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.addDepartmentUnitPrice(departmentUnitPrice);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("部门单价表新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑部门单价表页面
     * 
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/departmentalUnitPrice/edit/view")
    public String departmentalUnitPriceEditView(Model model, Integer serialNumber) throws Exception {
        DepartmentUnitPrice departmentUnitPrice = costService.getDepartmentUnitPriceById(serialNumber);
        model.addAttribute("departmentUnitPrice", departmentUnitPrice);
        return "/modules/yearParamSet/departmentalUnitPrice_edit";
    }

    /**
     * 编辑部门单价表
     * 
     * @param departmentUnitPrice
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/departmentalUnitPrice/edit")
    @ResponseBody
    public Object departmentalUnitPriceEdit(DepartmentUnitPrice departmentUnitPrice) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            // 根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            departmentUnitPrice.setUpdaterName(user.getUserName());
            departmentUnitPrice.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.modifyDepartmentUnitPrice(departmentUnitPrice);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("部门单价表编辑异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================成本参数复制======================================
     */
    /**
     * 成本参数复制页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/copyCostParameters/list/view")
    public String copyCostParametersListView(Model model) throws Exception {
        return "/modules/yearParamSet/copyCostParameters_list";
    }

    /**
     * 成本参数复制操作
     * 将费用项目单价表、原料项目表、导体单价明细表、油漆单价明细表、线盘单价明细表
     * 从源年度复制到目标年度
     *
     * @param fromYear 源年度
     * @param toYear   目标年度
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/copyCostParameters/copy")
    @ResponseBody
    public Object copyCostParameters(String fromYear, String toYear) throws Exception {
        try {
            // 参数验证
            if (fromYear == null || fromYear.trim().isEmpty()) {
                return new HrResult(CommonReturnCode.FAILED, "请选择源年度");
            }
            if (toYear == null || toYear.trim().isEmpty()) {
                return new HrResult(CommonReturnCode.FAILED, "请选择目标年度");
            }
            if (fromYear.equals(toYear)) {
                return new HrResult(CommonReturnCode.FAILED, "源年度和目标年度不能相同");
            }

            // 执行复制操作
            int copiedCount = costService.copyCostParameters(fromYear, toYear);

            return new HrResult(CommonReturnCode.SUCCESS, "成功复制" + copiedCount + "条数据");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("成本参数复制异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
    }

    /*
     * =================================费用项目单价表=====================================
     */
    /**
     * 费用项目单价表列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/unitPriceOfExpenseItems/list/view")
    public String unitPriceOfExpenseItemsListView(Model model) throws Exception {
        return "/modules/yearParamSet/unitPriceOfExpenseItems_list";
    }

    /**
     * 费用项目单价表检索
     *
     * @param isSearch 是否检索
     * @param year     年度
     * @param pageInfo 分页信息
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/unitPriceOfExpenseItems/listPage")
    @ResponseBody
    public Object unitPriceOfExpenseItemsListPage(short isSearch, String year, PageInfo pageInfo) throws Exception {
        if (isSearch == 1) {
            // 当isSearch=1时进行查询，允许条件为空（查询全部）
            ExpenseItemUnitPriceDTO expenseItemUnitPriceDTO = costService.listExpenseItemUnitPricePage(year, pageInfo);
            return new HrPageResult(expenseItemUnitPriceDTO.getExpenseItemUnitPriceList(),
                    expenseItemUnitPriceDTO.getPageInfo().getTotal());
        } else {
            // 当isSearch=0时返回空结果
            return new HrPageResult(new ArrayList<ExpenseItemUnitPrice>(), 0);
        }
    }

    /**
     * 新增费用项目单价表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/unitPriceOfExpenseItems/add/view")
    public String unitPriceOfExpenseItemsAddView(Model model) throws Exception {
        return "/modules/yearParamSet/unitPriceOfExpenseItems_add";
    }

    /**
     * 新增费用项目单价表
     *
     * @param expenseItemUnitPrice
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/unitPriceOfExpenseItems/add")
    @ResponseBody
    public Object unitPriceOfExpenseItemsAdd(ExpenseItemUnitPrice expenseItemUnitPrice) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            // 根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            expenseItemUnitPrice.setCreatorName(user.getUserName());
            expenseItemUnitPrice.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            expenseItemUnitPrice.setUpdaterName(user.getUserName());
            expenseItemUnitPrice.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.addExpenseItemUnitPrice(expenseItemUnitPrice);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("费用项目单价表新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑费用项目单价表页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/unitPriceOfExpenseItems/edit/view")
    public String unitPriceOfExpenseItemsEditView(Model model, Integer serialNumber) throws Exception {
        ExpenseItemUnitPrice expenseItemUnitPrice = costService.getExpenseItemUnitPriceById(serialNumber);
        model.addAttribute("expenseItemUnitPrice", expenseItemUnitPrice);
        return "/modules/yearParamSet/unitPriceOfExpenseItems_edit";
    }

    /**
     * 编辑费用项目单价表
     *
     * @param expenseItemUnitPrice
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/unitPriceOfExpenseItems/edit")
    @ResponseBody
    public Object unitPriceOfExpenseItemsEdit(ExpenseItemUnitPrice expenseItemUnitPrice) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            // 根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            expenseItemUnitPrice.setUpdaterName(user.getUserName());
            expenseItemUnitPrice.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.modifyExpenseItemUnitPrice(expenseItemUnitPrice);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("费用项目单价表编辑异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================原料单价======================================
     */
    /**
     * 原料单价列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/unitPriceOfRawMaterials/list/view")
    public String rawMaterialUnitPriceListView(Model model) throws Exception {
        return "/modules/yearParamSet/rawMaterialUnitPrice_list";
    }

    /**
     * 原料单价列表数据
     *
     * @param isSearch 是否搜索
     * @param year     年度
     * @param pageInfo 分页信息
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/unitPriceOfRawMaterials/listPage")
    @ResponseBody
    public Object rawMaterialUnitPriceListPage(short isSearch, String year, String materialType, PageInfo pageInfo)
            throws Exception {
        if (isSearch == 1) {
            RawMaterialItemDTO rawMaterialItemDTO = costService.listRawMaterialItemPageByMaterialType(year,
                    materialType, pageInfo);
            return new HrPageResult(rawMaterialItemDTO.getRawMaterialItemList(),
                    rawMaterialItemDTO.getPageInfo().getTotal());
        } else {
            return new HrPageResult(new ArrayList<>(), 0);
        }
    }

    /**
     * 新增原料单价页面
     *
     * @param model
     * @param materialType 原料类型
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/unitPriceOfRawMaterials/add/view")
    public String rawMaterialUnitPriceAddView(Model model, String materialType) throws Exception {
        model.addAttribute("materialType", materialType);

        // 根据原料类型设置页面标题
        String materialTypeName = "";
        if ("01".equals(materialType)) {
            materialTypeName = "导体";
        } else if ("02".equals(materialType)) {
            materialTypeName = "油漆";
        } else if ("03".equals(materialType)) {
            materialTypeName = "线盘";
        }
        model.addAttribute("materialTypeName", materialTypeName);

        return "/modules/yearParamSet/rawMaterialUnitPrice_add";
    }

    /**
     * 获取导体成本编码列表
     *
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/unitPriceOfRawMaterials/getConductorCodeList")
    @ResponseBody
    public Object getConductorCodeList() throws Exception {
        try {
            List<ConductorCode> conductorCodeList = costService.getConductorCodeList();
            return new HrResult(CommonReturnCode.SUCCESS, conductorCodeList);
        } catch (Exception e) {
            logger.error("获取导体成本编码列表失败", e);
            return new HrResult(CommonReturnCode.FAILED, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增原料单价（复杂版本，支持明细表）
     *
     * @param rawMaterialItemDTO
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/unitPriceOfRawMaterials/add")
    @ResponseBody
    public Object rawMaterialUnitPriceAdd(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            // 根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();
            rawMaterialItem.setCreatorName(user.getUserName());
            rawMaterialItem.setCreatedTime(new java.util.Date());
            rawMaterialItem.setUpdaterName(user.getUserName());
            rawMaterialItem.setUpdatedTime(new java.util.Date());

            // 使用复杂的新增方法，同时处理明细表
            costService.addRawMaterialItemWithDetail(rawMaterialItemDTO);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("原料单价新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 原料单价详情页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/unitPriceOfRawMaterials/detail/view")
    public String rawMaterialUnitPriceDetailView(Model model, Integer serialNumber) throws Exception {
        RawMaterialItem rawMaterialItem = costService.getRawMaterialItemById(serialNumber);
        model.addAttribute("rawMaterialItem", rawMaterialItem);

        // 根据原料区分查询对应的明细信息
        String materialType = rawMaterialItem.getMaterialType();
        String year = rawMaterialItem.getYear();
        String itemCode = rawMaterialItem.getItemCode();

        if ("01".equals(materialType)) {
            // 导体明细
            ConductorUnitPriceDetail conductorDetail = costService.getConductorUnitPriceDetailByYearAndItemCode(year,
                    itemCode);
            model.addAttribute("conductorDetail", conductorDetail);
        } else if ("02".equals(materialType)) {
            // 油漆明细
            PaintUnitPriceDetail paintDetail = costService.getPaintUnitPriceDetailByYearAndItemCode(year, itemCode);
            model.addAttribute("paintDetail", paintDetail);
        } else if ("03".equals(materialType)) {
            // 线盘明细
            WireDiscUnitPriceDetail wireDiscDetail = costService.getWireDiscUnitPriceDetailByYearAndItemCode(year,
                    itemCode);
            model.addAttribute("wireDiscDetail", wireDiscDetail);
        }

        return "/modules/yearParamSet/rawMaterialUnitPrice_detail";
    }

    /**
     * 编辑原料单价页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/unitPriceOfRawMaterials/edit/view")
    public String rawMaterialUnitPriceEditView(Model model, Integer serialNumber) throws Exception {
        RawMaterialItem rawMaterialItem = costService.getRawMaterialItemById(serialNumber);
        model.addAttribute("rawMaterialItem", rawMaterialItem);
        return "/modules/yearParamSet/rawMaterialUnitPrice_edit";
    }

    /**
     * 编辑原料单价
     *
     * @param rawMaterialItem
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/unitPriceOfRawMaterials/edit")
    @ResponseBody
    public Object rawMaterialUnitPriceEdit(RawMaterialItem rawMaterialItem) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            // 根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            rawMaterialItem.setUpdaterName(user.getUserName());
            rawMaterialItem.setUpdatedTime(new java.util.Date());

            costService.modifyRawMaterialItem(rawMaterialItem);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("原料单价编辑异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================运费单价表======================================
     */
    /**
     * 运费单价表列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/freightUnitPrice/list/view")
    public String freightUnitPriceListView(Model model) throws Exception {
        return "/modules/yearParamSet/freightUnitPrice_list";
    }

    /**
     * 运费单价表检索
     *
     * @param isSearch 是否检索
     * @param year     年度
     * @param pageInfo 分页信息
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/freightUnitPrice/listPage")
    @ResponseBody
    public Object freightUnitPriceListPage(short isSearch, String year, PageInfo pageInfo) throws Exception {
        if (isSearch == 1) {
            // 当isSearch=1时进行查询，允许条件为空（查询全部）
            FreightUnitPriceDTO freightUnitPriceDTO = costService.listFreightUnitPricePage(year, pageInfo);
            return new HrPageResult(freightUnitPriceDTO.getFreightUnitPriceList(),
                    freightUnitPriceDTO.getPageInfo().getTotal());
        } else {
            // 当isSearch=0时返回空结果
            return new HrPageResult(new ArrayList<FreightUnitPrice>(), 0);
        }
    }

    /**
     * 运费单价表新增页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/freightUnitPrice/add/view")
    public String freightUnitPriceAddView(Model model) throws Exception {
        return "/modules/yearParamSet/freightUnitPrice_add";
    }

    /**
     * 新增运费单价表
     *
     * @param freightUnitPrice
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/freightUnitPrice/add")
    @ResponseBody
    public Object freightUnitPriceAdd(FreightUnitPrice freightUnitPrice) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            // 根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            freightUnitPrice.setCreatorName(user.getUserName());
            freightUnitPrice.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            freightUnitPrice.setUpdaterName(user.getUserName());
            freightUnitPrice.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.addFreightUnitPrice(freightUnitPrice);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("运费单价新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 运费单价表编辑页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/freightUnitPrice/edit/view")
    public String freightUnitPriceEditView(Model model, Integer serialNumber) throws Exception {
        FreightUnitPrice freightUnitPrice = costService.getFreightUnitPriceById(serialNumber);
        model.addAttribute("freightUnitPrice", freightUnitPrice);
        return "/modules/yearParamSet/freightUnitPrice_edit";
    }

    /**
     * 编辑运费单价表
     *
     * @param freightUnitPrice
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/freightUnitPrice/edit")
    @ResponseBody
    public Object freightUnitPriceEdit(FreightUnitPrice freightUnitPrice) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            // 根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());

            freightUnitPrice.setUpdaterName(user.getUserName());
            freightUnitPrice.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.modifyFreightUnitPrice(freightUnitPrice);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("运费单价编辑异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 运费单价数据读取页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/freightUnitPrice/dataImport/view")
    public String freightUnitPriceDataImportView(Model model) throws Exception {
        return "/modules/yearParamSet/freightUnitPrice_dataImport";
    }

    /**
     * 运费单价数据读取
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/freightUnitPrice/dataImport")
    @ResponseBody
    public Object freightUnitPriceDataImport(String startDate, String endDate) throws Exception {
        try {
            int importCount = costService.importFreightUnitPriceFromSummary(startDate, endDate);
            return new HrResult(CommonReturnCode.SUCCESS, "成功导入 " + importCount + " 条记录");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("运费单价数据读取异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
    }

    /* ======================导体单价管理====================== */

    /**
     * 导体单价列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/conductorUnitPrice/list/view")
    public String conductorUnitPriceListView(Model model) throws Exception {
        return "/modules/cost/conductorUnitPrice_list";
    }

    /**
     * 导体单价列表数据
     *
     * @param year  年度
     * @param page  页码
     * @param limit 每页数量
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/conductorUnitPrice/listPage")
    @ResponseBody
    public Object conductorUnitPriceListPage(short isSearch, String year, PageInfo pageInfo) throws Exception {
        if (isSearch == 1) {
            RawMaterialItemDTO rawMaterialItemDTO = costService.listRawMaterialItemPageByMaterialType(year, "01",
                    pageInfo);
            return new HrPageResult(rawMaterialItemDTO.getRawMaterialItemList(),
                    rawMaterialItemDTO.getPageInfo().getTotal());
        } else {
            return new HrPageResult(new ArrayList<>(), 0);
        }
    }

    /**
     * 导体单价新增页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/conductorUnitPrice/add/view")
    public String conductorUnitPriceAddView(Model model) throws Exception {
        // 查询导体成本编码数据
        List<ConductorCode> conductorCodeList = costService.getConductorCodeList();

        model.addAttribute("conductorCodeList", conductorCodeList);

        return "/modules/cost/conductorUnitPrice_add";
    }

    /**
     * 新增导体单价
     *
     * @param rawMaterialItemDTO
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/conductorUnitPrice/add")
    @ResponseBody
    public Object conductorUnitPriceAdd(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            User user = userService.getById(authorizingUser.getUserId());

            RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();
            rawMaterialItem.setCreatorName(user.getUserName());
            rawMaterialItem.setCreatedTime(new java.util.Date());
            rawMaterialItem.setUpdaterName(user.getUserName());
            rawMaterialItem.setUpdatedTime(new java.util.Date());

            // 调试日志：检查导体明细数据
            ConductorUnitPriceDetail conductorDetail = rawMaterialItemDTO.getConductorUnitPriceDetail();
            logger.info("导体明细数据: " + (conductorDetail != null ? conductorDetail.toString() : "null"));
            if (conductorDetail != null) {
                logger.info("关税率: " + conductorDetail.getTariffRate());
                logger.info("采购单价: " + conductorDetail.getPurchaseUnitPrice());
                logger.info("溢价: " + conductorDetail.getPremium());
                logger.info("附随费用: " + conductorDetail.getIncidentalExpenses());
                logger.info("屑铜原料单价: " + conductorDetail.getScrapCopperUnitPrice());
                logger.info("比重: " + conductorDetail.getSpecificGravity());
            }

            // 使用复杂的新增方法，同时处理明细表
            costService.addRawMaterialItemWithDetail(rawMaterialItemDTO);

            return new HrResult(CommonReturnCode.SUCCESS, "新增成功");
        } catch (Exception e) {
            logger.error("新增导体单价失败", e);
            return new HrResult(CommonReturnCode.FAILED, "新增失败：" + e.getMessage());
        }
    }

    /**
     * 导体单价编辑页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/conductorUnitPrice/edit/view")
    public String conductorUnitPriceEditView(Model model, Integer serialNumber) throws Exception {
        // 查询导体成本编码数据
        List<ConductorCode> conductorCodeList = costService.getConductorCodeList();
        model.addAttribute("conductorCodeList", conductorCodeList);

        RawMaterialItem rawMaterialItem = costService.getRawMaterialItemById(serialNumber);
        model.addAttribute("rawMaterialItem", rawMaterialItem);

        // 查询导体明细
        String year = rawMaterialItem.getYear();
        String itemCode = rawMaterialItem.getItemCode();
        ConductorUnitPriceDetail conductorDetail = costService.getConductorUnitPriceDetailByYearAndItemCode(year,
                itemCode);
        model.addAttribute("conductorDetail", conductorDetail);

        return "/modules/cost/conductorUnitPrice_edit";
    }

    /**
     * 编辑导体单价
     *
     * @param rawMaterialItemDTO
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/conductorUnitPrice/edit")
    @ResponseBody
    public Object conductorUnitPriceEdit(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            User user = userService.getById(authorizingUser.getUserId());

            RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();
            rawMaterialItem.setUpdaterName(user.getUserName());
            rawMaterialItem.setUpdatedTime(new java.util.Date());

            // 使用复杂的编辑方法，同时处理明细表
            costService.updateRawMaterialItemWithDetail(rawMaterialItemDTO);

            return new HrResult(CommonReturnCode.SUCCESS, "编辑成功");
        } catch (Exception e) {
            logger.error("编辑导体单价失败", e);
            return new HrResult(CommonReturnCode.FAILED, "编辑失败：" + e.getMessage());
        }
    }

    /**
     * 导体单价详情页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/conductorUnitPrice/detail/view")
    public String conductorUnitPriceDetailView(Model model, Integer serialNumber) throws Exception {
        RawMaterialItem rawMaterialItem = costService.getRawMaterialItemById(serialNumber);
        model.addAttribute("rawMaterialItem", rawMaterialItem);

        // 查询导体明细
        String year = rawMaterialItem.getYear();
        String itemCode = rawMaterialItem.getItemCode();
        ConductorUnitPriceDetail conductorDetail = costService.getConductorUnitPriceDetailByYearAndItemCode(year,
                itemCode);
        model.addAttribute("conductorDetail", conductorDetail);

        return "/modules/cost/conductorUnitPrice_detail";
    }

    /* ======================油漆单价管理====================== */

    /**
     * 油漆单价列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/paintUnitPrice/list/view")
    public String paintUnitPriceListView(Model model) throws Exception {
        return "/modules/cost/paintUnitPrice_list";
    }

    /**
     * 油漆单价列表数据
     *
     * @param year  年度
     * @param page  页码
     * @param limit 每页数量
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/paintUnitPrice/listPage")
    @ResponseBody
    public Object paintUnitPriceListPage(short isSearch, String year, PageInfo pageInfo) throws Exception {
        if (isSearch == 1) {
            RawMaterialItemDTO rawMaterialItemDTO = costService.listRawMaterialItemPageByMaterialType(year, "02",
                    pageInfo);
            return new HrPageResult(rawMaterialItemDTO.getRawMaterialItemList(),
                    rawMaterialItemDTO.getPageInfo().getTotal());
        } else {
            return new HrPageResult(new ArrayList<>(), 0);
        }
    }

    /**
     * 油漆单价新增页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/paintUnitPrice/add/view")
    public String paintUnitPriceAddView(Model model) throws Exception {
        // 查询导体成本编码数据
        List<ConductorCode> conductorCodeList = costService.getConductorCodeList();
        model.addAttribute("conductorCodeList", conductorCodeList);

        return "/modules/cost/paintUnitPrice_add";
    }

    /**
     * 新增油漆单价
     *
     * @param rawMaterialItemDTO
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/paintUnitPrice/add")
    @ResponseBody
    public Object paintUnitPriceAdd(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            User user = userService.getById(authorizingUser.getUserId());

            RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();
            rawMaterialItem.setCreatorName(user.getUserName());
            rawMaterialItem.setCreatedTime(new java.util.Date());
            rawMaterialItem.setUpdaterName(user.getUserName());
            rawMaterialItem.setUpdatedTime(new java.util.Date());

            // 使用复杂的新增方法，同时处理明细表
            costService.addRawMaterialItemWithDetail(rawMaterialItemDTO);

            return new HrResult(CommonReturnCode.SUCCESS, "新增成功");
        } catch (Exception e) {
            logger.error("新增油漆单价失败", e);
            return new HrResult(CommonReturnCode.FAILED, "新增失败：" + e.getMessage());
        }
    }

    /**
     * 油漆单价编辑页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/paintUnitPrice/edit/view")
    public String paintUnitPriceEditView(Model model, Integer serialNumber) throws Exception {
        // 查询导体成本编码数据
        List<ConductorCode> conductorCodeList = costService.getConductorCodeList();
        model.addAttribute("conductorCodeList", conductorCodeList);

        RawMaterialItem rawMaterialItem = costService.getRawMaterialItemById(serialNumber);
        model.addAttribute("rawMaterialItem", rawMaterialItem);

        // 查询油漆明细
        String year = rawMaterialItem.getYear();
        String itemCode = rawMaterialItem.getItemCode();
        PaintUnitPriceDetail paintDetail = costService.getPaintUnitPriceDetailByYearAndItemCode(year, itemCode);
        model.addAttribute("paintDetail", paintDetail);

        return "/modules/cost/paintUnitPrice_edit";
    }

    /**
     * 编辑油漆单价
     *
     * @param rawMaterialItemDTO
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/paintUnitPrice/edit")
    @ResponseBody
    public Object paintUnitPriceEdit(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            User user = userService.getById(authorizingUser.getUserId());

            RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();
            rawMaterialItem.setUpdaterName(user.getUserName());
            rawMaterialItem.setUpdatedTime(new java.util.Date());

            // 调试日志：检查明细数据是否正确传递
            PaintUnitPriceDetail paintDetail = rawMaterialItemDTO.getPaintUnitPriceDetail();
            logger.info("油漆明细数据: " + (paintDetail != null ? paintDetail.toString() : "null"));
            if (paintDetail != null) {
                logger.info("关税率: " + paintDetail.getTariffRate());
                logger.info("采购单价: " + paintDetail.getPurchaseUnitPrice());
                logger.info("油漆主体: " + paintDetail.getPaintBody());
            }

            // 使用复杂的编辑方法，同时处理明细表
            costService.updateRawMaterialItemWithDetail(rawMaterialItemDTO);

            return new HrResult(CommonReturnCode.SUCCESS, "编辑成功");
        } catch (Exception e) {
            logger.error("编辑油漆单价失败", e);
            return new HrResult(CommonReturnCode.FAILED, "编辑失败：" + e.getMessage());
        }
    }

    /**
     * 油漆单价详情页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/paintUnitPrice/detail/view")
    public String paintUnitPriceDetailView(Model model, Integer serialNumber) throws Exception {
        RawMaterialItem rawMaterialItem = costService.getRawMaterialItemById(serialNumber);
        model.addAttribute("rawMaterialItem", rawMaterialItem);

        // 查询油漆明细
        String year = rawMaterialItem.getYear();
        String itemCode = rawMaterialItem.getItemCode();
        PaintUnitPriceDetail paintDetail = costService.getPaintUnitPriceDetailByYearAndItemCode(year, itemCode);
        model.addAttribute("paintDetail", paintDetail);

        return "/modules/cost/paintUnitPrice_detail";
    }

    /* ======================线盘单价管理====================== */

    /**
     * 线盘单价列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/wireDiscUnitPrice/list/view")
    public String wireDiscUnitPriceListView(Model model) throws Exception {
        return "/modules/cost/wireDiscUnitPrice_list";
    }

    /**
     * 线盘单价列表数据
     *
     * @param year  年度
     * @param page  页码
     * @param limit 每页数量
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/wireDiscUnitPrice/listPage")
    @ResponseBody
    public Object wireDiscUnitPriceListPage(short isSearch, String year, PageInfo pageInfo) throws Exception {
        if (isSearch == 1) {
            RawMaterialItemDTO rawMaterialItemDTO = costService.listRawMaterialItemPageByMaterialType(year, "03",
                    pageInfo);
            return new HrPageResult(rawMaterialItemDTO.getRawMaterialItemList(),
                    rawMaterialItemDTO.getPageInfo().getTotal());
        } else {
            return new HrPageResult(new ArrayList<>(), 0);
        }
    }

    /**
     * 线盘单价新增页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/wireDiscUnitPrice/add/view")
    public String wireDiscUnitPriceAddView(Model model) throws Exception {
        // 查询导体成本编码数据
        List<ConductorCode> conductorCodeList = costService.getConductorCodeList();
        model.addAttribute("conductorCodeList", conductorCodeList);

        return "/modules/cost/wireDiscUnitPrice_add";
    }

    /**
     * 新增线盘单价
     *
     * @param rawMaterialItemDTO
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/wireDiscUnitPrice/add")
    @ResponseBody
    public Object wireDiscUnitPriceAdd(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            User user = userService.getById(authorizingUser.getUserId());

            RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();
            rawMaterialItem.setCreatorName(user.getUserName());
            rawMaterialItem.setCreatedTime(new java.util.Date());
            rawMaterialItem.setUpdaterName(user.getUserName());
            rawMaterialItem.setUpdatedTime(new java.util.Date());

            // 使用复杂的新增方法，同时处理明细表
            costService.addRawMaterialItemWithDetail(rawMaterialItemDTO);

            return new HrResult(CommonReturnCode.SUCCESS, "新增成功");
        } catch (Exception e) {
            logger.error("新增线盘单价失败", e);
            return new HrResult(CommonReturnCode.FAILED, "新增失败：" + e.getMessage());
        }
    }

    /**
     * 线盘单价编辑页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/wireDiscUnitPrice/edit/view")
    public String wireDiscUnitPriceEditView(Model model, Integer serialNumber) throws Exception {
        // 查询导体成本编码数据
        List<ConductorCode> conductorCodeList = costService.getConductorCodeList();
        model.addAttribute("conductorCodeList", conductorCodeList);

        RawMaterialItem rawMaterialItem = costService.getRawMaterialItemById(serialNumber);
        model.addAttribute("rawMaterialItem", rawMaterialItem);

        // 查询线盘明细
        String year = rawMaterialItem.getYear();
        String itemCode = rawMaterialItem.getItemCode();
        WireDiscUnitPriceDetail wireDiscDetail = costService.getWireDiscUnitPriceDetailByYearAndItemCode(year,
                itemCode);
        model.addAttribute("wireDiscDetail", wireDiscDetail);

        return "/modules/cost/wireDiscUnitPrice_edit";
    }

    /**
     * 编辑线盘单价
     *
     * @param rawMaterialItemDTO
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/wireDiscUnitPrice/edit")
    @ResponseBody
    public Object wireDiscUnitPriceEdit(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            User user = userService.getById(authorizingUser.getUserId());

            RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();
            rawMaterialItem.setUpdaterName(user.getUserName());
            rawMaterialItem.setUpdatedTime(new java.util.Date());

            // 使用复杂的编辑方法，同时处理明细表
            costService.updateRawMaterialItemWithDetail(rawMaterialItemDTO);

            return new HrResult(CommonReturnCode.SUCCESS, "编辑成功");
        } catch (Exception e) {
            logger.error("编辑线盘单价失败", e);
            return new HrResult(CommonReturnCode.FAILED, "编辑失败：" + e.getMessage());
        }
    }

    /**
     * 线盘单价详情页面
     *
     * @param model
     * @param serialNumber 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/wireDiscUnitPrice/detail/view")
    public String wireDiscUnitPriceDetailView(Model model, Integer serialNumber) throws Exception {
        RawMaterialItem rawMaterialItem = costService.getRawMaterialItemById(serialNumber);
        model.addAttribute("rawMaterialItem", rawMaterialItem);

        // 查询线盘明细
        String year = rawMaterialItem.getYear();
        String itemCode = rawMaterialItem.getItemCode();
        WireDiscUnitPriceDetail wireDiscDetail = costService.getWireDiscUnitPriceDetailByYearAndItemCode(year,
                itemCode);
        model.addAttribute("wireDiscDetail", wireDiscDetail);

        return "/modules/cost/wireDiscUnitPrice_detail";
    }

    /*
     * =================================导体成本编码表=====================================
     * =
     */
    /**
     * 导体成本编码表列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/conductorCode/list/view")
    public String conductorCodeListView(Model model) throws Exception {
        return "/modules/yearParamSet/conductorCode_list";
    }

    /**
     * 导体成本编码表列表数据
     *
     * @param isSearch      是否检索
     * @param conductorName 导体名称
     * @param pageInfo      分页信息
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/conductorCode/listPage")
    @ResponseBody
    public Object conductorCodeListPage(short isSearch, String conductorName, PageInfo pageInfo) throws Exception {
        if (isSearch == 1) {
            try {
                ConductorCodeDTO conductorCodeDTO = costService.listConductorCodePage(conductorName, pageInfo);
                return new HrPageResult(conductorCodeDTO.getConductorCodeList(),
                        conductorCodeDTO.getPageInfo().getTotal());
            } catch (Exception e) {
                logger.error("查询导体成本编码列表异常", e);
                return new HrResult(CommonReturnCode.FAILED, "查询失败");
            }
        } else {
            return new HrPageResult(new ArrayList<ConductorCode>(), 0);
        }
    }

    /**
     * 导体成本编码新增页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/conductorCode/add/view")
    public String conductorCodeAddView(Model model) throws Exception {
        return "/modules/yearParamSet/conductorCode_add";
    }

    /**
     * 新增导体成本编码
     *
     * @param conductorCode 导体成本编码实体
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/conductorCode/add")
    @ResponseBody
    public Object conductorCodeAdd(ConductorCode conductorCode) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 生成成本编码
            String costCode = costService.generateNextConductorCode();
            conductorCode.setCostCode(costCode);
            conductorCode.setState(ConductorCode.STATE_NORMAL);
            conductorCode.setCreatorName(authorizingUser.getUserName());
            conductorCode.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            conductorCode.setLastModifierName(authorizingUser.getUserName());
            conductorCode.setLastModifiedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            int result = costService.addConductorCode(conductorCode);
            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "新增成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "新增失败");
            }
        } catch (Exception e) {
            logger.error("新增导体成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "新增失败：" + e.getMessage());
        }
    }

    /**
     * 导体成本编码编辑页面
     *
     * @param model
     * @param codeId 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/conductorCode/edit/view")
    public String conductorCodeEditView(Model model, Integer codeId) throws Exception {
        ConductorCode conductorCode = costService.getConductorCodeById(codeId);
        model.addAttribute("conductorCode", conductorCode);
        return "/modules/yearParamSet/conductorCode_edit";
    }

    /**
     * 编辑导体成本编码
     *
     * @param conductorCode 导体成本编码实体
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/conductorCode/edit")
    @ResponseBody
    public Object conductorCodeEdit(ConductorCode conductorCode) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            conductorCode.setLastModifierName(authorizingUser.getUserName());
            conductorCode.setLastModifiedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.modifyConductorCode(conductorCode);
            return new HrResult(CommonReturnCode.SUCCESS, "编辑成功");
        } catch (Exception e) {
            logger.error("编辑导体成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "编辑失败：" + e.getMessage());
        }
    }

    /**
     * 删除导体成本编码
     *
     * @param codeId 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/conductorCode/delete")
    @ResponseBody
    public Object conductorCodeDelete(Integer codeId) throws Exception {
        try {
            costService.deleteConductorCode(codeId);
            return new HrResult(CommonReturnCode.SUCCESS, "删除成功");
        } catch (Exception e) {
            logger.error("删除导体成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "删除失败：" + e.getMessage());
        }
    }

    /*
     * =================================油漆成本编码表=====================================
     * =
     */
    /**
     * 油漆成本编码表列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/paintCode/list/view")
    public String paintCodeListView(Model model) throws Exception {
        return "/modules/yearParamSet/paintCode_list";
    }

    /**
     * 油漆成本编码表列表数据
     *
     * @param isSearch        是否检索
     * @param insidePaintName 社内油漆名
     * @param pageInfo        分页信息
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/paintCode/listPage")
    @ResponseBody
    public Object paintCodeListPage(short isSearch, String insidePaintName, PageInfo pageInfo) throws Exception {
        if (isSearch == 1) {
            try {
                PaintCodeDTO paintCodeDTO = costService.listPaintCodePageByName(insidePaintName, pageInfo);
                return new HrPageResult(paintCodeDTO.getPaintCodeList(), paintCodeDTO.getPageInfo().getTotal());
            } catch (Exception e) {
                logger.error("查询油漆成本编码列表异常", e);
                return new HrResult(CommonReturnCode.FAILED, "查询失败");
            }
        } else {
            return new HrPageResult(new ArrayList<PaintCode>(), 0);
        }
    }

    /**
     * 油漆成本编码新增页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/paintCode/add/view")
    public String paintCodeAddView(Model model) throws Exception {
        // 获取油漆品种下拉列表
        List<String> paintVarieties = costService.listPaintVarieties();
        model.addAttribute("paintVarieties", paintVarieties);
        return "/modules/yearParamSet/paintCode_add";
    }

    /**
     * 新增油漆成本编码
     *
     * @param paintCode 油漆成本编码实体
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/paintCode/add")
    @ResponseBody
    public Object paintCodeAdd(PaintCode paintCode) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 生成成本编码
            String costCode = costService.generateNextPaintCode();
            paintCode.setPaintCode(costCode);
            paintCode.setState(PaintCode.STATE_NORMAL);
            paintCode.setCreatorId(authorizingUser.getUserId().intValue());
            paintCode.setCreatorName(authorizingUser.getUserName());
            paintCode.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            paintCode.setLastModifierId(authorizingUser.getUserId().intValue());
            paintCode.setLastModifierName(authorizingUser.getUserName());
            paintCode.setLastModifiedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            int result = costService.addPaintCode(paintCode);
            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "新增成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "新增失败");
            }
        } catch (Exception e) {
            logger.error("新增油漆成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "新增失败：" + e.getMessage());
        }
    }

    /**
     * 油漆成本编码编辑页面
     *
     * @param model
     * @param codeId 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/paintCode/edit/view")
    public String paintCodeEditView(Model model, Integer codeId) throws Exception {
        PaintCode paintCode = costService.getPaintCodeByCodeId(codeId);
        model.addAttribute("paintCode", paintCode);
        // 获取油漆品种下拉列表
        List<String> paintVarieties = costService.listPaintVarieties();
        model.addAttribute("paintVarieties", paintVarieties);
        return "/modules/yearParamSet/paintCode_edit";
    }

    /**
     * 编辑油漆成本编码
     *
     * @param paintCode 油漆成本编码实体
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/paintCode/edit")
    @ResponseBody
    public Object paintCodeEdit(PaintCode paintCode) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            paintCode.setLastModifierId(authorizingUser.getUserId().intValue());
            paintCode.setLastModifierName(authorizingUser.getUserName());
            paintCode.setLastModifiedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.modifyPaintCode(paintCode);
            return new HrResult(CommonReturnCode.SUCCESS, "编辑成功");
        } catch (Exception e) {
            logger.error("编辑油漆成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "编辑失败：" + e.getMessage());
        }
    }

    /**
     * 删除油漆成本编码
     *
     * @param codeId 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/paintCode/delete")
    @ResponseBody
    public Object paintCodeDelete(Integer codeId) throws Exception {
        try {
            costService.deletePaintCode(codeId);
            return new HrResult(CommonReturnCode.SUCCESS, "删除成功");
        } catch (Exception e) {
            logger.error("删除油漆成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "删除失败：" + e.getMessage());
        }
    }

    /*
     * =================================线盘成本编码表=====================================
     * =
     */
    /**
     * 线盘成本编码表列表页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/wireDiscCode/list/view")
    public String wireDiscCodeListView(Model model) throws Exception {
        return "/modules/yearParamSet/wireDiscCode_list";
    }

    /**
     * 线盘成本编码表列表数据
     *
     * @param isSearch     是否检索
     * @param wireDiscName 线盘名称
     * @param pageInfo     分页信息
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/wireDiscCode/listPage")
    @ResponseBody
    public Object wireDiscCodeListPage(short isSearch, String wireDiscName, PageInfo pageInfo) throws Exception {
        if (isSearch == 1) {
            try {
                WireDiscCodeDTO wireDiscCodeDTO = costService.listWireDiscCodePage(wireDiscName, pageInfo);
                return new HrPageResult(wireDiscCodeDTO.getWireDiscCodeList(),
                        wireDiscCodeDTO.getPageInfo().getTotal());
            } catch (Exception e) {
                logger.error("查询线盘成本编码列表异常", e);
                return new HrResult(CommonReturnCode.FAILED, "查询失败");
            }
        } else {
            return new HrPageResult(new ArrayList<WireDiscCode>(), 0);
        }
    }

    /**
     * 线盘成本编码新增页面
     *
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/wireDiscCode/add/view")
    public String wireDiscCodeAddView(Model model) throws Exception {
        return "/modules/yearParamSet/wireDiscCode_add";
    }

    /**
     * 新增线盘成本编码
     *
     * @param wireDiscCode 线盘成本编码实体
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/wireDiscCode/add")
    @ResponseBody
    public Object wireDiscCodeAdd(WireDiscCode wireDiscCode) throws Exception {
        try {
            logger.info("开始新增线盘成本编码，参数：{}", wireDiscCode.getWireDiscName());

            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                logger.error("用户未登录");
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            logger.info("当前登录用户：{}", authorizingUser.getUserName());

            // 生成成本编码
            logger.info("开始生成线盘成本编码");
            String costCode = costService.generateNextWireDiscCode();
            logger.info("生成的成本编码：{}", costCode);

            wireDiscCode.setCostCode(costCode);
            wireDiscCode.setState(WireDiscCode.STATE_NORMAL);
            wireDiscCode.setCreatorName(authorizingUser.getUserName());
            wireDiscCode.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            wireDiscCode.setLastModifierName(authorizingUser.getUserName());
            wireDiscCode.setLastModifiedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            logger.info("设置线盘成本编码参数完成，状态：{}", wireDiscCode.getState());

            logger.info("开始调用addWireDiscCode方法");
            int result = costService.addWireDiscCode(wireDiscCode);
            logger.info("addWireDiscCode方法返回结果：{}", result);

            if (result > 0) {
                logger.info("线盘成本编码新增成功");
                return new HrResult(CommonReturnCode.SUCCESS, "新增成功");
            } else {
                logger.error("线盘成本编码新增失败，返回值：{}", result);
                return new HrResult(CommonReturnCode.FAILED, "新增失败");
            }
        } catch (Exception e) {
            logger.error("新增线盘成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "新增失败：" + e.getMessage());
        }
    }

    /**
     * 线盘成本编码编辑页面
     *
     * @param model
     * @param codeId 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/wireDiscCode/edit/view")
    public String wireDiscCodeEditView(Model model, Integer codeId) throws Exception {
        WireDiscCode wireDiscCode = costService.getWireDiscCodeById(codeId);
        model.addAttribute("wireDiscCode", wireDiscCode);
        return "/modules/yearParamSet/wireDiscCode_edit";
    }

    /**
     * 编辑线盘成本编码
     *
     * @param wireDiscCode 线盘成本编码实体
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/wireDiscCode/edit")
    @ResponseBody
    public Object wireDiscCodeEdit(WireDiscCode wireDiscCode) throws Exception {
        try {
            // 获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 设置修改相关字段
            wireDiscCode.setLastModifierName(authorizingUser.getUserName());
            wireDiscCode.setLastModifiedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            costService.modifyWireDiscCode(wireDiscCode);
            return new HrResult(CommonReturnCode.SUCCESS, "编辑成功");
        } catch (Exception e) {
            logger.error("编辑线盘成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "编辑失败：" + e.getMessage());
        }
    }

    /**
     * 删除线盘成本编码
     *
     * @param codeId 流水号
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/wireDiscCode/delete")
    @ResponseBody
    public Object wireDiscCodeDelete(Integer codeId) throws Exception {
        try {
            costService.deleteWireDiscCode(codeId);
            return new HrResult(CommonReturnCode.SUCCESS, "删除成功");
        } catch (Exception e) {
            logger.error("删除线盘成本编码异常", e);
            return new HrResult(CommonReturnCode.FAILED, "删除失败：" + e.getMessage());
        }
    }

}
