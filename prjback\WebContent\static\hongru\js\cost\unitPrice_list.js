
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year', //指定元素
        btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
    });

    //执行一个 table 实例
    var url = baselocation+'/costPrice/unitPrice/listPage';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '单价列表'
        ,page: false //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'orderNo', title: '',width:30,fixed: 'left',type:"numbers",align:'center'}
            ,{field: 'year',title: '年度',align:'center', width:80}
            ,{field: 'electricUnitPrice',title: '电力单价',align:'center', width:120}
            ,{field: 'generalUnitPrice',title: '一般社员单价',align:'center', width:120}
            ,{field: 'temporaryUnitPrice',title: '临时工单价',align:'center', width:120}
            ,{field: 'saveDamageUnitPrice',title: '保全单价',align:'center', width:120}
            ,{field: 'wrEMUnitPrice',title: 'WR加工费EM',align:'center', width:120}
            ,{field: 'wrEFUnitPrice',title: 'WR加工费EF',align:'center', width:120}
            ,{field: 'wrERUnitPrice',title: 'WR加工费ER',align:'center', width:120}
            ,{field: 'wrEHUnitPrice',title: 'WR加工费EH',align:'center', width:120}
            ,{field: 'tranUnitPrice',title: '运费单价',align:'center', width:120}
            ,{field: 'wireUnitPrice',title: '线盘单价',align:'center', width:120}
            ,{field: 'wireRecycling',title: '线盘回收率',align:'center', width:120}
            ,{field: 'cuCrumbsUnitPrice',title: '铜屑单价',align:'center', width:120}
            ,{field: 'gasUnitPrice',title: '天然气单价',align:'center', width:120}
            ,{field: 'nitrogenUnitPrice',title: '窒素单价',align:'center', width:120}
            ,{title: '操作',minWidth:150, align:'left',fixed: 'right', toolbar: '#barDemo',width: 80}
        ]]
    });
    
    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                layer_show('添加', baselocation+"/costPrice/unitPrice/add/view", 750, document.body.clientHeight-50)
                break;
        };
    });
    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值
        if(layEvent === 'edit'){
            layer_show('编辑',baselocation+"/costPrice/unitPrice/modify/view?statId="+data.statId,document.body.clientWidth-10, document.body.clientHeight-10);
        }
    });
});

function search() {
    var arr = new Array();
    $("input:checkbox[name='processIds']:checked").each(function(i){
        arr[i] = $(this).val();
    });
    $("#processIdStr").val(arr.join(","));
    $("#isSearch").val(1);
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    //执行重载
    layui.table.reload('demo', {
//        page: {
//            curr: 1 //重新从第 1 页开始${ctx}
//        }
//        ,where: temp
    	where: temp
    }, 'data');
}
