layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate
        ,layer = layui.layer
        ,table = layui.table
        ,element = layui.element
        ,dropdown = layui.dropdown
    var form = layui.form;

    //类别选择
    form.on('select(outPaintNameFun)', function(data){
        listOutPaintName();
    });

    //获取列表
    function listOutPaintName(){
        var outPaintName = $("#outPaintName option:checked").val();
        var index = layer.load(2,{
            shade:[0.1,'#fff']
        });
        $.ajax({
            url : baselocation+'/costPrice/listByFullPaintNameList/json',
            type : 'post',
            data : {"outPaintName":outPaintName},
            success : function(result) {
                layer.closeAll();
                var html ='<option value="">全部</option>';
                if(result.code == 1){
                    var fullPaintNameList = result.data;
                    if(fullPaintNameList != null && fullPaintNameList.length>0){
                        for(var i=0;i<fullPaintNameList.length;i++) {
                            var fullPaintName = fullPaintNameList[i];
                            html+='<option value="'+fullPaintName+'">'+fullPaintName+'</option>';
                        }
                    }
                }else{
                    layer.alert(result.message);
                }
                $("#producer").html(html);
                form.render();//重新渲染
            }
        });
    }

    //监听提交
    form.on('submit(formDemo)', function(data){
        var flag = true;

        var yearMonth = $("#yearMonth").val();
        var producer = $("#producer").val();
        var outPaintName = $("#outPaintName").val();
        var paintPrice = $("#paintPrice").val();
        var inventoryPrice = $("#inventoryPrice").val();
        var costId = $("#costId").val();

        if(flag){
            var index = layer.load(2, {
                shade: [0.1, '#fff']
            });
            var actionUrl = $("#submitForm").attr('action');
            $.ajax({
                url: actionUrl,
                type: 'post',
                data: {
                    "yearMonth":yearMonth,
                    "producer":producer,
                    "outPaintName":outPaintName,
                    "paintPrice":paintPrice,
                    "inventoryPrice":inventoryPrice,
                    "costId":costId
                },
                success: function (result) {
                    layer.closeAll();
                    if (result.code == 1) {
                        parent.layer.msg("操作成功!", {
                            shade: 0.3,
                            time: 1500
                        });
                        parent.search();
                        parent.layer.closeAll();
                    } else {
                        layer.alert(result.message);
                    }
                }
            });
        }
        return false;
    });


});


/**
 * 实时动态强制更改用户录入
 * arg1 inputObject
 **/
function amount(th){
    var regStrs = [
        ['^0(\\d+)$', '$1'], //禁止录入整数部分两位以上，但首位为0
        ['[^\\d\\.]+$', ''], //禁止录入任何非数字和点
        ['\\.(\\d?)\\.+', '.$1'], //禁止录入两个以上的点
        ['^(\\d+\\.\\d{4}).+', '$1'] //禁止录入小数点后两位以上
    ];
    for(i=0; i<regStrs.length; i++){
        var reg = new RegExp(regStrs[i][0]);
        th.value = th.value.replace(reg, regStrs[i][1]);
    }
}

/**
 * 录入完成后，输入模式失去焦点后对录入进行判断并强制更改，并对小数点进行0补全
 * arg1 inputObject
 * 这个函数写得很傻，是我很早以前写的了，没有进行优化，但功能十分齐全，你尝试着使用
 * 其实有一种可以更快速的JavaScript内置函数可以提取杂乱数据中的数字：
 * parseFloat('10');
 **/
function overFormat(th){
    var v = th.value;
    if(v === ''){
        v = "";
    }else if(v === '0'){//表示输入框值为0
        v = '0.0000';
    }else if(v === '0.'){//表示输入框值为0.
        v = '0.0000';
    }else if(/^0+\d+\.?\d*.*$/.test(v)){// ^0+表示以一个或多个0开头  \d+表示之后需出现一个或多个数字  \.?表示之后需出现0个或一个小数点  \d*表示之后需出现0个或多个的数字 .*$表示之后需出现0个或多个除换行和回车以外的任意字符结尾
        v = v.replace(/^0+(\d+\.?\d*).*$/, '$1'); //^0+表示以一个或多个0开头 (\d+\.?\d*)表示之后需出现（一个或多个数字，0个或一个小数点，0个或多个数字） .*$表示以0个或多个除换行和回车以外的任意字符结尾
        v = inp.getRightPriceFormat(v).val;
    }else if(/^0\.\d$/.test(v)){// ^0表示以0开头 \.表示之后需出现一个小数点 \d$表示以一个数字结尾
        v = v + '0';
    }else if(!/^\d+\.\d{4}$/.test(v)){// ^\d+表示以一个或多个数字开头 \.表示之后需出现一个小数点 \d{4}$表示以两个数字结尾，因为前面有一个!，所以是前面的相反
        if(/^\d+\.\d{4}.+/.test(v)){// ^\d+表示以一个或多个数字开头 \.表示之后需出现一个小数点 \d{4}表示两个数字 .+表示一个或多个除换行和回车以外的任意字符
            v = v.replace(/^(\d+\.\d{4}).*$/, '$1'); //^(\d+\.\d{4})表示以（之后需出现一个或多个数字，之后需出现一个小数点，之后需出现两个数字）开头  .*$表示0个或多个除换行和回车以外的任意字符结尾
        }else if(/^\d+$/.test(v)){// ^\d+$表示以一个或多个数字开头并且以一个或多个数字结尾
            v = v + '.0000';
        }else if(/^\d+\.$/.test(v)){// ^\d+表示以一个或多个数字开头 \.$表示以一个小数点结尾
            v = v + '0000';
        }else if(/^\d+\.\d$/.test(v)){// ^\d+表示以一个或多个数字开头 \.表示之后需出现一个小数点 \d$表示以一个数字结尾
            v = v + '0';
        }else if(/^[^\d]+\d+\.?\d*$/.test(v)){// ^[^\d]+表示以一个或多个不是数字的字符开头 \d+表示之后需出现一个或多个数字 \.?表示之后需出现0个或一个小数点 \d*$表示以0个或多个数字结尾
            v = v.replace(/^[^\d]+(\d+\.?\d*)$/, '$1');//^[^\d]+表示以一个或多个不是数字的字符开头 (\d+\.?\d*)$表示以（之后需出现一个或多个数字，之后需出现0个或一个小数点，之后需出现0个或多个数字）结尾
        }else if(/\d+/.test(v)){// \d+表示之后需出现一个或多个数字 /.表示之后需出现一个小数点
            v = v.replace(/^[^\d]*(\d+\.?\d*).*$/, '$1'); //^[^\d]*表示以0个或多个不是数字的字符开头 (\d+\.?\d*)表示（之后需出现一个或多个数字，之后需出现0个或一个小数点，之后需出现0个或多个数字） .*$表示以（0个或多个除换行和回车以外的任意字符）结尾
            ty = false;
        }else if(/^0+\d+\.?\d*$/.test(v)){// ^0+表示以一个或多个0开头 \d+表示之后需出现一个或多个数字 \.?表示之后需出现0个或一个小数点 \d*$表示以0个或多个数字结尾
            v = v.replace(/^0+(\d+\.?\d*)$/, '$1'); //^0+表示以一个或多个0开头 (\d+\.?\d*)$表示以（之后需出现一个或多个数字，之后需出现0个或一个小数点，之后需出现0个或多个数字）结尾
            ty = false;
        }else{
            v = "";
        }
    }
    th.value = v;
}
