package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("预定辅助部门回收计算人工表")//AuxiliaryRecyclingArtificial
public class AuxiliaryRecyclingArtificial {
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 年月 */
	protected String yearMonth;
	/* 导入标识 */
	protected int importId;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 区分 */
	protected String auxiliaryCode;
	/* 直接部门 */
	protected String directDepartmentCode;
	/* 辅助部门 */
	protected String auxiliaryDepartmentCode;
	/* 费用项目 */
	protected String expenseItem;
	/* SMCH */
	protected BigDecimal sMCHNum;
	/* SH */
	protected BigDecimal sHNum;
	/* 单价 */
	protected BigDecimal unitPrice;
	/* 金额 */
	protected BigDecimal amount;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	/* 直接部门名称 */
	@TableField(exist = false)
	protected String directDepartmentName;
	/* 辅助部门名称 */
	@TableField(exist = false)
	protected String auxiliaryDepartmentName;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public int getImportId() {
		return importId;
	}

	public void setImportId(int importId) {
		this.importId = importId;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public String getAuxiliaryCode() {
		return auxiliaryCode;
	}

	public void setAuxiliaryCode(String auxiliaryCode) {
		this.auxiliaryCode = auxiliaryCode;
	}

	public String getDirectDepartmentCode() {
		return directDepartmentCode;
	}

	public void setDirectDepartmentCode(String directDepartmentCode) {
		this.directDepartmentCode = directDepartmentCode;
	}

	public String getAuxiliaryDepartmentCode() {
		return auxiliaryDepartmentCode;
	}

	public void setAuxiliaryDepartmentCode(String auxiliaryDepartmentCode) {
		this.auxiliaryDepartmentCode = auxiliaryDepartmentCode;
	}

	public String getExpenseItem() {
		return expenseItem;
	}

	public void setExpenseItem(String expenseItem) {
		this.expenseItem = expenseItem;
	}

	public BigDecimal getsMCHNum() {
		return sMCHNum;
	}

	public void setsMCHNum(BigDecimal sMCHNum) {
		this.sMCHNum = sMCHNum;
	}

	public BigDecimal getsHNum() {
		return sHNum;
	}

	public void setsHNum(BigDecimal sHNum) {
		this.sHNum = sHNum;
	}

	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public String getDirectDepartmentName() {
		return directDepartmentName;
	}

	public void setDirectDepartmentName(String directDepartmentName) {
		this.directDepartmentName = directDepartmentName;
	}

	public String getAuxiliaryDepartmentName() {
		return auxiliaryDepartmentName;
	}

	public void setAuxiliaryDepartmentName(String auxiliaryDepartmentName) {
		this.auxiliaryDepartmentName = auxiliaryDepartmentName;
	}
}