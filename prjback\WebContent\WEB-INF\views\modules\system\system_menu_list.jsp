<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ page import="com.hongru.common.util.Constants" %>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE html>
<html lang="en">
<head>
	<title>菜单列表</title>
	<style>
		/*设置数据表表头字体*/
		.layui-table th .ew-tree-table-cell-content{
			text-align: center;
		}
	</style>
</head>
<body class="layui-hide">
<div class="ibox-content">
	<div class="page-wrapper">
    <table id="demoTreeTb"></table>
	</div>
</div>

<script type="text/html" id="tbBar">
	<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
	{{#  if(d.menuType == 1){ }}
		<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="add"><i class="layui-icon layui-icon-add-1"></i>添加子菜单</a>
	{{# } else if(d.menuType == 2){ }}
		<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="add"><i class="layui-icon layui-icon-add-1"></i>添加操作</a>
	{{# } }}
</script>

<script type="text/html" id="tool">
	<div class="layui-btn-container">
		<button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="toAdd"><i class="layui-icon layui-icon-add-1"></i>添加</button>
  	<button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
  </div>
</script>
<script src="${ctxsta}/hongru/js/system/system_menu_list.js?time=7"></script>
</body>
</html>