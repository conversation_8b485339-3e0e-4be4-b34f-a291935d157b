package com.hongru.common.poiExcelExport.bean;

/**
* 读取text获得到的内容
* <AUTHOR>
* @create 2022/11/09 10:55
*/
/**
 * 这里用一句话描述这个类的作用
 * <AUTHOR>
 * @create 2023年1月3日
 * @version 1.0
 */
public class ReadTxtBean {
	/*条码*/
	protected String code;
	/*条码*/
	protected String barCode;
	/* 批号 */
	protected String batchNo;
	/* 序列号(LOT) */
	protected String serialNo;
	/*重量*/
	protected String weight;
	/*日期*/
	protected String date;
	/* 产品代码 */
	protected String productCode;
	/* 设备代码 */
	protected String equipmentCode;
	/* 尺寸 */
	protected String size;
	/* 客户简称 */
	protected String customerAbbreviation;
	/* 线盘型号 */
	protected String wireDiscModel;
	/* 线盘数 */
	protected String coilNum;

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getEquipmentCode() {
		return equipmentCode;
	}

	public void setEquipmentCode(String equipmentCode) {
		this.equipmentCode = equipmentCode;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	
	public String getSize() {
		return size;
	}

	public void setSize(String size) {
		this.size = size;
	}

	public String getCustomerAbbreviation() {
		return customerAbbreviation;
	}

	public void setCustomerAbbreviation(String customerAbbreviation) {
		this.customerAbbreviation = customerAbbreviation;
	}

	public String getWireDiscModel() {
		return wireDiscModel;
	}

	public void setWireDiscModel(String wireDiscModel) {
		this.wireDiscModel = wireDiscModel;
	}

	public String getCoilNum() {
		return coilNum;
	}

	public void setCoilNum(String coilNum) {
		this.coilNum = coilNum;
	}
}
