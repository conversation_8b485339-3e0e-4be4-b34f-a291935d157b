package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("铜加工費用明细表")//CostPrice
public class CuPriceCost {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 区分-圆线 */
	public static final short LINETYPE_CIRCULAR = 1;
	/* 区分-平角线 */
	public static final short LINETYPE_FLAT_ANGLE = 2;

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 状态 */
	protected short state;
	/* 年月 */
	protected String yearMonth;
	/* 供应商编码 */
	protected String supplierCode;
	/* 供应商名称 */
	protected String supplierName;
	/* 区分 */
	protected short lineType;
	/* 铜购入量 */
	protected BigDecimal purchasNum;
	/* 铜加工费单价 */
	protected BigDecimal procePrice;
	/* 升水 */
	protected BigDecimal ascendingWater;
	/* 汇率 */
	protected BigDecimal exchangeRate;
	/* 铜单价 */
	protected BigDecimal cuUnitPrice;
	/* 综合加工费 */
	protected BigDecimal comProcePrice;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 最后修改人标识 */
	protected int lastModifierId;
	/* 最后修改人姓名 */
	protected String lastModifierName;
	/* 最后修改时间 */
	protected String lastModifiedTime;

	@TableField(exist = false)
	protected Integer supplierId;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}

	public short getState() {
		return state;
	}

	public void setState(short state) {
		this.state = state;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public String getSupplierCode() {
		return supplierCode;
	}

	public void setSupplierCode(String supplierCode) {
		this.supplierCode = supplierCode;
	}

	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}

	public short getLineType() {
		return lineType;
	}

	public void setLineType(short lineType) {
		this.lineType = lineType;
	}

	public BigDecimal getPurchasNum() {
		return purchasNum;
	}

	public void setPurchasNum(BigDecimal purchasNum) {
		this.purchasNum = purchasNum;
	}

	public BigDecimal getProcePrice() {
		return procePrice;
	}

	public void setProcePrice(BigDecimal procePrice) {
		this.procePrice = procePrice;
	}

	public BigDecimal getAscendingWater() {
		return ascendingWater;
	}

	public void setAscendingWater(BigDecimal ascendingWater) {
		this.ascendingWater = ascendingWater;
	}

	public BigDecimal getExchangeRate() {
		return exchangeRate;
	}

	public void setExchangeRate(BigDecimal exchangeRate) {
		this.exchangeRate = exchangeRate;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public int getLastModifierId() {
		return lastModifierId;
	}

	public void setLastModifierId(int lastModifierId) {
		this.lastModifierId = lastModifierId;
	}

	public String getLastModifierName() {
		return lastModifierName;
	}

	public void setLastModifierName(String lastModifierName) {
		this.lastModifierName = lastModifierName;
	}

	public String getLastModifiedTime() {
		return lastModifiedTime;
	}

	public void setLastModifiedTime(String lastModifiedTime) {
		this.lastModifiedTime = lastModifiedTime;
	}

	public Integer getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Integer supplierId) {
		this.supplierId = supplierId;
	}

	public BigDecimal getCuUnitPrice() {
		return cuUnitPrice;
	}

	public void setCuUnitPrice(BigDecimal cuUnitPrice) {
		this.cuUnitPrice = cuUnitPrice;
	}

	public BigDecimal getComProcePrice() {
		return comProcePrice;
	}

	public void setComProcePrice(BigDecimal comProcePrice) {
		this.comProcePrice = comProcePrice;
	}
}