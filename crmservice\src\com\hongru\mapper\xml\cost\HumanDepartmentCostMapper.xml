<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.HumanDepartmentCostMapper">

    <sql id="humanDepartmentCost_sql">
		hum.[流水号] AS costId,hum.[状态] AS state,hum.[年月] AS yearMonth,hum.[部门] AS department,hum.[部门编号] AS departmentCode,
		hum.[人件费区分] AS humanPriceCode,hum.[创建人标识] AS creatorId,hum.[创建人姓名] AS creatorName,hum.[创建时间] AS createdTime,
		hum.[最后修改人标识] AS lastModifierId,hum.[最后修改人姓名] AS lastModifierName,hum.[最后修改时间] AS lastModifiedTime
	</sql>

    <insert id="insertHumanDepartmentCost" parameterType="com.hongru.entity.cost.HumanDepartmentCost">
        INSERT INTO [CostPrice].[dbo].[人件部门表]
		(
		[状态],
		[年月],
		[部门],
		[部门编号],
		[人件费区分],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[最后修改人标识],
		[最后修改人姓名],
		[最后修改时间]
		)VALUES(
		#{humanDepartmentCost.state},
		#{humanDepartmentCost.yearMonth},
		#{humanDepartmentCost.department},
		#{humanDepartmentCost.departmentCode},
		#{humanDepartmentCost.humanPriceCode},
		#{humanDepartmentCost.creatorId},
		#{humanDepartmentCost.creatorName},
		#{humanDepartmentCost.createdTime},
		#{humanDepartmentCost.lastModifierId},
		#{humanDepartmentCost.lastModifierName},
		#{humanDepartmentCost.lastModifiedTime}
		)
    </insert>

    <select id="selectByCostId" resultType="com.hongru.entity.cost.HumanDepartmentCost">
        SELECT
        <include refid="humanDepartmentCost_sql"/>
        FROM [CostPrice].[dbo].[人件部门表] hum
        <where>
            hum.[状态] != 9
            <if test="costId != null">
                AND hum.[流水号] = #{costId}
            </if>
        </where>
    </select>

    <select id="selectByYearMonth" resultType="com.hongru.entity.cost.HumanDepartmentCost">
        SELECT
        TOP 1
        <include refid="humanDepartmentCost_sql"/>
        FROM [CostPrice].[dbo].[人件部门表] hum
        <where>
            hum.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND hum.[年月] = #{yearMonth}
            </if>
        </where>
    </select>

	<select id="listCostPricePage" resultType="com.hongru.entity.cost.HumanDepartmentCost">
		SELECT
		<include refid="humanDepartmentCost_sql"/>
		FROM [CostPrice].[dbo].[人件部门表] hum
		<where>
			hum.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND hum.[年月] = #{yearMonth}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY hum.[流水号] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listCostPricePageCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[人件部门表] hum
		<where>
			hum.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND hum.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<update id="updateState">
		UPDATE [CostPrice].[dbo].[人件部门表]
		SET [状态] = #{state}
		WHERE [流水号] = #{costId}
	</update>
</mapper>