package com.hongru.entity.yearRevise;

import java.math.BigDecimal;

/**
* 电燃氮查询返回结果实体类
* <AUTHOR>
* @create 2024/01/04 09:55
*/
public class PowerShCoefficientBean {
	//部门:06 UF氮气  05 EH天然气 
	/* 部门 */
	protected String department;
	/* 电力使用量实绩合计 */
	protected BigDecimal eleUsedActTotal;
	/* 电力费合计 */
	protected BigDecimal elePriceTotal;
	/* 水费合计 */
	protected BigDecimal waterPriceTotal;
	/* 电力单价实绩 */
	protected BigDecimal eleUnitPriceActAvg;
	/* 天燃气费合计 */
	protected BigDecimal gasPriceTotal;
	/* 天燃气费使用量合计 */
	protected BigDecimal gasUsedActTotal;
	/* 天燃气单价实绩 */
	protected BigDecimal gasUnitPriceActAvg;
	/* 氮气费合计 */
	protected BigDecimal nitrogenPriceTotal;
	/* 氮气试用量平均 */
	protected BigDecimal nitrogenActAvg;
	/* 氮气费使用量合计 */
	protected BigDecimal nitrogenUsedActTotal;
	/* 氮气单价实绩 */
	protected BigDecimal nitrogenUnitPriceActAvg;
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public BigDecimal getEleUsedActTotal() {
		return eleUsedActTotal;
	}
	public void setEleUsedActTotal(BigDecimal eleUsedActTotal) {
		this.eleUsedActTotal = eleUsedActTotal;
	}
	public BigDecimal getElePriceTotal() {
		return elePriceTotal;
	}
	public void setElePriceTotal(BigDecimal elePriceTotal) {
		this.elePriceTotal = elePriceTotal;
	}
	public BigDecimal getWaterPriceTotal() {
		return waterPriceTotal;
	}
	public void setWaterPriceTotal(BigDecimal waterPriceTotal) {
		this.waterPriceTotal = waterPriceTotal;
	}
	public BigDecimal getEleUnitPriceActAvg() {
		return eleUnitPriceActAvg;
	}
	public void setEleUnitPriceActAvg(BigDecimal eleUnitPriceActAvg) {
		this.eleUnitPriceActAvg = eleUnitPriceActAvg;
	}
	public BigDecimal getGasPriceTotal() {
		return gasPriceTotal;
	}
	public void setGasPriceTotal(BigDecimal gasPriceTotal) {
		this.gasPriceTotal = gasPriceTotal;
	}
	public BigDecimal getGasUsedActTotal() {
		return gasUsedActTotal;
	}
	public void setGasUsedActTotal(BigDecimal gasUsedActTotal) {
		this.gasUsedActTotal = gasUsedActTotal;
	}
	public BigDecimal getGasUnitPriceActAvg() {
		return gasUnitPriceActAvg;
	}
	public void setGasUnitPriceActAvg(BigDecimal gasUnitPriceActAvg) {
		this.gasUnitPriceActAvg = gasUnitPriceActAvg;
	}
	public BigDecimal getNitrogenPriceTotal() {
		return nitrogenPriceTotal;
	}
	public void setNitrogenPriceTotal(BigDecimal nitrogenPriceTotal) {
		this.nitrogenPriceTotal = nitrogenPriceTotal;
	}
	public BigDecimal getNitrogenActAvg() {
		return nitrogenActAvg;
	}
	public void setNitrogenActAvg(BigDecimal nitrogenActAvg) {
		this.nitrogenActAvg = nitrogenActAvg;
	}
	public BigDecimal getNitrogenUsedActTotal() {
		return nitrogenUsedActTotal;
	}
	public void setNitrogenUsedActTotal(BigDecimal nitrogenUsedActTotal) {
		this.nitrogenUsedActTotal = nitrogenUsedActTotal;
	}
	public BigDecimal getNitrogenUnitPriceActAvg() {
		return nitrogenUnitPriceActAvg;
	}
	public void setNitrogenUnitPriceActAvg(BigDecimal nitrogenUnitPriceActAvg) {
		this.nitrogenUnitPriceActAvg = nitrogenUnitPriceActAvg;
	}

}