package com.hongru.entity.pims;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

/**
 * 产品设计数据表实体类 (PIMS数据库)
 * 
 * <AUTHOR>
 */
@TableName("产品设计数据表")
public class ProductDesignData {

    /* 流水号 */
    @TableId(value = "流水号", type = IdType.AUTO)
    protected int serialNumber;

    /* 产品代码 */
    @TableField("产品代码")
    protected String productCode;

    /* 条码 */
    @TableField("条码")
    protected String barcode;

    /* 标签尺寸名称 */
    @TableField("标签尺寸名称")
    protected String labelSizeName;

    /* 膜厚底1.STD */
    @TableField("膜厚底1.STD")
    protected BigDecimal filmThickness1Std;

    /* 膜厚底2.STD */
    @TableField("膜厚底2.STD")
    protected BigDecimal filmThickness2Std;

    /* 膜厚中.STD */
    @TableField("膜厚中.STD")
    protected BigDecimal filmThickness3Std;

    /* 膜厚上.STD */
    @TableField("膜厚上.STD")
    protected BigDecimal filmThickness4Std;

    /* 线盘名称 */
    @TableField("线盘名称")
    protected String wireDiscName;

    /* 有效状态 */
    @TableField("有效状态")
    protected String validStatus;

    // Getters and Setters
    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getLabelSizeName() {
        return labelSizeName;
    }

    public void setLabelSizeName(String labelSizeName) {
        this.labelSizeName = labelSizeName;
    }

    public BigDecimal getFilmThickness1Std() {
        return filmThickness1Std;
    }

    public void setFilmThickness1Std(BigDecimal filmThickness1Std) {
        this.filmThickness1Std = filmThickness1Std;
    }

    public BigDecimal getFilmThickness2Std() {
        return filmThickness2Std;
    }

    public void setFilmThickness2Std(BigDecimal filmThickness2Std) {
        this.filmThickness2Std = filmThickness2Std;
    }

    public BigDecimal getFilmThickness3Std() {
        return filmThickness3Std;
    }

    public void setFilmThickness3Std(BigDecimal filmThickness3Std) {
        this.filmThickness3Std = filmThickness3Std;
    }

    public BigDecimal getFilmThickness4Std() {
        return filmThickness4Std;
    }

    public void setFilmThickness4Std(BigDecimal filmThickness4Std) {
        this.filmThickness4Std = filmThickness4Std;
    }

    public String getWireDiscName() {
        return wireDiscName;
    }

    public void setWireDiscName(String wireDiscName) {
        this.wireDiscName = wireDiscName;
    }

    public String getValidStatus() {
        return validStatus;
    }

    public void setValidStatus(String validStatus) {
        this.validStatus = validStatus;
    }
}
