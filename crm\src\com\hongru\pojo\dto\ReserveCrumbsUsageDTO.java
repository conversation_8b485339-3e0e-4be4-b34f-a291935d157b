package com.hongru.pojo.dto;

import com.hongru.entity.cost.ReserveCrumbsUsage;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class ReserveCrumbsUsageDTO {

    private PageInfo pageInfo;

    private List<ReserveCrumbsUsage> reserveCrumbsUsages;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<ReserveCrumbsUsage> getReserveCrumbsUsages() {
        return reserveCrumbsUsages;
    }

    public void setReserveCrumbsUsages(List<ReserveCrumbsUsage> reserveCrumbsUsages) {
        this.reserveCrumbsUsages = reserveCrumbsUsages;
    }

    public ReserveCrumbsUsageDTO(PageInfo pageInfo, List<ReserveCrumbsUsage> reserveCrumbsUsages) {
        this.pageInfo = pageInfo;
        this.reserveCrumbsUsages = reserveCrumbsUsages;
    }
}
