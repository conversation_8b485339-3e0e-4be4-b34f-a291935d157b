<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>予定入库量-添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
        .layui-input, .layui-textarea {
            display: inline-block !important;
            width: 40% !important;
            padding-left: 10px !important;
        }
        .layui-form-item .layui-inline {
            clear: both;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/yearRevise/newYearStoragePre/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>年度:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="year"  name="year" value=""   required=""  lay-verify="required"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"  style="width: 150px"><span class="star">*</span>EM年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="emNewYearStoragePre" name="emNewYearStoragePre" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required" placeholder="入库"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>EF年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="efNewYearStoragePre" name="efNewYearStoragePre" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                         </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>EF09年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ef09NewYearStoragePre" name="ef09NewYearStoragePre" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                         </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>ER年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="erNewYearStoragePre" name="erNewYearStoragePre" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                         </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 150px"><span class="star">*</span>EH年度予定入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ehNewYearStoragePre" name="ehNewYearStoragePre" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label" style="width: 150px"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="statId" name="statId" value="0"/>
                            <input type="hidden" id="state" name="state" value="0"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/yearRevise/newYearStoragePre_addOrModify.js?time=1"></script>
</myfooter>
</body>
</html>
