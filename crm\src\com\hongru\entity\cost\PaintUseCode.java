package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("使用区分表")//CostPrice
public class PaintUseCode {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 小分类编号 */
	protected String smallCode;
	/* 小分类部门 */
	protected String smallDepartment;
	/* 中分类编号 */
	protected String midCode;
	/* 中分类部门 */
	protected String midDepartment;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	@TableField(exist = false)
	protected Integer supplierId;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}

	public String getSmallCode() {
		return smallCode;
	}

	public void setSmallCode(String smallCode) {
		this.smallCode = smallCode;
	}

	public String getSmallDepartment() {
		return smallDepartment;
	}

	public void setSmallDepartment(String smallDepartment) {
		this.smallDepartment = smallDepartment;
	}

	public String getMidCode() {
		return midCode;
	}

	public void setMidCode(String midCode) {
		this.midCode = midCode;
	}

	public String getMidDepartment() {
		return midDepartment;
	}

	public void setMidDepartment(String midDepartment) {
		this.midDepartment = midDepartment;
	}

	public Integer getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Integer supplierId) {
		this.supplierId = supplierId;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
}