<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.DepartmentUnitPriceMapper">
    
    <sql id="departmentUnitPrice_sql">
        dup.[流水号] AS serialNumber,
        dup.[年度] AS year,
        dup.[机械类别] AS machineType,
        dup.[属性] AS attribute,
        dup.[费用项目] AS expenseItem,
        dup.[工场区分] AS factoryType,
        dup.[SH系数] AS shCoefficient,
        dup.[单价] AS unitPrice,
        dup.[项目费用] AS projectCost,
        dup.[创建人姓名] AS creatorName,
        dup.[创建时间] AS createdTime,
        dup.[更新人姓名] AS updaterName,
        dup.[更新时间] AS updatedTime
    </sql>

    <select id="listDepartmentUnitPricePage" resultType="com.hongru.entity.cost.DepartmentUnitPrice">
        SELECT
        <include refid="departmentUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[部门单价表] dup
        <where>
            <if test="year != null and year != ''">
                AND dup.[年度] = #{year}
            </if>
            <if test="machineType != null and machineType != ''">
                AND dup.[机械类别] = #{machineType}
            </if>
            <if test="factoryType != null and factoryType != ''">
                AND dup.[工场区分] = #{factoryType}
            </if>
        </where>
        ORDER BY dup.[年度] DESC, dup.[机械类别]
        <if test="pageInfo != null">
            OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
        </if>
    </select>

    <select id="listDepartmentUnitPricePageCount" resultType="int">
        SELECT COUNT(1)
        FROM [CostPrice].[dbo].[部门单价表] dup
        <where>
            <if test="year != null and year != ''">
                AND dup.[年度] = #{year}
            </if>
            <if test="machineType != null and machineType != ''">
                AND dup.[机械类别] = #{machineType}
            </if>
            <if test="factoryType != null and factoryType != ''">
                AND dup.[工场区分] = #{factoryType}
            </if>
        </where>
    </select>

    <select id="selectBySerialNumber" resultType="com.hongru.entity.cost.DepartmentUnitPrice">
        SELECT
        <include refid="departmentUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[部门单价表] dup
        WHERE dup.[流水号] = #{serialNumber}
    </select>

    <insert id="insertDepartmentUnitPrice" parameterType="com.hongru.entity.cost.DepartmentUnitPrice">
        INSERT INTO [CostPrice].[dbo].[部门单价表]
        (
            [年度],
            [机械类别],
            [属性],
            [费用项目],
            [工场区分],
            [SH系数],
            [单价],
            [项目费用],
            [创建人姓名],
            [创建时间],
            [更新人姓名],
            [更新时间]
        )
        VALUES
        (
            #{departmentUnitPrice.year},
            #{departmentUnitPrice.machineType},
            #{departmentUnitPrice.attribute},
            #{departmentUnitPrice.expenseItem},
            #{departmentUnitPrice.factoryType},
            #{departmentUnitPrice.shCoefficient},
            #{departmentUnitPrice.unitPrice},
            #{departmentUnitPrice.projectCost},
            #{departmentUnitPrice.creatorName},
            #{departmentUnitPrice.createdTime},
            #{departmentUnitPrice.updaterName},
            #{departmentUnitPrice.updatedTime}
        )
    </insert>

    <update id="updateDepartmentUnitPrice" parameterType="com.hongru.entity.cost.DepartmentUnitPrice">
        UPDATE [CostPrice].[dbo].[部门单价表]
        SET
            [SH系数] = #{departmentUnitPrice.shCoefficient},
            [单价] = #{departmentUnitPrice.unitPrice},
            [项目费用] = #{departmentUnitPrice.shCoefficient} * #{departmentUnitPrice.unitPrice},
            [更新人姓名] = #{departmentUnitPrice.updaterName},
            [更新时间] = #{departmentUnitPrice.updatedTime}
        WHERE [流水号] = #{departmentUnitPrice.serialNumber}
    </update>

    <select id="selectDepartmentUnitPriceByYear" resultType="com.hongru.entity.cost.DepartmentUnitPrice">
        SELECT
        <include refid="departmentUnitPrice_sql"/>
        FROM [CostPrice].[dbo].[部门单价表] dup
        WHERE dup.[年度] = #{year}
        ORDER BY dup.[机械类别], dup.[费用项目]
    </select>

    <delete id="deleteDepartmentUnitPriceByYear">
        DELETE FROM [CostPrice].[dbo].[部门单价表]
        WHERE [年度] = #{year}
    </delete>

    <delete id="deleteDepartmentUnitPriceByYearAndFactoryType">
        DELETE FROM [CostPrice].[dbo].[部门单价表]
        WHERE [年度] = #{year} AND [工场区分] = #{factoryType}
    </delete>

    <insert id="batchInsertDepartmentUnitPrice" parameterType="java.util.List">
        INSERT INTO [CostPrice].[dbo].[部门单价表]
        (
            [年度],
            [机械类别],
            [属性],
            [费用项目],
            [工场区分],
            [SH系数],
            [单价],
            [项目费用],
            [创建人姓名],
            [创建时间],
            [更新人姓名],
            [更新时间]
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.year},
            #{item.machineType},
            #{item.attribute},
            #{item.expenseItem},
            #{item.factoryType},
            #{item.shCoefficient},
            #{item.unitPrice},
            #{item.projectCost},
            #{item.creatorName},
            #{item.createdTime},
            #{item.updaterName},
            #{item.updatedTime}
        )
        </foreach>
    </insert>

    <update id="updateDepartmentUnitPriceByExpenseItem">
        UPDATE [CostPrice].[dbo].[部门单价表]
        SET
            [单价] = #{unitPrice},
            [项目费用] = [SH系数] * #{unitPrice}
        WHERE [年度] = #{year}
        AND [费用项目] = #{expenseItemName}
    </update>

</mapper>
