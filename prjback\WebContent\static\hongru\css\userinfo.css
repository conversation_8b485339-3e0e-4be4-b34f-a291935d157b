.user-info-head {
	width: 110px;
	height: 110px;
	line-height: 110px;
	position: relative;
	display: inline-block;
	border: 2px solid #eee;
	border-radius: 50%;
	overflow: hidden;
	cursor: pointer;
	margin: 0 auto;
}

.user-info-head1111:hover:after {
	content: '\e681';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	color: #fff;
	background-color: rgba(0, 0, 0, 0.3);
	font-size: 28px;
	padding-top: 2px;
	font-style: normal;
	font-family: layui-icon;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.user-info-head img {
	width: 110px;
	height: 110px;
}

.user-info-list-item {
	position: relative;
	padding-bottom: 8px;
}

.user-info-list-item>.layui-icon {
	position: absolute;
}

.user-info-list-item>p {
	padding-left: 30px;
}

.layui-line-dash {
	border-bottom: 1px dashed #ccc;
	margin: 15px 0;
}

#userInfoForm .layui-form-item {
	margin-bottom: 25px;
}

#pwdForm .layui-form-item {
	margin-bottom: 25px;
}

.user-bd-list-item {
	padding: 14px 60px 14px 10px;
	border-bottom: 1px solid #e8e8e8;
	position: relative;
}

.user-bd-list-item .user-bd-list-lable {
	color: #333;
	margin-bottom: 4px;
}

.user-bd-list-item .user-bd-list-oper {
	position: absolute;
	top: 50%;
	right: 10px;
	margin-top: -8px;
	cursor: pointer;
}

.user-bd-list-item .user-bd-list-img {
	width: 48px;
	height: 48px;
	line-height: 48px;
	position: absolute;
	top: 50%;
	left: 10px;
	margin-top: -24px;
}

.user-bd-list-item .user-bd-list-img+.user-bd-list-content {
	margin-left: 68px;
}