<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report2" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="733faca4-61d5-4358-8562-156073881700">
	<property name="ireport.zoom" value="1.1000000000000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table" fontName="黑体" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="1f62aec1-8739-40d4-be67-0c267e9d4cfd">
		<field name="productCode" class="java.lang.String"/>
		<field name="palletNumberStr" class="java.lang.String"/>
		<field name="wireReelNumberStr" class="java.lang.String"/>
		<field name="totalWeight" class="java.lang.String"/>
	</subDataset>
	<field name="time" class="java.lang.String"/>
	<field name="totalWeight" class="java.lang.String"/>
	<field name="subList" class="java.util.List"/>
	<field name="stockInTime" class="java.lang.String"/>
	<field name="totalWireReelNumber" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="31"/>
	</title>
	<pageHeader>
		<band height="40">
			<staticText>
				<reportElement uuid="b2340e08-9072-43ec-bc34-c7eb924372b9" x="0" y="6" width="555" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="22" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[入库明细表-汇总打印]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="120">
			<componentElement>
				<reportElement uuid="b2293c99-a625-4d0c-9a7a-f4158eff6204" key="table 1" x="0" y="39" width="555" height="77"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="Table Dataset 1" uuid="39159ebe-6b2b-4e67-8b73-5b91e3ed206f">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{subList})]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="235" uuid="d4e1c399-6cbd-48a5-89ce-2cf638ee1184">
						<jr:tableHeader height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="1f23ab15-917a-4c90-ad73-f78ceb8fa2a5" x="0" y="0" width="235" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[产品代码]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField>
								<reportElement uuid="b7d67b5e-3dd5-4040-b2cb-367410bbc25c" x="0" y="0" width="235" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{productCode}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="3748fe94-9da8-4923-b848-085919de6802">
						<jr:tableHeader height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="4be8b476-740b-4ca7-98e0-43252621232e" x="0" y="0" width="100" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[木托数]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField>
								<reportElement uuid="77fa5af0-8c6d-4660-a6d0-d1e213bbec3e" x="0" y="0" width="100" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{palletNumberStr}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="e0fb9969-71bd-496d-9cdd-cb0c8f77023f">
						<jr:tableHeader height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="447004a6-a99a-4000-91e7-ac06bafc6718" x="0" y="0" width="100" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[线盘数]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField>
								<reportElement uuid="02432bbb-baff-414a-9997-62f1af0b68b7" x="0" y="0" width="100" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{wireReelNumberStr}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="80294236-2cd7-4861-8553-922dcb858f93">
						<jr:tableHeader height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="fd897a1c-bf3f-4f88-97e2-c23924c69dc4" x="0" y="0" width="120" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<text><![CDATA[小计重量（KG）]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:detailCell height="20" rowSpan="1">
							<textField>
								<reportElement uuid="0783a76b-d6c2-4d1c-b58e-c510758d9fd2" x="0" y="0" width="120" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="宋体" size="14" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{totalWeight}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
			<textField>
				<reportElement uuid="bf49e1d2-b5f7-42d2-b444-d1f650ac9044" x="425" y="5" width="130" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{stockInTime}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="d8fd6135-668b-4e76-8ed6-1568cb93d6b2" x="335" y="5" width="90" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[入库日期]]></text>
			</staticText>
		</band>
	</detail>
	<pageFooter>
		<band height="89">
			<staticText>
				<reportElement uuid="63111271-87c6-4734-99a1-bede82f47037" x="335" y="0" width="90" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[总计]]></text>
			</staticText>
			<textField>
				<reportElement uuid="2a2b755d-58a2-4060-bbe5-8fd75452f272" x="425" y="0" width="130" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalWeight}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="23bf35c9-6b23-4b96-8c10-40c34bffa202" x="425" y="66" width="130" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2acd5f10-4b82-40a3-9f8b-0715055a9537" x="29" y="56" width="130" height="30"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{time}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="b3420999-2cc9-4a27-8f72-73783545b1e0" x="335" y="30" width="90" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[合计线盘数]]></text>
			</staticText>
			<textField>
				<reportElement uuid="663f106e-1623-4c14-becf-42614ef8e0f7" x="425" y="30" width="130" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalWireReelNumber}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
