package com.hongru.controller.administrator;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.hongru.base.BaseController;
import com.hongru.base.BasePageDTO;
import com.hongru.common.enums.StatusEnum;
import com.hongru.common.exception.ValidateException;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.RegexUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.Organization;
import com.hongru.entity.admin.Role;
import com.hongru.entity.admin.User;
import com.hongru.entity.admin.UserLoginLog;
import com.hongru.pojo.dto.UserPageDTO;
import com.hongru.service.admin.IOrganizationService;
import com.hongru.service.admin.IRoleService;
import com.hongru.service.admin.IUserLoginLogService;
import com.hongru.service.admin.IUserRoleService;
import com.hongru.service.admin.IUserService;
import com.hongru.support.page.PageInfo;

/**
 * 
* 类名称：AdministratorListController   
* 类描述：管理员列表表示层控制器     
* 创建人：hongru   
* 创建时间：2017年4月2日 下午4:12:17   
*
 */
@Controller
@RequestMapping(value = "/administrator/list")
public class AdministratorListController extends BaseController{
	
	@Autowired
	private IUserService userService;
	@Autowired
	private IRoleService roleService;
	@Autowired
	private IUserRoleService userRoleService;
	@Autowired
	private IUserLoginLogService userLoginLogService;
	@Autowired
	private IOrganizationService organizationService;

	/**
	 * GET 管理员列表页面
	 * @param model
	 * @return
	 */
	@RequiresPermissions("administrator:list:view")
	@GetMapping(value = "/view")
	public String getListPage(Model model) {
		// 用户权限
		List<Role> roles = roleService.listBySataus(StatusEnum.NORMAL.getStatus());
		model.addAttribute("roles", roles);

		// 部门列表
		List<Organization> organizations = organizationService.listBySataus(StatusEnum.NORMAL.getStatus());
		model.addAttribute("organizations", organizations);
		return "/modules/admin/admin_user_list";
	}
	
	/**
	 * GET 管理员列表
	 * @return
	 */
	@PostMapping(value = "/json")
	@ResponseBody
	public Object listUser(Integer page, Integer limit, @RequestParam(required = false, value = "search") String search,
	    Integer roleId,String realNameLike,Integer organizationId, String telephoneLike, String emailLike) {
		PageInfo pageInfo = new PageInfo(limit, page);
		Integer[] roleIds = null;
		if(roleId != null){
			roleIds = new Integer[]{roleId};
		}
		UserPageDTO userPageDTO = userService.listByPage(pageInfo, search,roleIds,realNameLike,organizationId, telephoneLike,emailLike);
		for(User user : userPageDTO.getUserVOs()) {
			// 分配角色
			String roleName="";
			List<Role> roles = userRoleService.listByUserId(user.getUserId(), StatusEnum.NORMAL.getStatus());
			if(roles !=null && roles.size()>0){
				for(Role role:roles){
					if(StringUtil.isStringEmpty(roleName)){
						roleName = role.getRoleName();
					}else{
						roleName +=","+role.getRoleName();
					}
				}
			}
			user.setRoleName(roleName);
		}
		return new HrPageResult(userPageDTO.getUserVOs(), userPageDTO.getPageInfo().getTotal());
	}
	
	/**
	 * PUT 启用/冻结管理员
	 * @return
	 */
	/*@RequiresPermissions("administrator:list:audit")*/
	@PutMapping(value = "/{userId}/audit")
	@ResponseBody
	public Object audit(@PathVariable("userId") Long userId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			Integer count = userService.updateStatus(userId);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * DELETE 删除管理员
	 * @return
	 */
	@RequiresPermissions("administrator:list:delete")
	@DeleteMapping(value = "/{userId}")
	@ResponseBody
	public Object delete(@PathVariable("userId") Long userId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			Integer count = userService.deleteByUserId(userId);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * GET 管理员登录日志列表页面
	 * @return
	 */
	@RequiresPermissions("administrator:list:view")
	@GetMapping(value = "/{userId}/log")
	public String getLogPage(Model model, @PathVariable("userId") Long userId) {
		model.addAttribute("userId", userId);
		return "/modules/admin/admin_user_login_log";
	}
	
	/**
	 * GET 管理员登录日志列表
	 * @return
	 */
	@RequiresPermissions("administrator:list:view")
	@PostMapping(value = "/{userId}/logs")
	@ResponseBody
	public Object listLogs(@PathVariable("userId") Long userId, Integer page, Integer limit,
			@RequestParam(required = false, value = "search") String search) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		PageInfo pageInfo = new PageInfo(limit, page);
		if (authorizingUser != null) {
			// 用户日志
			BasePageDTO<UserLoginLog> basePageDTO = userLoginLogService.listByUserId(userId, pageInfo, search);
			return new HrPageResult(basePageDTO.getList(), basePageDTO.getPageInfo().getTotal());
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * GET 创建管理员页面
	 * @return
	 */
	@RequiresPermissions("administrator:list:create")
	@GetMapping(value = "/create/view")
	public String getInsertPage(Model model) {
		// 用户权限
		List<Role> roles = roleService.listBySataus(StatusEnum.NORMAL.getStatus());
		model.addAttribute("roles", roles);

		// 部门列表
		List<Organization> organizations = organizationService.listBySataus(StatusEnum.NORMAL.getStatus());
		model.addAttribute("organizations", organizations);

		return "/modules/admin/admin_user_add";
	}
	
	/**
	 * POST 创建管理员
	 * @return
	 */
	@RequiresPermissions("administrator:list:create")
	@PostMapping(value = "/create")
	@ResponseBody
	public Object insert(User user, @RequestParam(required = false, value = "roleId") String[] roleIds) {
		if (!StringUtil.isStringEmpty(user.getEmail()) && !RegexUtils.isEmail(user.getEmail())) {
			return new HrResult(CommonReturnCode.FAILED.getCode(), "请输入正确的电子邮箱!");
		}
		if (!StringUtil.isStringEmpty(user.getTelephone()) && !RegexUtils.isTelephone(user.getTelephone())) {
			return new HrResult(CommonReturnCode.FAILED.getCode(), "请输入正确的手机号码!");
		}

		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			try {
				// 创建用户及插入角色记录
				Integer count = userService.insertUser(user, roleIds, authorizingUser.getUserName());
				return new HrResult(CommonReturnCode.SUCCESS, count);
			} catch (ValidateException e) {
				logger.error(e.getMessage(), e);
				return new HrResult(e.getCode(), e.getMessage());
			}
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * GET 更新管理员页面
	 * @return
	 */
	@RequiresPermissions("administrator:list:edit")
	@GetMapping(value = "/{userId}/edit/view")
	public String getUpdatePage(Model model, @PathVariable("userId") Long userId) {
		// 用户信息
		User user = userService.getById(userId);
		model.addAttribute("user", user);

		// 用户权限
		List<Role> roles = roleService.listBySataus(StatusEnum.NORMAL.getStatus());
		model.addAttribute("roles", roles);

		// 部门列表
		List<Organization> organizations = organizationService.listBySataus(StatusEnum.NORMAL.getStatus());
		model.addAttribute("organizations", organizations);

		// 分配角色
		List<Role> userRoles = userRoleService.listByUserId(user.getUserId(), StatusEnum.NORMAL.getStatus());
		model.addAttribute("userRoles", userRoles);

		return "/modules/admin/admin_user_edit";
	}
	
	/**
	 * PUT 更新管理员信息
	 * @return
	 */
	@RequiresPermissions("administrator:list:edit")
	@PostMapping(value = "/{userId}/edit")
	@ResponseBody
	public Object update(User user, @PathVariable("userId") Long userId,
			@RequestParam(required = false, value = "roleId") String[] roleIds) {
		if (!StringUtil.isStringEmpty(user.getEmail()) && !RegexUtils.isEmail(user.getEmail())) {
			return new HrResult(CommonReturnCode.FAILED.getCode(), "请输入正确的电子邮箱!");
		}
		if (!StringUtil.isStringEmpty(user.getTelephone()) && !RegexUtils.isTelephone(user.getTelephone())) {
			return new HrResult(CommonReturnCode.FAILED.getCode(), "请输入正确的手机号码!");
		}

		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			// 更新用户及角色记录
			Integer count = userService.updateUser(user, roleIds, authorizingUser.getUserName());
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}

	/**
	* 批量删除理员
	* @param userIds
	* <AUTHOR>
	* @create 2022/02/11 14:09
	*/
	@PostMapping(value = "/delete/forBatch")
	@ResponseBody
	public Object deleteBatchUser(String userIds) {
		try{
			AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
			if (authorizingUser != null) {
				if(!StringUtil.isStringEmpty(userIds)){
					String[] arr =userIds.split(",");
					for(String idStr:arr){
						userService.deleteByUserId(Long.valueOf(idStr));
					}
				}
				return new HrResult(CommonReturnCode.SUCCESS);
			} else {
				return new HrResult(CommonReturnCode.UNAUTHORIZED);
			}
		}catch (Exception e){
			e.printStackTrace();
			return new HrResult(CommonReturnCode.FAILED.getCode(),"操作失败！");
		}
	}

}
