<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>线盘成本编码设定</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-card">
            <div class="layui-card-body">
                <!-- 搜索条件 -->
                <form class="layui-form" id="formSearch">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">线盘名称:</label>
                            <div class="layui-input-inline">
                                <input type="text" name="wireDiscName" placeholder="请输入线盘名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="button" class="layui-btn layui-bg-blue" onclick="search()">
                                <i class="layui-icon layui-icon-search"></i>检索
                            </button>
                        </div>
                    </div>
                </form>

                <!-- 数据表格 -->
                <table class="layui-hide" id="demo" lay-filter="test"></table>

                <!-- 自定义头部工具栏 -->
                <script type="text/html" id="toolbarDemo">
                    <div class="layui-btn-container">
                        <button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="toAdd">
                            <i class="layui-icon layui-icon-add-1"></i>新增线盘成本编码
                        </button>
                        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh">
                            <i class="layui-icon layui-icon-refresh"></i>刷新
                        </button>
                    </div>
                </script>

                <!-- 自定义表中工具栏 -->
                <script type="text/html" id="barDemo">
                    <a class="layui-btn layui-btn-xs layui-bg-green" lay-event="edit">
                        <i class="layui-icon layui-icon-edit"></i>编辑
                    </a>
                    <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="remove">
                        <i class="layui-icon layui-icon-delete"></i>删除
                    </a>
                </script>
            </div>
        </div>
    </div>
</div>

<script src="${ctx}/static/hongru/js/yearParamSet/wireDiscCode_list.js?v=1.0.2"></script>
</body>
</html>
