var table = null;
var laydate = null;
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var layer = layui.layer //弹层
        ,element = layui.element //元素操作
        ,form = layui.form;
    
    laydate = layui.laydate; //日期
    table = layui.table; //表格

    // 自动生成表头
    autoTableHead();
    // 自动生成日期查询条件
    autoYearDiv();

    //监听头工具栏事件
    table.on('toolbar(demo)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                add();
                break;
            case 'dataImport':
                dataImport();
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
        };
    });

    //监听行工具事件
    table.on('tool(demo)', function(obj){
        var data = obj.data;
        if(obj.event === 'edit'){
            edit(data.serialNumber);
        }
    });

});

function search() {
    $("#isSearch").val(1);
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);

    var factoryType = $("#factoryType").val();

    if(factoryType == "HZ"){
        var startDate = $("#startDate").val();
        var endDate = $("#endDate").val();
        
        if(!startDate || !endDate){
            layer.msg("请先选择开始日期和结束日期");
            return;
        }

        temp.startDate = startDate;
        temp.endDate = endDate;
    }

    //执行重载
    layui.table.reload('demo', {
       where: temp
    }, 'data');
}

function reset() {
    $("#year").val('');
    $("#machineType").val('');
    $("#isSearch").val(0);
    //执行重载，显示空数据
    layui.table.reload('demo', {
       where: {isSearch: 0}
    }, 'data');
}

// MW、UF Tab切换
function toPage(factoryType) {
    $("#factoryType").val(factoryType);
    $("#year").val('');
    $("#machineType").val('');

    if(factoryType == "MW"){
        $("#UF").removeClass("layui-this");
        $("#HZ").removeClass("layui-this");
        $("#MW").addClass("layui-this");
    }else if(factoryType == "UF"){
        $("#MW").removeClass("layui-this");
        $("#HZ").removeClass("layui-this");
        $("#UF").addClass("layui-this");
    }else if(factoryType == "HZ"){
        $("#MW").removeClass("layui-this");
        $("#UF").removeClass("layui-this");
        $("#HZ").addClass("layui-this");
    }

    // 自动生成表头
    autoTableHead();
    // 自动生成日期查询条件
    autoYearDiv();

    $("#isSearch").val(0);
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    //执行重载
    layui.table.reload('demo', {
        where: temp
    }, 'data');
}

// 自动生成表头
function autoTableHead() {
    // 表头
    var tableHead = [
        {field: 'orderNo', title: '',width:50,fixed: 'left',type:"numbers",align:'center'}
        ,{field: 'year',title: '年度',align:'center', width:100}
        ,{field: 'machineType',title: '机械类别',align:'center', width:120}
        ,{field: 'attribute',title: '属性',align:'center', width:100,templet: function (d) {
                if(d.attribute == '01'){
                    return "直接";
                }else if(d.attribute == '02'){
                    return "辅助";
                }
                return d.attribute;
            }
        }
        ,{field: 'expenseItem',title: '费用项目',align:'center', width:120}
        ,{field: 'shCoefficient',title: 'SH系数',align:'center', width:120}
        ,{field: 'unitPrice',title: '单价',align:'center', width:120}
        ,{field: 'projectCost',title: '项目费用',align:'center', width:120}
        ,{field: 'creatorName',title: '创建人姓名',align:'center', width:120}
        ,{field: 'createdTime',title: '创建时间',align:'center', width:180}
        ,{field: 'updaterName',title: '更新人姓名',align:'center', width:120}
        ,{field: 'updatedTime',title: '更新时间',align:'center', width:180}
        ,{title: '操作', width:120, align:'center', toolbar: '#barDemo'}
    ];

    // 开启分页
    var pageType = true;

    var factoryType = $("#factoryType").val();

    if(factoryType == "HZ"){
        // 汇总查询表头
        tableHead = [
            {
                field: 'serialNo',
                title: 'No.',
                width: 60,
                fixed: 'left',
                align: 'center',
                templet: function(d){
                    // d.LAY_INDEX 是layui自动传递的当前行下标（从1开始）
                    return d.LAY_INDEX;
                }
            },
            {field: 'machineType', title: '部门C', align: 'center', width: 120},
            {field: 'departmentCode', title: '部门名称', align: 'center', width: 150},
            {field: 'directDeptCostTotal', title: '直接部门费用合计', align: 'center', width: 180},
            {field: 'auxDeptAllocTotal', title: '辅助部门分配额合计', align: 'center', width: 180},
            {field: 'deptCostTotal', title: '部门费用合计', align: 'center', width: 150}
        ];

        // 关闭分页
        pageType = false;
    }

    //执行一个 table 实例
    var url = baselocation+'/yearParamSet/departmentalUnitPrice/listPage';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '部门单价表'
        ,page: pageType //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏
        ,defaultToolbar: ['filter','exports']
        ,totalRow: false //开启合计行
        ,cols: [tableHead] //表头
    });
}

// 自动生成日期查询条件
function autoYearDiv() {
    var factoryType = $("#factoryType").val();

    if(factoryType != "HZ"){
        $("#yearDiv").html('<div class="layui-inline layui-col-md2">\
            <label class="layui-form-label">年度:</label>\
            <div class="layui-input-block">\
                <input type="text" class="layui-input" id="year" name="year" value="" />\
            </div>\
        </div>');
    }else{
        $("#yearDiv").html('<div class="layui-inline layui-col-md2">\
            <label class="layui-form-label">开始日期:</label>\
            <div class="layui-input-block">\
                <input type="text" class="layui-input" id="startDate" name="startDate" value="" />\
            </div>\
        </div>\
        <div class="layui-inline layui-col-md2">\
            <label class="layui-form-label">结束日期:</label>\
            <div class="layui-input-block">\
                <input type="text" class="layui-input" id="endDate" name="endDate" value="" />\
            </div>\
        </div>');
    }

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    // 日期选择器
    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#startDate',
        btns: ['clear','confirm']
    });

    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#endDate',
        btns: ['clear','confirm']
    });
}

function dataImport() {
    var currentFactoryType = $("#factoryType").val();
    layer.open({
        type: 2,
        title: '部门单价数据读取 - ' + currentFactoryType,
        shadeClose: true,
        shade: 0.8,
        area: ['600px', '350px'],
        content: baselocation + '/yearParamSet/departmentalUnitPrice/dataImport/view?factoryType=' + currentFactoryType,
        end: function(){
            search();
        }
    });
}

function add() {
    var currentFactoryType = $("#factoryType").val();
    layer.open({
        type: 2,
        title: '新增部门单价 - ' + currentFactoryType,
        shadeClose: true,
        shade: 0.8,
        area: ['800px', '600px'],
        content: baselocation + '/yearParamSet/departmentalUnitPrice/add/view?factoryType=' + currentFactoryType,
        end: function(){
            search();
        }
    });
}

function edit(serialNumber) {
    layer.open({
        type: 2,
        title: '编辑部门单价',
        shadeClose: true,
        shade: 0.8,
        area: ['800px', '600px'],
        content: baselocation + '/yearParamSet/departmentalUnitPrice/edit/view?serialNumber=' + serialNumber,
        end: function(){
            search();
        }
    });
}
