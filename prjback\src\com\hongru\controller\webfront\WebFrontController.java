package com.hongru.controller.webfront;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import com.hongru.base.BaseController;
import com.hongru.common.enums.StatusEnum;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.pojo.vo.RoleMenuVO;
import com.hongru.pojo.vo.UserVO;
import com.hongru.service.admin.IRoleMenuService;
import com.hongru.service.admin.IUserService;

/**
 * 
* 类名称：WebFrontController   
* 类描述：后台管理系统首页表示层控制器   
* 创建人：hongru   
* 创建时间：2017年4月1日 下午11:14:45   
*
 */
@Controller
public class WebFrontController extends BaseController {
	
	@Autowired
	private IUserService userService;
	@Autowired
	private IRoleMenuService roleMenuService;
	
	/**
	 * GET 首页
	 * @return
	 */
	@GetMapping("/")
	public String index() {
		return redirectTo("/index");
	}
	
	/**
	 * GET 首页/操作中心
	 * @return
	 */
	@GetMapping(value = "/index")
	public String index(Model model) {
		// 管理员信息
		UserVO user = userService.getById(SingletonLoginUtils.getUserId());
		model.addAttribute("user", user);
		// 系统目录
		List<RoleMenuVO> menus = roleMenuService.listByUserId(SingletonLoginUtils.getUserId(), StatusEnum.NORMAL.getStatus());
		model.addAttribute("menus", menus);
		return "/modules/webfront/index";
	}
	
	/**
	 * GET 首页/操作中心
	 * @return
	 */
	@GetMapping(value = "/home")
	public String home(Model model) {
		return "/modules/webfront/home";
	}

	/**
	 * GET 首页/操作中心
	 * @return
	 */
	@GetMapping(value = "/lockScreen")
	public String lockScreen(Model model) {
		return "/modules/webfront/lockscreen";
	}

	/**
	 * GET 主题
	 * @return
	 */
	@GetMapping(value = "/theme")
	public String theme(Model model) {
		return "/modules/webfront/theme";
	}
}
