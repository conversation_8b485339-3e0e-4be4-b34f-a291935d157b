package com.hongru.mapper.admin;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.admin.Role;
import com.hongru.entity.admin.UserRole;
import com.hongru.pojo.vo.UserVO;
import com.hongru.support.page.PageInfo;

/**
 * 
* 类名称：UserRoleMapper   
* 类描述：UserRole / 管理员角色关联表  数据访问层接口   
* 创建人：hongru   
* 创建时间：2017年4月1日 下午5:51:58   
*
 */
public interface UserRoleMapper extends BaseMapper<UserRole> {
	
	/**
	 * 插入用户角色记录 
	 * @param userRoles 用户角色记录信息
	 * @return
	 */
	Integer insertUserRoles(@Param("userRoles") List<UserRole> userRoles);
	
	/**
	 * 根据管理员ID查找角色列表
	 * @param userId 管理员ID
	 * @param status 角色状态
	 * @return List<Role>
	 */
	List<Role> listByUserId(@Param("userId") Long userId, @Param("status") Integer status);
	
	/**
	 * 根据角色ID查找管理员列表
	 * @param roleId 角色ID
	 * @param pageInfo 分页信息
	 * @param search 搜索内容
	 * @return
	 */
	List<UserVO> listByRoleId(@Param("roleId") Long roleId, @Param("pageInfo") PageInfo pageInfo,
			@Param("search") String search);
	Integer listByRoleIdCount(@Param("roleId") Long roleId,@Param("search") String search);
}