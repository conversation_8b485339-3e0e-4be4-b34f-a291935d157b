<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
        .layui-input, .layui-textarea {
            display: inline-block !important;
            width: 40% !important;
            padding-left: 10px !important;
        }
        .layui-form-item .layui-inline {
            clear: both;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/boundQuantity/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EM入/出库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="emInboundQuantity" name="emInboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required" placeholder="入库"/>
                            <input type="text" class="layui-input" id="emOutboundQuantity" name="emOutboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required" placeholder="出库"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF入/出库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="efInboundQuantity" name="efInboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                            <input type="text" class="layui-input" id="efOutboundQuantity" name="efOutboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF09入/出库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ef09InboundQuantity" name="ef09InboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                            <input type="text" class="layui-input" id="ef09OutboundQuantity" name="ef09OutboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>ER入/出库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="erInboundQuantity" name="erInboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                            <input type="text" class="layui-input" id="erOutboundQuantity" name="erOutboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH(日立外)入/出库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ehInboundQuantity1" name="ehInboundQuantity1" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                            <input type="text" class="layui-input" id="ehOutboundQuantity1" name="ehOutboundQuantity1" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH(日立)入/出库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ehInboundQuantity2" name="ehInboundQuantity2" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                            <input type="text" class="layui-input" id="ehOutboundQuantity2" name="ehOutboundQuantity2" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>太線入/出库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="uftxInboundQuantity" name="uftxInboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                            <input type="text" class="layui-input" id="uftxOutboundQuantity" name="uftxOutboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>細線入/出库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ufxxInboundQuantity" name="ufxxInboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                            <input type="text" class="layui-input" id="ufxxOutboundQuantity" name="ufxxOutboundQuantity" value="0" onKeyUp="amount2(this)" onBlur="overFormat2(this)" required="" lay-verify="required"/>
                        </div>
                    </div>


                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="" readonly required="" lay-verify="required"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="statId" name="statId" value="0"/>
                            <input type="hidden" id="state" name="state" value="0"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/costPrice_addOrModify.js?time=1"></script>
</myfooter>
</body>
</html>
