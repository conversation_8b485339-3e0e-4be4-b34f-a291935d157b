package com.hongru.entity.cost;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("小部门明细表")//CostPrice
public class SmallDepartment {

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 年度 */
	protected String year;
	/* 编号 */
	protected String departmentCode;
	/* 部门 */
	protected String departmentName;
	/* 类别 */
	protected String category;
	/* 补修UF分摊比例 */
	protected BigDecimal ufShareRateOfRepaire;
	/* 辅材UF分摊比例 */
	protected BigDecimal ufShareRateOfAuxiliary;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	@TableField(exist = false)
	protected Integer supplierId;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}
	
	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}
	
	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public BigDecimal getUfShareRateOfRepaire() {
		return ufShareRateOfRepaire;
	}

	public void setUfShareRateOfRepaire(BigDecimal ufShareRateOfRepaire) {
		this.ufShareRateOfRepaire = ufShareRateOfRepaire;
	}

	public BigDecimal getUfShareRateOfAuxiliary() {
		return ufShareRateOfAuxiliary;
	}

	public void setUfShareRateOfAuxiliary(BigDecimal ufShareRateOfAuxiliary) {
		this.ufShareRateOfAuxiliary = ufShareRateOfAuxiliary;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public Integer getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Integer supplierId) {
		this.supplierId = supplierId;
	}



}