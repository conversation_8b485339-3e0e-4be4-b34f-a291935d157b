<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.ConductorUnitPriceDetailMapper">
    <sql id="conductorUnitPriceDetail_sql">
        cupd.[年度] AS year,cupd.[品目] AS itemCode,cupd.[品目名] AS itemName,cupd.[原料区分] AS materialType,
        cupd.[关税率] AS tariffRate,cupd.[采购单价] AS purchaseUnitPrice,cupd.[溢价] AS premium,
        cupd.[附随费用] AS incidentalExpenses,cupd.[屑铜原料单价] AS scrapCopperUnitPrice,cupd.[比重] AS specificGravity,
        cupd.[创建人姓名] AS creatorName,cupd.[创建时间] AS createdTime,
        cupd.[更新人姓名] AS updaterName,cupd.[更新时间] AS updatedTime
    </sql>

    <select id="selectByYearAndItemCode" resultType="com.hongru.entity.cost.ConductorUnitPriceDetail">
        SELECT
        <include refid="conductorUnitPriceDetail_sql"/>
        FROM [CostPrice].[dbo].[导体单价明细表] cupd
        WHERE cupd.[年度] = #{year} AND cupd.[品目] = #{itemCode}
    </select>

    <insert id="insertConductorUnitPriceDetail" parameterType="com.hongru.entity.cost.ConductorUnitPriceDetail">
        INSERT INTO [CostPrice].[dbo].[导体单价明细表]
        (
            [年度],
            [品目],
            [品目名],
            [原料区分],
            [关税率],
            [采购单价],
            [溢价],
            [附随费用],
            [屑铜原料单价],
            [比重],
            [创建人姓名],
            [创建时间]
        )
        VALUES
        (
            #{conductorUnitPriceDetail.year},
            #{conductorUnitPriceDetail.itemCode},
            #{conductorUnitPriceDetail.itemName},
            #{conductorUnitPriceDetail.materialType},
            #{conductorUnitPriceDetail.tariffRate},
            #{conductorUnitPriceDetail.purchaseUnitPrice},
            #{conductorUnitPriceDetail.premium},
            #{conductorUnitPriceDetail.incidentalExpenses},
            #{conductorUnitPriceDetail.scrapCopperUnitPrice},
            #{conductorUnitPriceDetail.specificGravity},
            #{conductorUnitPriceDetail.creatorName},
            #{conductorUnitPriceDetail.createdTime}
        )
    </insert>

    <update id="updateConductorUnitPriceDetail" parameterType="com.hongru.entity.cost.ConductorUnitPriceDetail">
        UPDATE [CostPrice].[dbo].[导体单价明细表]
        SET
            [品目名] = #{conductorUnitPriceDetail.itemName},
            [原料区分] = #{conductorUnitPriceDetail.materialType},
            [关税率] = #{conductorUnitPriceDetail.tariffRate},
            [采购单价] = #{conductorUnitPriceDetail.purchaseUnitPrice},
            [溢价] = #{conductorUnitPriceDetail.premium},
            [附随费用] = #{conductorUnitPriceDetail.incidentalExpenses},
            [屑铜原料单价] = #{conductorUnitPriceDetail.scrapCopperUnitPrice},
            [比重] = #{conductorUnitPriceDetail.specificGravity},
            [更新人姓名] = #{conductorUnitPriceDetail.updaterName},
            [更新时间] = #{conductorUnitPriceDetail.updatedTime}
        WHERE [年度] = #{conductorUnitPriceDetail.year} AND [品目] = #{conductorUnitPriceDetail.itemCode}
    </update>

    <select id="selectConductorUnitPriceDetailByYear" resultType="com.hongru.entity.cost.ConductorUnitPriceDetail">
        SELECT
        <include refid="conductorUnitPriceDetail_sql"/>
        FROM [CostPrice].[dbo].[导体单价明细表] cupd
        WHERE cupd.[年度] = #{year}
    </select>

    <delete id="deleteConductorUnitPriceDetailByYear">
        DELETE FROM [CostPrice].[dbo].[导体单价明细表]
        WHERE [年度] = #{year}
    </delete>

    <insert id="batchInsertConductorUnitPriceDetail" parameterType="java.util.List">
        INSERT INTO [CostPrice].[dbo].[导体单价明细表]
        (
            [年度],
            [品目],
            [品目名],
            [原料区分],
            [关税率],
            [采购单价],
            [溢价],
            [附随费用],
            [屑铜原料单价],
            [比重],
            [创建人姓名],
            [创建时间]
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.year},
            #{item.itemCode},
            #{item.itemName},
            #{item.materialType},
            #{item.tariffRate},
            #{item.purchaseUnitPrice},
            #{item.premium},
            #{item.incidentalExpenses},
            #{item.scrapCopperUnitPrice},
            #{item.specificGravity},
            #{item.creatorName},
            #{item.createdTime}
        )
        </foreach>
    </insert>
</mapper>
