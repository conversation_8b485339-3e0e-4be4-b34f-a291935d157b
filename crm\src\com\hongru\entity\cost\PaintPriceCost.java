package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("油漆使用明细表")//CostPrice
public class PaintPriceCost {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 状态 */
	protected short state;
	/* 导入标识 */
	protected int importId;
	/* 年月 */
	protected String yearMonth;
	/* 供应商油漆名 */
	protected String outPaintName;
	/* 社内油漆名 */
	protected String insidePaintName;
	/* 油漆使用量 */
	protected BigDecimal paintUsage;
	/* 油漆使用金额 */
	protected BigDecimal paintUsageAmount;
	/* 使用区分（小分类部门） */
	protected String paintUseCode;
	/* 小分类编号 */
	protected String smallCode;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 最后修改人标识 */
	protected int lastModifierId;
	/* 最后修改人姓名 */
	protected String lastModifierName;
	/* 最后修改时间 */
	protected String lastModifiedTime;

	@TableField(exist = false)
	protected Integer supplierId;

	/* 油漆成本编码 */
	@TableField(exist = false)
	protected String paintCode;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}

	public short getState() {
		return state;
	}

	public void setState(short state) {
		this.state = state;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public Integer getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Integer supplierId) {
		this.supplierId = supplierId;
	}

	public String getOutPaintName() {
		return outPaintName;
	}

	public void setOutPaintName(String outPaintName) {
		this.outPaintName = outPaintName;
	}

	public String getInsidePaintName() {
		return insidePaintName;
	}

	public void setInsidePaintName(String insidePaintName) {
		this.insidePaintName = insidePaintName;
	}

	public String getSmallCode() {
		return smallCode;
	}

	public void setSmallCode(String smallCode) {
		this.smallCode = smallCode;
	}

	public String getPaintUseCode() {
		return paintUseCode;
	}

	public void setPaintUseCode(String paintUseCode) {
		this.paintUseCode = paintUseCode;
	}

	public int getImportId() {
		return importId;
	}

	public void setImportId(int importId) {
		this.importId = importId;
	}

	public BigDecimal getPaintUsage() {
		return paintUsage;
	}

	public void setPaintUsage(BigDecimal paintUsage) {
		this.paintUsage = paintUsage;
	}

	public BigDecimal getPaintUsageAmount() {
		return paintUsageAmount;
	}

	public void setPaintUsageAmount(BigDecimal paintUsageAmount) {
		this.paintUsageAmount = paintUsageAmount;
	}

	public int getLastModifierId() {
		return lastModifierId;
	}

	public void setLastModifierId(int lastModifierId) {
		this.lastModifierId = lastModifierId;
	}

	public String getLastModifierName() {
		return lastModifierName;
	}

	public void setLastModifierName(String lastModifierName) {
		this.lastModifierName = lastModifierName;
	}

	public String getLastModifiedTime() {
		return lastModifiedTime;
	}

	public void setLastModifiedTime(String lastModifiedTime) {
		this.lastModifiedTime = lastModifiedTime;
	}

	public String getPaintCode() {
		return paintCode;
	}

	public void setPaintCode(String paintCode) {
		this.paintCode = paintCode;
	}
}