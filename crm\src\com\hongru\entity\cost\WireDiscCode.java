package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("线盘成本编码表") // CostPrice
public class WireDiscCode {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 流水号 */
	@TableId(value = "流水号", type = IdType.AUTO)
	protected int codeId;
	/* 状态 */
	protected short state;
	/* 线盘名称 */
	protected String wireDiscName;
	/* 成本编码 */
	protected String costCode;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 最后修改人标识 */
	protected int lastModifierId;
	/* 最后修改人姓名 */
	protected String lastModifierName;
	/* 最后修改时间 */
	protected String lastModifiedTime;

	public int getCodeId() {
		return codeId;
	}

	public void setCodeId(int codeId) {
		this.codeId = codeId;
	}

	public short getState() {
		return state;
	}

	public void setState(short state) {
		this.state = state;
	}

	public String getWireDiscName() {
		return wireDiscName;
	}

	public void setWireDiscName(String wireDiscName) {
		this.wireDiscName = wireDiscName;
	}

	public String getCostCode() {
		return costCode;
	}

	public void setCostCode(String costCode) {
		this.costCode = costCode;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public String getLastModifierName() {
		return lastModifierName;
	}

	public void setLastModifierName(String lastModifierName) {
		this.lastModifierName = lastModifierName;
	}

	public int getLastModifierId() {
		return lastModifierId;
	}

	public void setLastModifierId(int lastModifierId) {
		this.lastModifierId = lastModifierId;
	}

	public String getLastModifiedTime() {
		return lastModifiedTime;
	}

	public void setLastModifiedTime(String lastModifiedTime) {
		this.lastModifiedTime = lastModifiedTime;
	}
}