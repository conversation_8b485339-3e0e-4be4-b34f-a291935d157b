package com.hongru.common.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.text.DecimalFormat;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * String Utility Class This is used to encode passwords programmatically
 *
 * <p>
 * <a h
 * ref="StringUtil.java.html"><i>View Source</i></a>
 * </p>
 * 
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 */
public class StringUtil {
    //~ Static fields/initializers =============================================

    private final static Log log = LogFactory.getLog(StringUtil.class);

    //~ Methods ================================================================

    /**
     * Encode a string using algorithm specified in web.xml and return the
     * resulting encrypted password. If exception, the plain credentials
     * string is returned
     *
     * @param password Password or other credentials to use in authenticating
     *        this username
     * @param algorithm Algorithm used to do the digest
     *
     * @return encypted password based on the algorithm.
     */
    public static String encodePassword(String password, String algorithm) {
        byte[] unencodedPassword = password.getBytes();

        MessageDigest md = null;

        try {
            // first create an instance, given the provider
            md = MessageDigest.getInstance(algorithm);
        } catch (Exception e) {
            log.error("Exception: " + e);

            return password;
        }

        md.reset();

        // call the update method one or more times
        // (useful when you don't know the size of your data, eg. stream)
        md.update(unencodedPassword);

        // now calculate the hash
        byte[] encodedPassword = md.digest();

        StringBuffer buf = new StringBuffer();

        for (int i = 0; i < encodedPassword.length; i++) {
            if ((encodedPassword[i] & 0xff) < 0x10) {
                buf.append("0");
            }

            buf.append(Long.toString(encodedPassword[i] & 0xff, 16));
        }

        return buf.toString();
    }
    
    /**
	 * 汉字转换为拼音
	 * 
	 * @param
	 * @return String quguanglei 2014年12月16日 下午3:30:32
	 */
	public static String getPinYin(String paramStr) throws Exception {
		if (paramStr == null) {
			return null;
		} else {
			HanyuPinyinOutputFormat pyStyle = new HanyuPinyinOutputFormat();
			pyStyle.setToneType(HanyuPinyinToneType.WITHOUT_TONE);// 定义音调的显示方式,不显示音调
			@SuppressWarnings("deprecation")
			String pinyin = PinyinHelper.toHanyuPinyinString(paramStr, pyStyle, "");
			return pinyin;
		}
	}
    /**
     * 流水号补位返回 ，流水号长度为format的长度  
     * @param liuShuiHao
     * @param format：返回格式：4位格式为"0000"，5位格式为"00000",以此类推。表示左边补0
     * @return
     * <AUTHOR>
     * @create 2015年7月8日 上午10:15:49
     */
    public static String serialNoAddZero(String liuShuiHao, String format){  
        Integer intHao = Integer.parseInt(liuShuiHao);  
        DecimalFormat df = new DecimalFormat(format);  
        return df.format(intHao);  
    }  
    
    /**
	 * 生成随机字符串<br />
	 * 当前时戳 hex转换。
	 * <AUTHOR>
	 */
	public static String createRandomString()throws Exception{
		//1、获取当前时间
		long currentTime = System.nanoTime();
		String randomString = Long.toHexString(currentTime);
		return randomString;
	}
	
	/**
	 * 随机生成0(包含)~value(不包含)的整数 
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2016年8月12日 下午2:41:19
	 * 返回一个伪随机数，它是取自此随机数生成器序列的、在 0（包括）和指定值（不包括）之间均匀分布的 <code>int</code>
	 */
	public static int generateRandomInteger(int value) throws Exception{
		Random rd = new Random();
		int random = rd.nextInt(value);
		return random;
	}
	
    /**
     * Encode a string using Base64 encoding. Used when storing passwords
     * as cookies.
     *
     * This is weak encoding in that anyone can use the decodeString
     * routine to reverse the encoding.
     *
     * @param str
     * @return String
     */
    public static String encodeString(String str)  {
//        sun.misc.BASE64Encoder encoder = new sun.misc.BASE64Encoder();
//        return encoder.encodeBuffer(str.getBytes()).trim();
    	return str;
    }

    /**
     * Decode a string using Base64 encoding.
     *
     * @param str
     * @return String
     */
    public static String decodeString(String str) {
//        sun.misc.BASE64Decoder dec = new sun.misc.BASE64Decoder();
//        try {
//            return new String(dec.decodeBuffer(str));
//        } catch (IOException io) {
//        	throw new RuntimeException(io.getMessage(), io.getCause());
//        }
    	return str;
    }
    
    public static String replaceString(String str){
    	str = str.replaceAll("&","&amp;").replaceAll("<","&lt;").replaceAll(">","&gt;").replaceAll("\'","&apos;").replaceAll("\"","&quot;");
    	return str;
    }
    
    public static String delHTML(String htmlStr){
    	String str = Pattern.compile("(<\\s*[a-z]+.*?/?>)|(</?\\s*[a-z]+.*?>)|(<\\!--.*?-->)|(<\\!--\\[.*?\\]>)|(<\\!\\[.*?\\]-->)|(<\\?.*?xml.*?>)", Pattern.CASE_INSENSITIVE).matcher(htmlStr).replaceAll("");
    	return str;
    }
    
    public static Integer isPageNoNull(Integer pageNo){
    	if(pageNo != null && pageNo == -99){
    		return null;
    	}else{
    		return pageNo;
    	}
    }
    
    public static Integer isPageSizeNull(Integer pageSize){
    	if(pageSize != null && pageSize == -99){
    		return null;
    	}else{
    		return pageSize;
    	}
    }
    
    /**
	 * 生成随机密码<br />
	 * 获取当前时间，获取随机数，联合起来进行MD5加密，取加密后的最后六位作为密码。
	 * <AUTHOR>
	 */
	public static String createRandomPassword()throws Exception{
		//1、获取当前时间
		long currentTime = System.nanoTime();
		
		//2、获取随机数
		int sixRandom = sixRandom();
		
		//3、MD5加密
		String encryptedStr = Md5Encrypt.md5(String.valueOf(currentTime + sixRandom));
		
		//4、取encryptedStr的最后六位
		int strLength = encryptedStr.length();
		String randomPassword = encryptedStr.substring(strLength-6, strLength);
		
		return randomPassword;
	}
	
	/**
	 * 随机生成6位数字
	 * @return
	 * <AUTHOR>
	 * @create 2014-5-27 下午02:02:43
	 */
	public static int sixRandom() {
		int[] array = {0,1,2,3,4,5,6,7,8,9};
		Random rand = new Random();
		for (int i = 10; i > 1; i--) {
		    int index = rand.nextInt(i);
		    int tmp = array[index];
		    array[index] = array[i - 1];
		    array[i - 1] = tmp;
		}
		int result = 0;
		for(int i = 0; i < 6; i++)
		{
			result = result * 10 + array[i];
		}
		    
		return result;
	}
	
	/**
	 * 随机生成4位数字
	 * @return
	 * <AUTHOR>
	 * @create 2014-5-27 下午02:02:43
	 */
	public static int fourRandom() {
		int[] array = {0,1,2,3,4,5,6,7,8,9};
		Random rand = new Random();
		for (int i = 10; i > 1; i--) {
		    int index = rand.nextInt(i);
		    int tmp = array[index];
		    array[index] = array[i - 1];
		    array[i - 1] = tmp;
		}
		int result = 0;
		for(int i = 0; i < 4; i++)
		{
			result = result * 10 + array[i];
		}
		    
		return result;
	}
	
	/**
	 * 
	 * @function 生成num位的随机字符串(数字、小写字母随机混排)
	 * @param num
	 * @return
	 */
	public static String createSmallStrOrNumberRadom(int num) {  
		
		String str = "";
		for(int i=0;i < num;i++){  
			int intVal=(int)(Math.random()*26+97);
			if(intVal%2==0){
				str += (char)intVal;  
			}else{
				str += (int)(Math.random()*10);
			}
		}  
		return str;
	}  
	
	/**
	 * 
	 * @function 生成num位的随机字符串(大写字母与数字混排)
	 * @param num
	 * @return
	 */
	public static String createBigStrOrNumberRadom(int num) {  
		
		String str = "";
		for(int i=0;i < num;i++){  
			int intVal=(int)(Math.random()*26+65);
			if(intVal%2==0){
				str += (char)intVal;  
			}else{
				str += (int)(Math.random()*10);
			}
		}  
		return str;
	}  
	
//	public static String pingyin(String name) throws BadHanyuPinyinOutputFormatCombination{
//		HanyuPinyinOutputFormat pyStyle = new HanyuPinyinOutputFormat();
//		pyStyle.setToneType(HanyuPinyinToneType.WITHOUT_TONE);//定义音调的显示方式,不显示音调
//		if(name != null){
//			name = name.trim();
//		}
//		return PinyinHelper.toHanyuPinyinString(name, pyStyle, "");
//	}
	
	/**
	 * //将手机号码第4位到第7位替换成*
	 * @param tel
	 * @return
	 * <AUTHOR>
	 * @create 2015年11月19日 上午10:35:44
	 */
	public static String replaceMobileString(String tel){
		//括号表示   组    被替换的部分   $n 表示  第n组的内容
		tel = tel.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    	return tel;
    }
	
	public static boolean isStringEmpty(String param) {
		boolean flag = false;
		if((null == param) || ("".equals(param.trim()))){
			flag = true; 
		}
		return flag;
	}
	
	public static boolean isIntegerEmpty(Integer param) {
		boolean flag = false;
		if(null == param){
			flag = true; 
		}
		return flag;
	}
	
	public static boolean isShortEmpty(Short param) {
		boolean flag = false;
		if(null == param){
			flag = true; 
		}
		return flag;
	}
	
	
	public static  String filterOffUtf8Mb4(String text) throws UnsupportedEncodingException {
		if(null == text || "".equals(text)){
			return text;
		}
		byte[] bytes = text.getBytes("UTF-8");
		ByteBuffer buffer = ByteBuffer.allocate(bytes.length);
		int i = 0;
		while (i < bytes.length) {
			short b = bytes[i];
			if (b > 0) {
				buffer.put(bytes[i++]);
				continue;
			}
			b += 256;
			if ((b ^ 0xC0) >> 4 == 0) {
				buffer.put(bytes, i, 2);
				i += 2;
			}
			else if ((b ^ 0xE0) >> 4 == 0) {
				buffer.put(bytes, i, 3);
				i += 3;
			}
			else if ((b ^ 0xF0) >> 4 == 0) {
				i += 4;
			}
		}
		buffer.flip();
		return new String(buffer.array(), "utf-8");
	}
	
	/**
	 * 获得后缀【不包括"."】
	 * @param s
	 * @param split
	 * @return
	 * <AUTHOR>
	 * @create 2016年2月23日 上午10:06:13
	 */
	public static String getExtName(String s, String split) {
		String [] ExtName = s.split(split);
        int i = ExtName.length;
        return ExtName[i-1];
    }

    /**
    * 判断字符是否为英文
    * @throws
    * <AUTHOR>
    * @create 2022/11/3 17:48
    * @return
    */
	public static boolean check(String fstrData) {
		char c = fstrData.charAt(0);
		if (((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z'))) {
			return true;
		} else {
			return false;
		}
	}

	/**
	* 判断字符串是否是数字
	* @throws
	* <AUTHOR>
	* @create 2022/11/16 18:50
	* @return
	*/
	public static boolean isNumber(String str) {
		if(str != null && str.indexOf("-")>0){//过滤掉类似2023-09
			return  false;
		}
		return str.matches("-?[0-9]+.?[0-9]*");
	}
	
	
	/**
	 * 产品代码中截取产品尺寸
	 * @param productCode
	 * @return
	 * <AUTHOR>
	 * @create 2022年12月1日
	 */
	public static String subModelSize(String productCode){
		String result="";
		if(!StringUtil.isStringEmpty(productCode)) {
			productCode =productCode.trim();
			productCode = productCode.substring(0,productCode.length()-4);
			String str = productCode.substring(productCode.length()-1, productCode.length());
			if(" ".equals(str) || str.matches("[A-Z]")) {
				result = productCode.substring(0,productCode.length()-1);
			}else {
				result = productCode;
			}
		}
		return result.trim();
    }

    /**
     * 截取线盘代码客户代码
     * @param productCode
     * @throws
     * <AUTHOR>
     * @create 2022/12/14 14:54
     * @return java.lang.String
    */
	public static String subCoilCodeCustomerCode(String productCode){
		String result="";
		if(!StringUtil.isStringEmpty(productCode)) {
			productCode =productCode.trim();
			productCode = productCode.substring(productCode.length()-5,productCode.length());
			String str = productCode.substring(0, 1);
			if(!" ".equals(str) && !str.matches("[A-Z]")) {
				result = productCode.substring(1,productCode.length());
			}else{
				result = productCode;
			}
		}
		return result.trim();
	}

	/**
	 * 对字符串内容进行Base64编码
	 * @param str
	 * @throws
	 * <AUTHOR>
	 * @create 2023/5/31 20:08
	 * @return java.lang.String
	 */
	public static String encryptionText(String str) {
		Base64 base64 = new Base64();
		String encodedText = null;
		try {
			byte[] textByte;
			textByte = str.getBytes("UTF-8");
			encodedText = base64.encodeToString(textByte);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return encodedText;
	}

	/**
	 * 对字符串内容进行解码
	 * @param str
	 * @throws
	 * <AUTHOR>
	 * @create 2023/5/31 20:10
	 * @return java.lang.String
	 */
	public static String decryptionText(String str){
		String result =null;
		try{
			Base64 base=new Base64();
			result = new String(base.decode(str), "UTF-8");
		}catch (Exception e){
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 判断String数组中是否存在某个值的方法
	 * @return :存在 true :不存在 false
	 */
	public static boolean arrIsHaveStr(String[] strArr, String str) {
		for (String i : strArr) {
			if (str.equals(i + "")) {
				return true;
			}
		}
		return false;
	}

	public static void main(String[] args) {
//		String num = createBigStrOrNumberRadom(5);
//		System.out.println(num);
//		String modelAndSize = subModelSize("H-UTZ(A3RX) 0.82GA004");
//		String modelAndSize2 = subModelSize("LTZ-0BA-172 1.815*3.310 Z096");
//		String wireDiscCodeAndCustomerCode = subCoilCodeCustomerCode("H-UTZ(A3RX) 0.82GA004");
//		String wireDiscCodeAndCustomerCode2 = subCoilCodeCustomerCode("LTZ-0BA-172 1.815*3.310 Z096");
//
//		String[] modelAndSizeArr = modelAndSize.split(" ");
//		String model = "";
//		String sizeFront = "0";
//		String sizeBehind = "0";
//		if(modelAndSizeArr.length > 0){
//			model = modelAndSizeArr[0];
//		}
//		if(modelAndSizeArr.length > 1){
//			String size = modelAndSizeArr[1];
//			String[] sizeArr = size.split("\\*");
//			if(sizeArr.length > 0){
//				sizeFront = sizeArr[0];
//			}
//			if(sizeArr.length > 1){
//				sizeBehind = sizeArr[1];
//			}
//		}
//		System.out.println(model);
//		System.out.println(sizeFront);
//		System.out.println(sizeBehind);
//
//		String wireDiscCode = "";
//		if(wireDiscCodeAndCustomerCode.length() > 3){
//			wireDiscCode = wireDiscCodeAndCustomerCode.substring(0,wireDiscCodeAndCustomerCode.length()-3);
//		}
//		System.out.println(wireDiscCode);
//		String customerCode = "";
//		if(wireDiscCodeAndCustomerCode.length() > 3){
//			customerCode = wireDiscCodeAndCustomerCode.substring(wireDiscCodeAndCustomerCode.length()-3);
//		}
//		System.out.println(customerCode);
//
//		String[] modelAndSizeArr2 = modelAndSize2.split(" ");
//		String model2 = "";
//		String size2Front = "0";
//		String size2Behind = "0";
//		if(modelAndSizeArr2.length > 0){
//			model2 = modelAndSizeArr2[0];
//		}
//		if(modelAndSizeArr2.length > 1){
//			String size2 = modelAndSizeArr2[1];
//			String[] size2Arr = size2.split("\\*");
//			if(size2Arr.length > 0){
//				size2Front = size2Arr[0];
//			}
//			if(size2Arr.length > 1){
//				size2Behind = size2Arr[1];
//			}
//		}
//		System.out.println(model2);
//		System.out.println(size2Front);
//		System.out.println(size2Behind);
//
//		String wireDiscCode2 = "";
//		if(wireDiscCodeAndCustomerCode2.length() > 3){
//			wireDiscCode2 = wireDiscCodeAndCustomerCode2.substring(0,wireDiscCodeAndCustomerCode2.length()-3);
//		}
//		System.out.println(wireDiscCode2);
//		String customerCode2 = "";
//		if(wireDiscCodeAndCustomerCode2.length() > 3){
//			customerCode2 = wireDiscCodeAndCustomerCode2.substring(wireDiscCodeAndCustomerCode2.length()-3);
//		}
//		System.out.println(customerCode2);
//		String token = encryptionText("45");
//		System.out.println(token);
//		System.out.println(decryptionText(token));

		System.out.println(isNumber("45"));
		System.out.println(isNumber("4.5"));
		System.out.println(isNumber("-45"));
		System.out.println(isNumber("-4.5"));
		System.out.println(isNumber("张三123"));
	}


}
