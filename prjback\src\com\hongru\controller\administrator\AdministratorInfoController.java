package com.hongru.controller.administrator;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.hongru.base.BaseController;
import com.hongru.base.BasePageDTO;
import com.hongru.common.constant.UserReturnCode;
import com.hongru.common.enums.StatusEnum;
import com.hongru.common.exception.ValidateException;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.PasswordUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.Role;
import com.hongru.entity.admin.User;
import com.hongru.entity.admin.UserLoginLog;
import com.hongru.pojo.vo.UserVO;
import com.hongru.service.admin.IUserLoginLogService;
import com.hongru.service.admin.IUserRoleService;
import com.hongru.service.admin.IUserService;
import com.hongru.support.page.PageInfo;

/**
 *
* 类名称：AdministratorInfoController
* 类描述：管理员个人信息表示层控制器
* 创建人：hongru
* 创建时间：2017年4月2日 上午1:49:53
*
 */
@Controller
@RequestMapping(value = "/administrator/info")
public class AdministratorInfoController extends BaseController {

	@Autowired
	private IUserService userService;
	@Autowired
	private IUserLoginLogService userLoginLogService;
	@Autowired
	private IUserRoleService userRoleService;

	/**
	 * GET 管理员个人信息
	 * @return
	 */
	@GetMapping(value = "/view")
	public String getInfoPage(Model model) {
		// 管理员信息
		UserVO userVO = userService.getById(SingletonLoginUtils.getUserId());
		// 管理员权限
		List<Role> roles = userRoleService.listByUserId(SingletonLoginUtils.getUserId(),
				StatusEnum.NORMAL.getStatus());
		model.addAttribute("roles", roles);
		String roleName ="";
		for(Role role:roles){
			if(StringUtil.isStringEmpty(roleName)){
				roleName = role.getRoleName();
			}else{
				roleName +="," + role.getRoleName();
			}
		}
		userVO.setRoleName(roleName);
		model.addAttribute("user", userVO);
		return "/modules/admin/admin_user_view";
	}

	/**
	 * GET 管理员个人登录日志
	 * @return
	 */
	@RequiresPermissions("administrator:info:view")
	@GetMapping(value = "/logs")
	@ResponseBody
	public Object listLogs(PageInfo pageInfo, @RequestParam(required = false, value = "search") String search) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();

		if (authorizingUser != null) {
			// 管理员日志
			BasePageDTO<UserLoginLog> basePageDTO = userLoginLogService.listByUserId(authorizingUser.getUserId(),
					pageInfo, search);
			return new HrPageResult(basePageDTO.getList(), basePageDTO.getPageInfo().getTotal());
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}

	/**
	 * 更新管理员信息
	 * @return
	 */
	/*@RequiresPermissions("administrator:info:edit")*/
	@PostMapping(value = "/edit")
	@ResponseBody
	public Object updateUser(User user) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			user.setUserId(authorizingUser.getUserId());
			user.setUpdateBy(authorizingUser.getUserName());
			int count = userService.updateByUserId(user);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}

	/**
	 * POST 修改管理员密码
	 * @return
	 */
	/*@RequiresPermissions("administrator:info:edit")*/
	@PutMapping(value = "/edit/psw")
	@ResponseBody
	public Object updatePwd(@RequestParam("nowPassword") String nowPassword,
			@RequestParam("newPassword") String newPassword, @RequestParam("confirmPwd") String confirmPwd) {

//		if (!RegexUtils.isPassword(newPassword)) {
//			return new HrResult(UserReturnCode.PASSWORD_AUTHENTICATION_ERROR);
//		}
		if (!newPassword.equals(confirmPwd)) {
			return new HrResult(UserReturnCode.ENTERED_PASSWORDS_DIFFER);
		}

		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			try {
				Integer count = userService.updatePsw(nowPassword, newPassword, authorizingUser.getUserId(),
						authorizingUser.getUserName());
				return new HrResult(CommonReturnCode.SUCCESS, count);
			} catch (ValidateException e) {
				logger.error(e.getMessage(), e);
				return new HrResult(e.getCode(), e.getMessage());
			}
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}

	/**
	 * PUT 修改管理员头像
	 * @return
	 */
	@RequiresPermissions("administrator:info:edit")
	@PutMapping(value = "/avatar")
	@ResponseBody
	public Object avatar(@RequestParam("picImg") String picImg) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		Integer count = userService.updateAvatar(authorizingUser.getUserId(), picImg);
		return new HrResult(CommonReturnCode.SUCCESS, count);
	}

	/**
	* 重置密码
	* @param userId
	* @param password
	* <AUTHOR>
	* @create 2022/02/11 15:07
	*/
	@PostMapping(value = "/reset/psw")
	@ResponseBody
	public Object resetPwd(long userId ,String password) {
		try{
//			if (!RegexUtils.isPassword(password)) {
//				return new HrResult(UserReturnCode.PASSWORD_AUTHENTICATION_ERROR);
//			}

			AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
			if (authorizingUser != null) {
				UserVO user = userService.getById(userId);
				String newPassword  = PasswordUtils.getMd5(password, user.getLoginName(), user.getSalt());
				userService.resetPwd(userId, newPassword);
				return new HrResult(CommonReturnCode.SUCCESS);
			} else {
				return new HrResult(CommonReturnCode.UNAUTHORIZED);
			}
		}catch (Exception e){
			e.printStackTrace();
			return new HrResult(CommonReturnCode.FAILED.getCode(),"操作失败");
		}
	}
}
