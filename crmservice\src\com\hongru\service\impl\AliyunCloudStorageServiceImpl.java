package com.hongru.service.impl;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.hongru.common.constant.OSSReturnCode;
import com.hongru.common.enums.CloudServiceEnum;
import com.hongru.common.exception.OSSException;
import com.hongru.common.util.FileUtils;
import com.hongru.entity.ImageLog;
import com.hongru.mapper.ImageLogMapper;
import com.hongru.service.IAliyunCloudStorageService;

/**
 * 
*    
* 类名称：AliyunCloudStorageServiceImpl   
* 类描述：AliyunCloudStorageServiceImpl 阿里云云存储上传文件接口实现   
* 创建人：hongru   
* 创建时间：2017年7月17日 下午4:50:02   
*
 */
@Service
public class AliyunCloudStorageServiceImpl implements IAliyunCloudStorageService {
	
	@Value("${aliyun.accessKeyId}")
	private String accessKeyId;
	
	@Value("${aliyun.accessKeySecret}")
	private String accessKeySecret;
	
	@Value("${aliyun.bucketName}")
	private String bucketName;	

	@Value("${aliyun.endpoint}")
	private String endpoint;	
	
	private OSSClient ossClient;
	
	@Autowired
	private ImageLogMapper imageLogMapper;
	
	private void init() {
		ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
	}
	
	@Override
	public CloudServiceEnum getCloudServiceType() {
		return CloudServiceEnum.ALIYUN;
	}

	@Override
	public String getUploadToken() throws OSSException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ImageLog upload(byte[] data, String belong, String sourceFileName) throws OSSException {
		ImageLog imageLog;
		try {
			// 初始化上传对象
			init();
			// 上传文件
			//String fileName = FileUtils.getFileName(belong, sourceFileName); //因为得到的文件名太长了，所以不用这个了
			String fileName = FileUtils.getFileNameHex(belong, sourceFileName);
			ossClient.putObject(bucketName, fileName, new ByteArrayInputStream(data));
			// 获取文件的全部元信息
			ObjectMetadata metadata = ossClient.getObjectMetadata(bucketName, fileName);
			// 对上传的文件进行记录
			imageLog = new ImageLog();
			imageLog.setCreateTime(new Date());
			imageLog.setBucket(bucketName);
			imageLog.setEtag(metadata.getETag());
			imageLog.setName(fileName);
			imageLog.setFileSize(metadata.getContentLength());
			imageLog.setMimeType(metadata.getContentType());
			imageLog.setImageUrl(FileUtils.getImageUrl(endpoint, fileName));
			imageLog.setCloudName(CloudServiceEnum.ALIYUN.getName());
			imageLog.setBelong(belong);
			imageLogMapper.insert(imageLog);
		} catch (Exception e) {
			// 抛出一个配置出错的异常
			throw new OSSException(OSSReturnCode.CONFIGURATION_INFORMATION_ERROR.getCode(), e.getMessage());
		}finally {
			// 关闭client
			ossClient.shutdown();
		}
		return imageLog;
	}
	
	/**
	 * 上传后可直接下载
	 * @param data
	 * @param belong
	 * @param fileName
	 * @return
	 * @throws OSSException
	 * <AUTHOR>
	 * @create 2021年12月27日
	 */
	@Override
	public ImageLog uploadForDirectDownload(byte[] data, String belong, String sourceFileName) throws OSSException {
		ImageLog imageLog;
		try {
			// 初始化上传对象
			init();
			// 上传文件
			//String fileName = FileUtils.getFileName(belong, sourceFileName); //因为得到的文件名太长了，所以不用这个了
			String fileName = FileUtils.getFileNameHex(belong, sourceFileName);
			PutObjectRequest request = new PutObjectRequest(bucketName, fileName, new ByteArrayInputStream(data));//oss 直接下载
		    ObjectMetadata metadata = new ObjectMetadata();
		    metadata.setContentType("application/octet-stream");
		    request.setMetadata(metadata);
		    ossClient.putObject(request);
			// 对上传的文件进行记录
			imageLog = new ImageLog();
			imageLog.setCreateTime(new Date());
			imageLog.setBucket(bucketName);
			imageLog.setEtag(metadata.getETag());
			imageLog.setName(fileName);
			imageLog.setFileSize(metadata.getContentLength());
			imageLog.setMimeType(metadata.getContentType());
			imageLog.setImageUrl(FileUtils.getImageUrl(endpoint, fileName));
			imageLog.setCloudName(CloudServiceEnum.ALIYUN.getName());
			imageLog.setBelong(belong);
			imageLogMapper.insert(imageLog);
		} catch (Exception e) {
			// 抛出一个配置出错的异常
			throw new OSSException(OSSReturnCode.CONFIGURATION_INFORMATION_ERROR.getCode(), e.getMessage());
		}finally {
			// 关闭client
			ossClient.shutdown();
		}
		return imageLog;
	}

	@Override
	public ImageLog upload(InputStream inputStream, String belong, String sourceFileName) throws OSSException {
		ImageLog imageLog;
		try {
			// 初始化上传对象
			init();
			// 上传文件
			//String fileName = FileUtils.getFileName(belong, sourceFileName); //因为得到的文件名太长了，所以不用这个了
			String fileName = FileUtils.getFileNameHex(belong, sourceFileName);
			ossClient.putObject(bucketName, fileName, inputStream);
			// 获取文件的全部元信息
			ObjectMetadata metadata = ossClient.getObjectMetadata(bucketName, fileName);
			// 对上传的文件进行记录
			imageLog = new ImageLog();
			imageLog.setCreateTime(new Date());
			imageLog.setBucket(bucketName);
			imageLog.setEtag(metadata.getETag());
			imageLog.setName(fileName);
			imageLog.setFileSize(metadata.getContentLength());
			imageLog.setMimeType(metadata.getContentType());
			imageLog.setImageUrl(FileUtils.getImageUrl(endpoint, fileName));
			imageLog.setCloudName(CloudServiceEnum.ALIYUN.getName());
			imageLog.setBelong(belong);
			imageLogMapper.insert(imageLog);
		} catch (Exception e) {
			// 抛出一个配置出错的异常
			throw new OSSException(OSSReturnCode.CONFIGURATION_INFORMATION_ERROR.getCode(), e.getMessage());
		}finally {
			// 关闭client
			ossClient.shutdown();
		}
		return imageLog;
	}
}
