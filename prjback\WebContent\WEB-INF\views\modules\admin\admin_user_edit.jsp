<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <title>编辑管理员</title>
</head>
<body >
<form class="layui-form hr-form-add" action="${ctx}/administrator/list/${user.userId}/edit" method="post" id="submitForm">
  <div class="layui-fluid hr-layui-fluid">
  <div class="layui-card">

    <div class="layui-card-body">
      <div class="layui-form-item layui-row">
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><%--<span class="star">*</span>--%>登陆名：</label>
          <div class="layui-input-block">
            <input type="text" <%--id="loginName" name="loginName"--%> disabled value="${user.loginName}" required="" lay-verify="required" placeholder="请输入用户名" class="layui-input" />
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span  class="star">*</span>姓名：</label>
          <div class="layui-input-block">
            <input type="text" name="realName" value="${user.realName}" required="" lay-verify="required" placeholder="请输入姓名" autocomplete="off" class="layui-input">
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span  class="star">*</span>状态：</label>
          <div class="layui-input-block">
            <input type="radio" name="status" value="1" title="开启" ${user.status eq '1'?'checked':''}>
            <input type="radio" name="status" value="0" title="关闭" ${user.status eq '0'?'checked':''}>
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span  class="star">*</span>权限：</label>
          <div class="layui-input-block">
            <c:forEach items="${roles}" var="role">
              <input type="checkbox" name="roleId" title="${role.roleName}" value="${role.roleId }" lay-skin="primary" <c:forEach items="${userRoles}" var="userRole">${role.roleId eq userRole.roleId ?'checked':''}</c:forEach> />
            </c:forEach>
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label">手机号：</label>
          <div class="layui-input-block">
            <input type="text" name="telephone" value="${user.telephone}"  placeholder="请输入手机号" autocomplete="off" class="layui-input">
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label">电子邮箱：</label>
          <div class="layui-input-block">
            <input type="text" name="email" value="${user.email}"  placeholder="请输入电子邮箱" autocomplete="off" class="layui-input">
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label">归属部门：</label>
          <div class="layui-input-block">
            <select name="organizationId" id="organizationId" >
              <option value="">请选择</option>
              <c:forEach items="${organizations }" var="organization">
                <option value="${organization.organizationId }" ${user.organizationId eq organization.organizationId ?'selected="selected"':''}>${organization.organizationName }</option>
              </c:forEach>
            </select>
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span  class="star">*</span>性别：</label>
          <div class="layui-input-block">
            <input type="radio" name="sex" value="1" title="男" ${user.sex eq '1'?'checked':''}>
            <input type="radio" name="sex" value="2" title="女" ${user.sex eq '2'?'checked':''}>
            <input type="radio" name="sex" value="0" title="保密" ${user.sex eq '0'?'checked':''}>
          </div>
        </div>
      </div>
      <div class="layui-form-item layui-row">
        <div class="layui-inline layui-col-md12">
          <label class="layui-form-label"></label>
          <div class="layui-input-block">
            <button type="button" class="layui-btn" lay-submit lay-filter="formDemo"><i class="layui-icon layui-icon-ok"></i>保存</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();"><i class="layui-icon layui-icon-close"></i>取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</form>
<myfooter>

  <script src="${ctxsta}/hongru/js/admin/admin_user_addOrModify.js?timer=6"></script>
</myfooter>
</body>
</html>
