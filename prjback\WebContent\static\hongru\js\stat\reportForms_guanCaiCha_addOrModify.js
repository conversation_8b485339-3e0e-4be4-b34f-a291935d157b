layui.use(['laydate','upload','element','layer','form'], function() {
    var form = layui.form;
    var $ = layui.jquery
        ,upload = layui.upload
        ,element = layui.element
        ,layer = layui.layer;
     var laydate = layui.laydate;

    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#yearAndMonth', //指定元素
        btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
    });

    form.on('submit(formDemo)', function(data) {
        var index = layer.load(2, {
            shade : [ 0.1, '#fff' ]
        });

        // var excelFile = $("#excelFile").val();
        // var excelFileRex = /\.(xls|xlsx)$/;
        // if (excelFile == null || excelFile == "") {
        //     layer.alert("请上传文件");
        //     return false;
        // } else {
        //     if (!excelFileRex.test(excelFile)) {
        //         layer.alert("文件必须是.xlsx");
        //         return false;
        //     }
        // }

        var form = document.querySelector("#submitForm");
        var formData = new FormData(form);
        // formData.append('filename', $('input[type="file"]')[0].files[0]);

        var actionUrl = $("#submitForm").attr('action');
        $.ajax({
            url : actionUrl,
            type : 'post',
            cache: false,
            data: formData,
            processData: false, //因为data值是FormData对象,不需要对数据做处理
            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
            success : function(result) {
                layer.closeAll();
                if (result.code == 1) {
                    parent.layer.msg("操作成功!", {
                        shade : 0.3,
                        time : 1500
                    }, function() {
                        parent.window.location.reload();
                    });
                } else {
                    layer.alert(result.message);
                }
            }
        });
        return false;
    });
});

function closeAll() {
    parent.layer.closeAll();
}