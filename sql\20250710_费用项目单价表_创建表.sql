-- 费用项目单价表DDL
-- 创建时间：2025-07-10
-- 说明：根据需求文档创建费用项目单价表，用于管理各年度的费用项目单价信息

CREATE TABLE [dbo].[费用项目单价表] (
  [流水号] int IDENTITY(1,1) NOT NULL,
  [年度] char(4) COLLATE Chinese_PRC_CI_AS NOT NULL,
  [费用项目名称] varchar(25) COLLATE Chinese_PRC_CI_AS NOT NULL,
  [费用项目单价] decimal(10,4) NOT NULL,
  [比例] decimal(10,4) NOT NULL,
  [创建人姓名] varchar(10) COLLATE Chinese_PRC_CI_AS NOT NULL,
  [创建时间] datetime NOT NULL,
  [更新人姓名] varchar(10) COLLATE Chinese_PRC_CI_AS NOT NULL,
  [更新时间] datetime NOT NULL,
  CONSTRAINT [PK_费用项目单价表] PRIMARY KEY CLUSTERED ([流水号])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
)  
ON [PRIMARY]
GO

-- 添加注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'费用项目单价表主键' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'流水号'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'年度' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'年度'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'费用项目名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'费用项目名称'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'费用项目单价' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'费用项目单价'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'比例' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'比例'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人姓名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'创建人姓名'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'创建时间'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人姓名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'更新人姓名'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表', @level2type=N'COLUMN',@level2name=N'更新时间'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'费用项目单价表，用于管理各年度的费用项目单价信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'费用项目单价表'
GO
