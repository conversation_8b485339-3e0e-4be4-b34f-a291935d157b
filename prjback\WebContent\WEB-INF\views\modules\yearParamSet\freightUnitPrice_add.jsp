<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>新增运费单价</title>
    <style>
        .layui-form-label {
            width: 120px;
            text-align: right;
        }
        .layui-input-block {
            margin-left: 150px;
        }
        .layui-input-inline {
            width: 200px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="addForm" class="layui-form" method="post" action="">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年度<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="year" id="year" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">地区<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="area" id="area" lay-verify="required" autocomplete="off" class="layui-input" maxlength="50">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">运费<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="freight" id="freight" lay-verify="required|number" autocomplete="off" class="layui-input" oninput="validateNumberInput(this)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-bg-blue" onclick="submitForm()">
                        <i class="layui-icon layui-icon-ok"></i>保存
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="closeWindow()">
                        <i class="layui-icon layui-icon-close"></i>取消
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['laydate', 'layer', 'form'], function(){
    var laydate = layui.laydate
        ,layer = layui.layer
        ,form = layui.form;

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    // 页面加载时设置当前年度
    var currentYear = new Date().getFullYear();
    $("#year").val(currentYear);
});

// 提交表单
function submitForm() {
    var formData = $("#addForm").serializeJsonObject();
    
    // 验证必填项
    if (!formData.year) {
        layer.msg('请选择年度', {icon: 2});
        return;
    }
    if (!formData.area) {
        layer.msg('请输入地区', {icon: 2});
        return;
    }
    if (!formData.freight) {
        layer.msg('请输入运费', {icon: 2});
        return;
    }

    $.ajax({
        url: baselocation + '/yearParamSet/freightUnitPrice/add',
        type: 'POST',
        data: formData,
        success: function(result) {
            if (result.code == 1) {
                layer.msg('保存成功', {icon: 1});
                setTimeout(function() {
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }, 1000);
            } else {
                layer.msg(result.message || '保存失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络异常，请稍后重试', {icon: 2});
        }
    });
}

// 关闭窗口
function closeWindow() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}

// 限制只能输入数字和小数点
function validateNumberInput(input) {
    input.value = input.value.replace(/[^0-9.]/g, '');
    // 防止输入多个小数点
    var parts = input.value.split('.');
    if (parts.length > 2) {
        input.value = parts[0] + '.' + parts.slice(1).join('');
    }
}
</script>
</body>
</html>
