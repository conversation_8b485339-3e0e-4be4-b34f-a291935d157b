# Spring启动问题修复说明

## 问题描述
应用启动时出现Spring Bean创建错误，错误信息显示Bean工厂创建失败。

## 问题原因
通过分析项目模块依赖关系和Spring Bean依赖，发现存在两层循环依赖问题：

### 1. 模块层面的循环依赖（已修复）
- `crmservice`模块依赖`prjback`模块
- `prjback`模块依赖`crmservice`模块

### 2. Spring Bean层面的循环依赖（主要问题）
- `CostServiceImpl`注入了`IYearReviseService`
- `YearReviseServiceImpl`注入了`ICostService`

这种Spring Bean级别的循环依赖是导致应用启动失败的根本原因。

## 解决方案

### 1. 修复模块依赖关系
**文件**: `crmservice/crmservice.iml`

移除了crmservice模块对prjback模块的依赖：
```xml
<!-- 删除了这一行 -->
<!-- <orderEntry type="module" module-name="prjback" /> -->
```

**文件**: `prjback/prjback.iml`

确保prjback模块正确依赖crmservice模块：
```xml
<orderEntry type="module" module-name="crmservice" />
```

### 2. 修复Spring Bean循环依赖
**文件**: `crmservice/src/com/hongru/service/impl/yearRevise/YearReviseServiceImpl.java`

移除了对ICostService的依赖，直接注入所需的Mapper：
```java
// 移除循环依赖
// @Autowired
// private ICostService costService;

// 直接注入所需的Mapper
@Autowired
private HumanPriceCostMapper humanPriceCostMapper;
// HumanHourCostMapper已存在
```

更新了方法调用，直接使用Mapper：
```java
// 原来的调用
// List<HumanHourCost> humanHourCostList = costService.getHumanHourCostsByYearMonth(yearMonthStart, yearMonthEnd);
// List<HumanPriceCost> HumanPriceCostList = costService.getHumanPriceCostByYearMonth(yearMonthStart, yearMonthEnd);

// 修复后的调用
List<HumanHourCost> humanHourCostList = humanHourCostMapper.getHumanHourCostsByYearMonth(yearMonthStart, yearMonthEnd);
List<HumanPriceCost> HumanPriceCostList = humanPriceCostMapper.getHumanPriceCostByYearMonth(yearMonthStart, yearMonthEnd);
```

### 3. 正确的依赖关系
修复后的模块依赖关系：
- `crm` - 基础实体模块
- `crmservice` - 服务层模块，依赖`crm`
- `prjback` - Web控制器模块，依赖`crm`和`crmservice`

修复后的Spring Bean依赖关系：
- 移除了`YearReviseServiceImpl`对`ICostService`的依赖
- `CostServiceImpl`仍然可以依赖`IYearReviseService`（单向依赖）

### 4. 验证修复
1. 检查编译错误是否解决
2. 重新启动应用程序
3. 验证所有Service Bean是否正常创建
4. 验证ProductCostDesignService功能是否正常

## 技术说明
- Spring容器不允许循环依赖的Bean创建
- 模块依赖关系应该是单向的，避免循环引用
- 正确的分层架构：Entity -> Service -> Controller
- 当Service之间存在循环依赖时，可以通过直接注入Mapper来解决
- 保持业务逻辑的完整性，只是改变了数据访问的方式

## 预期结果
修复后应用程序应该能够正常启动，所有Service Bean包括ProductCostDesignService都能够正常创建和注入。

## 修复影响
- 解决了Spring启动时的Bean创建错误
- 保持了原有的业务逻辑不变
- 提高了系统的稳定性和可维护性
- 为ProductCostDesignService的正常运行扫清了障碍
