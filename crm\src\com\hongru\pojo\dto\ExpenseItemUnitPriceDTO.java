package com.hongru.pojo.dto;

import com.hongru.entity.cost.ExpenseItemUnitPrice;
import com.hongru.support.page.PageInfo;

import java.util.List;

/**
 * 费用项目单价表DTO
 * <AUTHOR>
 */
public class ExpenseItemUnitPriceDTO {
    
    private List<ExpenseItemUnitPrice> expenseItemUnitPriceList;
    private PageInfo pageInfo;

    public List<ExpenseItemUnitPrice> getExpenseItemUnitPriceList() {
        return expenseItemUnitPriceList;
    }

    public void setExpenseItemUnitPriceList(List<ExpenseItemUnitPrice> expenseItemUnitPriceList) {
        this.expenseItemUnitPriceList = expenseItemUnitPriceList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
}
