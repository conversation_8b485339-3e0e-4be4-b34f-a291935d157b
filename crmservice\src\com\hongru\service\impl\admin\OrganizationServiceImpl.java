package com.hongru.service.impl.admin;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.hongru.base.BasePageDTO;
import com.hongru.common.enums.StatusEnum;
import com.hongru.common.util.StringUtil;
import com.hongru.entity.admin.Organization;
import com.hongru.entity.admin.User;
import com.hongru.mapper.admin.OrganizationMapper;
import com.hongru.mapper.admin.UserMapper;
import com.hongru.pojo.vo.OrganizationVO;
import com.hongru.service.admin.IOrganizationService;
import com.hongru.support.page.PageInfo;

/**
 * 
*    
* 类名称：OrganizationServiceImpl   
* 类描述：Organization / 部门表 业务逻辑层接口实现      
* 创建人：hongru   
* 创建时间：2017年3月31日 下午6:04:32   
*
 */
@Service
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, Organization> implements IOrganizationService {
	
	@Autowired
	private OrganizationMapper organizationMapper;
	@Autowired
	private UserMapper userMapper;
	
	@Override
	public Integer insertOrganization(Organization organization, String userName) {
		organization.setCreateBy(userName);
		organization.setCreateTime(new Date());
		organization.setUpdateBy(userName);
		organization.setUpdateTime(new Date());
		return organizationMapper.insert(organization);
	}

	@Override
	public List<Organization> listBySataus(Integer status) {
		Organization organization = new Organization();
		organization.setStatus(status);
		return organizationMapper.selectList(new EntityWrapper<Organization>(organization));
	}

	@Override
	public BasePageDTO<Organization> listByPage(PageInfo pageInfo, String search) {
		if(!StringUtil.isStringEmpty(search)){
			search = "%"+search.trim()+"%";
		}else{
			search = null;
		}
		List<Organization> organizations = organizationMapper.listByPage(pageInfo, search);
		Integer total = organizationMapper.listByPageCount(search);
		pageInfo.setTotal(total);
		return new BasePageDTO<Organization>(pageInfo, organizations);
	}
	
	@Override
	public List<OrganizationVO> listOrganizationsDetail() {
		List<OrganizationVO> organizationVOs = new ArrayList<>();

		// 查询所有部门
		List<Organization> organizations = organizationMapper.selectList(new EntityWrapper<Organization>());

		// 循环遍历部门,将用户信息添加该部门中
		for (Organization organization : organizations) {
			User user = new User();
			user.setOrganizationId(organization.getOrganizationId());
			List<User> users = userMapper.selectList(new EntityWrapper<User>(user));
			OrganizationVO organizationVO = new OrganizationVO();
			BeanUtils.copyProperties(organization, organizationVO);
			organizationVO.setUsers(users);
			organizationVO.setNumberUser(users.size());
			organizationVOs.add(organizationVO);
		}
		return organizationVOs;
	}

	@Override
	public Integer updateStatus(Long organizationId) {
		Organization organization = organizationMapper.selectById(organizationId);

		if (organization != null && StatusEnum.NORMAL.getStatus().equals(organization.getStatus())) {
			Organization updateOrganization = new Organization();
			updateOrganization.setOrganizationId(organization.getOrganizationId());
			updateOrganization.setStatus(StatusEnum.FREEZE.getStatus());
			return organizationMapper.updateById(updateOrganization);
		} else if (organization != null && StatusEnum.FREEZE.getStatus().equals(organization.getStatus())) {
			Organization updateOrganization = new Organization();
			updateOrganization.setOrganizationId(organization.getOrganizationId());
			updateOrganization.setStatus(StatusEnum.NORMAL.getStatus());
			return organizationMapper.updateById(updateOrganization);
		}
		return null;
	}
	
	@Override
	public Integer updateOrganization(Organization organization, String userName) {
		organization.setUpdateBy(userName);
		organization.setUpdateTime(new Date());
		return organizationMapper.updateById(organization);
	}

	@Override
	public Integer deleteByOrganizationId(Long organizationId) {

		// 重置管理员部门记录
		userMapper.updateOrganization(organizationId);

		// 删除部门表中记录
		return organizationMapper.deleteById(organizationId);
	}

	/**
	* 批量删除部门
	* @throws
	* <AUTHOR>
	* @create 2022/2/11 11:34
	* @return
	*/
	@Override
	public void deleteOrganizations(Integer[] organizationIdArr) {
		organizationMapper.deleteOrganizations(organizationIdArr);
	}

}
