package com.hongru.common.poiExcelExport.bean;

import java.util.List;

public class BaseBeanV2 {
	/*出库报表*/
	protected List<DeliveryReportBean> deliveryReportBeanList;
	/* 总计总重量 */
	protected double mainWeight;
	/* 总计总线圈数 */
	protected int mainCoilNum;
	/* 总计总木托数 */
	protected int mainPalletNum;

	public List<DeliveryReportBean> getDeliveryReportBeanList() {
		return deliveryReportBeanList;
	}

	public void setDeliveryReportBeanList(List<DeliveryReportBean> deliveryReportBeanList) {
		this.deliveryReportBeanList = deliveryReportBeanList;
	}

	public double getMainWeight() {
		return mainWeight;
	}

	public void setMainWeight(double mainWeight) {
		this.mainWeight = mainWeight;
	}

	public int getMainCoilNum() {
		return mainCoilNum;
	}

	public void setMainCoilNum(int mainCoilNum) {
		this.mainCoilNum = mainCoilNum;
	}

	public int getMainPalletNum() {
		return mainPalletNum;
	}

	public void setMainPalletNum(int mainPalletNum) {
		this.mainPalletNum = mainPalletNum;
	}
}
