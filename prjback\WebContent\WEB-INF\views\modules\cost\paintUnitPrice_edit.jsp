<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>编辑油漆单价</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="formEdit" class="layui-form" method="post" action="">
            <input type="hidden" name="rawMaterialItem.serialNumber" value="${rawMaterialItem.serialNumber}">
            
            <!-- 基础信息 -->
            <fieldset class="layui-elem-field layui-field-title">
                <legend>基础信息</legend>
            </fieldset>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年度<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="rawMaterialItem.year" id="year" lay-verify="required" value="${rawMaterialItem.year}" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">品目名<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <select name="rawMaterialItem.itemName" id="itemName" lay-filter="itemNameFn">
                            <option value="">请选择品目名</option>
                            <c:forEach items="${conductorCodeList }" var="conductorInfo">
                                <option value="${conductorInfo.conductorName },${conductorInfo.costCode }" <c:if test="${conductorInfo.conductorName == rawMaterialItem.itemName}">selected</c:if>>${conductorInfo.conductorName }</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">品目<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="rawMaterialItem.itemCode" id="itemCode" placeholder="请先选择品目名" lay-verify="required" value="${rawMaterialItem.itemCode}" autocomplete="off" class="layui-input layui-disabled" maxlength="3">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">原料类型:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="油漆" readonly class="layui-input layui-disabled">
                        <input type="hidden" name="rawMaterialItem.materialType" value="02">
                    </div>
                </div>
            </div>

            <!-- 油漆单价明细 -->
            <fieldset class="layui-elem-field layui-field-title">
                <legend>油漆单价明细</legend>
            </fieldset>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">关税率<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="paintUnitPriceDetail.tariffRate" lay-verify="required" value="${paintDetail.tariffRate}" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">采购单价<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="paintUnitPriceDetail.purchaseUnitPrice" lay-verify="required" value="${paintDetail.purchaseUnitPrice}" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">油漆主体<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="paintUnitPriceDetail.paintBody" lay-verify="required" value="${paintDetail.paintBody}" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">油漆单位长重量<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="paintUnitPriceDetail.paintUnitLengthWeight" lay-verify="required" value="${paintDetail.paintUnitLengthWeight}" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">油漆需求率<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="paintUnitPriceDetail.paintDemandRate" lay-verify="required" value="${paintDetail.paintDemandRate}" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">备注:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="paintUnitPriceDetail.remarks" value="${paintDetail.remarks}" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formEdit">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="cancel();">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'layer', 'laydate'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    var conductorName = "";
    form.on('select(itemNameFn)', function (data) {
        var value = "";
        conductorName = "";

        if (data?.value) {
            var valList = data.value.split(",");

            conductorName = valList[0];
            value = valList?.length > 1 ? valList[1] : "";
        }

        $("#itemCode").val(value);
    });

    //监听提交
    form.on('submit(formEdit)', function(data){
        // 验证油漆明细信息
        if(!data.field['paintUnitPriceDetail.tariffRate'] || 
           !data.field['paintUnitPriceDetail.purchaseUnitPrice'] ||
           !data.field['paintUnitPriceDetail.paintBody'] ||
           !data.field['paintUnitPriceDetail.paintUnitLengthWeight'] ||
           !data.field['paintUnitPriceDetail.paintDemandRate']) {
            layer.msg('请填写完整的油漆单价明细信息', {icon: 2});
            return false;
        }

        data.field['rawMaterialItem.itemName'] = conductorName;
        
        $.ajax({
            url: baselocation + '/yearParamSet/paintUnitPrice/edit',
            type: 'POST',
            data: data.field,
            success: function(result) {
                if(result.code == 1) {
                    layer.msg('编辑成功', {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(result.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统异常', {icon: 2});
            }
        });
        return false;
    });
});

// 取消按钮
function cancel() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>
<style>
    .layui-form-label {
        width: 120px;
    }
    .layui-disabled {
        background-color: #f2f2f2;
        cursor: not-allowed;
    }
</style>
</body>
</html>
