package com.hongru.service.impl.admin;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.hongru.common.util.StringUtil;
import com.hongru.entity.admin.Role;
import com.hongru.entity.admin.UserRole;
import com.hongru.mapper.admin.UserRoleMapper;
import com.hongru.pojo.dto.UserPageDTO;
import com.hongru.pojo.dto.UserRoleDTO;
import com.hongru.pojo.vo.UserVO;
import com.hongru.service.admin.IUserRoleService;
import com.hongru.support.page.PageInfo;

/**
 * 
*    
* 类名称：UserRoleServiceImpl   
* 类描述：UserRole / 管理员角色关联表 业务逻辑层接口实现   
* 创建人：hongru   
* 创建时间：2017年4月1日 下午5:51:10   
*
 */
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements IUserRoleService {
	
	@Autowired
	private UserRoleMapper userRoleMapper;

	@Override
	public UserRoleDTO getByUserId(Long userId, Integer status) {

		List<Role> roles = userRoleMapper.listByUserId(userId, status);

		Set<String> roleSigns = new HashSet<>();
		Set<String> roleIds = new HashSet<>();

		// 遍历角色列表
		for (Role role : roles) {
			roleSigns.add(role.getRoleSign());
			roleIds.add(role.getRoleId().toString());
		}
		return new UserRoleDTO(roleSigns, roleIds);
	}

	@Override
	public List<Role> listByUserId(Long userId, Integer status) {
		return userRoleMapper.listByUserId(userId, status);
	}

	@Override
	public UserPageDTO listByRoleId(Long roleId, PageInfo pageInfo, String search) {
		if(!StringUtil.isStringEmpty(search)){
			search = "%"+search.trim()+"%";
		}else{
			search = null;
		}
		List<UserVO> userVOs = userRoleMapper.listByRoleId(roleId, pageInfo, search);
		Integer total = userRoleMapper.listByRoleIdCount(roleId,search);
		pageInfo.setTotal(total);
		return new UserPageDTO(userVOs, pageInfo);
	}
}
