package com.hongru.entity.yearRevise;

/**
* 机械时间予定查询返回结果实体类
* <AUTHOR>
* @create 2024/01/04 09:55
*/
public class MachineTimePreBean {
	/* 部门 */
	protected String department;
	/* 入库量实绩(平均) */
	protected String storageActAvg;
	/* 机械时间予定(平均) */
	protected String machineTimePreAvg;
	/* 机械时间实绩(平均) */
	protected String machineTimeActAvg;
	/* 单位机械时间 */
	protected String unitMechanicalTime;
	/* 新年度入库量予定 */
	protected String newYearStoragePre;
	/* 新年度UP率 */
	protected String newYearUpRate;
	/* 新年度机械时间予定 */
	protected String newYearMachineTimePre;
	
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public String getStorageActAvg() {
		return storageActAvg;
	}
	public void setStorageActAvg(String storageActAvg) {
		this.storageActAvg = storageActAvg;
	}
	public String getMachineTimePreAvg() {
		return machineTimePreAvg;
	}
	public void setMachineTimePreAvg(String machineTimePreAvg) {
		this.machineTimePreAvg = machineTimePreAvg;
	}
	public String getMachineTimeActAvg() {
		return machineTimeActAvg;
	}
	public void setMachineTimeActAvg(String machineTimeActAvg) {
		this.machineTimeActAvg = machineTimeActAvg;
	}
	public String getUnitMechanicalTime() {
		return unitMechanicalTime;
	}
	public void setUnitMechanicalTime(String unitMechanicalTime) {
		this.unitMechanicalTime = unitMechanicalTime;
	}
	public String getNewYearStoragePre() {
		return newYearStoragePre;
	}
	public void setNewYearStoragePre(String newYearStoragePre) {
		this.newYearStoragePre = newYearStoragePre;
	}
	public String getNewYearUpRate() {
		return newYearUpRate;
	}
	public void setNewYearUpRate(String newYearUpRate) {
		this.newYearUpRate = newYearUpRate;
	}
	public String getNewYearMachineTimePre() {
		return newYearMachineTimePre;
	}
	public void setNewYearMachineTimePre(String newYearMachineTimePre) {
		this.newYearMachineTimePre = newYearMachineTimePre;
	}
}
