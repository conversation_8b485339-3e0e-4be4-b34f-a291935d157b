<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/boundQuantity/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md10">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="${boundQuantity.yearMonth}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EM入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="emInboundQuantity" name="emInboundQuantity" value="${boundQuantity.emInboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EM出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="emOutboundQuantity" name="emOutboundQuantity" value="${boundQuantity.emOutboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="efInboundQuantity" name="efInboundQuantity" value="${boundQuantity.efInboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="efOutboundQuantity" name="efOutboundQuantity" value="${boundQuantity.efOutboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF09入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ef09InboundQuantity" name="ef09InboundQuantity" value="${boundQuantity.ef09InboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF09出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ef09OutboundQuantity" name="ef09OutboundQuantity" value="${boundQuantity.ef09OutboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>ER入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="erInboundQuantity" name="erInboundQuantity" value="${boundQuantity.erInboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>ER出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="erOutboundQuantity" name="erOutboundQuantity" value="${boundQuantity.erOutboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH日立外入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ehInboundQuantity1" name="ehInboundQuantity1" value="${boundQuantity.ehInboundQuantity1}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH日立外出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ehOutboundQuantity1" name="ehOutboundQuantity1" value="${boundQuantity.ehOutboundQuantity1}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ehInboundQuantity2" name="ehInboundQuantity2" value="${boundQuantity.ehInboundQuantity2}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ehOutboundQuantity2" name="ehOutboundQuantity2" value="${boundQuantity.ehOutboundQuantity2}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>UF太线入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="uftxInboundQuantity" name="uftxInboundQuantity" value="${boundQuantity.uftxInboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>UF太线出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="uftxOutboundQuantity" name="uftxOutboundQuantity" value="${boundQuantity.uftxOutboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>UF细线入库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ufxxInboundQuantity" name="ufxxInboundQuantity" value="${boundQuantity.ufxxInboundQuantity}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>UF细线出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ufxxOutboundQuantity" name="ufxxOutboundQuantity" value="${boundQuantity.ufxxOutboundQuantity}"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="statId" name="statId" value="${boundQuantity.statId}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/boundQuantity_addOrModify.js?time=2"></script>
</myfooter>
</body>
</html>
