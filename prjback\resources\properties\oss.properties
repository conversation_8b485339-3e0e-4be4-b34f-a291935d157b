
#===============================#
#===  阿里云oss云存储系统配置    ===#
#===============================#
#
#定义阿里云accessKeyId
aliyun.accessKeyId=LTAIZNGYmEMxw8RR
#定义阿里云accessKeySecret
aliyun.accessKeySecret=4xI26aDZhDD9HKOj1oWpoXf8jhgV54
#定义阿里云存储空间
aliyun.bucketName=crmpic
#定义阿里云云外网域名地址
aliyun.endpoint=http://oss-cn-hangzhou.aliyuncs.com
#OSS上的bucket中的某个图片文件夹的名称，也就是要上传到的图片文件夹
IMAGE_UPLOAD_PATH=crm_img
#OSS的处理图片的绑定域名，可以在后面加一些规则来显示图片，详情查看OSS API文档。
IMAGE_OSS_PATH_READ=http://crmpic.oss-cn-hangzhou.aliyuncs.com/crm_img
#路径详解商品shop_img/s_shopId/p  广告shop_img/s_shopId/ad 文章shop_img/s_shopId/at 分享会员图片shop_img/s_shopId/sm分享商品shop_img/s_shopId/s
FILE_PATH=C:/upload
