package com.hongru.entity.xieFenXi;

/*
 * author yhshi
 * EM品质不良屑率统计报表Model
 */
public class EMScrapAmountReport {

	protected  String userName;//客户名称
	protected  String machineId;//设备号
	protected  String productCode;//产品型号
	protected  Float outputproduct;  //产量
	protected  String scrapAmounts;//屑量
	protected  String scrapRate;//屑率
	protected  String notEnoughNum; //换线径
	protected  String standardNum; //断线
	protected  String lineNum;//线盘数
	protected  String DiffDate;//停机时间
	protected  String DiffDateBreak;//断线时间
	protected  String machineTime;//机械时间
	protected  String otherReason;//烧付其他
	protected  String notEnoughRate; //量少品率
	protected  String standardRate;//标准品率
	protected  String tgCode;//稼动率
	protected  String activationRate;//断线率
	protected String machineName;//设备名称
	protected String causeBy;//产生原因
	protected int flag;//标记
	protected Float outputproductTotal;//产量汇总
	
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public Float getOutputproduct() {
		return outputproduct;
	}
	public void setOutputproduct(Float outputproduct) {
		this.outputproduct = outputproduct;
	}
	public String getScrapAmounts() {
		return scrapAmounts;
	}
	public void setScrapAmounts(String scrapAmounts) {
		this.scrapAmounts = scrapAmounts;
	}
	public String getScrapRate() {
		return scrapRate;
	}
	public void setScrapRate(String scrapRate) {
		this.scrapRate = scrapRate;
	}
	public String getNotEnoughNum() {
		return notEnoughNum;
	}
	public void setNotEnoughNum(String notEnoughNum) {
		this.notEnoughNum = notEnoughNum;
	}
	public String getStandardNum() {
		return standardNum;
	}
	public void setStandardNum(String standardNum) {
		this.standardNum = standardNum;
	}
	public String getLineNum() {
		return lineNum;
	}
	public void setLineNum(String lineNum) {
		this.lineNum = lineNum;
	}
	public String getDiffDate() {
		return DiffDate;
	}
	public void setDiffDate(String diffDate) {
		DiffDate = diffDate;
	}
	public String getDiffDateBreak() {
		return DiffDateBreak;
	}
	public void setDiffDateBreak(String diffDateBreak) {
		DiffDateBreak = diffDateBreak;
	}
	public String getMachineTime() {
		return machineTime;
	}
	public void setMachineTime(String machineTime) {
		this.machineTime = machineTime;
	}
	public String getOtherReason() {
		return otherReason;
	}
	public void setOtherReason(String otherReason) {
		this.otherReason = otherReason;
	}
	public String getNotEnoughRate() {
		return notEnoughRate;
	}
	public void setNotEnoughRate(String notEnoughRate) {
		this.notEnoughRate = notEnoughRate;
	}
	public String getStandardRate() {
		return standardRate;
	}
	public void setStandardRate(String standardRate) {
		this.standardRate = standardRate;
	}
	public String getTgCode() {
		return tgCode;
	}
	public void setTgCode(String tgCode) {
		this.tgCode = tgCode;
	}
	public String getActivationRate() {
		return activationRate;
	}
	public void setActivationRate(String activationRate) {
		this.activationRate = activationRate;
	}
	public String getMachineId() {
		return machineId;
	}
	public void setMachineId(String machineId) {
		this.machineId = machineId;
	}
	public String getMachineName() {
		return machineName;
	}
	public void setMachineName(String machineName) {
		this.machineName = machineName;
	}
	public String getCauseBy() {
		return causeBy;
	}
	public void setCauseBy(String causeBy) {
		this.causeBy = causeBy;
	}
	public int getFlag() {
		return flag;
	}
	public void setFlag(int flag) {
		this.flag = flag;
	}
	public Float getOutputproductTotal() {
		return outputproductTotal;
	}
	public void setOutputproductTotal(Float outputproductTotal) {
		this.outputproductTotal = outputproductTotal;
	}

}
