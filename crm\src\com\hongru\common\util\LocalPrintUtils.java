package com.hongru.common.util;

import java.awt.print.Book;
import java.awt.print.PageFormat;
import java.awt.print.Paper;
import java.awt.print.PrinterJob;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

import javax.print.Doc;
import javax.print.DocFlavor;
import javax.print.DocPrintJob;
import javax.print.PrintException;
import javax.print.PrintService;
import javax.print.SimpleDoc;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.standard.Copies;
import javax.print.attribute.standard.Sides;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.printing.PDFPrintable;
import org.apache.pdfbox.printing.Scaling;

import com.hongru.common.exception.BaseException;

/**
* 本地打印机工具类
* <AUTHOR>
* @create 2022/01/06 13:13
 * 参考资料地址：https://blog.csdn.net/vatxiongxiaohui/article/details/83985896#2.3%20Word%E6%96%87%E4%BB%B6%E6%A0%BC%E5%BC%8F%E6%89%93%E5%8D%B0%E5%AE%9E%E7%8E%B0
 * 参考资料地址：http://jszx-jxpt.cuit.edu.cn/JavaAPI/javax/print/package-summary.html
*/
public class LocalPrintUtils {
    /**
    * 打印pdf
    * @param file 打印的文件
    * @param printerName  打印机名称
    * @param copiesNum    打印份数
    * @param orientation  打印方向  横向：0  竖向：1
    * <AUTHOR>
    * @create 2022/01/14 15:03
    */
    public static void PDFprint(File file ,String printerName,int copiesNum,int orientation) throws Exception {
        if (file == null || !file.exists()) {
            throw new BaseException("缺少打印文件");
        }
        if(StringUtil.isStringEmpty(printerName)){
            throw new BaseException("未指定打印机");
        }
        PDDocument document = null;
        try {
            document = PDDocument.load(file);
            PrinterJob printJob = PrinterJob.getPrinterJob();
            printJob.setJobName(file.getName());
            // 查找并设置打印机
            //获得本台电脑连接的所有打印机
            PrintService[] printServices = PrinterJob.lookupPrintServices();
            if(printServices == null || printServices.length == 0) {
                throw new BaseException("打印失败，未找到可用打印机，请检查。");
            }
            PrintService printService = null;
            //匹配指定打印机
            for (int i = 0;i < printServices.length; i++) {
//                    System.out.println(printServices[i].getName());
                if (printServices[i].getName().contains(printerName)) {
                    printService = printServices[i];
                    break;
                }
            }
            if(printService==null){
                throw new BaseException("打印失败，未找到名称为" + printerName + "的打印机，请检查。");
            }
            printJob.setPrintService(printService);

            //设置纸张及缩放
            PDFPrintable pdfPrintable = new PDFPrintable(document, Scaling.ACTUAL_SIZE);
            //设置多页打印
            Book book = new Book();
            PageFormat pageFormat = new PageFormat();
            //设置打印方向
            if(orientation == PageFormat.PORTRAIT){
                pageFormat.setOrientation(PageFormat.PORTRAIT);//竖向
            }else{
                pageFormat.setOrientation(PageFormat.LANDSCAPE);//横向
            }
            pageFormat.setPaper(getPaperForA4());//设置纸张
            book.append(pdfPrintable, pageFormat, document.getNumberOfPages());
            printJob.setPageable(book);
            printJob.setCopies(copiesNum);//设置打印份数
            //添加打印属性
            HashPrintRequestAttributeSet pars = new HashPrintRequestAttributeSet();
            pars.add(Sides.ONE_SIDED);//设置单面打印
//            pars.add(Sides.DUPLEX); //设置双面打印
            printJob.print(pars);
        }catch (Exception e1) {
            e1.printStackTrace();
        }finally {
            if (document != null) {
                try {
                    document.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
    * a4纸
    * @param
    * <AUTHOR>
    * @create 2022/01/14 15:18
    */
    public static Paper getPaperForA4() {
        Paper paper = new Paper();
        // 默认为A4纸张，对应像素宽和高分别为 595, 842
        int width = 595;
        int height = 842;
        // 设置边距，单位是像素，10mm边距，对应 28px
        int marginLeft = 10;
        int marginRight = 0;
        int marginTop = 10;
        int marginBottom = 0;
        paper.setSize(width, height);
        // 下面一行代码，解决了打印内容为空的问题
        paper.setImageableArea(marginLeft, marginRight, width - (marginLeft + marginRight), height - (marginTop + marginBottom));
        return paper;
    }

    // 传入文件和打印机名称
    public static void JPGPrint(File file,String printerName) throws PrintException {
        if (file == null || !file.exists()) {
            throw new BaseException("缺少打印文件");
        }
        InputStream fis = null;
        try {
            // 设置打印格式，如果未确定类型，可选择autosense
            DocFlavor flavor = DocFlavor.INPUT_STREAM.JPEG;
            // 设置打印参数
            PrintRequestAttributeSet aset = new HashPrintRequestAttributeSet();
            aset.add(new Copies(1)); //份数
            //aset.add(MediaSize.ISO.A4); //纸张
            // aset.add(Finishings.STAPLE);//装订
            aset.add(Sides.DUPLEX);//单双面
            // 定位打印服务
            PrintService printService = null;
            if (printerName != null) {
                //获得本台电脑连接的所有打印机
                PrintService[] printServices = PrinterJob.lookupPrintServices();
                if(printServices == null || printServices.length == 0) {
                    throw new BaseException("打印失败，未找到可用打印机，请检查。");
                }
                //匹配指定打印机
                for (int i = 0;i < printServices.length; i++) {
//                    System.out.println(printServices[i].getName());
                    if (printServices[i].getName().contains(printerName)) {
                        printService = printServices[i];
                        break;
                    }
                }
                if(printService==null){
                    throw new BaseException("打印失败，未找到名称为" + printerName + "的打印机，请检查。");
                }
            }
            fis = new FileInputStream(file); // 构造待打印的文件流
            Doc doc = new SimpleDoc(fis, flavor, null);
            DocPrintJob job = printService.createPrintJob(); // 创建打印作业
            job.print(doc, aset);
        } catch (FileNotFoundException e1) {
            e1.printStackTrace();
        } finally {
            // 关闭打印的文件流
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void main(String args[]) throws Exception {
        PDFprint(new File("C:/test.pdf"),"HP DeskJet 2130 series",1,1);
    }

}
