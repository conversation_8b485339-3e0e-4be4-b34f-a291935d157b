package com.hongru.entity.yearRevise;

import com.baomidou.mybatisplus.annotations.TableField;

/**
* 机械时间予定查询返回结果实体类
* <AUTHOR>
* @create 2024/02/23 15:55
*/
public class TransportationCostBean {
	/* 地区 */
	protected String area;
	/* 运输费单价 */
	protected String transportationCostUnit;
	/* 出库量 */
	protected String outQuantity ;
	/* 运输费金额*/
	protected String transportationCost;
	/* 回收运输费单价 */
	protected String recycleTransportationCostUnit;
	/* 销售量 */
	protected String salesQuantity ;
	/* 回收运输费金额*/
	protected String recycleTransportationCost;
	
	/* 回收运输费平均单价 */
	@TableField(exist = false)
	protected String recycleTransportationCostAvgUnit;
	/* 综合运费(含回收) */
	@TableField(exist = false)
	protected String comprehensiveFreight;
	
	public String getArea() {
		return area;
	}
	public void setArea(String area) {
		this.area = area;
	}
	public String getTransportationCostUnit() {
		return transportationCostUnit;
	}
	public void setTransportationCostUnit(String transportationCostUnit) {
		this.transportationCostUnit = transportationCostUnit;
	}
	public String getOutQuantity() {
		return outQuantity;
	}
	public void setOutQuantity(String outQuantity) {
		this.outQuantity = outQuantity;
	}
	public String getTransportationCost() {
		return transportationCost;
	}
	public void setTransportationCost(String transportationCost) {
		this.transportationCost = transportationCost;
	}
	public String getRecycleTransportationCostUnit() {
		return recycleTransportationCostUnit;
	}
	public void setRecycleTransportationCostUnit(String recycleTransportationCostUnit) {
		this.recycleTransportationCostUnit = recycleTransportationCostUnit;
	}
	public String getSalesQuantity() {
		return salesQuantity;
	}
	public void setSalesQuantity(String salesQuantity) {
		this.salesQuantity = salesQuantity;
	}
	public String getRecycleTransportationCost() {
		return recycleTransportationCost;
	}
	public void setRecycleTransportationCost(String recycleTransportationCost) {
		this.recycleTransportationCost = recycleTransportationCost;
	}
	public String getRecycleTransportationCostAvgUnit() {
		return recycleTransportationCostAvgUnit;
	}
	public void setRecycleTransportationCostAvgUnit(String recycleTransportationCostAvgUnit) {
		this.recycleTransportationCostAvgUnit = recycleTransportationCostAvgUnit;
	}
	public String getComprehensiveFreight() {
		return comprehensiveFreight;
	}
	public void setComprehensiveFreight(String comprehensiveFreight) {
		this.comprehensiveFreight = comprehensiveFreight;
	}
	
}
