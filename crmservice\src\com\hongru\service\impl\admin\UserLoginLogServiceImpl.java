package com.hongru.service.impl.admin;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.hongru.base.BasePageDTO;
import com.hongru.common.util.StringUtil;
import com.hongru.entity.admin.UserLoginLog;
import com.hongru.mapper.admin.UserLoginLogMapper;
import com.hongru.service.admin.IUserLoginLogService;
import com.hongru.support.page.PageInfo;

/**
 * 
*    
* 类名称：UserLoginLogServiceImpl   
* 类描述：UserLoginLog / 管理员登陆表 业务逻辑层接口实现      
* 创建人：hongru   
* 创建时间：2017年3月31日 下午6:05:35   
*
 */
@Service
public class UserLoginLogServiceImpl extends ServiceImpl<UserLoginLogMapper, UserLoginLog> implements IUserLoginLogService {
	
	@Autowired
	private UserLoginLogMapper userLoginLogMapper;

	@Override
	public BasePageDTO<UserLoginLog> listByUserId(Long userId, PageInfo pageInfo, String search) {
		if(!StringUtil.isStringEmpty(search)){
			search = "%"+search.trim()+"%";
		}else{
			search = null;
		}
		List<UserLoginLog> userLoginLogs = userLoginLogMapper.listByPage(userId, pageInfo, search);
		Integer total = userLoginLogMapper.listByPageCount(userId,search);
		pageInfo.setTotal(total);
		return new BasePageDTO<UserLoginLog>(pageInfo, userLoginLogs);
	}
}
