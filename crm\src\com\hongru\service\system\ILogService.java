package com.hongru.service.system;

import java.util.List;

import com.baomidou.mybatisplus.service.IService;
import com.hongru.base.BasePageDTO;
import com.hongru.entity.system.Log;
import com.hongru.support.page.PageInfo;

/**
 * 
*    
* 类名称：ILogService   
* 类描述：Log / 日志记录表 业务逻辑层接口      
* 创建人：hongru   
* 创建时间：2017-10-25 上午10:26:12   
*
 */
public interface ILogService extends IService<Log> {
	
	/**
	 * 根据分页信息/搜索内容查找日志记录列表
	 * @param pageInfo 分页信息
	 * @param search 搜索内容
	 * @return
	 */
	BasePageDTO<Log> listByPage(PageInfo pageInfo, String search);

	/**
	* 根据参数删除日志
	* @throws
	* <AUTHOR>
	* @create 2022/1/14 16:12
	* @return
	*/
	void removeLogByParam(Integer[] logIds);

	/**
	* 根据参数获取日志列表
	* @throws
	* <AUTHOR>
	* @create 2022/1/14 16:12
	* @return
	*/
	List<Log> listLogByParam(Integer[] logIds);
}
