<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/views/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑产品成本设计</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="${ctxStatic}/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="${ctxStatic}/css/admin.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2 class="header-title">编辑产品成本设计</h2>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" lay-filter="editForm">
                    <input type="hidden" name="serialNumber" value="${productCostDesign.serialNumber}">
                    
                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">年度<span style="color: red;">*</span></label>
                                <div class="layui-input-block">
                                    <input type="text" name="year" value="${productCostDesign.year}" required lay-verify="required" placeholder="请输入年度" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户简称<span style="color: red;">*</span></label>
                                <div class="layui-input-block">
                                    <select name="customerName" lay-verify="required" lay-filter="customerSelect">
                                        <option value="">请选择客户</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">产品分类<span style="color: red;">*</span></label>
                                <div class="layui-input-block">
                                    <select name="productCategory" lay-verify="required" lay-filter="productCategorySelect">
                                        <option value="">请选择产品分类</option>
                                        <option value="平角线">平角线</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">产品代码<span style="color: red;">*</span></label>
                                <div class="layui-input-block">
                                    <select name="productCode" lay-verify="required" lay-filter="productCodeSelect">
                                        <option value="">请选择产品代码</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">产品条码<span style="color: red;">*</span></label>
                                <div class="layui-input-block">
                                    <input type="text" name="productBarcode" value="${productCostDesign.productBarcode}" required lay-verify="required" placeholder="请输入产品条码" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">线盘名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="wireDiscName" value="${productCostDesign.wireDiscName}" placeholder="请输入线盘名称" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">产品尺寸</label>
                                <div class="layui-input-block">
                                    <input type="text" name="productSize" value="${productCostDesign.productSize}" placeholder="请输入产品尺寸" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">计算区分</label>
                                <div class="layui-input-block">
                                    <select name="calculationType">
                                        <option value="">请选择计算区分</option>
                                        <option value="标准" <c:if test="${productCostDesign.calculationType == '标准'}">selected</c:if>>标准</option>
                                        <option value="特殊" <c:if test="${productCostDesign.calculationType == '特殊'}">selected</c:if>>特殊</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">皮膜厚1</label>
                                <div class="layui-input-block">
                                    <input type="number" name="filmThickness1" value="${productCostDesign.filmThickness1}" placeholder="请输入皮膜厚1" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">皮膜厚2</label>
                                <div class="layui-input-block">
                                    <input type="number" name="filmThickness2" value="${productCostDesign.filmThickness2}" placeholder="请输入皮膜厚2" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">皮膜厚3</label>
                                <div class="layui-input-block">
                                    <input type="number" name="filmThickness3" value="${productCostDesign.filmThickness3}" placeholder="请输入皮膜厚3" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">皮膜厚4</label>
                                <div class="layui-input-block">
                                    <input type="number" name="filmThickness4" value="${productCostDesign.filmThickness4}" placeholder="请输入皮膜厚4" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">导体品目</label>
                                <div class="layui-input-block">
                                    <select name="conductorItem" lay-filter="conductorItemSelect">
                                        <option value="">请选择导体品目</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">线盘品目</label>
                                <div class="layui-input-block">
                                    <select name="wireDiscItem" lay-filter="wireDiscItemSelect">
                                        <option value="">请选择线盘品目</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">油漆品目1</label>
                                <div class="layui-input-block">
                                    <select name="paintItem1" lay-filter="paintItem1Select">
                                        <option value="">请选择油漆品目1</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">油漆品目2</label>
                                <div class="layui-input-block">
                                    <select name="paintItem2" lay-filter="paintItem2Select">
                                        <option value="">请选择油漆品目2</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">油漆品目3</label>
                                <div class="layui-input-block">
                                    <select name="paintItem3" lay-filter="paintItem3Select">
                                        <option value="">请选择油漆品目3</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">油漆品目4</label>
                                <div class="layui-input-block">
                                    <select name="paintItem4" lay-filter="paintItem4Select">
                                        <option value="">请选择油漆品目4</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">油漆品目5</label>
                                <div class="layui-input-block">
                                    <select name="paintItem5" lay-filter="paintItem5Select">
                                        <option value="">请选择油漆品目5</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">运输费</label>
                                <div class="layui-input-block">
                                    <select name="transportFee" lay-filter="transportFeeSelect">
                                        <option value="">请选择运输费</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">量试区分</label>
                                <div class="layui-input-block">
                                    <select name="quantityTestType">
                                        <option value="">请选择量试区分</option>
                                        <option value="量产" <c:if test="${productCostDesign.quantityTestType == '量产'}">selected</c:if>>量产</option>
                                        <option value="试作" <c:if test="${productCostDesign.quantityTestType == '试作'}">selected</c:if>>试作</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="save">确认</button>
                            <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
                            <button type="button" class="layui-btn layui-btn-normal" id="referenceBtn">参考</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="${ctxStatic}/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.$;

            // 初始化数据
            var currentData = {
                customerName: '${productCostDesign.customerName}',
                productCategory: '${productCostDesign.productCategory}',
                productCode: '${productCostDesign.productCode}',
                conductorItem: '${productCostDesign.conductorItem}',
                paintItem1: '${productCostDesign.paintItem1}',
                paintItem2: '${productCostDesign.paintItem2}',
                paintItem3: '${productCostDesign.paintItem3}',
                paintItem4: '${productCostDesign.paintItem4}',
                paintItem5: '${productCostDesign.paintItem5}',
                wireDiscItem: '${productCostDesign.wireDiscItem}',
                transportFee: '${productCostDesign.transportFee}'
            };

            loadCustomers();
            
            // 监听客户选择
            form.on('select(customerSelect)', function(data){
                var customerName = data.value;
                var productCategory = $('select[name="productCategory"]').val();
                if(customerName && productCategory){
                    loadProducts(customerName, productCategory);
                }
            });

            // 监听产品分类选择
            form.on('select(productCategorySelect)', function(data){
                var productCategory = data.value;
                var customerName = $('select[name="customerName"]').val();
                if(customerName && productCategory){
                    loadProducts(customerName, productCategory);
                }
                
                // 加载原料品目
                var year = $('input[name="year"]').val();
                if(year){
                    loadRawMaterialItems(year);
                    loadTransportUnitPrices(year);
                }
            });

            // 监听产品代码选择
            form.on('select(productCodeSelect)', function(data){
                var productCode = data.value;
                if(productCode){
                    loadProductDesignData(productCode);
                }
            });

            // 加载客户列表
            function loadCustomers(){
                $.get('${ctx}/productCostDesign/getCustomers', function(res){
                    if(res.code === 200){
                        var html = '<option value="">请选择客户</option>';
                        for(var i = 0; i < res.data.length; i++){
                            var selected = res.data[i].customerName === currentData.customerName ? 'selected' : '';
                            html += '<option value="' + res.data[i].customerName + '" ' + selected + '>' + res.data[i].customerName + '</option>';
                        }
                        $('select[name="customerName"]').html(html);
                        form.render('select');
                        
                        // 加载产品列表
                        if(currentData.customerName && currentData.productCategory){
                            loadProducts(currentData.customerName, currentData.productCategory);
                        }
                    }
                });
            }

            // 加载产品列表
            function loadProducts(customerName, productCategory){
                $.get('${ctx}/productCostDesign/getProducts', {
                    customerName: customerName,
                    productCategory: productCategory
                }, function(res){
                    if(res.code === 200){
                        var html = '<option value="">请选择产品代码</option>';
                        for(var i = 0; i < res.data.length; i++){
                            var selected = res.data[i].productCode === currentData.productCode ? 'selected' : '';
                            html += '<option value="' + res.data[i].productCode + '" ' + selected + '>' + res.data[i].productCode + '</option>';
                        }
                        $('select[name="productCode"]').html(html);
                        form.render('select');
                    }
                });
            }

            // 加载产品设计数据
            function loadProductDesignData(productCode){
                $.get('${ctx}/productCostDesign/getProductDesignData', {
                    productCode: productCode
                }, function(res){
                    if(res.code === 200 && res.data){
                        var data = res.data;
                        $('input[name="productBarcode"]').val(data.barcode);
                        $('input[name="wireDiscName"]').val(data.wireDiscName);
                        $('input[name="productSize"]').val(data.labelSizeName);
                        $('input[name="filmThickness1"]').val(data.filmThickness1Std);
                        $('input[name="filmThickness2"]').val(data.filmThickness2Std);
                        $('input[name="filmThickness3"]').val(data.filmThickness3Std);
                        $('input[name="filmThickness4"]').val(data.filmThickness4Std);
                    }
                });
            }

            // 加载原料项目单价
            function loadRawMaterialItems(year){
                // 导体原料
                $.get('${ctx}/productCostDesign/getRawMaterialItems', {
                    year: year,
                    materialType: '导体'
                }, function(res){
                    if(res.code === 200){
                        var html = '<option value="">请选择导体品目</option>';
                        for(var i = 0; i < res.data.length; i++){
                            var selected = res.data[i].itemCode === currentData.conductorItem ? 'selected' : '';
                            html += '<option value="' + res.data[i].itemCode + '" ' + selected + '>' + res.data[i].itemName + '</option>';
                        }
                        $('select[name="conductorItem"]').html(html);
                        form.render('select');
                    }
                });

                // 涂料原料
                $.get('${ctx}/productCostDesign/getRawMaterialItems', {
                    year: year,
                    materialType: '涂料'
                }, function(res){
                    if(res.code === 200){
                        var html = '<option value="">请选择油漆品目</option>';
                        for(var i = 0; i < res.data.length; i++){
                            html += '<option value="' + res.data[i].itemCode + '">' + res.data[i].itemName + '</option>';
                        }
                        
                        // 设置选中值
                        var html1 = html;
                        var html2 = html;
                        var html3 = html;
                        var html4 = html;
                        var html5 = html;
                        
                        html1 = html1.replace('value="' + currentData.paintItem1 + '"', 'value="' + currentData.paintItem1 + '" selected');
                        html2 = html2.replace('value="' + currentData.paintItem2 + '"', 'value="' + currentData.paintItem2 + '" selected');
                        html3 = html3.replace('value="' + currentData.paintItem3 + '"', 'value="' + currentData.paintItem3 + '" selected');
                        html4 = html4.replace('value="' + currentData.paintItem4 + '"', 'value="' + currentData.paintItem4 + '" selected');
                        html5 = html5.replace('value="' + currentData.paintItem5 + '"', 'value="' + currentData.paintItem5 + '" selected');
                        
                        $('select[name="paintItem1"]').html(html1);
                        $('select[name="paintItem2"]').html(html2);
                        $('select[name="paintItem3"]').html(html3);
                        $('select[name="paintItem4"]').html(html4);
                        $('select[name="paintItem5"]').html(html5);
                        form.render('select');
                    }
                });

                // 线盘原料
                $.get('${ctx}/productCostDesign/getRawMaterialItems', {
                    year: year,
                    materialType: '线盘'
                }, function(res){
                    if(res.code === 200){
                        var html = '<option value="">请选择线盘品目</option>';
                        for(var i = 0; i < res.data.length; i++){
                            var selected = res.data[i].itemCode === currentData.wireDiscItem ? 'selected' : '';
                            html += '<option value="' + res.data[i].itemCode + '" ' + selected + '>' + res.data[i].itemName + '</option>';
                        }
                        $('select[name="wireDiscItem"]').html(html);
                        form.render('select');
                    }
                });
            }

            // 加载运费单价
            function loadTransportUnitPrices(year){
                $.get('${ctx}/productCostDesign/getTransportUnitPrices', {
                    year: year
                }, function(res){
                    if(res.code === 200){
                        var html = '<option value="">请选择运输费</option>';
                        for(var i = 0; i < res.data.length; i++){
                            var selected = res.data[i].region === currentData.transportFee ? 'selected' : '';
                            html += '<option value="' + res.data[i].region + '" ' + selected + '>' + res.data[i].region + '</option>';
                        }
                        $('select[name="transportFee"]').html(html);
                        form.render('select');
                    }
                });
            }

            // 初始化加载下拉数据
            var year = $('input[name="year"]').val();
            if(year){
                loadRawMaterialItems(year);
                loadTransportUnitPrices(year);
            }

            // 监听年度输入，加载下拉数据
            $('input[name="year"]').on('blur', function(){
                var year = $(this).val();
                if(year){
                    loadRawMaterialItems(year);
                    loadTransportUnitPrices(year);
                }
            });

            // 提交表单
            form.on('submit(save)', function(data){
                $.post('${ctx}/productCostDesign/edit', data.field, function(res){
                    if(res.code === 200){
                        layer.msg('更新成功', {icon: 1}, function(){
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg || '更新失败', {icon: 2});
                    }
                });
                return false;
            });

            // 取消按钮
            $('#cancelBtn').on('click', function(){
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            // 参考按钮
            $('#referenceBtn').on('click', function(){
                var year = $('input[name="year"]').val();
                var productCode = $('select[name="productCode"]').val();
                var productBarcode = $('input[name="productBarcode"]').val();
                var customerName = $('select[name="customerName"]').val();
                
                if(!year || !productCode || !productBarcode || !customerName){
                    layer.msg('请先填写年度、客户简称、产品代码和产品条码');
                    return;
                }
                
                $.get('${ctx}/productCostDesign/getProductManufacturingDesigns', {
                    year: year,
                    productCode: productCode,
                    productBarcode: productBarcode,
                    customerName: customerName
                }, function(res){
                    if(res.code === 200 && res.data && res.data.length > 0){
                        var data = res.data[0]; // 取第一条数据作为参考
                        
                        // 填充表单数据
                        $('input[name="wireDiscName"]').val(data.wireDiscName);
                        $('input[name="productSize"]').val(data.productSize);
                        $('input[name="filmThickness1"]').val(data.filmThickness1);
                        $('input[name="filmThickness2"]').val(data.filmThickness2);
                        $('input[name="filmThickness3"]').val(data.filmThickness3);
                        $('input[name="filmThickness4"]').val(data.filmThickness4);
                        
                        $('select[name="conductorItem"]').val(data.conductorMaterial);
                        $('select[name="paintItem1"]').val(data.paint1);
                        $('select[name="paintItem2"]').val(data.paint2);
                        $('select[name="paintItem3"]').val(data.paint3);
                        $('select[name="paintItem4"]').val(data.paint4);
                        $('select[name="paintItem5"]').val(data.paint5);
                        $('select[name="wireDiscItem"]').val(data.wireDiscMaterial);
                        $('select[name="transportFee"]').val(data.transportFee);
                        $('select[name="quantityTestType"]').val(data.quantityTestType);
                        $('select[name="calculationType"]').val(data.calculationType);
                        
                        form.render('select');
                        layer.msg('参考数据加载成功');
                    } else {
                        layer.msg('未找到参考数据');
                    }
                });
            });
        });
    </script>
</body>
</html>
