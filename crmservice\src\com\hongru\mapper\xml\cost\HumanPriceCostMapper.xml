<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.HumanPriceCostMapper">

    <sql id="humanPriceCost_sql">
		hum.[流水号] AS costId,hum.[状态] AS state,hum.[导入标识] AS importId,hum.[工种] AS workType,hum.[年月] AS yearMonth,hum.[年] AS year,hum.[月] AS month,hum.[人件费区分] AS humanPriceCode,
		hum.[人件费] AS humanPrice,hum.[创建人标识] AS creatorId,hum.[创建人姓名] AS creatorName,hum.[创建时间] AS createdTime,
		hum.[最后修改人标识] AS lastModifierId,hum.[最后修改人姓名] AS lastModifierName,hum.[最后修改时间] AS lastModifiedTime
	</sql>

    <insert id="insertHumanPriceCost" parameterType="com.hongru.entity.cost.HumanPriceCost">
        INSERT INTO [CostPrice].[dbo].[人件费用表]
		(
		[状态],
		[导入标识],
		[工种],
		[年月],
		[年],
		[月],
		[人件费区分],
		[人件费],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[最后修改人标识],
		[最后修改人姓名],
		[最后修改时间]
		)VALUES(
		#{humanPriceCost.state},
		#{humanPriceCost.importId},
		#{humanPriceCost.workType},
		#{humanPriceCost.yearMonth},
		#{humanPriceCost.year},
		#{humanPriceCost.month},
		#{humanPriceCost.humanPriceCode},
		#{humanPriceCost.humanPrice},
		#{humanPriceCost.creatorId},
		#{humanPriceCost.creatorName},
		#{humanPriceCost.createdTime},
		#{humanPriceCost.lastModifierId},
		#{humanPriceCost.lastModifierName},
		#{humanPriceCost.lastModifiedTime}
		)
    </insert>

    <select id="selectByCostId" resultType="com.hongru.entity.cost.HumanPriceCost">
        SELECT
        <include refid="humanPriceCost_sql"/>
        FROM [CostPrice].[dbo].[人件费用表] hum
        <where>
            hum.[状态] != 9
            <if test="costId != null">
                AND hum.[流水号] = #{costId}
            </if>
        </where>
    </select>

    <select id="selectByYearMonth" resultType="com.hongru.entity.cost.HumanPriceCost">
        SELECT
        <include refid="humanPriceCost_sql"/>
        FROM [CostPrice].[dbo].[人件费用表] hum
        <where>
            hum.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND hum.[年月] = #{yearMonth}
            </if>
        </where>
    </select>

    <select id="getHumanPriceCostByYearMonth" resultType="com.hongru.entity.cost.HumanPriceCost">
        SELECT
        <include refid="humanPriceCost_sql"/>
        FROM [CostPrice].[dbo].[人件费用表] hum
        <where>
            hum.[状态] != 9
			<if test="startYearMonth != null and startYearMonth != ''">
                AND hum.[年月] >= #{startYearMonth}
            </if>
			<if test="endYearMonth != null and endYearMonth != ''">
                AND #{endYearMonth} >= hum.[年月]
            </if>
        </where>
    </select>

	<select id="listCostPricePage" resultType="com.hongru.entity.cost.HumanPriceCost">
		SELECT
		<include refid="humanPriceCost_sql"/>
		FROM [CostPrice].[dbo].[人件费用表] hum
		<where>
			hum.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND hum.[年月] = #{yearMonth}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY hum.[年月] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listCostPricePageCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[人件费用表] hum
		<where>
			hum.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND hum.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<update id="updateState">
		UPDATE [CostPrice].[dbo].[人件费用表]
		SET [状态] = #{state}
		WHERE [流水号] = #{costId}
	</update>
</mapper>