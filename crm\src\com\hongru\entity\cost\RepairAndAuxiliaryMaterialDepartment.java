package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("补辅部门列表")//CostPrice
public class RepairAndAuxiliaryMaterialDepartment {
	/* 部门属性-直接部门 */
	public static final String STATE_1 = "1";
	/* 部门属性-辅助部门 */
	public static final String STATE_2 = "2";
	/* 部门属性-共通部门 */
	public static final String STATE_3 = "3";
	/* 部门属性-特殊部门 */
	public static final String STATE_4 = "4";
	
	/* 类别-MW */
	public static final String CATEGORY_1 = "1";
	/* 类别-UF */
	public static final String CATEGORY_2= "2";
	
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int id;
	/* 年度 */
	protected String year;
	/* 部门 */
	protected String department;
	/* 部门属性 */
	protected String departmentAttributes;
	/* 类别 */
	protected String category;
	/* 分配部门 */
	protected String assignDepartment;
	/* 比例 */
	protected String ratio;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	@TableField(exist = false)
	protected Integer supplierId;
	
	/* 部门属性标记Str */
	@TableField(exist = false)
	protected String departmentAttributesStr;
	/* 类别Str */
	@TableField(exist = false)
	protected String categoryStr;
	
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getDepartmentAttributes() {
		return departmentAttributes;
	}

	public void setDepartmentAttributes(String departmentAttributes) {
		this.departmentAttributes = departmentAttributes;
	}

	public String getCategory() {
		return category;
	}

	public String getAssignDepartment() {
		return assignDepartment;
	}

	public void setAssignDepartment(String assignDepartment) {
		this.assignDepartment = assignDepartment;
	}

	public String getRatio() {
		return ratio;
	}

	public void setRatio(String ratio) {
		this.ratio = ratio;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public Integer getSupplierId() {
		return supplierId;
	}
	
	public void setSupplierId(Integer supplierId) {
		this.supplierId = supplierId;
	}

	public String getDepartmentAttributesStr() {
		return departmentAttributesStr;
	}

	public void setDepartmentAttributesStr(String departmentAttributesStr) {
		this.departmentAttributesStr = departmentAttributesStr;
	}

	public String getCategoryStr() {
		return categoryStr;
	}

	public void setCategoryStr(String categoryStr) {
		this.categoryStr = categoryStr;
	}
	
}