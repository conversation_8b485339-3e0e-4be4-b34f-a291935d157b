package com.hongru.common.util.poiExcelExport;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;

import com.hongru.common.util.StringUtil;

public class ExcelExportUtil {

    /**
     *
     * @Title: exportManySheetExcel
     * @Description: 可生成单个、多个sheet
     * @param @param file 导出文件路径
     * @param @param mysheets
     * @return void
     * @throws
     */
    @SuppressWarnings("resource")
	public static void exportManySheetExcel(OutputStream out, List<ExcelExp> mysheets){

        HSSFWorkbook wb = new HSSFWorkbook();//创建工作薄
        List<ExcelExp> sheets = mysheets;

        //表头样式
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        //字体样式
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setFontName("微软雅黑");
        fontStyle.setFontHeightInPoints((short)12);
        style.setFont(fontStyle);

        for(ExcelExp excel: sheets){
            //新建一个sheet
            HSSFSheet sheet = wb.createSheet(excel.getFileName());//获取该sheet名称

            String[] handers = excel.getHanders();//获取sheet的标题名
            HSSFRow rowFirst = sheet.createRow(0);//第一个sheet的第一行为标题
            //写标题
            for(int i=0;i<handers.length;i++){
                //获取第一行的每个单元格
                HSSFCell cell = rowFirst.createCell(i);
                //往单元格里写数据
                cell.setCellValue(handers[i]);
                cell.setCellStyle(style); //加样式
                sheet.setColumnWidth(i, 4000); //设置每列的列宽
            }

            //写数据集
            List<String[]> dataset = excel.getDataset();
            //数字列
            Integer[] numericColumnArr = excel.getNumericColumnArr();
            for(int i=0;i<dataset.size();i++){
                String[] data = dataset.get(i);//获取该对象
                //创建数据行
                HSSFRow row = sheet.createRow(i+1);
                for(int j=0;j<data.length;j++){
                    short columnType = 0; //0:String 1:int
                    for(Integer numericColumn : numericColumnArr){
                        if(j == numericColumn){
                            columnType = 1;
                        }
                    }
                    if(columnType == 0){
                        //设置对应单元格的值
                        row.createCell(j).setCellType(CellType.STRING);
                        row.createCell(j).setCellValue(data[j]);
                    }else if(columnType == 1){
                        if(!StringUtil.isStringEmpty(data[j])){
                            //设置对应单元格的值
                            row.createCell(j).setCellType(CellType.NUMERIC);
                            row.createCell(j).setCellValue(Integer.parseInt(data[j]));
                        }else{
                            //设置对应单元格的值
                            row.createCell(j).setCellType(CellType.STRING);
                            row.createCell(j).setCellValue(data[j]);
                        }
                    }

                }
            }
        }

        // 写文件
        try {
            out.flush();
            wb.write(out);
            out.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
    * 导出，可导出多个sheet，第一行合并单元格
    * @param out
    * @param mysheets
    * @param firstValue
    * @throws
    * <AUTHOR>
    * @create 2021/8/26 11:17
    * @return void
    */
    @SuppressWarnings("resource")
    public static void exportManySheetExcelV2(OutputStream out, List<ExcelExp> mysheets,String firstValue){
        HSSFWorkbook wb = new HSSFWorkbook();//创建工作薄
        List<ExcelExp> sheets = mysheets;
        //表头样式
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        //字体样式
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setFontName("微软雅黑");
        fontStyle.setFontHeightInPoints((short)12);
        style.setFont(fontStyle);
        for(ExcelExp excel: sheets){
            //新建一个sheet
            HSSFSheet sheet = wb.createSheet(excel.getFileName());//获取该sheet名称
            String[] handers = excel.getHanders();//获取sheet的标题名

            //创建合并单元格的第一行标题
            HSSFRow rowFirst = sheet.createRow(0);
            //第一行第一格
            HSSFCell cell1_1 = rowFirst.createCell(0);
            //往单元格里写数据
            cell1_1.setCellValue(firstValue);
            //加样式
            cell1_1.setCellStyle(style);

            //sheet的第二行为标题
            HSSFRow rowSecond = sheet.createRow(1);
            //写标题
            for(int i=0;i<handers.length;i++){
                //获取第一行的每个单元格
                HSSFCell cell = rowSecond.createCell(i);
                //往单元格里写数据
                cell.setCellValue(handers[i]);
                cell.setCellStyle(style); //加样式
                sheet.setColumnWidth(i, 4000); //设置每列的列宽
            }

            //写数据集
            List<String[]> dataset = excel.getDataset();
            //数字列
            Integer[] numericColumnArr = excel.getNumericColumnArr();
            for(int i=0;i<dataset.size();i++){
                String[] data = dataset.get(i);//获取该对象
                //创建数据行
                HSSFRow row = sheet.createRow(i+2);
                for(int j=0;j<data.length;j++){
                    short columnType = 0; //0:String 1:int
                    for(Integer numericColumn : numericColumnArr){
                        if(j == numericColumn){
                            columnType = 1;
                        }
                    }
                    if(columnType == 0){
                        //设置对应单元格的值
                        row.createCell(j).setCellType(CellType.STRING);
                        row.createCell(j).setCellValue(data[j]);
                    }else if(columnType == 1){
                        if(!StringUtil.isStringEmpty(data[j])){
                            //设置对应单元格的值
                            row.createCell(j).setCellType(CellType.NUMERIC);
                            row.createCell(j).setCellValue(Integer.parseInt(data[j]));
                        }else{
                            //设置对应单元格的值
                            row.createCell(j).setCellType(CellType.STRING);
                            row.createCell(j).setCellValue(data[j]);
                        }
                    }

                }
            }

            //合并单元格（第一行到第一行，第一列到第六列）
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));
        }
        // 写文件
        try {
            out.flush();
            wb.write(out);
            out.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
    * 导出超细（专用）
    * @throws
    * <AUTHOR>
    * @create 2022/11/16 12:00
    * @return
    */
    @SuppressWarnings("resource")
    public static void exportManySheetExcelV3(OutputStream out, List<ExcelExp> mysheets,String firstValue,String type,String size,String client){
        HSSFWorkbook wb = new HSSFWorkbook();//创建工作薄
        List<ExcelExp> sheets = mysheets;
        //表头样式
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        //字体样式
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setFontName("微软雅黑");
        fontStyle.setFontHeightInPoints((short)16);
        fontStyle.setBold(true);
        style.setFont(fontStyle);

        //表头样式
        HSSFCellStyle style2 = wb.createCellStyle();
        style2.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        //字体样式
        HSSFFont fontStyle2 = wb.createFont();
        fontStyle2.setFontName("微软雅黑");
        fontStyle2.setFontHeightInPoints((short)14);
        fontStyle2.setBold(true);
        style2.setFont(fontStyle2);

        //表头样式
        HSSFCellStyle style3 = wb.createCellStyle();
        style3.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        //字体样式
        HSSFFont fontStyle3 = wb.createFont();
        fontStyle3.setFontName("微软雅黑");
        fontStyle3.setFontHeightInPoints((short)12);
        style3.setFont(fontStyle3);

        for(ExcelExp excel: sheets){
            //新建一个sheet
            HSSFSheet sheet = wb.createSheet(excel.getFileName());//获取该sheet名称
            String[] handers = excel.getHanders();//获取sheet的标题名

            //创建合并单元格的第一行标题
            HSSFRow rowFirst = sheet.createRow(0);
            //第一行第一格
            HSSFCell cell1_1 = rowFirst.createCell(0);
            //往单元格里写数据
            cell1_1.setCellValue(firstValue);
            //加样式
            cell1_1.setCellStyle(style);

            //写单独数据
            //创建第二行单独数据列
            HSSFRow rowSecond = sheet.createRow(1);
            //第二行第一格
            HSSFCell cell2_1 = rowSecond.createCell(0);
            //往单元格里写数据
            cell2_1.setCellValue(type);
            //加样式
            cell2_1.setCellStyle(style2);

            //第二行第三格
            HSSFCell cell2_3 = rowSecond.createCell(2);
            //往单元格里写数据
            cell2_3.setCellValue(size);
            //加样式
            cell2_3.setCellStyle(style2);
            //第二行第六格
            HSSFCell cell2_6= rowSecond.createCell(5);
            //往单元格里写数据
            cell2_6.setCellValue(client);
            //加样式
            cell2_6.setCellStyle(style2);

            //sheet的第二行为标题
            HSSFRow rowThird = sheet.createRow(2);
            //写标题
            for(int i=0;i<handers.length;i++){
                //获取第一行的每个单元格
                HSSFCell cell = rowThird.createCell(i);
                //往单元格里写数据
                cell.setCellValue(handers[i]);
                cell.setCellStyle(style3); //加样式
                sheet.setColumnWidth(i, 4000); //设置每列的列宽
            }

            //写数据集
            List<String[]> dataset = excel.getDataset();
            //数字列
            Integer[] numericColumnArr = excel.getNumericColumnArr();
            for(int i=0;i<dataset.size();i++){
                String[] data = dataset.get(i);//获取该对象
                //创建数据行
                HSSFRow row = sheet.createRow(i+3);
                for(int j=0;j<data.length;j++){
                    short columnType = 0; //0:String 1:int
                    for(Integer numericColumn : numericColumnArr){
                        if(j == numericColumn){
                            columnType = 1;
                        }
                    }
                    if(columnType == 0){
                        //设置对应单元格的值
                        row.createCell(j).setCellType(CellType.STRING);
                        row.createCell(j).setCellValue(data[j]);
                    }else if(columnType == 1){
                        if(!StringUtil.isStringEmpty(data[j])){
                            //设置对应单元格的值
                            row.createCell(j).setCellType(CellType.NUMERIC);
                            row.createCell(j).setCellValue(Integer.parseInt(data[j]));
                        }else{
                            //设置对应单元格的值
                            row.createCell(j).setCellType(CellType.STRING);
                            row.createCell(j).setCellValue(data[j]);
                        }
                    }
                }
            }

            //合并单元格（第一行到第一行，第一列到第六列）
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, handers.length-1));
            //合并单元格（第二行到第二行，第一列到第二列）
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 1));
            //合并单元格（第二行到第二行，第三列到第五列）
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 2, 4));
            //合并单元格（第三行到第三行，第六列到第六列）
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 5, handers.length-1));
        }
        // 写文件
        try {
            out.flush();
            wb.write(out);
            out.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
    * 导出日在库查询（专用）
    * @throws
    * <AUTHOR>
    * @create 2022/12/21 16:22
    * @return
    */
    @SuppressWarnings("resource")
    public static void exportManySheetExcelV4(OutputStream out, List<ExcelExp> mysheets,String recordNo,String revisionNo,String documentNo,String printTime){
        HSSFWorkbook wb = new HSSFWorkbook();//创建工作薄
        List<ExcelExp> sheets = mysheets;
        //字体样式1
        HSSFCellStyle style = wb.createCellStyle();
        // 创建一个居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        //字体样式
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setFontName("微软雅黑");
        fontStyle.setFontHeightInPoints((short)10);
        style.setFont(fontStyle);

        //字体样式2
        HSSFCellStyle style2 = wb.createCellStyle();
        // 创建一个居中格式
        style2.setAlignment(HorizontalAlignment.CENTER);
        //字体样式
        HSSFFont fontStyle2 = wb.createFont();
        fontStyle2.setFontName("微软雅黑");
        fontStyle2.setFontHeightInPoints((short)12);
        style2.setFont(fontStyle2);

        //字体样式3
        HSSFCellStyle style3 = wb.createCellStyle();
        // 创建一个居中格式
        style3.setAlignment(HorizontalAlignment.CENTER);
        //字体样式
        HSSFFont fontStyle3 = wb.createFont();
        fontStyle3.setFontName("微软雅黑");
        fontStyle3.setFontHeightInPoints((short)12);
        fontStyle3.setBold(true);
        style3.setFont(fontStyle3);

        //字体样式4
        HSSFCellStyle style4 = wb.createCellStyle();
        // 创建一个居中格式
        style4.setAlignment(HorizontalAlignment.CENTER);
        //字体样式
        HSSFFont fontStyle4 = wb.createFont();
        fontStyle4.setFontName("微软雅黑");
        fontStyle4.setFontHeightInPoints((short)16);
        fontStyle4.setBold(true);
        style4.setFont(fontStyle4);

        for(ExcelExp excel: sheets){
            //新建一个sheet
            HSSFSheet sheet = wb.createSheet(excel.getFileName());//获取该sheet名称
            String[] handers = excel.getHanders();//获取sheet的标题名

            //写单独数据
            //创建第零行单独数据列
            HSSFRow rowZero = sheet.createRow(0);
            //第零行第零格
            HSSFCell cell0_0 = rowZero.createCell(0);
            //往单元格里写数据
            cell0_0.setCellValue("");

            //写单独数据
            //创建第一行单独数据列
            HSSFRow rowFirst = sheet.createRow(1);
            //第一行第八格
            HSSFCell cell1_8 = rowFirst.createCell(8);
            //往单元格里写数据
            cell1_8.setCellValue(recordNo);
            //加样式
            cell1_8.setCellStyle(style);

            //写单独数据
            //创建第二行单独数据列
            HSSFRow rowSecond = sheet.createRow(2);
            //第二行第八格
            HSSFCell cell2_8 = rowSecond.createCell(8);
            //往单元格里写数据
            cell2_8.setCellValue(revisionNo);
            //加样式
            cell2_8.setCellStyle(style);

            //写单独数据
            //创建第三行单独数据列
            HSSFRow rowThird = sheet.createRow(3);
            //第三行第八格
            HSSFCell cell3_8= rowThird.createCell(8);
            //往单元格里写数据
            cell3_8.setCellValue(documentNo);
            //加样式
            cell3_8.setCellStyle(style);

            //写单独数据
            //创建第四行单独数据列
            HSSFRow rowFourth = sheet.createRow(4);
            //第四行第七格
            HSSFCell cell4_3= rowFourth.createCell(3);
            //往单元格里写数据
            cell4_3.setCellValue("日在库制品查询");
            //加样式
            cell4_3.setCellStyle(style4);

            //第四行第七格
            HSSFCell cell4_7= rowFourth.createCell(7);
            //往单元格里写数据
            cell4_7.setCellValue("日期：");
            //加样式
            cell4_7.setCellStyle(style2);

            //第四行第七格
            HSSFCell cell4_8= rowFourth.createCell(8);
            //往单元格里写数据
            cell4_8.setCellValue(printTime);
            //加样式
            cell4_8.setCellStyle(style3);

            //sheet的第二行为标题
            HSSFRow rowFifth = sheet.createRow(5);
            //写标题
            for(int i=0;i<handers.length;i++){
                //获取第一行的每个单元格
                HSSFCell cell = rowFifth.createCell(i);
                //往单元格里写数据
                cell.setCellValue(handers[i]);
                cell.setCellStyle(style2); //加样式
                sheet.setColumnWidth(i, 4000); //设置每列的列宽
            }

            //写数据集
            List<String[]> dataset = excel.getDataset();
            //数字列
            Integer[] numericColumnArr = excel.getNumericColumnArr();
            for(int i=0;i<dataset.size();i++){
                String[] data = dataset.get(i);//获取该对象
                //创建数据行
                HSSFRow row = sheet.createRow(i+6);
                for(int j=0;j<data.length;j++){
                    short columnType = 0; //0:String 1:int
                    for(Integer numericColumn : numericColumnArr){
                        if(j == numericColumn){
                            columnType = 1;
                        }
                    }
                    if(columnType == 0){
                        //设置对应单元格的值
                        row.createCell(j).setCellType(CellType.STRING);
                        row.createCell(j).setCellValue(data[j]);
                    }else if(columnType == 1){
                        if(!StringUtil.isStringEmpty(data[j])){
                            //设置对应单元格的值
                            row.createCell(j).setCellType(CellType.NUMERIC);
                            row.createCell(j).setCellValue(Integer.parseInt(data[j]));
                        }else{
                            //设置对应单元格的值
                            row.createCell(j).setCellType(CellType.STRING);
                            row.createCell(j).setCellValue(data[j]);
                        }
                    }
                }
            }

            //合并单元格（第一行到第一行，第一列到第六列）
            sheet.addMergedRegion(new CellRangeAddress(4, 4, 3, 6));
        }
        // 写文件
        try {
            out.flush();
            wb.write(out);
            out.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}