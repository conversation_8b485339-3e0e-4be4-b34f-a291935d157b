package com.hongru.common.util;

import java.text.SimpleDateFormat;
import java.util.Date;

public class CheckSign {

	/**
	 * 根据秘钥生成签名
	 * @param APIKey
	 * @return
	 * <AUTHOR>
	 * @create 2017年12月19日 上午10:33:46
	 * 生成方法：取当前日期（20171208）进行md5加密后取后8位，然后拼接上密钥再进行md5加密
	 */
	public static String createSign(String APIKey) {
		Date d = new Date();  
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");  
        String dateNowStr = sdf.format(d);
        String dateMd5 = Md5Encrypt.md5(dateNowStr);
        String endStr = dateMd5.substring(dateMd5.length()-8,dateMd5.length())+APIKey;
        String sign = Md5Encrypt.md5(endStr);
        System.out.println(sign);
		return sign;
	}
	
	/**
	 * 校验传过来的签名是否正确
	 * @param sign
	 * @param APIKey
	 * @return
	 * <AUTHOR>
	 * @create 2017年12月19日 上午10:36:19
	 * 实现说明：true-正确；false-错误
	 */
	public static boolean isSignRight(String sign, String APIKey) {
		String newSign = createSign(APIKey);
		boolean isRight = newSign.equals(sign);
		return isRight;
	}
}