<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>编辑</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/setting/proportion/modify" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                     <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" ><span class="star">*</span>年度:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="year"  name="year"  required=""  lay-verify="required" value="${proportion.year}"  />
                        </div>
                    </div>
                   <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>部门:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="departmentCode" name="departmentCode" value="${proportion.departmentCode}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EM:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="em" name="em" value="${proportion.em}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ef" name="ef" value="${proportion.ef}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>UF:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="uf" name="uf" value="${proportion.uf}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>ER:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="er" name="er" value="${proportion.er}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eh" name="eh" value="${proportion.eh}"/>
                        </div>
                    </div>
                 <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="statId" name="statId" value="${proportion.statId}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/setting/proportion_modify.js?time=1"></script>
</myfooter>
</body>
</html>
