<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.MaterialSummaryMapper">
    <sql id="materialSummary_sql">
		mat.[流水号] AS costId,mat.[状态] AS state,mat.[导入标识] AS importId,mat.[年月] AS yearMonth,mat.[年] AS year,mat.[月] AS month,
		mat.[性质] AS nature,mat.[摘要] AS abstracts,mat.[金额] AS amount,
		mat.[EM] AS em,mat.[EF] AS ef,mat.[UF] AS uf,mat.[ER] AS er,mat.[EH] AS eh,
		mat.[创建人标识] AS creatorId,mat.[创建人姓名] AS creatorName,mat.[创建时间] AS createdTime
	</sql>

	<insert id="insertAuxiliaryMaterialSummary" parameterType="com.hongru.entity.cost.AuxiliaryMaterialSummary">
		INSERT INTO [CostPrice].[dbo].[辅材费用汇总表]
		(
		[状态],
		[导入标识],
		[年月],
		[年],
		[月],
		[性质],
		[摘要],
		[金额],
		[EM],
		[EF],
		[UF],
		[ER],
		[EH],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{auxiliaryMaterialSummary.state},
		#{auxiliaryMaterialSummary.importId},
		#{auxiliaryMaterialSummary.yearMonth},
		#{auxiliaryMaterialSummary.year},
		#{auxiliaryMaterialSummary.month},
		#{auxiliaryMaterialSummary.nature},
		#{auxiliaryMaterialSummary.abstracts},
		#{auxiliaryMaterialSummary.amount},
		#{auxiliaryMaterialSummary.em},
		#{auxiliaryMaterialSummary.ef},
		#{auxiliaryMaterialSummary.uf},
		#{auxiliaryMaterialSummary.er},
		#{auxiliaryMaterialSummary.eh},
		#{auxiliaryMaterialSummary.creatorId},
		#{auxiliaryMaterialSummary.creatorName},
		#{auxiliaryMaterialSummary.createdTime}
		)
	</insert>

	<select id="selectByCostId" resultType="com.hongru.entity.cost.AuxiliaryMaterialSummary">
		SELECT
		<include refid="materialSummary_sql"/>
		FROM [CostPrice].[dbo].[辅材费用汇总表] mat
		<where>
			mat.[状态] != 9
			<if test="costId != null">
				AND mat.[流水号] = #{costId}
			</if>
		</where>
	</select>

	<select id="listAuxiliaryMaterialSummaryByYearMonth" resultType="com.hongru.entity.cost.AuxiliaryMaterialSummary">
		SELECT
		<include refid="materialSummary_sql"/>
		FROM [CostPrice].[dbo].[辅材费用汇总表] mat
		<where>
			mat.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND mat.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<select id="listMaterialSummaryPage" resultType="com.hongru.entity.cost.AuxiliaryMaterialSummary">
		SELECT
		<include refid="materialSummary_sql"/>
		FROM [CostPrice].[dbo].[辅材费用汇总表] mat
		<where>
			mat.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND mat.[年月] = #{yearMonth}
			</if>
			<if test="nature != null and nature != ''">
				AND mat.[性质] = #{nature}
			</if>
			<if test="abstracts != null and abstracts != ''">
				AND mat.[摘要] = #{abstracts}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY mat.[年月] DESC, mat.[性质] DESC, mat.[摘要]
		</if>
	</select>

	<select id="listMaterialSummaryPageCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[辅材费用汇总表] mat
		<where>
			mat.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND mat.[年月] = #{yearMonth}
			</if>
			<if test="nature != null and nature != ''">
				AND mat.[性质] = #{nature}
			</if>
			<if test="abstracts != null and abstracts != ''">
				AND mat.[摘要] = #{abstracts}
			</if>
		</where>
	</select>

	<update id="updateState">
		UPDATE [CostPrice].[dbo].[辅材费用汇总表]
		SET [状态] = #{state}
		WHERE [流水号] = #{costId}
	</update>

	<update id="updateAuxiliaryMaterialSummary">
		UPDATE [CostPrice].[dbo].[辅材费用汇总表]
		<set>
			<if test="auxiliaryMaterialSummary.yearMonth != null and auxiliaryMaterialSummary.yearMonth != ''">
				[年月] = #{auxiliaryMaterialSummary.yearMonth},
			</if>
			<if test="auxiliaryMaterialSummary.year != 0">
				[年] = #{auxiliaryMaterialSummary.year},
			</if>
			<if test="auxiliaryMaterialSummary.month != 0">
				[月] = #{auxiliaryMaterialSummary.month},
			</if>
			<if test="auxiliaryMaterialSummary.nature != null and auxiliaryMaterialSummary.nature != ''">
				[性质] = #{auxiliaryMaterialSummary.nature},
			</if>
			<if test="auxiliaryMaterialSummary.abstracts != null and auxiliaryMaterialSummary.abstracts != ''">
				[摘要] = #{auxiliaryMaterialSummary.abstracts},
			</if>
			<if test="auxiliaryMaterialSummary.amount != null">
				[金额] = #{auxiliaryMaterialSummary.amount},
			</if>
			<if test="auxiliaryMaterialSummary.em != null">
				[EM] = #{auxiliaryMaterialSummary.em},
			</if>
			<if test="auxiliaryMaterialSummary.ef != null">
				[EF] = #{auxiliaryMaterialSummary.ef},
			</if>
			<if test="auxiliaryMaterialSummary.uf != null">
				[UF] = #{auxiliaryMaterialSummary.uf},
			</if>
			<if test="auxiliaryMaterialSummary.er != null">
				[ER] = #{auxiliaryMaterialSummary.er},
			</if>
			<if test="auxiliaryMaterialSummary.eh != null">
				[EH] = #{auxiliaryMaterialSummary.eh},
			</if>
		</set>
		WHERE [流水号] = #{auxiliaryMaterialSummary.costId}
	</update>
</mapper>