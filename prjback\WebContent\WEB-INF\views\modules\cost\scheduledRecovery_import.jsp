<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8" %>
<%@ include file="/WEB-INF/layouts/base.jsp" %>
<!DOCTYPE HTML>
<html>
<head>
    <title></title>
</head>
<body>
<form class="layui-form hr-form-add" action="" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">

                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>区分:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="distinguish" name="distinguish" lay-verify="required" required>
                                <option value="">请选择</option>
                                <option value="1">EM</option>
                                <option value="2">EF</option>
                                <option value="3">EF09</option>
                                <option value="4">ER</option>
                                <option value="5">EH</option>
                                <option value="6">UF</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>日期:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="dateStr" name="dateStr" placeholder="" autocomplete="off" lay-verify="required">
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预定直接部门回收计算导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForZhiJie">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryDirectRecyclingImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForZhiJie" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForZhiJie" readonly></textarea>
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预定辅助部门回收计算（补修费、辅材费）导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForFuZhu">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryAuxiliaryRecyclingImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForFuZhu" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForFuZhu" readonly></textarea>
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预定辅助部门回收计算（人工费）导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForFuZhu2">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryAuxiliaryRecyclingArtificialImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForFuZhu2" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForFuZhu2" readonly></textarea>
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预定捆包费导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForKunBao">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryBookingBundlingImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForKunBao" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForKunBao" readonly></textarea>
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预定线盘回收计算导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForXianPanHuiShou">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryReservedRecyclingImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForXianPanHuiShou" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForXianPanHuiShou" readonly></textarea>
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预定屑使用量导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForXie">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryReserveCrumbsUsageImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForXie" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForXie" readonly></textarea>
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预定芯线使用量导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForXinXian">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryReserveCoreWireUsageImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForXinXian" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForXinXian" readonly></textarea>
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预定油漆导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForYouQi">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryReservePaintUsageImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForYouQi" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForYouQi" readonly></textarea>
                        </div>
                    </div>
                </div>

                <blockquote class="layui-elem-quote">预订运费导入</blockquote>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">文件:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            <input type="file" class="form-control" name="filename" id="excelFileForYunFei">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star"></span>导入注意事项:</label>
                        <div class="layui-input-block" style="display: flex;align-items: center;align-content: center">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/scheduledRecoveryReserveFreightImport.xls" style="color:red;">《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTrForYunFei" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsgForYunFei" readonly></textarea>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script>
        function closeAll() {
            parent.layer.closeAll();
        }

        layui.use(['form','laydate', 'layer', 'table', 'element'], function(){
            var form = layui.form;
            var laydate = layui.laydate //日期
                ,layer = layui.layer //弹层
                ,table = layui.table //表格
                ,element = layui.element //元素操作
            laydate.render({
                trigger: 'click',
                type: 'month',
                elem: '#dateStr', //指定元素
                btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
            });

            //监听提交
            form.on('submit(formDemo)', function (data) {

                //判断时间不可为空
                var dateStr = $('#dateStr').val();
                if (dateStr == null || dateStr == "") {
                    layer.alert("请选择时间");
                    return false;
                }

                //判断区分不可为空
                var distinguish = $('#distinguish').val();
                if (distinguish == null || distinguish == "") {
                    layer.alert("请选择区分");
                    return false;
                }


                layer.confirm('确定是否需要上传数据?',{
                    btn : [ '确定', '取消' ] //按钮
                } ,function(data){
                    //do something

                    //文件类型验证的正则表达式
                    var excelFileRex = /\.(xls|xlsx)$/;
                    var countNum = 0;

                    //预定直接部门回收计算判断
                    var excelFileForZhiJie = $("#excelFileForZhiJie").val();
                    if (excelFileForZhiJie == null || excelFileForZhiJie == "") {
                        // layer.alert("预定直接部门回收计算请上传导入文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForZhiJie)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //预定辅助部门回收计算（补修费、辅材费）判断
                    var excelFileForFuZhu = $("#excelFileForFuZhu").val();
                    if (excelFileForFuZhu == null || excelFileForFuZhu == "") {
                        // layer.alert("预定辅助部门回收计算（补修费、辅材费）请上传导入文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForFuZhu)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //预定辅助部门回收计算（人工费）判断
                    var excelFileForFuZhu2 = $("#excelFileForFuZhu2").val();
                    if (excelFileForFuZhu2 == null || excelFileForFuZhu2 == "") {
                        // layer.alert("预定辅助部门回收计算（人工费）请上传导入文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForFuZhu2)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //预定捆包费判断
                    var excelFileForKunBao = $("#excelFileForKunBao").val();
                    if (excelFileForKunBao == null || excelFileForKunBao == "") {
                        // layer.alert("预定捆包费请上传导入文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForKunBao)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //预定线盘回收计算判断
                    var excelFileForXianPanHuiShou = $("#excelFileForXianPanHuiShou").val();
                    if (excelFileForXianPanHuiShou == null || excelFileForXianPanHuiShou == "") {
                        // layer.alert("预定线盘回收计算请上传导入文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForXianPanHuiShou)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //预定屑使用量判断
                    var excelFileForXie = $("#excelFileForXie").val();
                    if (excelFileForXie == null || excelFileForXie == "") {
                        // layer.alert("预定屑使用量请上传导入文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForXie)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //预定芯线使用量判断
                    var excelFileForXinXian = $("#excelFileForXinXian").val();
                    if (excelFileForXinXian == null || excelFileForXinXian == "") {
                        // layer.alert("预定芯线使用量请上传导入文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForXinXian)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //预定油漆使用量判断
                    var excelFileForYouQi = $("#excelFileForYouQi").val();
                    if (excelFileForYouQi == null || excelFileForYouQi == "") {
                        // layer.alert("预定油漆使用量请上传导入文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForYouQi)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //预定运费判断
                    var excelFileForYunFei = $("#excelFileForYunFei").val();
                    if (excelFileForYunFei == null || excelFileForYunFei == "") {
                        // layer.alert("请上传文件");
                        // return false;
                    } else {
                        if (!excelFileRex.test(excelFileForYunFei)) {
                            layer.alert("文件必须是.xlsx或者xls");
                            return false;
                        }else{
                            countNum = countNum + 1;
                        }
                    }

                    //等待遮罩层
                    var index = layer.load(2, {
                        shade: [0.1, '#fff']
                    });

                    //预定直接部门回收计算实现导入
                    if (excelFileForZhiJie != null && excelFileForZhiJie != "") {
                        $("#errMsTrForZhiJie").hide();
                        $("#errmsgForZhiJie").text("")
                        var formDataV1 = new FormData();
                        formDataV1.append('dateStr', $('#dateStr').val());
                        formDataV1.append('distinguish', $('#distinguish').val());
                        formDataV1.append('filename', $('input[type="file"]')[0].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/directRecycling/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV1,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {
                                    // layer.msg("预定直接部门回收计算导入成功!", {
                                    //     shade: 0.3,
                                    //     time: 1500
                                    // });
                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForZhiJie = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForZhiJie = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForZhiJie").text(errmsgForZhiJie)
                                        $("#errMsTrForZhiJie").show();
                                        $("#excelFileForZhiJie").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    //预定辅助部门回收计算（补修费、辅材费）实现导入
                    if (excelFileForFuZhu != null && excelFileForFuZhu != "") {
                        $("#errMsTrForFuZhu").hide();
                        $("#errmsgForFuZhu").text("")
                        var formDataV2 = new FormData();
                        formDataV2.append('dateStr', $('#dateStr').val());
                        formDataV2.append('distinguish', $('#distinguish').val());
                        formDataV2.append('filename', $('input[type="file"]')[1].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/auxiliaryRecycling/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV2,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {
                                    // layer.msg("预定辅助部门回收计算（补修费、辅材费）导入成功!", {
                                    //     shade: 0.3,
                                    //     time: 1500
                                    // });
                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForFuZhu = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForFuZhu = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForFuZhu").text(errmsgForFuZhu)
                                        $("#errMsTrForFuZhu").show();
                                        $("#excelFileForFuZhu").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    //预定辅助部门回收计算（人工费）实现导入
                    if (excelFileForFuZhu2 != null && excelFileForFuZhu2 != "") {
                        $("#errMsTrForFuZhu2").hide();
                        $("#errmsgForFuZhu2").text("")
                        var formDataV3 = new FormData();
                        formDataV3.append('dateStr', $('#dateStr').val());
                        formDataV3.append('distinguish', $('#distinguish').val());
                        formDataV3.append('filename', $('input[type="file"]')[2].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/auxiliaryRecyclingArtificial/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV3,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {
                                    // layer.msg("预定辅助部门回收计算（人工费）导入成功!", {
                                    //     shade: 0.3,
                                    //     time: 1500
                                    // });
                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForFuZhu2 = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForFuZhu2 = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForFuZhu2").text(errmsgForFuZhu2)
                                        $("#errMsTrForFuZhu2").show();
                                        $("#excelFileForFuZhu2").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    //预定捆包费实现导入
                    if (excelFileForKunBao != null && excelFileForKunBao != "") {
                        $("#errMsTrForKunBao").hide();
                        $("#errmsgForKunBao").text("")
                        var formDataV4 = new FormData();
                        formDataV4.append('dateStr', $('#dateStr').val());
                        formDataV4.append('distinguish', $('#distinguish').val());
                        formDataV4.append('filename', $('input[type="file"]')[3].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/bookingBundling/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV4,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {
                                    // layer.msg("预定捆包费导入成功!", {
                                    //     shade: 0.3,
                                    //     time: 1500
                                    // });
                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForKunBao = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForKunBao = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForKunBao").text(errmsgForKunBao)
                                        $("#errMsTrForKunBao").show();
                                        $("#excelFileForKunBao").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    //预定线盘回收计算实现导入
                    if (excelFileForXianPanHuiShou != null && excelFileForXianPanHuiShou != ""){
                        $("#errMsTrForXianPanHuiShou").hide();
                        $("#errmsgForXianPanHuiShou").text("")
                        var formDataV5 = new FormData();
                        formDataV5.append('dateStr', $('#dateStr').val());
                        formDataV5.append('distinguish', $('#distinguish').val());
                        formDataV5.append('filename', $('input[type="file"]')[4].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/reservedRecycling/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV5,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {
                                    // layer.msg("预定线盘回收计算导入成功!", {
                                    //     shade: 0.3,
                                    //     time: 1500
                                    // });
                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForXianPanHuiShou = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForXianPanHuiShou = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForXianPanHuiShou").text(errmsgForXianPanHuiShou)
                                        $("#errMsTrForXianPanHuiShou").show();
                                        $("#excelFileForXianPanHuiShou").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    //预定屑使用量实现导入
                    if (excelFileForXie != null && excelFileForXie != "") {
                        $("#errMsTrForXie").hide();
                        $("#errmsgForXie").text("")
                        var formDataV6 = new FormData();
                        formDataV6.append('dateStr', $('#dateStr').val());
                        formDataV6.append('distinguish', $('#distinguish').val());
                        formDataV6.append('filename', $('input[type="file"]')[5].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/reserveCrumbsUsage/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV6,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {
                                    // layer.msg("预定屑使用量导入成功!", {
                                    //     shade: 0.3,
                                    //     time: 1500
                                    // });
                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForXie = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForXie = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForXie").text(errmsgForXie)
                                        $("#errMsTrForXie").show();
                                        $("#excelFileForXie").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    //预定芯线使用量实现导入
                    if (excelFileForXinXian != null && excelFileForXinXian != "") {
                        $("#errMsTrForXinXian").hide();
                        $("#errmsgForXinXian").text("")
                        var formDataV7 = new FormData();
                        formDataV7.append('dateStr', $('#dateStr').val());
                        formDataV7.append('distinguish', $('#distinguish').val());
                        formDataV7.append('filename', $('input[type="file"]')[6].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/reserveCoreWireUsage/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV7,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {
                                    // layer.msg("预定芯线使用量导入成功!", {
                                    //     shade: 0.3,
                                    //     time: 1500
                                    // });
                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForXinXian = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForXinXian = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForXinXian").text(errmsgForXinXian)
                                        $("#errMsTrForXinXian").show();
                                        $("#excelFileForXinXian").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    //预定油漆使用量实现导入
                    if (excelFileForYouQi != null && excelFileForYouQi != "") {
                        $("#errMsTrForYouQi").hide();
                        $("#errmsgForYouQi").text("")
                        var formDataV8 = new FormData();
                        formDataV8.append('dateStr', $('#dateStr').val());
                        formDataV8.append('distinguish', $('#distinguish').val());
                        formDataV8.append('filename', $('input[type="file"]')[7].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/reservePaintUsage/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV8,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {
                                    // layer.msg("预定油漆使用量导入成功!", {
                                    //     shade: 0.3,
                                    //     time: 1500
                                    // });
                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForYouQi = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForYouQi = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForYouQi").text(errmsgForYouQi)
                                        $("#errMsTrForYouQi").show();
                                        $("#excelFileForYouQi").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    //预定运费实现导入
                    if (excelFileForYunFei != null && excelFileForYunFei != "") {
                        $("#errMsTrForYunFei").hide();
                        $("#errmsgForYunFei").text("")
                        var formDataV9 = new FormData();
                        formDataV9.append('dateStr', $('#dateStr').val());
                        formDataV9.append('distinguish', $('#distinguish').val());
                        formDataV9.append('filename', $('input[type="file"]')[8].files[0]);
                        $.ajax({
                            url: "${ctx}/costPrice/scheduledRecovery/reserveFreight/import",
                            async: false,
                            type: 'post',
                            cache: false,
                            data: formDataV9,
                            processData: false, //因为data值是FormData对象,不需要对数据做处理
                            contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                            success: function (result) {
                                if (result.code == 1) {

                                } else {
                                    var importBean = result.data;
                                    if (importBean != null) {
                                        var errMsgList = importBean.errMsgList;
                                        var errmsgForYunFei = "";
                                        if (errMsgList.length > 0) {
                                            errmsgForYunFei = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                        }
                                        $("#errmsgForYunFei").text(errmsgForYunFei)
                                        $("#errMsTrForYunFei").show();
                                        $("#excelFileForYunFei").val("");
                                    } else {
                                        layer.alert(result.message);
                                    }
                                }
                            }
                        });
                    }

                    if(countNum > 0){
                        parent.layer.msg("导入成功!", {
                            shade: 0.3,
                            time: 1500
                        });
                    }else{
                        parent.layer.msg("不能为空!", {
                            shade: 0.3,
                            time: 1500
                        });
                    }

                    layer.close(data);

                    $("#distinguish").val("");
                    $("#dateStr").val("");
                    $("#excelFileForZhiJie").val("");
                    $("#excelFileForFuZhu").val("");
                    $("#excelFileForFuZhu2").val("");
                    $("#excelFileForKunBao").val("");
                    $("#excelFileForXianPanHuiShou").val("");
                    $("#excelFileForXie").val("");
                    $("#excelFileForXinXian").val("");
                    $("#excelFileForYouQi").val("");
                    $("#excelFileForYunFei").val("");
                    form.render();//重新渲染
                    layer.closeAll();
                });

                return false;
            });


        });
    </script>
</myfooter>
</body>
</html>