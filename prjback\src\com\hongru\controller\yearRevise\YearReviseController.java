package com.hongru.controller.yearRevise;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.*;
import com.hongru.common.util.poiExcelExport.ExcelExp;
import com.hongru.common.util.poiExcelExport.ExcelExportUtil;
import com.hongru.common.util.poiExcelExport.ServletUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.cost.*;
import com.hongru.entity.stat.Proportion;
import com.hongru.entity.yearRevise.*;
import com.hongru.pojo.dto.NewYearStoragePreDTO;
import com.hongru.service.admin.IUserService;
import com.hongru.service.cost.ICostService;
import com.hongru.service.setting.ISettingService;
import com.hongru.service.stat.IStatService;
import com.hongru.service.xieFenXi.IXieFenXiService;
import com.hongru.service.yearRevise.IYearReviseService;
import com.hongru.support.page.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "/yearRevise")
public class YearReviseController extends BaseController {
    @Autowired
    private IYearReviseService yearReviseService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IXieFenXiService xieFenXiService;
    @Autowired
    private IStatService statService;
    @Autowired
    private ISettingService settingService;
	@Autowired
	private ICostService costService;

    /*=================================年度予定入库量======================================*/
    /**
     * 年度予定入库量列表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/02 13:43
     * @return
     */
    @GetMapping("/newYearStoragePre/list/view")
    public String newYearStoragePreListView(Model model) throws Exception{
        return "/modules/yearRevise/newYearStoragePre_list";
    }
    
    /**
     * 查询年度予定入库量
     * @param year
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/02 13:43
     * @return
     */
    @PostMapping("newYearStoragePre/listPage")
    @ResponseBody
    public Object newYearStoragePreListPage(PageInfo pageInfo,short isSearch,String year) throws Exception{
        //新建页面对象
        if(isSearch == 1){
        	NewYearStoragePreDTO newYearStoragePreDTO = yearReviseService.listNewYearStoragePrePage(year, pageInfo);
            return new HrPageResult(newYearStoragePreDTO.getNewYearStoragePreList(), newYearStoragePreDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<NewYearStoragePre>(), 0);
        }
    }
    /**
     * 新增年度予定入库量页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/02 13:43
     * @return
     */
    @GetMapping("/newYearStoragePre/add/view")
    public String newYearStoragePreAddView(Model model) throws Exception{
        return "/modules/yearRevise/newYearStoragePre_add";
    }

    /**
     * 新增年度予定入库量
     * @param newYearStoragePre
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/02 13:43
     * @return
     */
    @PostMapping("/newYearStoragePre/add")
    @ResponseBody
    public Object newYearStoragePreAdd(NewYearStoragePre newYearStoragePre) throws Exception{
        try {
            //获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());
            
            //将用户信息插入年度予定入库量对象中
            newYearStoragePre.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            newYearStoragePre.setCreatorName(user.getUserName());
            newYearStoragePre.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            newYearStoragePre.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
            newYearStoragePre.setLastModifierName(user.getUserName());
            newYearStoragePre.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));

            //插入铜供应商表中
            yearReviseService.addNewYearStoragePre(newYearStoragePre);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("年度予定入库量新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 编辑年度予定入库量页面
     * @param model
     * @param year
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/02 13:43
     * @return
     */
    @GetMapping("/newYearStoragePre/modify/view")
    public Object newYearStoragePreModifyView(Model model, String year) throws Exception{
    	// 根据条件「年度」检索需要编辑的年度予定入库量
    	NewYearStoragePre newYearStoragePre = yearReviseService.selectNewYearStoragePreByYear(year);
        model.addAttribute("newYearStoragePre",newYearStoragePre);
        return "/modules/yearRevise/newYearStoragePre_modify";
    }

    /**
     *  编辑年度予定入库量
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/02 13:43
     * @return
     */
    @PostMapping("/newYearStoragePre/modify")
    @ResponseBody
    public Object newYearStoragePreModify(NewYearStoragePre newYearStoragePre) throws Exception{
        try {
            //获取当前登录对象
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id查询用户信息
            User user = userService.getById(authorizingUser.getUserId());
            //修改表中‘最后修改人id name time’字段
            newYearStoragePre.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
            newYearStoragePre.setLastModifierName(user.getUserName());
            newYearStoragePre.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            yearReviseService.updateNewYearStoragePreInfo(newYearStoragePre);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("年度予定入库量修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除年度予定入库量
     * @param year
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/02 13:43
     * @return
     */
    @PostMapping("/newYearStoragePre/delete")
    @ResponseBody
    public Object newYearStoragePreDelete(String year) throws Exception{
        try {
        	yearReviseService.deleteNewYearStoragePreByYear(year);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("年度予定入库量删除异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /*=================================入库量&机械时间予定======================================*/
    /**
     * 机械时间予定(MW)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/03 14:18
     * @return
     */
    @GetMapping("/machineTimePre/list/view")
    public String machineTimePreListView(Model model) throws Exception{
        return "/modules/yearRevise/machineTimePre_list";
    }
    
    /**
     * 根据时间范围获取机械时间予定明细(MW)
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/04 09:43
     * @return
     */
    @PostMapping("/machineTimePre/list")
    @ResponseBody
    public Object machineTimePreListForMW(short isSearch, String time) throws Exception{
        if(isSearch == 1){
  		  // 截取时间范围
  		  String timeStartStr = time.substring(0, 7);
  		  String timeEndStr = time.substring(time.length()-7);
  		  
        	// 获取两个日期相差的月数(加权平均用)
        	int monthInterval =  DateUtils.getMonthDiff(timeStartStr,timeEndStr);
            //根据条件获取机械时间予定明细信息
        	List<MachineTimePreBean> machineTimePreBeanList = yearReviseService.listMachineTimePreBeanForMW(monthInterval, timeStartStr, timeEndStr);
        	
        	// 检索入库表、取得各入库量实际 
        	List<StorageInActBean> storageInActBeanList = new ArrayList<>();
        	storageInActBeanList = yearReviseService.selectStorageInActForMWList(monthInterval, timeStartStr, timeEndStr);
        	// 实际入库量总计  
        	BigDecimal storageInActTotal = new BigDecimal(0);
        	// EM实际入库量总计
        	BigDecimal storageInActEM = new BigDecimal(0);
        	// EF实际入库量总计
        	BigDecimal storageInActEF = new BigDecimal(0);
        	// EF09实际入库量总计
        	BigDecimal storageInActEF09= new BigDecimal(0);
        	// ER实际入库量总计
        	BigDecimal storageInActER = new BigDecimal(0);
        	// EH实际入库量总计
        	BigDecimal storageInActEH = new BigDecimal(0);
        	
        	if(storageInActBeanList != null && storageInActBeanList.size() > 0) {
        		for(StorageInActBean storageInActBean:storageInActBeanList) {
        			// 实际入库量总计  =EM+EF+EF09+ER+EH
        			storageInActTotal = storageInActBean.getStorageInActEM()
							.add(storageInActBean.getStorageInActEF().add(storageInActBean.getStorageInActEF09().add(
									storageInActBean.getStorageInActER().add(storageInActBean.getStorageInActEH()))));
        			// EM实际入库量
        			storageInActEM =storageInActBean.getStorageInActEM();
        			// EF实际入库量 
        			storageInActEF = storageInActBean.getStorageInActEF();
        			// EF09实际入库量 
        			storageInActEF09 = storageInActBean.getStorageInActEF09();
        			// ER实际入库量
        			storageInActER = storageInActBean.getStorageInActER();
        			// EH实际入库量
        			storageInActEH = storageInActBean.getStorageInActEH();
        		}
        	}
        	//新年度入库量予定
        	int nextYear =YearUtil.getNextYear(timeStartStr); // 获取新的年份值
        	 
        	// 根据条件「年度」检索需要编辑的年度予定入库量
        	NewYearStoragePre newYearStoragePre = yearReviseService.selectNewYearStoragePreByYear(String.valueOf(nextYear));
        	BigDecimal newYearStoragePreTotal = null;
        	
        	// 新年度入库量予定总计= EM+EF+EF09+ER+EH
        	if(newYearStoragePre != null) {
   			 newYearStoragePreTotal = newYearStoragePre.getEmNewYearStoragePre().add(newYearStoragePre.getEfNewYearStoragePre()
						.add(newYearStoragePre.getEf09NewYearStoragePre().add(newYearStoragePre.getErNewYearStoragePre().add(newYearStoragePre.getEhNewYearStoragePre()))));
        	}
			
        	//	机械时间实际
        	HashMap<String,Float> totalDepartScrapAmountMap = new HashMap<String, Float>();
        	totalDepartScrapAmountMap = xieFenXiService.listxieFenXiBeanForMachineTime(monthInterval, timeStartStr, timeEndStr);
        	if(machineTimePreBeanList != null && machineTimePreBeanList.size() > 0){
        		for(MachineTimePreBean machineTimePreBean:machineTimePreBeanList) {
        			
        			switch(machineTimePreBean.getDepartment()) {
        				case "DH":
        					// 入库量实绩总计  
        					machineTimePreBean.setStorageActAvg(storageInActTotal.toString());
        					//单位机械时间=机械时间予定(平均)/入库量实绩(平均)
        					machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimePreAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
        					//新年度入库量予定:EM+EF+EF09+ER+EH
        					if(newYearStoragePreTotal != null) {
        						machineTimePreBean.setNewYearStoragePre(newYearStoragePreTotal.toString());
               					//新年度UP率=新年度入库量予定/入库量实绩
            					machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
               					//新年度机械时间予定 = 机械时间予定*新年度UP率
            					machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimePreAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
        					}
        					break;
        				case "DM":
        					// 入库量实绩  DM
        					machineTimePreBean.setStorageActAvg(storageInActEF.toString());
        					//单位机械时间=机械时间予定(平均)/入库量实绩(平均)
        					machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimePreAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
        					if(newYearStoragePre != null) {
        						//新年度入库量予定:DM
        						machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEfNewYearStoragePre().toString());
              					//新年度UP率=新年度入库量予定/入库量实绩
            					machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
             					//新年度机械时间予定 = 机械时间予定*新年度UP率
            					machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimePreAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
        					}
         					break;
        				case "EM":
        					// 入库量实绩  EM
        					machineTimePreBean.setStorageActAvg(storageInActEM.toString());
        					// 机械时间实绩(平均) = EM
        					machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("EM").toString());
        					//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
           					machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
        				
           					if(newYearStoragePre != null) {
           						//新年度入库量予定
           						machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEmNewYearStoragePre().toString());
               					//新年度UP率=新年度入库量予定/入库量实绩
            					machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
              					//新年度机械时间予定 = 机械时间实绩*新年度UP率
            					machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
           					}
        					break;
        				case "EF":
        					// 入库量实绩  EF
        					machineTimePreBean.setStorageActAvg(storageInActEF.toString());
        					// 机械时间实绩(平均) =  EF 
        					machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("EF").toString());
           					//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
           					machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
        					
           					if(newYearStoragePre != null) {
           					//新年度入库量予定
           						machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEfNewYearStoragePre().toString());
              					//新年度UP率=新年度入库量予定/入库量实绩
            					machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
              					//新年度机械时间予定 = 机械时间实绩*新年度UP率
            					machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
           					}
        					break;
        				case "EF09":
        					// 入库量实绩  EF09
        					machineTimePreBean.setStorageActAvg(storageInActEF09.toString());
        					// 机械时间实绩(平均)  
        					machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("EF09").toString());
           					//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
           					machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
        					
           					if(newYearStoragePre != null) {
           					//新年度入库量予定
           						machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEf09NewYearStoragePre().toString());
               					//新年度UP率=新年度入库量予定/入库量实绩
            					machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
             					//新年度机械时间予定 = 机械时间实绩*新年度UP率
            					machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
           					}
        					break;
        				case "ER":
        					// 入库量实绩  ER
        					machineTimePreBean.setStorageActAvg(storageInActER.toString());
        					// 机械时间实绩(平均)  
        					machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("ER").toString());
           					//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
           					machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
        					
           					if(newYearStoragePre != null) {
           						//新年度入库量予定
           						machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getErNewYearStoragePre().toString());
               					//新年度UP率=新年度入库量予定/入库量实绩
            					machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
             					//新年度机械时间予定 = 机械时间实绩*新年度UP率
            					machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
           					}
        					break;
        				case "EH":
        					// 入库量实绩  EH
        					machineTimePreBean.setStorageActAvg(storageInActEH.toString());
        					// 机械时间实绩(平均) 
        					machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("EH").toString());
           					//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
           					machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
        					
           					if(newYearStoragePre != null) {
           						//新年度入库量予定
           						machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEhNewYearStoragePre().toString());
               					//新年度UP率=新年度入库量予定/入库量实绩
            					machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
             					//新年度机械时间予定 = 机械时间实绩*新年度UP率
            					machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
           					}
        					break;
        			}
        		}
        	}
            return new HrPageResult(machineTimePreBeanList, machineTimePreBeanList.size());
        }else{
        	return new HrPageResult(new ArrayList<MachineTimePreBean>(), 0);
        }
    
    }
    /**
     * 机械时间予定(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/03 14:28
     * @return
     */
    @GetMapping("/machineTimePreUF/list/view")
    public String machineTimePreUFListView(Model model) throws Exception{
        return "/modules/yearRevise/machineTimePreUF_list";
    }
    
    /**
     * 根据时间范围获取机械时间予定明细(UF)
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/04 09:43
     * @return
     */
    @PostMapping("/machineTimePreUF/list")
    @ResponseBody
    public Object machineTimePreUFList(short isSearch, String time) throws Exception{
        if(isSearch == 1){
        	// 截取时间范围
        	String timeStartStr = time.substring(0, 7);
        	String timeEndStr = time.substring(time.length()-7);
    		  
        	List<MachineTimePreBean> machineTimePreBeanList = new ArrayList<>();
        	// 获取两个日期相差的月数(加权平均用)
        	int monthInterval =  DateUtils.getMonthDiff(timeStartStr,timeEndStr);
            //根据条件获取机械时间予定明细信息
        	machineTimePreBeanList = yearReviseService.listMachineTimePreBeanForUF(monthInterval, timeStartStr, timeEndStr);
            return new HrPageResult(machineTimePreBeanList, machineTimePreBeanList.size());
        }else{
            return new HrPageResult(new ArrayList<MachineTimePreBean>(), 0);
        }
    }

    /*=================================动力SH系数-电燃氮======================================*/
    /**
     * 各动力单价实绩
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/03 14:18
     * @return
     */
    @GetMapping("/powerShCoefficient/list/view")
    public String powerShCoefficientView(Model model) throws Exception{
        return "/modules/yearRevise/powerShCoefficient _list";
    }
    
    /**
     * 各动力单价实绩明细
     * @param timeStartStr
     * @param timeEndStr
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/04 09:43
     * @return
     */
    @PostMapping("/powerShCoefficient/list")
    @ResponseBody
    public Object powerShCoefficientList(short isSearch, String time) throws Exception{
        if(isSearch == 1){
        	// 截取时间范围
        	String timeStartStr = time.substring(0, 7);
        	String timeEndStr = time.substring(time.length()-7);
        	
        	PowerShCoefficientBean powerShCoefficientBean = new PowerShCoefficientBean();
        	List<PowerShCoefficientBean> powerShCoefficientBeanList = new ArrayList<>();
        	//根据日期范围检索[电费用表]取得「电力使用量实绩合计」、「电力费合计」
        	PowerShCoefficientBean powerElectric  =  yearReviseService.selectPowerElectricByDateRange(timeStartStr, timeEndStr);
        	//根据日期范围检索[水费用表]取得「水费合计」
        	PowerShCoefficientBean powerWater  = yearReviseService.selectPowerWaterByDateRange(timeStartStr, timeEndStr);
        	// 	电力单价实绩=(电力费合计+水费合计)/电力使用量实绩合计
        	BigDecimal tempPriceAct =powerElectric.getElePriceTotal().add(powerWater.getWaterPriceTotal());
        	powerShCoefficientBean.setEleUnitPriceActAvg(tempPriceAct.divide(powerElectric.getEleUsedActTotal(), 3,  RoundingMode.HALF_UP));
        	
        	//根据日期范围检索[燃气费用表]取得「天燃气单价实绩」、「氮气单价实绩」
        	List<PowerShCoefficientBean> powerOtherList  =  yearReviseService.selectPowerOtherByDateRange(timeStartStr, timeEndStr);
        	for(PowerShCoefficientBean powerOther:powerOtherList) {
        		//部门:06 UF氮气  05 EH天然气 
        		if("05".equals(powerOther.getDepartment())) {
        			powerShCoefficientBean.setGasUnitPriceActAvg(powerOther.getNitrogenUnitPriceActAvg().setScale(3,  RoundingMode.HALF_UP));
        		}else {
        			powerShCoefficientBean.setNitrogenUnitPriceActAvg(powerOther.getNitrogenUnitPriceActAvg().setScale(3,  RoundingMode.HALF_UP));
        		}
        	}
        	
        	//将上记检索结果放入	电燃氮查询返回结果实体类
        	powerShCoefficientBeanList.add(0, powerShCoefficientBean);
            return new HrPageResult(powerShCoefficientBeanList, powerShCoefficientBeanList.size());
        }else{
            return new HrPageResult(new ArrayList<MachineTimePreBean>(), 0);
        }
    }  
    
    /**
     * 新年度予定电量系数(MW)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/10 14:18
     * @return
     */
    @GetMapping("/newYearEleCoefficient/list/view")
    public String newYearEleCoefficientView(Model model) throws Exception{
        return "/modules/yearRevise/newYearEleCoefficient_list";
    }
    
    /**
     * 新年度予定电量系数明细(MW)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/10 14:28
     * @return
     */
    @PostMapping("/newYearEleCoefficient/list")
    @ResponseBody
    public Object newYearEleCoefficientList(short isSearch, String time) throws Exception{
		if (isSearch == 1) {
        	// 截取时间范围
        	String timeStartStr = time.substring(0, 7);
        	String timeEndStr = time.substring(time.length()-7);
        	
			List<NewYearEleCoeffucientBean> newYearEleCoeffucientBeanList = new ArrayList<>();
			// 检索条件部门
			String[] departArr = {"DH总", "DM总", "EM总", "EF总", "EF09总", "ER总", "EH总", "公用设施 总", "食堂","其他"} ;
			// 直接平均电量总计
			BigDecimal directEleAvgTotal = new BigDecimal(0);
			// 间接接平均电量总计
			BigDecimal indirectEleAvgTotal = new BigDecimal(0);

			// 检索[电力使用明细表] 获取电量实绩平均
			newYearEleCoeffucientBeanList=yearReviseService.selectElectricActAvgByDepart(departArr, timeStartStr, timeEndStr);
			if(newYearEleCoeffucientBeanList != null && newYearEleCoeffucientBeanList.size() > 0) {
				for(NewYearEleCoeffucientBean newYearEleCoeffucient:newYearEleCoeffucientBeanList) {
					// 间接的场合
					if ("公用设施 总".equals(newYearEleCoeffucient.getDepartment())
							|| "食堂".equals(newYearEleCoeffucient.getDepartment())
							|| "其他".equals(newYearEleCoeffucient.getDepartment())) {
						indirectEleAvgTotal = indirectEleAvgTotal.add(newYearEleCoeffucient.getElectricityActAvg());
					}else {
						// 直接的场合
						directEleAvgTotal = directEleAvgTotal.add(newYearEleCoeffucient.getElectricityActAvg());
					}
				}
			}
        	// 获取两个日期相差的月数(加权平均用)
        	int monthInterval =  DateUtils.getMonthDiff(timeStartStr,timeEndStr);
        	// 机械时间予定平均
        	BigDecimal dhMachineTimePreAvg = new BigDecimal(0);
        	// 机械时间予定平均  
        	BigDecimal dmMachineTimePreAvg = new BigDecimal(0);
            //根据条件获取机械时间予定明细信息
        	List<MachineTimePreBean> machineTimePreBeanList = yearReviseService.listMachineTimePreBeanForMW(monthInterval, timeStartStr, timeEndStr);
        	if(machineTimePreBeanList != null && machineTimePreBeanList.size() > 0){
        		for(MachineTimePreBean machineTimePreBean:machineTimePreBeanList) {
        			if("DH".equals(machineTimePreBean.getDepartment())) {
        				dhMachineTimePreAvg =  new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
        			}else if("DM".equals(machineTimePreBean.getDepartment())) {
        				dmMachineTimePreAvg =  new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
        			}else {
        				;
        			}
        		}
        	}
        		
        	// 实际入库量总计  
        	BigDecimal storageInActTotal = new BigDecimal(0);
        	// EM实际入库量
        	BigDecimal storageInActEM = new BigDecimal(0);
        	// EF实际入库量
        	BigDecimal storageInActEF = new BigDecimal(0);
        	// EF09实际入库量
        	BigDecimal storageInActEF09= new BigDecimal(0);
        	// ER实际入库量
        	BigDecimal storageInActER = new BigDecimal(0);
        	// EH实际入库量
        	BigDecimal storageInActEH = new BigDecimal(0);
	       	// 检索入库表、取得各入库量实际 
        	List<StorageInActBean> storageInActBeanList = yearReviseService.selectStorageInActForMWList(monthInterval, timeStartStr, timeEndStr);
        	if(storageInActBeanList != null && storageInActBeanList.size() > 0) {
        		for(StorageInActBean storageInActBean:storageInActBeanList) {
        			// 实际入库量总计  =EM+EF+EF09+ER+EH
        			storageInActTotal = storageInActBean.getStorageInActEM()
							.add(storageInActBean.getStorageInActEF().add(storageInActBean.getStorageInActEF09().add(
									storageInActBean.getStorageInActER().add(storageInActBean.getStorageInActEH()))));
        			// EM实际入库量
        			storageInActEM =storageInActBean.getStorageInActEM();
        			// EF实际入库量 
        			storageInActEF = storageInActBean.getStorageInActEF();
        			// EF09实际入库量 
        			storageInActEF09 = storageInActBean.getStorageInActEF09();
        			// ER实际入库量
        			storageInActER = storageInActBean.getStorageInActER();
        			// EH实际入库量
        			storageInActEH = storageInActBean.getStorageInActEH();
        		}
        	}
        	//新年度入库量予定
        	int nextYear = YearUtil.getNextYear(timeStartStr); // 获取新的年份值
        	 
        	// 根据条件「年度」检索需要编辑的年度予定入库量
        	NewYearStoragePre newYearStoragePre = yearReviseService.selectNewYearStoragePreByYear(String.valueOf(nextYear));
        	BigDecimal newYearStoragePreTotal = null;
            if (newYearStoragePre != null) {
            	// 新年度入库量予定总计= EM+EF+EF09+ER+EH
            	 newYearStoragePreTotal = newYearStoragePre.getEmNewYearStoragePre().add(newYearStoragePre.getEfNewYearStoragePre()
							.add(newYearStoragePre.getEf09NewYearStoragePre().add(newYearStoragePre.getErNewYearStoragePre().add(newYearStoragePre.getEhNewYearStoragePre()))));
            }

			
        	//	机械时间实际
        	HashMap<String,Float> totalDepartScrapAmountMap = new HashMap<String, Float>();
        	totalDepartScrapAmountMap = xieFenXiService.listxieFenXiBeanForMachineTime(monthInterval, timeStartStr, timeEndStr);
			
			//新年度予定电量系数查询返回结果实体类
			for(NewYearEleCoeffucientBean newYearEleCoeffucientBean:newYearEleCoeffucientBeanList) {
				switch(newYearEleCoeffucientBean.getDepartment()) {
				case "DH总":
					// 部门 
					newYearEleCoeffucientBean.setDepartment("DH");
					// 实绩比例 = 电量实绩平均/直接平均电量总计
					newYearEleCoeffucientBean.setProportionAct(newYearEleCoeffucientBean.getElectricityActAvg().divide(directEleAvgTotal, 4, RoundingMode.HALF_UP));
					//间接分摊 = 间接接平均电量总计*实绩比例
					newYearEleCoeffucientBean.setIndirectAllocation(indirectEleAvgTotal.multiply(newYearEleCoeffucientBean.getProportionAct()).setScale(3, RoundingMode.HALF_UP));
					//实绩总电量 = 电量实绩平均 + 间接分摊
					newYearEleCoeffucientBean.setElectricityActTotal(newYearEleCoeffucientBean.getElectricityActAvg().add(newYearEleCoeffucientBean.getIndirectAllocation()).setScale(3, RoundingMode.HALF_UP));
					//新年度机械时间 = 机械时间予定
					newYearEleCoeffucientBean.setNewYearMechanicalTime(dhMachineTimePreAvg.setScale(0, RoundingMode.DOWN).toString());
					if(newYearStoragePre != null) {
						// 新年度机械稼动UP率 = 新年度入库量予定/入库量实绩
						newYearEleCoeffucientBean.setNewYearMecUpRate(newYearStoragePreTotal.divide(storageInActTotal, 3, RoundingMode.HALF_UP));
						// 新年度予定电量 = 电量实绩平均* 新年度机械稼动UP率+间接分摊
						newYearEleCoeffucientBean.setNewYearElectricityPre(newYearEleCoeffucientBean.getElectricityActAvg().multiply(newYearEleCoeffucientBean.getNewYearMecUpRate()).add(newYearEleCoeffucientBean.getIndirectAllocation()));
						//新年度予定电量系数 = 新年度予定电量/新年度机械时间
						newYearEleCoeffucientBean.setNewYearElectricityPreSh(Float.toString(FloatUtil.IntForFloat(Float.valueOf(newYearEleCoeffucientBean.getNewYearElectricityPre().toString()), Float.valueOf(newYearEleCoeffucientBean.getNewYearMechanicalTime()),3)));						
					}
					break;
				case "DM总":
					// 部门 
					newYearEleCoeffucientBean.setDepartment("DM");
					// 实绩比例 = 电量实绩平均/直接平均电量总计
					newYearEleCoeffucientBean.setProportionAct(newYearEleCoeffucientBean.getElectricityActAvg().divide(directEleAvgTotal, 4, RoundingMode.HALF_UP));
					//间接分摊 = 间接接平均电量总计*实绩比例
					newYearEleCoeffucientBean.setIndirectAllocation(indirectEleAvgTotal.multiply(newYearEleCoeffucientBean.getProportionAct()).setScale(3, RoundingMode.HALF_UP));
					//实绩总电量 = 电量实绩平均 + 间接分摊
					newYearEleCoeffucientBean.setElectricityActTotal(newYearEleCoeffucientBean.getElectricityActAvg().add(newYearEleCoeffucientBean.getIndirectAllocation()).setScale(3, RoundingMode.HALF_UP));
					//新年度机械时间 = 机械时间予定
					newYearEleCoeffucientBean.setNewYearMechanicalTime(dmMachineTimePreAvg.setScale(0, RoundingMode.DOWN).toString());
					
					if(newYearStoragePre != null) {
						// 新年度机械稼动UP率 = 新年度入库量予定/入库量实绩
						newYearEleCoeffucientBean.setNewYearMecUpRate(newYearStoragePre.getEfNewYearStoragePre().divide(storageInActEF, 3, RoundingMode.HALF_UP));
						// 新年度予定电量 = 电量实绩平均* 新年度机械稼动UP率+间接分摊
						newYearEleCoeffucientBean.setNewYearElectricityPre(newYearEleCoeffucientBean.getElectricityActAvg().multiply(newYearEleCoeffucientBean.getNewYearMecUpRate()).add(newYearEleCoeffucientBean.getIndirectAllocation()));
						//新年度予定电量系数 = 新年度予定电量/新年度机械时间
						newYearEleCoeffucientBean.setNewYearElectricityPreSh(Float.toString(FloatUtil.IntForFloat(Float.valueOf(newYearEleCoeffucientBean.getNewYearElectricityPre().toString()), Float.valueOf(newYearEleCoeffucientBean.getNewYearMechanicalTime()),3)));
					}
					break;
				case "EM总":
					// 部门 
					newYearEleCoeffucientBean.setDepartment("EM");
					// 实绩比例 = 电量实绩平均/直接平均电量总计
					newYearEleCoeffucientBean.setProportionAct(newYearEleCoeffucientBean.getElectricityActAvg().divide(directEleAvgTotal, 4, RoundingMode.HALF_UP));
					//间接分摊 = 间接接平均电量总计*实绩比例
					newYearEleCoeffucientBean.setIndirectAllocation(indirectEleAvgTotal.multiply(newYearEleCoeffucientBean.getProportionAct()).setScale(3, RoundingMode.HALF_UP));
					//实绩总电量 = 电量实绩平均 + 间接分摊
					newYearEleCoeffucientBean.setElectricityActTotal(newYearEleCoeffucientBean.getElectricityActAvg().add(newYearEleCoeffucientBean.getIndirectAllocation()).setScale(3, RoundingMode.HALF_UP));
				
					if(newYearStoragePre != null) {
						// 新年度机械稼动UP率 = 新年度入库量予定/入库量实绩
						newYearEleCoeffucientBean.setNewYearMecUpRate(newYearStoragePre.getEmNewYearStoragePre().divide(storageInActEM, 3, RoundingMode.HALF_UP));
						// 新年度予定电量 = 电量实绩平均* 新年度机械稼动UP率+间接分摊
						newYearEleCoeffucientBean.setNewYearElectricityPre(newYearEleCoeffucientBean.getElectricityActAvg().multiply(newYearEleCoeffucientBean.getNewYearMecUpRate()).add(newYearEleCoeffucientBean.getIndirectAllocation()));
						//新年度机械时间 = 机械时间予定
						newYearEleCoeffucientBean.setNewYearMechanicalTime(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(totalDepartScrapAmountMap.get("EM").toString()), Double.parseDouble(newYearEleCoeffucientBean.getNewYearMecUpRate().toString()))));
						//新年度予定电量系数 = 新年度予定电量/新年度机械时间
						newYearEleCoeffucientBean.setNewYearElectricityPreSh(Float.toString(FloatUtil.IntForFloat(Float.valueOf(newYearEleCoeffucientBean.getNewYearElectricityPre().toString()), Float.valueOf(newYearEleCoeffucientBean.getNewYearMechanicalTime()),3)));
					}
					break;
				case "EF总":
					// 部门 
					newYearEleCoeffucientBean.setDepartment("EF");
					// 实绩比例 = 电量实绩平均/直接平均电量总计
					newYearEleCoeffucientBean.setProportionAct(newYearEleCoeffucientBean.getElectricityActAvg().divide(directEleAvgTotal, 4, RoundingMode.HALF_UP));
					//间接分摊 = 间接接平均电量总计*实绩比例
					newYearEleCoeffucientBean.setIndirectAllocation(indirectEleAvgTotal.multiply(newYearEleCoeffucientBean.getProportionAct()).setScale(3, RoundingMode.HALF_UP));
					//实绩总电量 = 电量实绩平均 + 间接分摊
					newYearEleCoeffucientBean.setElectricityActTotal(newYearEleCoeffucientBean.getElectricityActAvg().add(newYearEleCoeffucientBean.getIndirectAllocation()).setScale(3, RoundingMode.HALF_UP));
					
					if(newYearStoragePre != null) {
						// 新年度机械稼动UP率 = 新年度入库量予定/入库量实绩
						newYearEleCoeffucientBean.setNewYearMecUpRate(newYearStoragePre.getEfNewYearStoragePre().divide(storageInActEF, 3, RoundingMode.HALF_UP));
						// 新年度予定电量 = 电量实绩平均* 新年度机械稼动UP率+间接分摊
						newYearEleCoeffucientBean.setNewYearElectricityPre(newYearEleCoeffucientBean.getElectricityActAvg().multiply(newYearEleCoeffucientBean.getNewYearMecUpRate()).add(newYearEleCoeffucientBean.getIndirectAllocation()));
						//新年度机械时间 = 机械时间予定
						newYearEleCoeffucientBean.setNewYearMechanicalTime(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(totalDepartScrapAmountMap.get("EF").toString()), Double.parseDouble(newYearEleCoeffucientBean.getNewYearMecUpRate().toString()))));
						//新年度予定电量系数 = 新年度予定电量/新年度机械时间
						newYearEleCoeffucientBean.setNewYearElectricityPreSh(Float.toString(FloatUtil.IntForFloat(Float.valueOf(newYearEleCoeffucientBean.getNewYearElectricityPre().toString()), Float.valueOf(newYearEleCoeffucientBean.getNewYearMechanicalTime()),3)));
					}
					break;
				case "EF09总":
					// 部门 
					// 部门 
					newYearEleCoeffucientBean.setDepartment("EF09");
					// 实绩比例 = 电量实绩平均/直接平均电量总计
					newYearEleCoeffucientBean.setProportionAct(newYearEleCoeffucientBean.getElectricityActAvg().divide(directEleAvgTotal, 4, RoundingMode.HALF_UP));
					//间接分摊 = 间接接平均电量总计*实绩比例
					newYearEleCoeffucientBean.setIndirectAllocation(indirectEleAvgTotal.multiply(newYearEleCoeffucientBean.getProportionAct()).setScale(3, RoundingMode.HALF_UP));
					//实绩总电量 = 电量实绩平均 + 间接分摊
					newYearEleCoeffucientBean.setElectricityActTotal(newYearEleCoeffucientBean.getElectricityActAvg().add(newYearEleCoeffucientBean.getIndirectAllocation()).setScale(3, RoundingMode.HALF_UP));
					if(newYearStoragePre != null) {
						// 新年度机械稼动UP率 = 新年度入库量予定/入库量实绩
						newYearEleCoeffucientBean.setNewYearMecUpRate(newYearStoragePre.getEf09NewYearStoragePre().divide(storageInActEF09, 3, RoundingMode.HALF_UP));
						// 新年度予定电量 = 电量实绩平均* 新年度机械稼动UP率+间接分摊
						newYearEleCoeffucientBean.setNewYearElectricityPre(newYearEleCoeffucientBean.getElectricityActAvg().multiply(newYearEleCoeffucientBean.getNewYearMecUpRate()).add(newYearEleCoeffucientBean.getIndirectAllocation()));
						//新年度机械时间 = 机械时间予定
						newYearEleCoeffucientBean.setNewYearMechanicalTime(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(totalDepartScrapAmountMap.get("EF09").toString()), Double.parseDouble(newYearEleCoeffucientBean.getNewYearMecUpRate().toString()))));
						//新年度予定电量系数 = 新年度予定电量/新年度机械时间
						newYearEleCoeffucientBean.setNewYearElectricityPreSh(Float.toString(FloatUtil.IntForFloat(Float.valueOf(newYearEleCoeffucientBean.getNewYearElectricityPre().toString()), Float.valueOf(newYearEleCoeffucientBean.getNewYearMechanicalTime()),3)));						
					}
					break;
				case "ER总":
					// 部门 
					newYearEleCoeffucientBean.setDepartment("ER");
					// 实绩比例 = 电量实绩平均/直接平均电量总计
					newYearEleCoeffucientBean.setProportionAct(newYearEleCoeffucientBean.getElectricityActAvg().divide(directEleAvgTotal, 4, RoundingMode.HALF_UP));
					//间接分摊 = 间接接平均电量总计*实绩比例
					newYearEleCoeffucientBean.setIndirectAllocation(indirectEleAvgTotal.multiply(newYearEleCoeffucientBean.getProportionAct()).setScale(3, RoundingMode.HALF_UP));
					//实绩总电量 = 电量实绩平均 + 间接分摊
					newYearEleCoeffucientBean.setElectricityActTotal(newYearEleCoeffucientBean.getElectricityActAvg().add(newYearEleCoeffucientBean.getIndirectAllocation()).setScale(3, RoundingMode.HALF_UP));
					
					if(newYearStoragePre != null) {
						// 新年度机械稼动UP率 = 新年度入库量予定/入库量实绩
						newYearEleCoeffucientBean.setNewYearMecUpRate(newYearStoragePre.getErNewYearStoragePre().divide(storageInActER, 3, RoundingMode.HALF_UP));
						// 新年度予定电量 = 电量实绩平均* 新年度机械稼动UP率+间接分摊
						newYearEleCoeffucientBean.setNewYearElectricityPre(newYearEleCoeffucientBean.getElectricityActAvg().multiply(newYearEleCoeffucientBean.getNewYearMecUpRate()).add(newYearEleCoeffucientBean.getIndirectAllocation()));
						//新年度机械时间 = 机械时间予定
						newYearEleCoeffucientBean.setNewYearMechanicalTime(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(totalDepartScrapAmountMap.get("ER").toString()), Double.parseDouble(newYearEleCoeffucientBean.getNewYearMecUpRate().toString()))));
						//新年度予定电量系数 = 新年度予定电量/新年度机械时间
						newYearEleCoeffucientBean.setNewYearElectricityPreSh(Float.toString(FloatUtil.IntForFloat(Float.valueOf(newYearEleCoeffucientBean.getNewYearElectricityPre().toString()), Float.valueOf(newYearEleCoeffucientBean.getNewYearMechanicalTime()),3)));						
					}
					break;
				case "EH总":
					// 部门 
					newYearEleCoeffucientBean.setDepartment("EH");
					// 实绩比例 = 电量实绩平均/直接平均电量总计
					newYearEleCoeffucientBean.setProportionAct(newYearEleCoeffucientBean.getElectricityActAvg().divide(directEleAvgTotal, 4, RoundingMode.HALF_UP));
					//间接分摊 = 间接接平均电量总计*实绩比例
					newYearEleCoeffucientBean.setIndirectAllocation(indirectEleAvgTotal.multiply(newYearEleCoeffucientBean.getProportionAct()).setScale(3, RoundingMode.HALF_UP));
					//实绩总电量 = 电量实绩平均 + 间接分摊
					newYearEleCoeffucientBean.setElectricityActTotal(newYearEleCoeffucientBean.getElectricityActAvg().add(newYearEleCoeffucientBean.getIndirectAllocation()).setScale(3, RoundingMode.HALF_UP));
					
					if(newYearStoragePre != null) {
						// 新年度机械稼动UP率 = 新年度入库量予定/入库量实绩
						newYearEleCoeffucientBean.setNewYearMecUpRate(newYearStoragePre.getEhNewYearStoragePre().divide(storageInActEH, 3, RoundingMode.HALF_UP));
						// 新年度予定电量 = 电量实绩平均* 新年度机械稼动UP率+间接分摊
						newYearEleCoeffucientBean.setNewYearElectricityPre(newYearEleCoeffucientBean.getElectricityActAvg().multiply(newYearEleCoeffucientBean.getNewYearMecUpRate()).add(newYearEleCoeffucientBean.getIndirectAllocation()));
						//新年度机械时间 = 机械时间予定
						newYearEleCoeffucientBean.setNewYearMechanicalTime(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(totalDepartScrapAmountMap.get("EH").toString()), Double.parseDouble(newYearEleCoeffucientBean.getNewYearMecUpRate().toString()))));
						//新年度予定电量系数 = 新年度予定电量/新年度机械时间
						newYearEleCoeffucientBean.setNewYearElectricityPreSh(Float.toString(FloatUtil.IntForFloat(Float.valueOf(newYearEleCoeffucientBean.getNewYearElectricityPre().toString()), Float.valueOf(newYearEleCoeffucientBean.getNewYearMechanicalTime()),3)));						
					}
					break;
				}
			}
			return new HrPageResult(newYearEleCoeffucientBeanList, newYearEleCoeffucientBeanList.size());
		} else {
			return new HrPageResult(new ArrayList<NewYearEleCoeffucientBean>(), 0);
		}
    }
    /**
     * 新年度予定电量系数(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/11 10:18
     * @return
     */
    @GetMapping("/newYearEleCoefficientUF/list/view")
    public String newYearEleCoefficientUFView(Model model) throws Exception{
        return "/modules/yearRevise/newYearEleCoefficientUF_list";
    }
    
    /**
     * 新年度予定电量系数明细(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/11 10:18
     * @return
     */
    @PostMapping("/newYearEleCoefficientUF/list")
    @ResponseBody
    public Object newYearEleCoefficientUFList(short isSearch, String time) throws Exception{
		if (isSearch == 1) {
        	// 截取时间范围
        	String timeStartStr = time.substring(0, 7);
        	String timeEndStr = time.substring(time.length()-7);
        	
			List<NewYearEleCoeffucientBean> newYearEleCoeffucientBeanList = new ArrayList<>();
			
			// 检索条件部门
			String[] departUFArr = {"超细"} ;
			// 检索[电力使用明细表] 获取电量实绩平均
			List<NewYearEleCoeffucientBean> electricActAvgList=yearReviseService.selectElectricActAvgByDepart(departUFArr, timeStartStr, timeEndStr);
			// 超细实际电量平均
			BigDecimal ufEleActAvg = new BigDecimal(0);
			if (electricActAvgList != null && electricActAvgList.size() > 0) {
				for(NewYearEleCoeffucientBean electricActAvg:electricActAvgList) {
					ufEleActAvg = ufEleActAvg.add(electricActAvg.getElectricityActAvg());
				}
			}
			
			// 获取两个日期相差的月数(加权平均用)
        	int monthInterval =  DateUtils.getMonthDiff(timeStartStr,timeEndStr);
        	
			// 检索预定直接部门回收计算表超细不包含下记部门
			String[] departArr = {"UFINS", "DH", "DM"} ;
			newYearEleCoeffucientBeanList = yearReviseService.listDirectRecyclingForUF(departArr, monthInterval, timeStartStr, timeEndStr);
			
			if (newYearEleCoeffucientBeanList != null && newYearEleCoeffucientBeanList.size() > 0) {
				// 予定电量合计
				BigDecimal elePreTotal = new BigDecimal(0);
				for (NewYearEleCoeffucientBean elePreTemp : newYearEleCoeffucientBeanList) {
					elePreTotal = elePreTotal.add(elePreTemp.getElectricityPreAvg());
				}
				
				//新年度予定电量系数查询返回结果实体类
				for (NewYearEleCoeffucientBean newYearEleCoeffucientBean : newYearEleCoeffucientBeanList) {
					switch(newYearEleCoeffucientBean.getDepartment()) {
					case "DE":
						//予定比例 = 电量予定平均/予定电量合计
						newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP));
						//实绩电量分配 = 予定比例*超细实际电量平均
						newYearEleCoeffucientBean.setEleActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP));
						//新年度予定SYS系数 = 实绩电量分配/机械时间予定
						newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getEleActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
						break;	
				case "DFA":
					//予定比例 = 电量予定平均/予定电量合计
					newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP));
					//实绩电量分配 = 予定比例*超细实际电量平均
					newYearEleCoeffucientBean.setEleActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP));
					//新年度予定SYS系数 = 实绩电量分配/机械时间予定
					newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getEleActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
					break;	
				case "DS":
					//予定比例 = 电量予定平均/予定电量合计
					newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP));
					//实绩电量分配 = 予定比例*超细实际电量平均
					newYearEleCoeffucientBean.setEleActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP));
					//新年度予定SYS系数 = 实绩电量分配/机械时间予定
					newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getEleActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
					break;						
				case "DU":
					//予定比例 = 电量予定平均/予定电量合计
					newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP));
					//实绩电量分配 = 予定比例*超细实际电量平均
					newYearEleCoeffucientBean.setEleActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP));
					//新年度予定SYS系数 = 实绩电量分配/机械时间予定
					newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getEleActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
					break;						
				case "EE":
					//予定比例 = 电量予定平均/予定电量合计
					newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP));
					//实绩电量分配 = 予定比例*超细实际电量平均
					newYearEleCoeffucientBean.setEleActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP));
					//新年度予定SYS系数 = 实绩电量分配/机械时间予定
					newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getEleActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
					break;						
				case "ES":
					//予定比例 = 电量予定平均/予定电量合计
					newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP));
					//实绩电量分配 = 予定比例*超细实际电量平均
					newYearEleCoeffucientBean.setEleActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP));
					//新年度予定SYS系数 = 实绩电量分配/机械时间予定
					newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getEleActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
					break;						
				case "EU":
					//予定比例 = 电量予定平均/予定电量合计
					newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP));
					//实绩电量分配 = 予定比例*超细实际电量平均
					newYearEleCoeffucientBean.setEleActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP));
					//新年度予定SYS系数 = 实绩电量分配/机械时间予定
					newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getEleActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
					break;						
				case "EW":
					//予定比例 = 电量予定平均/予定电量合计
					newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP));
					//实绩电量分配 = 予定比例*超细实际电量平均
					newYearEleCoeffucientBean.setEleActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP));
					//新年度予定SYS系数 = 实绩电量分配/机械时间予定
					newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getEleActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
					break;
					}
				}
			}
			return new HrPageResult(newYearEleCoeffucientBeanList, newYearEleCoeffucientBeanList.size());
		} else {
			return new HrPageResult(new ArrayList<NewYearEleCoeffucientBean>(), 0);
		}
    }
    
    /**
     * 新年度予定氮气系数(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/11 13:18
     * @return
     */
    @GetMapping("/newYearNitrogenCoefficientUF/list/view")
    public String newYearNitrogenCoefficientUFView(Model model) throws Exception{
        return "/modules/yearRevise/newYearNitrogenCoefficientUF_list";
    }
    
    /**
     * 新年度予定氮气系数明细(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/11 13:18
     * @return
     */
    @PostMapping("/newYearNitrogenCoefficientUF/list")
    @ResponseBody
    public Object newYearNitrogenCoefficientUFList(short isSearch, String time) throws Exception{
		if (isSearch == 1) {
        	// 截取时间范围
        	String timeStartStr = time.substring(0, 7);
        	String timeEndStr = time.substring(time.length()-7);
        	
			List<NewYearEleCoeffucientBean> newYearEleCoeffucientBeanList = new ArrayList<>();
			//检索燃气费用表获取氮气实际使用量平均「部门:06 UF氮气  05 EH天然气 」
			BigDecimal nitrogenActAvg = yearReviseService.selectNitrogenActAvgByDateRange("06", timeStartStr, timeEndStr);
	
			// 获取两个日期相差的月数(加权平均用)
			int monthInterval = DateUtils.getMonthDiff(timeStartStr, timeEndStr);
			// 检索预定直接部门回收计算表超细不包含下记部门
			String[] departArr = {"DE", "DFA", "DS", "DU", "UFINS", "DH", "DM"} ;
			newYearEleCoeffucientBeanList = yearReviseService.listDirectRecyclingForUF(departArr, monthInterval, timeStartStr, timeEndStr);
			if (newYearEleCoeffucientBeanList != null && newYearEleCoeffucientBeanList.size() > 0) {
				// 予定氮气合计
				BigDecimal nitrogenPreTotal = new BigDecimal(0);
				for (NewYearEleCoeffucientBean nitrogenPreTemp : newYearEleCoeffucientBeanList) {
					if(nitrogenPreTemp.getNitrogenPreAvg() != null) {
						nitrogenPreTotal = nitrogenPreTotal.add(nitrogenPreTemp.getNitrogenPreAvg());
					}
				}
				
				//新年度予定氮气系数查询返回结果实体类
				for (NewYearEleCoeffucientBean newYearEleCoeffucientBean : newYearEleCoeffucientBeanList) {
					switch(newYearEleCoeffucientBean.getDepartment()) {
					case "EE":
						if(newYearEleCoeffucientBean.getNitrogenPreAvg().compareTo(BigDecimal.ZERO)==0) {
							;
						}else {
							//予定比例
							newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getNitrogenPreAvg().divide(nitrogenPreTotal, 3, RoundingMode.HALF_UP));
							//实绩氮气量分配 = 予定比例*超细实际氮气量平均
							newYearEleCoeffucientBean.setNitrogenActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(nitrogenActAvg).setScale(3, RoundingMode.HALF_UP));
							//新年度予定SYS系数
							newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getNitrogenActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
						}
						break;
					case "ES":
						if(newYearEleCoeffucientBean.getNitrogenPreAvg().compareTo(BigDecimal.ZERO)==0) {
							;
						}else {
							//予定比例
							newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getNitrogenPreAvg().divide(nitrogenPreTotal, 3, RoundingMode.HALF_UP));
							//实绩氮气量分配 = 予定比例*超细实际氮气量平均
							newYearEleCoeffucientBean.setNitrogenActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(nitrogenActAvg).setScale(3, RoundingMode.HALF_UP));
							//新年度予定SYS系数
							newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getNitrogenActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
						}
						break;
					case "EU":
						if(newYearEleCoeffucientBean.getNitrogenPreAvg().compareTo(BigDecimal.ZERO)==0) {
							;
						}else {
							//予定比例
							newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getNitrogenPreAvg().divide(nitrogenPreTotal, 3, RoundingMode.HALF_UP));
							//实绩氮气量分配 = 予定比例*超细实际氮气量平均
							newYearEleCoeffucientBean.setNitrogenActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(nitrogenActAvg).setScale(3, RoundingMode.HALF_UP));
							//新年度予定SYS系数
							newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getNitrogenActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
						}
						break;
					case "EW":
						if(newYearEleCoeffucientBean.getNitrogenPreAvg().compareTo(BigDecimal.ZERO)==0) {
							;
						}else {
							//予定比例
							newYearEleCoeffucientBean.setProportionPre(newYearEleCoeffucientBean.getNitrogenPreAvg().divide(nitrogenPreTotal, 3, RoundingMode.HALF_UP));
							//实绩氮气量分配 = 予定比例*超细实际氮气量平均
							newYearEleCoeffucientBean.setNitrogenActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(nitrogenActAvg).setScale(3, RoundingMode.HALF_UP));
							//新年度予定SYS系数
							newYearEleCoeffucientBean.setNewYearPreCoefficient(newYearEleCoeffucientBean.getNitrogenActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP).toString());
						}
						break;
					}	
				}
			}
			return new HrPageResult(newYearEleCoeffucientBeanList, newYearEleCoeffucientBeanList.size());
		} else {
			return new HrPageResult(new ArrayList<NewYearEleCoeffucientBean>(), 0);
		}
    }
    
    /*=================================人件费SH系数========================================*/
    /**
     * 人件工时实绩(MW)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/22 10:18
     * @return
     */
    @GetMapping("/perManHourShCoefficient/list/view")
    public String perManHourShCoefficientView(Model model) throws Exception{
        return "/modules/yearRevise/perManHourShCoefficient_list";
    }
    
    /**
     * 人件工时实绩明细(MW)
     * @param timeStartStr
     * @param timeEndStr
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/22 10:18
     * @return
     */
    @PostMapping("/perManHourShCoefficient/list")
    @ResponseBody
    public Object perManHourShCoefficientList(short isSearch, String time,String departmentCode) throws Exception{
        if(isSearch == 1){
        	// 截取时间范围
        	String timeStartStr = time.substring(0, 7);
        	String timeEndStr = time.substring(time.length()-7);
        	// 获取两个日期相差的月数(加权平均用)
        	int monthInterval =  DateUtils.getMonthDiff(timeStartStr,timeEndStr);
        	//人件费SH系数明细(MW)
        	List<PersonManHourShCoefficientBean> personManHourShCoefficientBeanList = yearReviseService.getPersonManHourShCoefficientBeanList(monthInterval, timeStartStr, timeEndStr, departmentCode);
        	return new HrPageResult(personManHourShCoefficientBeanList, personManHourShCoefficientBeanList.size());
        } else {
        	return new HrPageResult(new ArrayList<PersonManHourShCoefficientBean>(), 0);
        }
    }
    
    /**
     * 人件费excell导出(MW)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/23 10:43
     * @return
     */
    @PostMapping("/perManHourShCoefficient/export")
    @ResponseBody
    public Object perManHourShCoefficientExport(String timeForExcell, String departmentCode, HttpServletRequest req, HttpServletResponse resp) throws Exception{
        try {
        	// 截取时间范围
        	String timeStartStrForExcell = timeForExcell.substring(0, 7);
        	String timeEndStrForExcell = timeForExcell.substring(timeForExcell.length()-7);
        	
        	List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
            //拼接需要导出的数据
            List<String[]> dataset = new ArrayList<String[]>();
            // 获取两个日期相差的月数(加权平均用)
        	int monthInterval =  DateUtils.getMonthDiff(timeStartStrForExcell,timeEndStrForExcell);
        	//人件费SH系数明细(MW)
        	List<PersonManHourShCoefficientBean> personManHourShCoefficientBeanList = yearReviseService.getPersonManHourShCoefficientBeanList(monthInterval, timeStartStrForExcell, timeEndStrForExcell, departmentCode);
 
	        for(PersonManHourShCoefficientBean personManHourShCoefficientBean:personManHourShCoefficientBeanList){
	        	String[] arr = new String[6];
	        	arr[0] = personManHourShCoefficientBean.getMachineType(); //机械类别
	        	arr[1] = personManHourShCoefficientBean.getDepartmentCode(); //部门
	        	arr[2] = personManHourShCoefficientBean.getWorkType(); //人件工种
	        	arr[3] = String.valueOf(personManHourShCoefficientBean.getWorkHour()); //人件工时
	        	arr[4] = String.valueOf(personManHourShCoefficientBean.getMachineTimePreAvg()); //机械时间予定
	        	arr[5] = personManHourShCoefficientBean.getPersonManHourSH(); //人件工时SH系数
	        	dataset.add(arr);
	        }
	         //表头
			String[] handers = new String[7];
			handers[0] = "机械类别";
			handers[1] = "部门";
			handers[2] = "人件工种";
			handers[3] = "人件工时";
			handers[4] = "机械时间予定";
			handers[5] = "人件工时SH系数";
	
			// 设置数字列
			Integer[] numericColumnArr = new Integer[0];
			// 对象
			ExcelExp e = new ExcelExp("MW人件费SH系数明细", handers, dataset, numericColumnArr);
			mysheet.add(e);
			String fileName = "MW人件费SH系数明细_"+ DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss");
			ServletUtil su = new ServletUtil(fileName, req, resp);
			su.poiExcelServlet();
			ExcelExportUtil.exportManySheetExcel(su.getOut(), mysheet); // 生成sheet

        }catch (Exception e){
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
        }
        return new HrResult(CommonReturnCode.SUCCESS);  		
    }
    
    /**
     * 人件工时实绩(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/22 10:18
     * @return
     */
    @GetMapping("/perManHourShCoefficientUF/list/view")
    public String perManHourShCoefficientUFView(Model model) throws Exception{
        return "/modules/yearRevise/perManHourShCoefficientUF_list";
    }

    /**
     * 人件工时实绩明细(UF)
     * @param timeStartStr
     * @param timeEndStr
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/22 10:18
     * @return
     */
    @PostMapping("/perManHourShCoefficientUF/list")
    @ResponseBody
    public Object perManHourShCoefficientUFList(short isSearch, String time, String departmentCode) throws Exception{
    	if(isSearch == 1){
        	// 截取时间范围
        	String timeStartStr = time.substring(0, 7);
        	String timeEndStr = time.substring(time.length()-7);
        	
        	// 获取两个日期相差的月数(加权平均用)
        	int monthInterval =  DateUtils.getMonthDiff(timeStartStr,timeEndStr);
        	//人件费SH系数明细(MW)
        	List<PersonManHourShCoefficientBean> personManHourShCoefficientBeanList = yearReviseService.getUfPersonManHourShCoefficientBeanList(monthInterval, timeStartStr, timeEndStr, departmentCode);
	    	return new HrPageResult(personManHourShCoefficientBeanList, personManHourShCoefficientBeanList.size());
	    } else {
	    	return new HrPageResult(new ArrayList<PersonManHourShCoefficientBean>(), 0);
	    }
    }
    
    /**
     * 人件费excell导出(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/23 10:43
     * @return
     */
    @PostMapping("/perManHourShCoefficientUF/export")
    @ResponseBody
    public Object perManHourShCoefficientUFExport(String timeForExcell, String departmentCode, HttpServletRequest req, HttpServletResponse resp) throws Exception{
    	try {
        	// 截取时间范围
        	String timeStartStrForExcell = timeForExcell.substring(0, 7);
        	String timeEndStrForExcell = timeForExcell.substring(timeForExcell.length()-7);
        	
    		List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
    		//拼接需要导出的数据
    		List<String[]> dataset = new ArrayList<String[]>();
    		// 获取两个日期相差的月数(加权平均用)
    		int monthInterval =  DateUtils.getMonthDiff(timeStartStrForExcell,timeEndStrForExcell);
    		//人件费SH系数明细(MW)
    		List<PersonManHourShCoefficientBean> personManHourShCoefficientBeanList = yearReviseService.getUfPersonManHourShCoefficientBeanList(monthInterval, timeStartStrForExcell, timeEndStrForExcell, departmentCode);

    		for(PersonManHourShCoefficientBean personManHourShCoefficientBean:personManHourShCoefficientBeanList){
    			String[] arr = new String[7];
    			arr[0] = personManHourShCoefficientBean.getMachineType(); //机械类别
    			arr[1] = personManHourShCoefficientBean.getDepartmentCode(); //部门
    			arr[2] = personManHourShCoefficientBean.getWorkType(); //人件工种
    			arr[3] = String.valueOf(personManHourShCoefficientBean.getPersonManHourPreAvg()); //予定人件工时
    			arr[4] = String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg()); //实绩人件工时
    			arr[5] = String.valueOf(personManHourShCoefficientBean.getMachineTimePreAvg()); //机械时间予定
    			arr[6] = personManHourShCoefficientBean.getPersonManHourSH(); //人件工时SH系数
    			dataset.add(arr);
    		}
    		//表头
    		String[] handers = new String[7];
    		handers[0] = "机械类别";
    		handers[1] = "部门";
    		handers[2] = "人件工种";
    		handers[3] = "予定人件工时";
    		handers[4] = "实绩人件工时";
    		handers[5] = "机械时间予定";
    		handers[6] = "人件工时SH系数";

    		// 设置数字列
    		Integer[] numericColumnArr = new Integer[0];
    		// 对象
    		ExcelExp e = new ExcelExp("UF人件费SH系数明细", handers, dataset, numericColumnArr);
    		mysheet.add(e);
    		String fileName = "UF人件费SH系数明细_"+ DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss");
    		ServletUtil su = new ServletUtil(fileName, req, resp);
    		su.poiExcelServlet();
    		ExcelExportUtil.exportManySheetExcel(su.getOut(), mysheet); // 生成sheet

    	}catch (Exception e){
    		e.printStackTrace();
    		logger.error("异常信息：", e);
    		return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
    	}
    	return new HrResult(CommonReturnCode.SUCCESS);  	

    }
  
    /*=================================补修辅材分配额SH系数========================================*/
    /**
     * 补修辅材(MW)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/29 13:18
     * @return
     */
    @GetMapping("/repairAndAuxiliaryShCoefficient/list/view")
    public String repairAndAuxiliaryShCoefficientView(Model model) throws Exception{
        return "/modules/yearRevise/repairAndAuxiliaryShCoefficient_list";
    }

    /**
     * 补修辅材明细(MW)
     * @param timeStartStr
     * @param timeEndStr
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/22 10:18
     * @return
     */
    @PostMapping("/repairAndAuxiliaryShCoefficient/list")
    @ResponseBody
    public Object repairAndAuxiliaryShCoefficientList(short isSearch, String time) throws Exception{
    	  if(isSearch == 1){
    		  // 截取时间范围
    		  String timeStartStr = time.substring(0, 7);
    		  String timeEndStr = time.substring(time.length()-7);
    	      // 根据开始年月取得年度
    	      String year = YearUtil.getYear(timeStartStr);
    	      // 检索[补辅部门列表] 取得MW 的直接部门
    	      List<String> directDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_1, RepairAndAuxiliaryMaterialDepartment.CATEGORY_1);
    	      if(directDepartList ==null || directDepartList.size()==0) {
    	      	return new HrPageResult(new ArrayList<RepairAndAuxiliaryShCoefficientBean>(), 0);
    	      }
    	      	
    		  List<RepairAndAuxiliaryShCoefficientBean> repairAndAuxiliaryShCoefficientBeanList = new ArrayList<>();
	          	// 获取两个日期相差的月数(加权平均用)
	          	int monthInterval =  DateUtils.getMonthDiff(timeStartStr,timeEndStr);		
	
	          	// 补修分配额SH系数明细
	  			List<RepairAndAuxiliaryShCoefficientBean> repairShCoefficientList = yearReviseService.getRepairAndAuxiliaryShCoefficientBeanList(directDepartList, monthInterval, timeStartStr, timeEndStr, AuxiliaryRecycling.EXPENSEITEM_0080, "补修费");
	  			repairAndAuxiliaryShCoefficientBeanList.addAll(repairShCoefficientList);
	  			// 辅材分配额SH系数明细
	  			List<RepairAndAuxiliaryShCoefficientBean> auxiliaryShCoefficient = yearReviseService.getRepairAndAuxiliaryShCoefficientBeanList(directDepartList, monthInterval, timeStartStr, timeEndStr,AuxiliaryRecycling.EXPENSEITEM_0090, "辅材费");
	  			repairAndAuxiliaryShCoefficientBeanList.addAll(auxiliaryShCoefficient);
	  			
	    		return new HrPageResult(repairAndAuxiliaryShCoefficientBeanList, repairAndAuxiliaryShCoefficientBeanList.size());
          } else {
          		return new HrPageResult(new ArrayList<RepairAndAuxiliaryShCoefficientBean>(), 0);
          }
    }
    
    /**
     * 补修辅材明细excell导出(MW)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/23 10:43
     * @return
     */
    @PostMapping("/repairAndAuxiliaryShCoefficient/export")
    @ResponseBody
    public Object repairAndAuxiliaryShCoefficientExport(String timeForExcell, HttpServletRequest req, HttpServletResponse resp) throws Exception{
        try {
  		  // 截取时间范围
  		  String timeStartStrForExcell = timeForExcell.substring(0, 7);
  		  String timeEndStrForExcell = timeForExcell.substring(timeForExcell.length()-7);
	      	// 根据开始年月取得年度
	      	String year = YearUtil.getYear(timeStartStrForExcell);
      	
	      	// 检索[补辅部门列表] 取得MW 的直接部门
	      	List<String> directDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_1, RepairAndAuxiliaryMaterialDepartment.CATEGORY_1);
	      	if(directDepartList ==null || directDepartList.size()==0) {
	      		return new HrResult(CommonReturnCode.FAILED,"未设定补辅部门");
	      	}
      	
			List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
			// 拼接需要导出的数据
			List<String[]> dataset = new ArrayList<String[]>();

			List<RepairAndAuxiliaryShCoefficientBean> repairAndAuxiliaryShCoefficientBeanList = new ArrayList<>();
			// 获取两个日期相差的月数(加权平均用)
			int monthInterval = DateUtils.getMonthDiff(timeStartStrForExcell, timeEndStrForExcell);

			// 补修分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> repairShCoefficientList = yearReviseService.getRepairAndAuxiliaryShCoefficientBeanList(
					directDepartList, monthInterval, timeStartStrForExcell, timeEndStrForExcell, AuxiliaryRecycling.EXPENSEITEM_0080, "补修费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(repairShCoefficientList);
			// 辅材分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> auxiliaryShCoefficient = yearReviseService.getRepairAndAuxiliaryShCoefficientBeanList(
					directDepartList, monthInterval, timeStartStrForExcell, timeEndStrForExcell, AuxiliaryRecycling.EXPENSEITEM_0090, "辅材费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(auxiliaryShCoefficient);

			for (RepairAndAuxiliaryShCoefficientBean repairAndAuxiliaryShCoefficientBean : repairAndAuxiliaryShCoefficientBeanList) {
				String[] arr = new String[7];
				arr[0] = repairAndAuxiliaryShCoefficientBean.getMachineType(); // 机械类别
				arr[1] = repairAndAuxiliaryShCoefficientBean.getDepartment(); // 部门
				arr[2] = repairAndAuxiliaryShCoefficientBean.getExpenseItem(); // 费用项目
				arr[3] = String.valueOf(repairAndAuxiliaryShCoefficientBean.getMoneyPre()); // 予定分配金额
				arr[4] = String.valueOf(repairAndAuxiliaryShCoefficientBean.getMoneyAct()); // 实绩人件工时
				arr[5] = String.valueOf(repairAndAuxiliaryShCoefficientBean.getMachineTimePreAvg()); // 机械时间予定
				arr[6] = String.valueOf(repairAndAuxiliaryShCoefficientBean.getRepairAndAuxiliarySH()); // 补辅材分配额SH系数
				dataset.add(arr);
			}

			// 表头
			String[] handers = new String[7];
			handers[0] = "机械类别";
			handers[1] = "部门";
			handers[2] = "费用项目";
			handers[3] = "予定分配金额";
			handers[4] = "实际分配金额";
			handers[5] = "机械时间予定";
			handers[6] = "补辅材分配额SH系数";

			// 设置数字列
			Integer[] numericColumnArr = new Integer[0];
			// 对象
			ExcelExp e = new ExcelExp("MW补辅材分配额SH系数明细", handers, dataset, numericColumnArr);
			mysheet.add(e);
			String fileName = "MW补辅材分配额SH系数_"
					+ DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss");
			ServletUtil su = new ServletUtil(fileName, req, resp);
			su.poiExcelServlet();
			ExcelExportUtil.exportManySheetExcel(su.getOut(), mysheet); // 生成sheet

		} catch (Exception e) {
			e.printStackTrace();
			logger.error("异常信息：", e);
			return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
		}
		return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 补修辅材(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/01 10:18
     * @return
     */
    @GetMapping("/repairAndAuxiliaryShCoefficientUF/list/view")
    public String repairAndAuxiliaryShCoefficientUFView(Model model) throws Exception{
        return "/modules/yearRevise/repairAndAuxiliaryShCoefficientUF_list";
    }

    /**
     * 补修辅材明细(UF)
     * @param timeStartStr
     * @param timeEndStr
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/01 10:18
     * @return
     */
    @PostMapping("/repairAndAuxiliaryShCoefficientUF/list")
    @ResponseBody
    public Object repairAndAuxiliaryShCoefficientUFList(short isSearch, String time) throws Exception{
		if (isSearch == 1) {
  		  // 截取时间范围
  		  String timeStartStr = time.substring(0, 7);
  		  String timeEndStr = time.substring(time.length()-7);
  		  
			List<RepairAndAuxiliaryShCoefficientBean> repairAndAuxiliaryShCoefficientBeanList = new ArrayList<>();
			// 获取两个日期相差的月数(加权平均用)
			int monthInterval = DateUtils.getMonthDiff(timeStartStr, timeEndStr);
			// 补修分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> repairShCoefficientBeanList = yearReviseService.getRepairAndAuxiliaryShCoefficientUFBeanList(
					monthInterval, timeStartStr, timeEndStr, "补修费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(repairShCoefficientBeanList);
			// 辅材分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> auxiliaryShCoefficientBeanList = yearReviseService.getRepairAndAuxiliaryShCoefficientUFBeanList(
					monthInterval, timeStartStr, timeEndStr, "辅材费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(auxiliaryShCoefficientBeanList);

			return new HrPageResult(repairAndAuxiliaryShCoefficientBeanList, repairAndAuxiliaryShCoefficientBeanList.size());
		} else {
			return new HrPageResult(new ArrayList<RepairAndAuxiliaryShCoefficientBean>(), 0);
		}
    }
    /**
     *  补修辅材明细excell导出(UF)
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/23 10:43
     * @return
     */
    @PostMapping("/repairAndAuxiliaryShCoefficientUF/export")
    @ResponseBody
    public Object repairAndAuxiliaryShCoefficientUFExport(String timeForExcell, HttpServletRequest req, HttpServletResponse resp) throws Exception{
        try {
    		 // 截取时间范围
    		 String timeStartStrForExcell = timeForExcell.substring(0, 7);
    		 String timeEndStrForExcell = timeForExcell.substring(timeForExcell.length()-7);
    		 
			List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
			// 拼接需要导出的数据
			List<String[]> dataset = new ArrayList<String[]>();

			List<RepairAndAuxiliaryShCoefficientBean> repairAndAuxiliaryShCoefficientBeanList = new ArrayList<>();
			// 获取两个日期相差的月数(加权平均用)
			int monthInterval = DateUtils.getMonthDiff(timeStartStrForExcell, timeEndStrForExcell);

			// 补修分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> repairShCoefficientBeanList = yearReviseService.getRepairAndAuxiliaryShCoefficientUFBeanList(
					monthInterval, timeStartStrForExcell, timeEndStrForExcell, "补修费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(repairShCoefficientBeanList);
			// 辅材分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> auxiliaryShCoefficientBeanList = yearReviseService.getRepairAndAuxiliaryShCoefficientUFBeanList(
					monthInterval, timeStartStrForExcell, timeEndStrForExcell, "辅材费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(auxiliaryShCoefficientBeanList);

			for (RepairAndAuxiliaryShCoefficientBean repairAndAuxiliaryShCoefficientBean : repairAndAuxiliaryShCoefficientBeanList) {
				String[] arr = new String[7];
				arr[0] = repairAndAuxiliaryShCoefficientBean.getMachineType(); // 机械类别
				arr[1] = repairAndAuxiliaryShCoefficientBean.getDepartment(); // 部门
				arr[2] = repairAndAuxiliaryShCoefficientBean.getExpenseItem(); // 费用项目
				arr[3] = String.valueOf(repairAndAuxiliaryShCoefficientBean.getMoneyPre()); // 予定分配金额
				arr[4] = String.valueOf(repairAndAuxiliaryShCoefficientBean.getMoneyAct()); // 实绩人件工时
				arr[5] = String.valueOf(repairAndAuxiliaryShCoefficientBean.getMachineTimePreAvg()); // 机械时间予定
				arr[6] = String.valueOf(repairAndAuxiliaryShCoefficientBean.getRepairAndAuxiliarySH()); // 补辅材分配额SH系数
				dataset.add(arr);
			}

			// 表头
			String[] handers = new String[7];
			handers[0] = "机械类别";
			handers[1] = "部门";
			handers[2] = "费用项目";
			handers[3] = "予定分配金额";
			handers[4] = "实际分配金额";
			handers[5] = "机械时间予定";
			handers[6] = "补辅材分配额SH系数";

			// 设置数字列
			Integer[] numericColumnArr = new Integer[0];
			// 对象
			ExcelExp e = new ExcelExp("UF补辅材分配额SH系数明细", handers, dataset, numericColumnArr);
			mysheet.add(e);
			String fileName = "UF补辅材分配额SH系数_"
					+ DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss");
			ServletUtil su = new ServletUtil(fileName, req, resp);
			su.poiExcelServlet();
			ExcelExportUtil.exportManySheetExcel(su.getOut(), mysheet); // 生成sheet

		} catch (Exception e) {
			e.printStackTrace();
			logger.error("异常信息：", e);
			return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
		}
		return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /*=================================运输费用查询======================================*/
    /**
     * 运输费用查询页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/23 13:43
     * @return
     */
    @GetMapping("/transportationCostSearch/list/view")
    public String transportationCostSearchListView(Model model) throws Exception{
        return "/modules/yearRevise/transportationCostSearch_list";
    }
    
    /**
     * 运输费用查询
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/23 13:43
     * @return
     */
    @PostMapping("/transportationCostSearch/list")
    @ResponseBody
    public Object transportCostListPage(short isSearch,String yearMonthStart, String yearMonthEnd) throws Exception{
        if(isSearch == 1){
        	// 根据年月范围取得回收运输费平均单价」
        	String  recycleTransportationCostAvgUnit = yearReviseService.selectRecycleTransportationCostAvgUnit(yearMonthStart, yearMonthEnd);
        	
        	 List<TransportationCostBean> transportationCostBeanList = yearReviseService.selectTransportationCostBeanList(yearMonthStart, yearMonthEnd);
        	 for(TransportationCostBean transportationCostBean : transportationCostBeanList) {
        		 transportationCostBean.setRecycleTransportationCostAvgUnit(recycleTransportationCostAvgUnit); //回收运输费平均单价
        		 // 综合运费(含回收) = 运输费单价+回收运输费平均单价
        		 transportationCostBean.setComprehensiveFreight(new BigDecimal(transportationCostBean.getTransportationCostUnit()).add(new BigDecimal(recycleTransportationCostAvgUnit)).toString()); 
        	 }
            return new HrPageResult(transportationCostBeanList, transportationCostBeanList.size());
        }else{
            return new HrPageResult(new ArrayList<TransportationCostBean>(), 0);
        }
    }

	/*=================================部门单价汇总======================================*/
    /**
     * 部门单价汇总页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @GetMapping("/summaryOfDepartmentalUnitPrices/list/view")
    public String summaryOfDepartmentalUnitPricesListView(Model model) throws Exception{
        return "/modules/yearRevise/summaryOfDepartmentalUnitPrices_list";
    }

	/**
     * 部门单价汇总查询
     * @param isSearch 是否查询
     * @param time 日期
     * @param machineType 机械类别
     * @param pageType 页面类型(MW/UF)
     * @throws Exception
     * <AUTHOR>
     * @return
     */
    @PostMapping("/summaryOfDepartmentalUnitPrices/list")
    @ResponseBody
    public Object summaryOfDepartmentalUnitPricesListPage(short isSearch, String time, String machineType, String pageType) throws Exception{
        if(isSearch == 1){
			// 获取部门单价汇总数据集合
			List<SummaryOfDepartmentalUnitPricesBean> summaryOfDepartmentalUnitPricesBeanList = yearReviseService.getSummaryOfDepartmentalUnitPricesList(time, machineType, pageType);

            return new HrPageResult(summaryOfDepartmentalUnitPricesBeanList, summaryOfDepartmentalUnitPricesBeanList.size());
        }else{
            return new HrPageResult(new ArrayList<SummaryOfDepartmentalUnitPricesBean>(), 0);
        }

    }

	/**
	 * 部门单价汇总excell导出(MW/UF)
	 * @param timeForExcell
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/23 10:43
	 * @return
	 */
	@PostMapping("/summaryOfDepartmentalUnitPrices/export")
	@ResponseBody
	public Object summaryOfDepartmentalUnitPricesExport(String timeForExcell, String machineTypeForExcell, String pageTypeForExcell, HttpServletRequest req, HttpServletResponse resp) throws Exception{
		try {
			// 获取部门单价汇总数据集合
			List<SummaryOfDepartmentalUnitPricesBean> summaryOfDepartmentalUnitPricesBeanList = yearReviseService.getSummaryOfDepartmentalUnitPricesList(timeForExcell, machineTypeForExcell, pageTypeForExcell);

			if (CollectionUtils.isNotEmpty(summaryOfDepartmentalUnitPricesBeanList)) {
				List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
				// 拼接需要导出的数据
				List<String[]> dataset = new ArrayList<String[]>();

				// 一级表头
				String[] firstLevelHanders = new String[16];
				firstLevelHanders[0] = "机械类别";
				firstLevelHanders[1] = "正式工";
				firstLevelHanders[2] = "劳务工";
				firstLevelHanders[3] = "保全劳务工";
				firstLevelHanders[4] = "保全正式工";
				firstLevelHanders[5] = "补修费";
				firstLevelHanders[6] = "辅材费";
				firstLevelHanders[7] = "辅助 汇总";
				firstLevelHanders[8] = "正式工";
				firstLevelHanders[9] = "劳务工";
				firstLevelHanders[10] = "电力";
				firstLevelHanders[11] = "天然气/氮气";
				firstLevelHanders[12] = "补修费";
				firstLevelHanders[13] = "辅材费";
				firstLevelHanders[14] = "直接 汇总";
				firstLevelHanders[15] = "总计";

				// 根据机械类别分组
				Map<String, List<SummaryOfDepartmentalUnitPricesBean>> summaryOfDepartmentalUnitPricesBeanMap = summaryOfDepartmentalUnitPricesBeanList.stream().collect(Collectors.groupingBy(SummaryOfDepartmentalUnitPricesBean::getMachineType));
				// 按照
				Map<String, List<SummaryOfDepartmentalUnitPricesBean>> sortedSummaryOfDepartmentalUnitPricesBeanMap = new TreeMap<>(summaryOfDepartmentalUnitPricesBeanMap);
				// 拼接需要导出的数据
				for (String machineType : sortedSummaryOfDepartmentalUnitPricesBeanMap.keySet()) {
					String[] arr = new String[16];
					arr[0] = machineType; // 机械类别

					// 设默认值
					arr[1] = "0";
					arr[2] = "0";
					arr[3] = "0";
					arr[4] = "0";
					arr[5] = "0";
					arr[6] = "0";
					arr[7] = "0";
					arr[8] = "0";
					arr[9] = "0";
					arr[10] = "0";
					arr[11] = "0";
					arr[12] = "0";
					arr[13] = "0";
					arr[14] = "0";
					arr[15] = "0";

					List<SummaryOfDepartmentalUnitPricesBean> summaryOfDepartmentalUnitPricesBeans = sortedSummaryOfDepartmentalUnitPricesBeanMap.get(machineType);

					// 直接汇总
					BigDecimal directProjectCostTotal = BigDecimal.ZERO;
					// 辅助汇总
					BigDecimal auxiliaryProjectCostTotal = BigDecimal.ZERO;

					if (CollectionUtils.isNotEmpty(summaryOfDepartmentalUnitPricesBeans)) {
						for (SummaryOfDepartmentalUnitPricesBean summaryOfDepartmentalUnitPricesBean : summaryOfDepartmentalUnitPricesBeans) {

							if ("辅助".equals(summaryOfDepartmentalUnitPricesBean.getAttribute()) 
									//UF特殊处理：属于直接部门，但是要和MW格式统一，数据落在「保全正式工」下
									||"保全".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
								// 项目费用
								BigDecimal projectCost = Optional.ofNullable(summaryOfDepartmentalUnitPricesBean.getProjectCost()).orElse(BigDecimal.ZERO);
								BigDecimal projectCostTemp = BigDecimal.ZERO;
								if ("正式工".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[1])) {
										arr[1] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[1]);
										arr[1] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								if ("劳务工".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[2])) {
										arr[2] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[2]);
										arr[2] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								if ("保全劳务工".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[3])) {
										arr[3] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[3]);
										arr[3] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}

								if ("保全正式工".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())
										//UF特殊处理：属于直接部门，但是要和MW格式统一，数据落在「保全正式工」下
										||"保全".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[4])) {
										arr[4] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[4]);
										arr[4] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								if ("补修费".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[5])) {
										arr[5] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[5]);
										arr[5] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}

								if ("辅材费".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[6])) {
										arr[6] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[6]);
										arr[6] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								// 辅助项目费用汇总
								auxiliaryProjectCostTotal = auxiliaryProjectCostTotal.add(projectCost);
							} else {
								// 项目费用
								BigDecimal projectCost = Optional.ofNullable(summaryOfDepartmentalUnitPricesBean.getProjectCost()).orElse(BigDecimal.ZERO);
								BigDecimal projectCostTemp = BigDecimal.ZERO;
								if ("正式工".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[8])) {
										arr[8] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[8]);
										arr[8] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								if ("劳务工".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[9])) {
										arr[9] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[9]);
										arr[9] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								if ("电力".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[10])) {
										arr[10] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[10]);
										arr[10] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								if ("天然气/氮气".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[11])) {
										arr[11] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[11]);
										arr[11] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								if ("补修费".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[12])) {
										arr[12] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[12]);
										arr[12] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								if ("辅材费".equals(summaryOfDepartmentalUnitPricesBean.getExpenseItem())) {
									if(StringUtil.isStringEmpty(arr[13])) {
										arr[13] = projectCost.toString();
									} else {
										BigDecimal bigDecimalArr = new BigDecimal(arr[13]);
										arr[13] = bigDecimalArr.add(projectCost).toString();
										projectCostTemp = projectCost;
									}
								}
								// 直接项目费用汇总
								directProjectCostTotal = directProjectCostTotal.add(projectCostTemp);
							}
						}

						// 辅助汇总
						arr[7] = auxiliaryProjectCostTotal.toString();
						// 直接汇总
						arr[14] = directProjectCostTotal.toString();
						// 总计
						arr[15] = auxiliaryProjectCostTotal.add(directProjectCostTotal).toString();
					}

					dataset.add(arr);
				}
				
				// 设置数字列
				Integer[] numericColumnArr = new Integer[0];
				// 对象
				ExcelExp e = new ExcelExp(pageTypeForExcell + "部门单价汇总明细", firstLevelHanders, dataset, numericColumnArr);
				mysheet.add(e);
				String fileName = pageTypeForExcell + "部门单价汇总_"
						+ DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss");

				ServletUtil su = new ServletUtil(fileName, req, resp);
				su.poiExcelServlet();
				ExcelExportUtil.exportManySheetExcel(su.getOut(), mysheet); // 生成sheet
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("异常信息：", e);
			return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
		}
		return new HrResult(CommonReturnCode.SUCCESS);
	}
}
