	@charset "utf-8";
a:focus {
	outline: none;
}
html, body {
	height: 100%;
}
.block {
	display: block;
}
.clear {
	display: block;
	overflow: hidden;
}
a {
	cursor: pointer;
}
a:hover, a:focus {
	text-decoration: none;
}
.grey-bg {
	background-color: #f5f7f9;
}
.ibox-content {
	background-color: #ffffff;
	color: inherit;
	padding: 10px;
	border-color: #e7eaec;
	-webkit-border-image: none;
	-o-border-image: none;
	border-image: none;
	border-style: none;
	border-width: 0px;
	height: calc(100vh - 20px);
	clear: both;
}
.layui-collapse{
	margin-bottom: 10px;
}
.layui-btn + .layui-btn {
  margin-left: 5px;
}
/* 图标选择 */
.layui-iconpicker-item{
	height: 36px!important;
	border-radius: 2px!important;
}
.layui-iconpicker.layui-form-selected .layui-anim {
	width: 300px;
}
/* treeTable图标 */
/** 修改文件夹图标 */
.layui-icon-layer:before {
    content: ""!important;
}
/** 修改文件图标 */
.layui-icon-file:before {
    content: ""!important;
}
/** 箭头未展开 */
.ew-tree-table .ew-tree-table-arrow::before {
    content: "\e602"!important;
}
/** 箭头展开 */
.ew-tree-table .ew-tree-table-open .ew-tree-table-arrow::before {
    content: "\e61a"!important;
}
/* 搜索框 */
.layui-colla-content{
	padding: 10px 10px 0 10px;
}
.layui-layer-btn .layui-layer-btn0 {
	border-color: #009688 !important;
	background-color: #009688 !important;
	color: #fff !important;
}
.layui-layer-btn .layui-layer-btn1 {
	border: 1px solid #C9C9C9 !important;
	background-color: #fff !important;
	color: #555 !important;
}
.hr-form-add .layui-form-label {
	width: 120px ;
	color: #595959;
}
.hr-form-add .layui-input-block {
	margin-left: 150px !important;
}
.hr-form-add .star{
	color:red;
	padding-right:2px ;
}
.layui-fluid{
	padding-bottom: 20px;
}
.hr-form-search .layui-inline {
	margin-right: 0px !important;
}
.hr-form-search .layui-form-item {
	margin-bottom: 0px !important;
}
.hr-form-search .hr-div-btn {
	text-align: right;
}
.hr-form-add{
	background: #F5F7F9 !important;
}
.hr-layui-fluid {
	padding: 10px !important;
}
.layui-card-body {
  padding: 10px;
}
.layui-form-item {
  margin-bottom: 0px;
}
.layui-form-item .layui-inline {
  margin-bottom: 10px;
}
/* tab */
.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title{
	margin-right: 80px;
}
.admin-tabs-control.layui-icon-next {
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .1);
}
.admin-tabs-control.layui-icon-down {
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .1);
}