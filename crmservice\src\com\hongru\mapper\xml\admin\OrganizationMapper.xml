<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.admin.OrganizationMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.hongru.entity.admin.Organization">
		<id column="organization_id" property="organizationId" />
		<result column="organization_name" property="organizationName" />
		<result column="is_system" property="isSystem" />
		<result column="status" property="status" />
		<result column="create_time" property="createTime" />
		<result column="create_by" property="createBy" />
		<result column="update_time" property="updateTime" />
		<result column="update_by" property="updateBy" />
		<result column="remarks" property="remarks" />
		<result column="orderNo" property="orderNo" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        organization_id AS organizationId, organization_name AS organizationName, is_system AS isSystem, status, create_time AS createTime, create_by AS createBy, update_time AS updateTime, update_by AS updateBy, remarks,orderNo
    </sql>
    
    <!-- 根据分页信息/搜索内容查找部门列表 -->
	<sql id="listByPage_where">
		<if test="search != null">
			AND (
			organization_name LIKE  #{search}
			OR create_by LIKE  #{search}
			OR update_by LIKE  #{search}
			OR remarks LIKE  #{search}
			)
		</if>
	</sql>
	<select id="listByPage" resultType="com.hongru.entity.admin.Organization">
		SELECT
        	organization_id AS organizationId, 
        	organization_name AS organizationName, 
        	is_system AS isSystem, 
        	status, 
        	create_time AS createTime, 
        	create_by AS createBy, 
        	update_time AS updateTime, 
        	update_by AS updateBy, 
        	remarks,
			orderNo
        FROM
        	hr_admin_organization
    	<where>
			<include refid="listByPage_where"></include>
    	</where>
		ORDER BY orderNo ASC,organization_id DESC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	<select id="listByPageCount" resultType="integer">
		SELECT
			COUNT(1)
		FROM
			hr_admin_organization
		<where>
			<include refid="listByPage_where"></include>
		</where>
	</select>

	<delete id="deleteOrganizations">
		DELETE FROM hr_admin_organization
		<where>
			<if test="organizationIdArr != null">
				AND organization_id in
				<foreach collection="organizationIdArr" index="index" item="organization_id" open="(" separator="," close=")">
					#{organization_id }
				</foreach>
			</if>
		</where>
	</delete>
</mapper>
