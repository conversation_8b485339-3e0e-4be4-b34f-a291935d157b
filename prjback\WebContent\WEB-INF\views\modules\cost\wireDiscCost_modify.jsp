<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>线盘费用添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/wireDiscCost/modify" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年度：</label>
	                       	<div class="layui-input-block">
                                 <input type="text" class="layui-input" id="year" name="year" value="${wireDiscCost.year}" />
                            </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>线盘型号：</label>
                        <div class="layui-input-block">
                             <select class="layui-select" id="wireDiscType" name="wireDiscType" lay-verify="required"  lay-search="true" required>
                                  <option value="${wireDiscCost.wireDiscType}">${wireDiscCost.wireDiscType}</option>
										<c:forEach items="${wireDiscTypeList}" var="wireDiscType">
											<option value="${wireDiscType}">${wireDiscType}</option>
										</c:forEach>
                             </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>单价(RMB/个):</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="buyPrice" name="buyPrice" value="${wireDiscCost.buyPrice}" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>标准回收率:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="standardRate" name="standardRate" value="${wireDiscCost.standardRate}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>回收率实绩:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="recoveryRate" name="recoveryRate" value="${wireDiscCost.recoveryRate}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>报废率实绩:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="scrapRate" name="scrapRate" value="${wireDiscCost.scrapRate}"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>单重:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="singleWeight" name="singleWeight" value="${wireDiscCost.singleWeight}" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                       	    <input type="hidden" id="costId" name="costId" value="${wireDiscCost.costId}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/wireDiscCost_add.js?time=6"></script>
</myfooter>
</body>
</html>
