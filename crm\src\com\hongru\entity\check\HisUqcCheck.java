package com.hongru.entity.check;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("his_UQC检查表")//QC
public class HisUqcCheck {
	public static final String CHECK_QCDR_NORMAL  = "0";//正常
	public static final String CHECK_QCDR_ERROR   = "1";//超出规格

	public static final short CHECK_ISZJCHECKED_NO   = 0;//没做过
	public static final short CHECK_ISZJCHECKED_YES  = 1;//做过

	public static final short CHECK_ISNEEDADDSEVENDAYS_NO   = 0;//不需要
	public static final short CHECK_ISNEEDADDSEVENDAYS_YES  = 1;//需要
	/* 流水号 */
	@TableId(value="checkId", type= IdType.AUTO)
	protected int checkId;
	protected String dateTime;//日期
	protected String customerCode;//客户代码（客户名称）
	protected String productCode;//产品代码
	protected String checkCategory;//检查种类（如：初物检查、日常检查等）
	protected String checkItem;//检查项目
	protected String itemCode;//项目编号  //20191125新增字段 gaoxi
	protected Integer orderNo;//序号
	protected String frequentness;//频度
	protected String condition;//试验条件
	protected String unit;//单位
	protected String direction1;//方向1
	protected String usl;//规格上限
	protected String direction2;//方向2
	protected String lsl;//规格下限
	protected String entry1;//输入值1
	protected String entry2;//输入值2
	protected String entry3;//输入值3
	protected String entry4;//输入值4
	protected String entry5;//输入值5
	protected String entry6;//输入值6
	protected String entry7;//输入值7
	protected String entry8;//输入值8
	protected String entry9;//输入值9
	protected String entry10;//输入值10
	protected String entry11;//输入值11
	protected String entry12;//输入值12
	protected String checkCategoryCode;//检查种类代号（如：初检的代号为CJ,日检为RJ）
	protected String fileNumber;//文件号
	protected String recordNumber;//记录号
	protected String modifyNumber;//修改号
	protected Integer count;//数量
	protected String barCode;//条码
	protected String  batchNumber;//批号（LOT号）
	protected String serialNumber ;//序列号
	protected String paint;//油漆
	protected String lineNumber;//线头号
	protected String weight;//荷重
	protected String qcdr;//QCDR
	protected String machineNumber;//设备代码
	protected String checkItemTime;//检查日期
	protected String shift;//班（早，中，晚）
	protected String sa;
	protected String creatorName;//操作员姓名
	protected String comment;//备注
	protected String entryMin;//最小值
	protected String entryAverage;//平均值
	protected String entryMax;//最大值
	protected String queryConfirm;//查询确认
	protected String confirmTime;//确认时间
	protected String comfirmer;//确认者

	public int getCheckId() {
		return checkId;
	}

	public void setCheckId(int checkId) {
		this.checkId = checkId;
	}

	public String getDateTime() {
		return dateTime;
	}

	public void setDateTime(String dateTime) {
		this.dateTime = dateTime;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getCheckCategory() {
		return checkCategory;
	}

	public void setCheckCategory(String checkCategory) {
		this.checkCategory = checkCategory;
	}

	public String getCheckItem() {
		return checkItem;
	}

	public void setCheckItem(String checkItem) {
		this.checkItem = checkItem;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public Integer getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}

	public String getFrequentness() {
		return frequentness;
	}

	public void setFrequentness(String frequentness) {
		this.frequentness = frequentness;
	}

	public String getCondition() {
		return condition;
	}

	public void setCondition(String condition) {
		this.condition = condition;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getDirection1() {
		return direction1;
	}

	public void setDirection1(String direction1) {
		this.direction1 = direction1;
	}

	public String getUsl() {
		return usl;
	}

	public void setUsl(String usl) {
		this.usl = usl;
	}

	public String getDirection2() {
		return direction2;
	}

	public void setDirection2(String direction2) {
		this.direction2 = direction2;
	}

	public String getLsl() {
		return lsl;
	}

	public void setLsl(String lsl) {
		this.lsl = lsl;
	}

	public String getEntry1() {
		return entry1;
	}

	public void setEntry1(String entry1) {
		this.entry1 = entry1;
	}

	public String getEntry2() {
		return entry2;
	}

	public void setEntry2(String entry2) {
		this.entry2 = entry2;
	}

	public String getEntry3() {
		return entry3;
	}

	public void setEntry3(String entry3) {
		this.entry3 = entry3;
	}

	public String getEntry4() {
		return entry4;
	}

	public void setEntry4(String entry4) {
		this.entry4 = entry4;
	}

	public String getEntry5() {
		return entry5;
	}

	public void setEntry5(String entry5) {
		this.entry5 = entry5;
	}

	public String getEntry6() {
		return entry6;
	}

	public void setEntry6(String entry6) {
		this.entry6 = entry6;
	}

	public String getEntry7() {
		return entry7;
	}

	public void setEntry7(String entry7) {
		this.entry7 = entry7;
	}

	public String getEntry8() {
		return entry8;
	}

	public void setEntry8(String entry8) {
		this.entry8 = entry8;
	}

	public String getEntry9() {
		return entry9;
	}

	public void setEntry9(String entry9) {
		this.entry9 = entry9;
	}

	public String getEntry10() {
		return entry10;
	}

	public void setEntry10(String entry10) {
		this.entry10 = entry10;
	}

	public String getEntry11() {
		return entry11;
	}

	public void setEntry11(String entry11) {
		this.entry11 = entry11;
	}

	public String getEntry12() {
		return entry12;
	}

	public void setEntry12(String entry12) {
		this.entry12 = entry12;
	}

	public String getCheckCategoryCode() {
		return checkCategoryCode;
	}

	public void setCheckCategoryCode(String checkCategoryCode) {
		this.checkCategoryCode = checkCategoryCode;
	}

	public String getFileNumber() {
		return fileNumber;
	}

	public void setFileNumber(String fileNumber) {
		this.fileNumber = fileNumber;
	}

	public String getRecordNumber() {
		return recordNumber;
	}

	public void setRecordNumber(String recordNumber) {
		this.recordNumber = recordNumber;
	}

	public String getModifyNumber() {
		return modifyNumber;
	}

	public void setModifyNumber(String modifyNumber) {
		this.modifyNumber = modifyNumber;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public String getBatchNumber() {
		return batchNumber;
	}

	public void setBatchNumber(String batchNumber) {
		this.batchNumber = batchNumber;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getPaint() {
		return paint;
	}

	public void setPaint(String paint) {
		this.paint = paint;
	}

	public String getLineNumber() {
		return lineNumber;
	}

	public void setLineNumber(String lineNumber) {
		this.lineNumber = lineNumber;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getQcdr() {
		return qcdr;
	}

	public void setQcdr(String qcdr) {
		this.qcdr = qcdr;
	}

	public String getMachineNumber() {
		return machineNumber;
	}

	public void setMachineNumber(String machineNumber) {
		this.machineNumber = machineNumber;
	}

	public String getCheckItemTime() {
		return checkItemTime;
	}

	public void setCheckItemTime(String checkItemTime) {
		this.checkItemTime = checkItemTime;
	}

	public String getShift() {
		return shift;
	}

	public void setShift(String shift) {
		this.shift = shift;
	}

	public String getSa() {
		return sa;
	}

	public void setSa(String sa) {
		this.sa = sa;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getEntryMin() {
		return entryMin;
	}

	public void setEntryMin(String entryMin) {
		this.entryMin = entryMin;
	}

	public String getEntryAverage() {
		return entryAverage;
	}

	public void setEntryAverage(String entryAverage) {
		this.entryAverage = entryAverage;
	}

	public String getEntryMax() {
		return entryMax;
	}

	public void setEntryMax(String entryMax) {
		this.entryMax = entryMax;
	}

	public String getQueryConfirm() {
		return queryConfirm;
	}

	public void setQueryConfirm(String queryConfirm) {
		this.queryConfirm = queryConfirm;
	}

	public String getConfirmTime() {
		return confirmTime;
	}

	public void setConfirmTime(String confirmTime) {
		this.confirmTime = confirmTime;
	}

	public String getComfirmer() {
		return comfirmer;
	}

	public void setComfirmer(String comfirmer) {
		this.comfirmer = comfirmer;
	}
}