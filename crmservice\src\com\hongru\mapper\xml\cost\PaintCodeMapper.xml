<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.PaintCodeMapper">

    <sql id="paintCode_sql">
		pc.[流水号] AS codeId,pc.[状态] AS state,pc.[社内油漆名] AS insidePaintName,
		pc.[成本编码] AS paintCode,pc.[创建人标识] AS creatorId,pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[最后修改人标识] AS lastModifierId,pc.[最后修改人姓名] AS lastModifierName,pc.[最后修改时间] AS lastModifiedTime
	</sql>

    <insert id="insertPaintCode" parameterType="com.hongru.entity.cost.PaintCode" useGeneratedKeys="true" keyProperty="paintCode.codeId">
        INSERT INTO [CostPrice].[dbo].[油漆成本编码表]
		(
		[状态],
		[社内油漆名],
		[成本编码],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[最后修改人标识],
		[最后修改人姓名],
		[最后修改时间]
		)VALUES(
		#{paintCode.state},
		#{paintCode.insidePaintName},
		#{paintCode.paintCode},
		#{paintCode.creatorId},
		#{paintCode.creatorName},
		#{paintCode.createdTime},
		#{paintCode.lastModifierId},
		#{paintCode.lastModifierName},
		#{paintCode.lastModifiedTime}
		)
    </insert>

    <select id="selectByCostId" resultType="com.hongru.entity.cost.PaintCode">
        SELECT
        <include refid="paintCode_sql"/>
        FROM [CostPrice].[dbo].[油漆成本编码表] pc
        <where>
            pc.[状态] != 9
            <if test="codeId != null">
                AND pc.[流水号] = #{codeId}
            </if>
        </where>
    </select>

	<select id="getPaintCodeByPaintName" resultType="com.hongru.entity.cost.PaintCode">
		SELECT
		<include refid="paintCode_sql"/>
		FROM [CostPrice].[dbo].[油漆成本编码表] pc
		<where>
			pc.[状态] != 9
			<if test="paintName != null and paintName != ''">
				AND pc.[流水号] = #{paintName}
			</if>
		</where>
	</select>

    <select id="selectByYearMonth" resultType="com.hongru.entity.cost.PaintCode">
        SELECT
        <include refid="paintCode_sql"/>
        FROM [CostPrice].[dbo].[油漆成本编码表] pc
        <where>
            pc.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND pc.[社内油漆名] = #{yearMonth}
            </if>
        </where>
    </select>

	<select id="listPaintCodePage" resultType="com.hongru.entity.cost.PaintCode">
		SELECT
		<include refid="paintCode_sql"/>
		FROM [CostPrice].[dbo].[油漆成本编码表] pc
		<where>
			pc.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND pc.[年月] = #{yearMonth}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY pc.[流水号] DESC
		</if>
	</select>

	<select id="listPaintCodePageCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[油漆成本编码表] pc
		<where>
			pc.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND pc.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<update id="updateState">
		UPDATE [CostPrice].[dbo].[油漆成本编码表]
		SET [状态] = #{state}
		WHERE [流水号] = #{codeId}
	</update>

	<select id="listPaintCodePageByName" resultType="com.hongru.entity.cost.PaintCode">
		SELECT
		<include refid="paintCode_sql"/>
		FROM [CostPrice].[dbo].[油漆成本编码表] pc
		<where>
			pc.[状态] != 9
			<if test="insidePaintName != null and insidePaintName != ''">
				AND pc.[社内油漆名] LIKE '%' + #{insidePaintName} + '%'
			</if>
		</where>
		ORDER BY pc.[流水号] DESC
		OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
	</select>

	<select id="listPaintCodePageCountByName" resultType="java.lang.Integer">
		SELECT COUNT(1)
		FROM [CostPrice].[dbo].[油漆成本编码表] pc
		<where>
			pc.[状态] != 9
			<if test="insidePaintName != null and insidePaintName != ''">
				AND pc.[社内油漆名] LIKE '%' + #{insidePaintName} + '%'
			</if>
		</where>
	</select>

	<update id="updatePaintCode" parameterType="com.hongru.entity.cost.PaintCode">
		UPDATE [CostPrice].[dbo].[油漆成本编码表]
		SET
			[社内油漆名] = #{paintCode.insidePaintName},
			[最后修改人标识] = #{paintCode.lastModifierId},
			[最后修改人姓名] = #{paintCode.lastModifierName},
			[最后修改时间] = #{paintCode.lastModifiedTime}
		WHERE [流水号] = #{paintCode.codeId}
	</update>

	<select id="selectPaintCodeById" resultType="com.hongru.entity.cost.PaintCode">
		SELECT
		<include refid="paintCode_sql"/>
		FROM [CostPrice].[dbo].[油漆成本编码表] pc
		WHERE pc.[流水号] = #{codeId} AND pc.[状态] != 9
	</select>

	<select id="selectMaxPaintCode" resultType="java.lang.String">
		SELECT TOP 1 pc.[成本编码]
		FROM [CostPrice].[dbo].[油漆成本编码表] pc
		WHERE pc.[成本编码] LIKE 'C2%' AND pc.[状态] != 9
		ORDER BY pc.[成本编码] DESC
	</select>

	<select id="listPaintVarieties" resultType="java.lang.String">
		SELECT DISTINCT([油漆名称])
		FROM [PIMS].[dbo].[油漆品种表]
		WHERE 状态='0'
		ORDER BY [油漆名称]
	</select>

</mapper>