<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>辅材费用汇总</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md2">
                                <label class="layui-form-label">年月:</label>
                                <div class="layui-input-block">
                                    <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="" />
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md2">
                                <label class="layui-form-label">性质:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="nature" name="nature" lay-verify="required" required>
                                        <option value="">请选择</option>
                                        <option value="直接">直接</option>
                                        <option value="间接">间接</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md2">
                                <label class="layui-form-label">摘要:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="abstracts" name="abstracts" lay-verify="required" required>
                                        <option value="">请选择</option>
                                        <option value="补修">补修</option>
                                        <option value="辅材">辅材</option>
                                        <option value="线盘">线盘</option>
                                        <option value="模具">模具</option>
                                        <option value="润滑剂">润滑剂</option>
                                        <option value="运输费">运输费</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md1 hr-div-btn">
                                <input type="hidden" id="isSearch" name="isSearch" value="0" />
                                <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="demo" lay-filter="test"></table>
        <!-- 自定义表中工具栏 -->
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs layui-bg-green" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
        </script>
        <!-- 自定义头部工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="import"><i class="layui-icon layui-icon-add-1"></i>导入</button>
                <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete"><i class="layui-icon layui-icon-delete"></i>删除</button>
            </div>
        </script>
    </div>
</div>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/materialSummary_list.js?time=6"></script>
</myfooter>
</body>
</html>
