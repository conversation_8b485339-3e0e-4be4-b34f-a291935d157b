package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import java.util.Date;

/**
 * 原料项目表实体类
 * 
 * <AUTHOR>
 */
@TableName("原料项目表")
public class RawMaterialItem {

    /* 状态-正常 */
    public static final short STATE_NORMAL = 0;
    /* 状态-删除 */
    public static final short STATE_DELETED = 9;

    /* 原料区分-导体 */
    public static final String MATERIAL_TYPE_CONDUCTOR = "01";
    /* 原料区分-油漆 */
    public static final String MATERIAL_TYPE_PAINT = "02";
    /* 原料区分-线盘 */
    public static final String MATERIAL_TYPE_WIRE_DISC = "03";

    /* 流水号 */
    @TableId(value = "流水号", type = IdType.AUTO)
    protected int serialNumber;

    /* 年度 */
    @TableField("年度")
    protected String year;

    /* 品目 */
    @TableField("品目")
    protected String itemCode;

    /* 品目名 */
    @TableField("品目名")
    protected String itemName;

    /* 原料区分 */
    @TableField("原料区分")
    protected String materialType;

    /* 创建人姓名 */
    @TableField("创建人姓名")
    protected String creatorName;

    /* 创建时间 */
    @TableField("创建时间")
    protected Date createdTime;

    /* 更新人姓名 */
    @TableField("更新人姓名")
    protected String updaterName;

    /* 更新时间 */
    @TableField("更新时间")
    protected Date updatedTime;

    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }
}
