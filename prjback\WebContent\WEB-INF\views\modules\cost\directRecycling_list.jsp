<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>电费列表</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <div class="layui-tab layui-tab-brief">
                        <ul class="layui-tab-title">
                            <li class="layui-this">预定直接部门回收计算</li>
                            <li onclick="toPage(2)">预定辅助部门回收计算（补修费、辅材费）</li>
                            <li onclick="toPage(3)">预定辅助部门回收计算（人工费）</li>
                            <li onclick="toPage(4)">预定捆包费</li>
                            <li onclick="toPage(5)">预定线盘回收计算</li>
                            <li onclick="toPage(6)">预定屑使用量</li>
                            <li onclick="toPage(7)">预定芯线使用量</li>
                            <li onclick="toPage(8)">预定油漆</li>
                            <li onclick="toPage(9)">预订运费</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                                    <div class="layui-form-item">
                                        <div class="layui-inline layui-col-md2">
                                            <label class="layui-form-label">年月:</label>
                                            <div class="layui-input-block">
                                                <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="" />
                                            </div>
                                        </div>
                                        <div class="layui-inline layui-col-md1 hr-div-btn">
                                            <input type="hidden" id="isSearch" name="isSearch" value="0" />
                                            <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="demo" lay-filter="test"></table>
        <!-- 自定义表中工具栏 -->
        <script type="text/html" id="barDemo">
        </script>
        <!-- 自定义头部工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
 				<button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete"><i class="layui-icon layui-icon-delete"></i>删除</button>
            </div>
        </script>
    </div>
</div>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/directRecycling_list.js?time=5"></script>
</myfooter>
</body>
</html>
