<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>导体单价详情</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <!-- 基础信息 -->
        <fieldset class="layui-elem-field layui-field-title">
            <legend>基础信息</legend>
        </fieldset>
        
        <div class="layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年度:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${rawMaterialItem.year}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">品目名:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${rawMaterialItem.itemName}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">品目:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${rawMaterialItem.itemCode}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">原料类型:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="导体" readonly class="layui-input layui-disabled">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">创建人:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${rawMaterialItem.creatorName}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="<fmt:formatDate value='${rawMaterialItem.createdTime}' pattern='yyyy-MM-dd HH:mm:ss'/>" readonly class="layui-input layui-disabled">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">更新人:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${rawMaterialItem.updaterName}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">更新时间:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="<fmt:formatDate value='${rawMaterialItem.updatedTime}' pattern='yyyy-MM-dd HH:mm:ss'/>" readonly class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <!-- 导体单价明细 -->
        <fieldset class="layui-elem-field layui-field-title">
            <legend>导体单价明细</legend>
        </fieldset>
        
        <div class="layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">关税率:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${conductorDetail.tariffRate}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">采购单价:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${conductorDetail.purchaseUnitPrice}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">溢价:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${conductorDetail.premium}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">附随费用:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${conductorDetail.incidentalExpenses}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">屑铜原料单价:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${conductorDetail.scrapCopperUnitPrice}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">比重:</label>
                    <div class="layui-input-inline">
                        <input type="text" value="${conductorDetail.specificGravity}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-primary" onclick="goBack();">返回</button>
            </div>
        </div>
    </div>
</div>

<script>
function goBack() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>
<style>
    .layui-form-label {
        width: 120px;
    }
    .layui-disabled {
        background-color: #f2f2f2;
        cursor: not-allowed;
    }
</style>
</body>
</html>
