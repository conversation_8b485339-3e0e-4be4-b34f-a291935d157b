<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.GasPriceCostMapper">

    <sql id="gasPriceCost_sql">
		gas.[流水号] AS costId,gas.[状态] AS state,gas.[年月] AS yearMonth,gas.[部门] AS department,
		gas.[用量] AS gasCost,gas.[金额] AS gasPrice,gas.[年] AS year,gas.[月] AS month,
		gas.[创建人标识] AS creatorId,gas.[创建人姓名] AS creatorName,gas.[创建时间] AS createdTime
	</sql>

    <insert id="insertGasPriceCost" parameterType="com.hongru.entity.cost.GasPriceCost">
        INSERT INTO [CostPrice].[dbo].[燃气费用表]
		(
		[状态],
		[年月],
		[部门],
		[用量],
		[金额],
		[年],
		[月],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{gasPriceCost.state},
		#{gasPriceCost.yearMonth},
		#{gasPriceCost.department},
		#{gasPriceCost.gasCost},
		#{gasPriceCost.gasPrice},
		#{gasPriceCost.year},
		#{gasPriceCost.month},
		#{gasPriceCost.creatorId},
		#{gasPriceCost.creatorName},
		#{gasPriceCost.createdTime}
		)
    </insert>

    <select id="selectByCostId" resultType="com.hongru.entity.cost.GasPriceCost">
        SELECT
        <include refid="gasPriceCost_sql"/>
        FROM [CostPrice].[dbo].[燃气费用表] gas
        <where>
            gas.[状态] != 9
            <if test="costId != null">
                AND gas.[流水号] = #{costId}
            </if>
        </where>
    </select>

    <select id="selectByYearMonth" resultType="com.hongru.entity.cost.GasPriceCost">
        SELECT
        TOP 1
        <include refid="gasPriceCost_sql"/>
        FROM [CostPrice].[dbo].[燃气费用表] gas
        <where>
            gas.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND gas.[年月] = #{yearMonth}
            </if>
            <if test="department != null and department != ''">
                AND gas.[部门] = #{department}
            </if>
        </where>
    </select>

    <select id="selectListByYearMonth" resultType="com.hongru.entity.cost.GasPriceCost">
        SELECT
        <include refid="gasPriceCost_sql"/>
        FROM [CostPrice].[dbo].[燃气费用表] gas
        <where>
            gas.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND gas.[年月] = #{yearMonth}
            </if>
        </where>
    </select>

    <select id="listCostPricePage" resultType="com.hongru.entity.cost.GasPriceCost">
        SELECT
        <if test="pageInfo.limit != null">
            TOP ${pageInfo.limit}
        </if>
        <include refid="gasPriceCost_sql"/>
        FROM [CostPrice].[dbo].[燃气费用表] gas
        <where>
            gas.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND gas.[年月] = #{yearMonth}
            </if>
        </where>
        <if test="pageInfo.sort != null">
            ORDER BY ${pageInfo.sort} ${pageInfo.order}
        </if>
        <if test="pageInfo.sort == null">
            ORDER BY gas.[年月] DESC, gas.[部门]
        </if>
    </select>
    
    <select id="listCostPricePageCount" resultType="integer">
        SELECT
        COUNT(1)
        FROM [CostPrice].[dbo].[燃气费用表] gas
        <where>
            gas.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND gas.[年月] = #{yearMonth}
            </if>
        </where>
    </select>

    <update id="updateState">
		UPDATE [CostPrice].[dbo].[燃气费用表]
		SET [状态] = #{state}
		WHERE [流水号] = #{costId}
    </update>

    <update id="updateGasPriceCost">
        UPDATE [CostPrice].[dbo].[燃气费用表]
        <set>
            <if test="gasPriceCost.yearMonth != null and gasPriceCost.yearMonth != ''">
                [年月] = #{gasPriceCost.yearMonth},
            </if>
            <if test="gasPriceCost.department != null and gasPriceCost.department != ''">
                [部门] = #{gasPriceCost.department},
            </if>
            <if test="gasPriceCost.year != null">
                [年] = #{gasPriceCost.year},
            </if>
            <if test="gasPriceCost.month != null">
                [月] = #{gasPriceCost.month},
            </if>
            <if test="gasPriceCost.gasCost != null">
                [用量] = #{gasPriceCost.gasCost},
            </if>
            <if test="gasPriceCost.gasPrice != null">
                [金额] = #{gasPriceCost.gasPrice},
            </if>
            <if test="gasPriceCost.lastModifierId != null">
                [最后修改人标识] = #{gasPriceCost.lastModifierId},
            </if>
            <if test="gasPriceCost.lastModifierName != null and gasPriceCost.lastModifierName != ''">
                [最后修改人姓名] = #{gasPriceCost.lastModifierName},
            </if>
            <if test="gasPriceCost.lastModifiedTime != null">
                [最后修改时间] = #{gasPriceCost.lastModifiedTime}
            </if>
        </set>
        WHERE [流水号] = #{gasPriceCost.costId}
    </update>
    <select id="selectPowerOtherByDateRange" resultType="com.hongru.entity.yearRevise.PowerShCoefficientBean">
        SELECT
         [部门] AS department, SUM([金额]) / SUM([用量]) AS nitrogenUnitPriceActAvg
        FROM [CostPrice].[dbo].[燃气费用表]
        <where>
            [状态] != 9
            <if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
        </where>
        GROUP BY [部门] 
    </select>
<!--         <select id="selectNitrogenActAvgByDateRange" resultType="com.hongru.entity.yearRevise.PowerShCoefficientBean"> -->
        <select id="selectNitrogenActAvgByDateRange" resultType="BigDecimal">
        SELECT
         AVG([用量]) AS nitrogenActAvg
        FROM [CostPrice].[dbo].[燃气费用表]
        <where>
            [状态] != 9
            <if test="department != null and department  != ''">
                AND [部门] =#{department}
            </if>
            <if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
        </where>
    </select>
</mapper>