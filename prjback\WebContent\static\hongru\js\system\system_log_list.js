layui.config({
	base: baselocationsta+'/common/layui/'
});

layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
	var laydate = layui.laydate
		,layer = layui.layer
		,table = layui.table
		,element = layui.element
		var form = layui.form;

	var url = baselocation+'/system/log/';
	table.render({
		elem: '#demo'
		,height: 'full-70'
		,url: url
		,parseData:function(res){
			return {
				"code": 0,
				"msg": '',
				"count": res.total,
				"data": res.rows
			};
		}
		,method:'post'
		,title: '日志列表'
		,page: true
		,limits: [10,20,50,100]
		,where:$("#formSearch").serializeJsonObject()
		,toolbar: '#toolbarDemo'
		,defaultToolbar: ['filter']
		,totalRow: false
		,cols: [[
			{type: 'checkbox', fixed: 'left'}
			,{field: 'zizeng', title: '',width:50,fixed: 'left',type:"numbers",align:'center'}
			,{field: 'logId',title:'编号',width:70,fixed: 'left',align:'center'}
			,{field: 'userName',width:100,title: '操作用户',align:'center'}
			,{field: 'createTime',width:180,title: '操作时间',align:'center',
				templet: function(d){
					return new Date(d.createTime).FormatV2("yyyy-MM-dd HH:mm:ss");
				}
			}
			,{field: 'url',title: '请求路径',align:'left'}
			,{field: 'userIp',width:150,title: 'IP地址',align:'center'}
			,{field: 'spendTime',width:70,title: '耗时',align:'center'}
		]]
	});

	table.on('toolbar(test)', function(obj){
		var checkStatus = table.checkStatus(obj.config.id)
			,data = checkStatus.data;
		switch(obj.event){
			case 'refresh':
				var temp = $("#formSearch").serializeJsonObject();
				console.info(temp);
				layui.table.reload('demo', {
					page: {
						curr: 1
					}
					,where: temp
				}, 'data');
				break;
			case 'daoChuXuanZhong':
				if(data.length == 0){
					layer.alert("请选择要导出的数据！");
					return;
				}else{
					var logIds = "";
					for(var i=0; i < data.length; i++){
						if(logIds==""){
							logIds = data[i].logId;
						}else{
							logIds+= ","+data[i].logId;
						}
					}
					$("#logIds").val(logIds);
					$("#formSearch").submit();
				}
				break;
			case 'daoChuQuanBu':
				$("#logIds").val("");
				$("#formSearch").submit();
				break;
			case 'shanChuXuanZhong':
				if(data.length == 0){
					layer.alert("请选择要删除的数据！");
					return;
				}else{
					var logIds = "";
					for(var i=0; i < data.length; i++){
						if(logIds==""){
							logIds = data[i].logId;
						}else{
							logIds+= ","+data[i].logId;
						}
					}
					var index = layer.load(2,{
						shade:[0.1,'#fff']
					});
					$.ajax({
						type : 'post',
						dataType : 'json',
						data: {"logIds":logIds},
						url : baselocation + '/system/log/delete/forBatch',
						success : function(result) {
							layer.closeAll();
							if (result.code == 1) {
								layer.msg("操作成功!", {
									shade : 0.3,
									time : 1500
								}, function() {
									layui.table.reload('demo');
								});
							} else {
								layer.alert(result.message, {
									icon : 2
								});
							}
						}
					})
				}
				break;
			case 'shanChuQuanBu':
				var index = layer.load(2,{
					shade:[0.1,'#fff']
				});
				$.ajax({
					type : 'post',
					dataType : 'json',
					data: {"logIds":""},
					url : baselocation + '/system/log/delete/forBatch',
					success : function(result) {
						layer.closeAll();
						if (result.code == 1) {
							layer.msg("操作成功!", {
								shade : 0.3,
								time : 1500
							}, function() {
								layui.table.reload('demo');
							});
						} else {
							layer.alert(result.message, {
								icon : 2
							});
						}
					}
				})
				break;
		};
	});

	table.on('tool(test)', function(obj){
		var data = obj.data
			,layEvent = obj.event;
	});

	$("#btn-resert").on("click",function(){
		$('#searchForm input[type="text"]').each(function (i, j) {
			$(j).attr("value", "");
		})

		$('#searchForm select').each(function (i, j) {
			$(j).find("option:selected").attr("selected", false);
			$(j).find("option").first().attr("selected", true);
		})
		$("#cityId").html('<option value="" >全部</option>');
	})

});

function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}