package com.hongru.service.impl.cost;

import com.hongru.common.util.DateUtils;
import com.hongru.common.util.YearUtil;
import com.hongru.entity.cost.*;
import com.hongru.entity.yearRevise.SummaryOfDepartmentalUnitPricesBean;
import com.hongru.entity.yearRevise.TransportationCostBean;
import com.hongru.mapper.cost.*;
import com.hongru.mapper.stat.UnitPriceRecordMapper;
import com.hongru.mapper.sumitomo.WireDiscSpecificationsMapper;
import com.hongru.pojo.dto.*;
import com.hongru.service.admin.IUserService;
import com.hongru.service.cost.ICostService;
import com.hongru.service.yearRevise.IYearReviseService;
import com.hongru.support.page.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CostServiceImpl implements ICostService {
	@Autowired
	private CuSupplierMapper cuSupplierMapper;
	@Autowired
	private CuPriceCostMapper cuPriceCostMapper;
	@Autowired
	private ElectricPriceCostMapper electricPriceCostMapper;
	@Autowired
	private ElectricPriceCostDetailMapper electricPriceCostDetailMapper;
	@Autowired
	private GasPriceCostMapper gasPriceCostMapper;
	@Autowired
	private WaterPriceCostMapper waterPriceCostMapper;
	@Autowired
	private ExeclImportMapper importMapper;
	@Autowired
	private HumanDepartmentCostMapper humanDepartmentCostMapper;
	@Autowired
	private HumanHourCostMapper humanHourCostMapper;
	@Autowired
	private HumanPriceCostMapper humanPriceCostMapper;
	@Autowired
	private PaintCodeMapper paintCodeMapper;
	@Autowired
	private PaintUseCodeMapper paintUseCodeMapper;
	@Autowired
	private PaintPriceMapper paintPriceMapper;
	@Autowired
	private PaintPriceCostMapper paintPriceCostMapper;
	@Autowired
	private PaintVarietyMapper paintVarietyMapper;
	@Autowired
	private SmallDepartmentMapper smallDepartmentMapper;
	@Autowired
	private MaterialCostMapper materialCostMapper;
	@Autowired
	private WireDiscCostMapper wireDiscCostMapper;
	@Autowired
	private TransportCostMapper transportCostMapper;
	@Autowired
	private DirectRecyclingMapper directRecyclingMapper;
	@Autowired
	private AuxiliaryRecyclingMapper auxiliaryRecyclingMapper;
	@Autowired
	private ReserveCrumbsUsageMapper reserveCrumbsUsageMapper;
	@Autowired
	private ReserveCoreWireUsageMapper reserveCoreWireUsageMapper;
	@Autowired
	private ReservePaintUsageMapper reservePaintUsageMapper;
	@Autowired
	private ReserveFreightMapper reserveFreightMapper;
	@Autowired
	private WireDiscCodeMapper wireDiscCodeMapper;
	@Autowired
	private AuxiliaryRecyclingArtificialMapper auxiliaryRecyclingArtificialMapper;
	@Autowired
	private BookingBundlingMapper bookingBundlingMapper;
	@Autowired
	private ReservedRecyclingMapper reservedRecyclingMapper;
	@Autowired
	private MaterialSummaryMapper materialSummaryMapper;
	@Autowired
	private WireDiscSpecificationsMapper wireDiscSpecificationsMapper;
	@Autowired
	private DepartmentUnitPriceMapper departmentUnitPriceMapper;
	@Autowired
	private ExpenseItemUnitPriceMapper expenseItemUnitPriceMapper;
	@Autowired
	private UnitPriceRecordMapper unitPriceRecordMapper;
	@Autowired
	private RawMaterialItemMapper rawMaterialItemMapper;
	@Autowired
	private ConductorCodeMapper conductorCodeMapper;
	@Autowired
	private ConductorUnitPriceDetailMapper conductorUnitPriceDetailMapper;
	@Autowired
	private PaintUnitPriceDetailMapper paintUnitPriceDetailMapper;
	@Autowired
	private WireDiscUnitPriceDetailMapper wireDiscUnitPriceDetailMapper;
	@Autowired
	private FreightUnitPriceMapper freightUnitPriceMapper;
	@Autowired
	private IUserService userService;
	@Autowired
	private IYearReviseService yearReviseService;

	/*
	 * =================================铜供应商表======================================
	 */
	/**
	 * 供应商列表
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/5 17:43
	 * @return java.util.List<com.hongru.entity.cost.CuSupplier>
	 */
	@Override
	public List<CuSupplier> listCuSupplier() throws Exception {
		return cuSupplierMapper.listCuSupplier();
	}

	/**
	 * 供应商详情
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/5 17:43
	 */
	@Override
	public CuSupplier getSupplierById(Integer supplierId) throws Exception {
		return cuSupplierMapper.selectSupplierById(supplierId);
	}

	/*
	 * =================================铜加工費用明细表====================================
	 * ==
	 */
	/**
	 * 添加铜加工費用明细
	 * 
	 * @param cuPriceCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/17 11:08
	 * @return int
	 */
	@Override
	public int addCuProceCost(CuPriceCost cuPriceCost) throws Exception {
		// 根据供应商id查询供应商编码和名称
		if (cuPriceCost.getSupplierId() != null) {
			CuSupplier cuSupplier = this.getSupplierById(cuPriceCost.getSupplierId());
			if (cuSupplier != null) {
				cuSupplier.setSupplierCode(cuSupplier.getSupplierCode());
				cuSupplier.setSupplierName(cuSupplier.getSupplierName());
			}
		}
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(cuPriceCost.getYearMonth())) {
			String[] yearAndMonth = cuPriceCost.getYearMonth().split("-");
			cuPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			cuPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		cuPriceCostMapper.insertCuPriceCost(cuPriceCost);
		return cuPriceCost.getCostId();
	}

	/**
	 * 铜加工費用明细详情
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/17 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public CuPriceCost getCuProceCostById(int costId) throws Exception {
		CuPriceCost cuPriceCost = cuPriceCostMapper.selectByCostId(costId);
		return cuPriceCost;
	}

	/**
	 * 根据年月获取铜加工費用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/17 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public CuPriceCost getCuProceCostByYearMonth(String supplierCode, String yearMonth, Short lineType)
			throws Exception {
		CuPriceCost cuPriceCost = cuPriceCostMapper.selectByYearMonth(supplierCode, yearMonth, lineType);
		return cuPriceCost;
	}

	/**
	 * 铜加工费用分页列表
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/17 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public CuPriceCostDTO listCostPricePage(String yearMonth, Integer lineType, PageInfo pageInfo) throws Exception {
		List<CuPriceCost> priceCostList = cuPriceCostMapper.listCostPricePage(yearMonth, lineType, pageInfo);
		Integer total = cuPriceCostMapper.listCostPricePageCount(yearMonth, lineType);
		pageInfo.setTotal(total);
		return new CuPriceCostDTO(pageInfo, priceCostList);
	}

	/**
	 * 铜加工费用
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/17 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<CuPriceCost> listCostPrice(String yearMonth, Integer lineType) throws Exception {
		List<CuPriceCost> priceCostList = cuPriceCostMapper.listCostPrice(yearMonth, lineType);
		return priceCostList;
	}

	/**
	 * 编辑铜加工費用明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:01
	 * @return
	 */
	@Override
	public void modifyCuPriceCost(CuPriceCost cuPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(cuPriceCost.getYearMonth())) {
			String[] yearAndMonth = cuPriceCost.getYearMonth().split("-");
			cuPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			cuPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		cuPriceCostMapper.updateCostInfo(cuPriceCost);
	}

	/**
	 * 删除铜加工費用明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:01
	 * @return
	 */
	@Override
	public void removeCuPriceCost(Integer costId, Short state) {
		cuPriceCostMapper.updateState(costId, state);
	}

	/*
	 * =================================电费用表======================================
	 */

	/**
	 * 添加电费用
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:02
	 * @return
	 */
	@Override
	public int addElectricPriceCost(ElectricPriceCost electricPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(electricPriceCost.getYearMonth())) {
			String[] yearAndMonth = electricPriceCost.getYearMonth().split("-");
			electricPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			electricPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		electricPriceCostMapper.insertelectricPriceCost(electricPriceCost);
		return electricPriceCost.getCostId();
	}

	/**
	 * 电费用明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:02
	 * @return
	 */
	@Override
	public ElectricPriceCost getElectricPriceCostById(int costId) throws Exception {
		ElectricPriceCost electricPriceCost = electricPriceCostMapper.selectByCostId(costId);
		HashMap<String, String> calculateTagMap = new HashMap<String, String>();
		calculateTagMap.put("1", "不分时电费");
		calculateTagMap.put("2", "分时电费（峰）");
		calculateTagMap.put("3", "分时电费（平）");
		calculateTagMap.put("4", "分时电费（谷）");
		calculateTagMap.put("5", "分时电费（尖峰）");
		electricPriceCost.setCalculateTagStr(calculateTagMap.get(electricPriceCost.getCalculateTag()));
		return electricPriceCost;
	}

	/**
	 * 根据年月获取电费用明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:02
	 * @return
	 */
	@Override
	public ElectricPriceCost getElectricPriceCostByYearMonth(String yearMonth, String calculateTag) throws Exception {
		ElectricPriceCost electricPriceCost = electricPriceCostMapper.selectByYearMonth(yearMonth, calculateTag);
		return electricPriceCost;
	}

	/**
	 * 根据年月获取电费用明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:02
	 * @return
	 */
	@Override
	public ElectricPriceCost getElectricPriceCostByYearMonth(String yearMonth) throws Exception {
		ElectricPriceCost electricPriceCost = electricPriceCostMapper.selectListByYearMonth(yearMonth);
		return electricPriceCost;
	}

	/**
	 * 根据年月获取电费用明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:02
	 * @return
	 */
	@Override
	public List<ElectricPriceCost> listElectricPriceCostListByYearMonth(String yearMonth) throws Exception {
		List<ElectricPriceCost> electricPriceCost = electricPriceCostMapper
				.selectElectricPriceCostListByYearMonth(yearMonth);
		return electricPriceCost;
	}

	/**
	 * 分页获取获取电费用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public ElectricPriceCostDTO listElectricPriceCostPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<ElectricPriceCost> priceCostList = electricPriceCostMapper.listCostPricePage(yearMonth, pageInfo);
		if (!CollectionUtils.isEmpty(priceCostList)) {
			for (ElectricPriceCost electricPriceCost : priceCostList) {
				if (electricPriceCost.getEleSinglePrice() != null && electricPriceCost.getEleCost() != null) {
					BigDecimal totalPrice = electricPriceCost.getEleSinglePrice()
							.multiply(electricPriceCost.getEleCost());
					electricPriceCost.setTotalPrice(totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP));
				}
			}
		}
		Integer total = electricPriceCostMapper.listCostPricePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new ElectricPriceCostDTO(pageInfo, priceCostList);
	}

	/**
	 * 删除电费費用
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/25 16:03
	 * @return int
	 */
	@Override
	public void removeElectricCostPrice(Integer costId, Short state) {
		electricPriceCostMapper.updateState(costId, state);
	}

	/**
	 * 编辑电费費用
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:01
	 * @return
	 */
	@Override
	public void modifyElectricPriceCost(ElectricPriceCost electricPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(electricPriceCost.getYearMonth())) {
			String[] yearAndMonth = electricPriceCost.getYearMonth().split("-");
			electricPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			electricPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		electricPriceCostMapper.updateElectricPriceCost(electricPriceCost);
	}
	/*
	 * =================================电力使用明细表=====================================
	 * =
	 */

	/**
	 * 分页获取电力使用明细列表
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public ElectricPriceCostDetailDTO listElectricPriceCostDetailPage(String yearMonth, PageInfo pageInfo)
			throws Exception {
		List<ElectricPriceCostDetail> priceCostList = electricPriceCostDetailMapper.listCostPricePage(yearMonth,
				pageInfo);
		Integer total = electricPriceCostDetailMapper.listCostPricePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new ElectricPriceCostDetailDTO(pageInfo, priceCostList);
	}

	/**
	 * 添加电力使用明细
	 * 
	 * @param electricPriceCostDetail
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addElectricPriceCostDetail(ElectricPriceCostDetail electricPriceCostDetail) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(electricPriceCostDetail.getYearMonth())) {
			String[] yearAndMonth = electricPriceCostDetail.getYearMonth().split("-");
			electricPriceCostDetail.setYear(Integer.valueOf(yearAndMonth[0]));
			electricPriceCostDetail.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		electricPriceCostDetailMapper.insertelectricPriceCostDetail(electricPriceCostDetail);
		return electricPriceCostDetail.getCostId();
	}

	/**
	 * 删除电力使用明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/26 16:39
	 * @return int
	 */
	@Override
	public void removeDetailCostPriceByCostId(Integer costId) throws Exception {
		electricPriceCostDetailMapper.deleteByCostId(costId);
	}

	/**
	 * 查询电费明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/26 16:39
	 * @return int
	 */
	@Override
	public List<ElectricPriceCostDetail> listElectricPriceCostDetailByYearMonth(String yearMonth) {
		return electricPriceCostDetailMapper.listElectricPriceCostDetailByYearMonth(yearMonth);
	}

	/*
	 * =================================燃气费用表======================================
	 */
	/**
	 * 添加燃气费用
	 * 
	 * @param gasPriceCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addGasPriceCost(GasPriceCost gasPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(gasPriceCost.getYearMonth())) {
			String[] yearAndMonth = gasPriceCost.getYearMonth().split("-");
			gasPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			gasPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		gasPriceCostMapper.insertGasPriceCost(gasPriceCost);
		return gasPriceCost.getCostId();
	}

	/**
	 * 燃气费用明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public GasPriceCost getGasPriceCostById(int costId) throws Exception {
		GasPriceCost gasPriceCost = gasPriceCostMapper.selectByCostId(costId);
		if (gasPriceCost != null) {
			PaintUseCode paintUseCode = paintUseCodeMapper.selectByCode(gasPriceCost.getDepartment());
			if (paintUseCode != null) {
				gasPriceCost.setDepartmentName(paintUseCode.getMidDepartment());
			}
		}
		return gasPriceCost;
	}

	/**
	 * 根据年月获取燃气费用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public GasPriceCost getGasPriceCostByYearMonth(String yearMonth, String department) throws Exception {
		return gasPriceCostMapper.selectByYearMonth(yearMonth, department);
	}

	/**
	 * 根据年月获取燃气费用明细集合
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<GasPriceCost> listGasPriceCostListByYearMonth(String yearMonth) throws Exception {
		return gasPriceCostMapper.selectListByYearMonth(yearMonth);
	}

	/**
	 * 根据年月获取燃气费用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public GasPriceCostDTO listGasPriceCostPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<GasPriceCost> priceCostList = gasPriceCostMapper.listCostPricePage(yearMonth, pageInfo);
		if (!CollectionUtils.isEmpty(priceCostList)) {
			for (GasPriceCost gasPriceCost : priceCostList) {
				PaintUseCode powerDepartment = paintUseCodeMapper.selectByCode(gasPriceCost.getDepartment());
				if (powerDepartment != null) {
					gasPriceCost.setDepartmentName(powerDepartment.getMidDepartment());
				}
			}
		}
		Integer total = gasPriceCostMapper.listCostPricePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new GasPriceCostDTO(pageInfo, priceCostList);
	}

	/**
	 * 删除燃气费費用
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/26 11:48
	 * @return int
	 */
	@Override
	public void removeGasCostPrice(Integer costId, Short state) {
		gasPriceCostMapper.updateState(costId, state);
	}

	/**
	 * 编辑水费費用
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:01
	 * @return
	 */
	@Override
	public void modifyGasPriceCost(GasPriceCost gasPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(gasPriceCost.getYearMonth())) {
			String[] yearAndMonth = gasPriceCost.getYearMonth().split("-");
			gasPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			gasPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		gasPriceCostMapper.updateGasPriceCost(gasPriceCost);
	}

	/*
	 * =================================电力使用区分表=====================================
	 * =
	 */

	/**
	 * 获取电力使用区分表列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/26 11:48
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<PaintUseCode> listPowerDepartment() {
		return paintUseCodeMapper.listPowerDepartment();
	}

	/*
	 * =================================水费用表======================================
	 */
	/**
	 * 添加水费用
	 * 
	 * @param waterPriceCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addWaterPriceCost(WaterPriceCost waterPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(waterPriceCost.getYearMonth())) {
			String[] yearAndMonth = waterPriceCost.getYearMonth().split("-");
			waterPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			waterPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		waterPriceCostMapper.insertWaterPriceCost(waterPriceCost);
		return waterPriceCost.getCostId();
	}

	/**
	 * 水费用明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public WaterPriceCost getWaterPriceCostById(int costId) throws Exception {
		return waterPriceCostMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取水费用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public WaterPriceCost getWaterPriceCostByYearMonth(String yearMonth) throws Exception {
		return waterPriceCostMapper.selectByYearMonth(yearMonth);
	}

	/**
	 * 根据年月获取水费用明细集合
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<WaterPriceCost> getWaterPriceCostListByYearMonth(String yearMonth) throws Exception {
		return waterPriceCostMapper.selectListByYearMonth(yearMonth);
	}

	/**
	 * 根据年月获取水费用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public WaterPriceCostDTO listWaterPriceCostPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<WaterPriceCost> priceCostList = waterPriceCostMapper.listCostPricePage(yearMonth, pageInfo);
		Integer total = waterPriceCostMapper.listCostPricePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new WaterPriceCostDTO(pageInfo, priceCostList);
	}

	/**
	 * 删除水费費用
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/26 11:48
	 * @return int
	 */
	@Override
	public void removeWaterCostPrice(Integer costId, Short state) {
		waterPriceCostMapper.updateState(costId, state);
	}

	/**
	 * 编辑水费費用
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:01
	 * @return
	 */
	@Override
	public void modifyWaterPriceCost(WaterPriceCost waterPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(waterPriceCost.getYearMonth())) {
			String[] yearAndMonth = waterPriceCost.getYearMonth().split("-");
			waterPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			waterPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		waterPriceCostMapper.updateWaterPriceCost(waterPriceCost);
	}

	/*
	 * =================================人件部门表======================================
	 */
	/**
	 * 添加人件部门
	 * 
	 * @param humanDepartmentCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/9 11:08
	 * @return int
	 */
	@Override
	public int addHumanDepartmentCost(HumanDepartmentCost humanDepartmentCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(humanDepartmentCost.getYearMonth())) {
			String[] yearAndMonth = humanDepartmentCost.getYearMonth().split("-");
			humanDepartmentCost.setYear(Integer.valueOf(yearAndMonth[0]));
			humanDepartmentCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		humanDepartmentCostMapper.insertHumanDepartmentCost(humanDepartmentCost);
		return humanDepartmentCost.getCostId();
	}

	/**
	 * 删除人件部门
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeHumanDepartmentCost(Integer costId, Short state) {
		humanDepartmentCostMapper.updateState(costId, state);
	}

	/**
	 * 人件部门明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/9 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public HumanDepartmentCost getHumanDepartmentCostById(int costId) throws Exception {
		return humanDepartmentCostMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取人件部门明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/9 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public HumanDepartmentCost getHumanDepartmentCostByYearMonth(String yearMonth) throws Exception {
		return humanDepartmentCostMapper.selectByYearMonth(yearMonth);
	}

	/**
	 * 根据年月获取人件部门明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/9 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public HumanDepartmentCostDTO listHumanDepartmentCostPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<HumanDepartmentCost> priceCostList = humanDepartmentCostMapper.listCostPricePage(yearMonth, pageInfo);
		Integer total = humanDepartmentCostMapper.listCostPricePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new HumanDepartmentCostDTO(pageInfo, priceCostList);
	}
	/*
	 * =================================文件导入表======================================
	 */

	/**
	 * 添加文件导入
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 11:35
	 * @return
	 */
	@Override
	public int addExcelImport(ExcelImport excelImport) throws Exception {
		importMapper.insertExcelImpot(excelImport);
		return excelImport.getImportId();
	}

	/**
	 * 新增文件导入信息
	 * 
	 * @param excelImport
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/9 11:09
	 * @return com.hongru.entity.cost.ExcelImpot
	 */
	@Override
	@Transactional(readOnly = false)
	public int addElectricDetailImport(ExcelImport excelImport, List<ElectricPriceCostDetail> detailList)
			throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (ElectricPriceCostDetail electricPriceCostDetail : detailList) {
				electricPriceCostDetail.setImportId(importId);
				electricPriceCostDetailMapper.insertelectricPriceCostDetail(electricPriceCostDetail);
			}
		}
		return importId;
	}

	/**
	 * 导入人件工时
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 11:09
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addHumanHourDetailImport(ExcelImport excelImport, List<HumanHourCost> detailList) throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (HumanHourCost humanHourCost : detailList) {
				humanHourCost.setImportId(importId);
				humanHourCostMapper.insertHumanHourCost(humanHourCost);
			}
		}
		return importId;
	}

	/**
	 * 导入人件费
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 11:09
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addHumanPriceDetailImport(ExcelImport excelImport, List<HumanPriceCost> detailList) throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (HumanPriceCost humanPriceCost : detailList) {
				humanPriceCost.setImportId(importId);
				humanPriceCostMapper.insertHumanPriceCost(humanPriceCost);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入信息-油漆费用
	 * 
	 * @param excelImport
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/9 11:09
	 * @return com.hongru.entity.cost.ExcelImpot
	 */
	@Override
	@Transactional(readOnly = false)
	public int addPaintDetailImport(ExcelImport excelImport, List<PaintPriceCost> detailList) throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (PaintPriceCost paintPriceCost : detailList) {
				paintPriceCost.setImportId(importId);
				paintPriceCostMapper.insertPaintPriceCost(paintPriceCost);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入信息-辅材
	 * 
	 * @param excelImport
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/9 11:09
	 * @return com.hongru.entity.cost.ExcelImpot
	 */
	@Override
	@Transactional(readOnly = false)
	public int addAuxiliaryMaterialDetailImport(ExcelImport excelImport, List<AuxiliaryMaterialCost> detailList)
			throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (AuxiliaryMaterialCost auxiliaryMaterialCost : detailList) {
				auxiliaryMaterialCost.setImportId(importId);
				materialCostMapper.insertAuxiliaryMaterialCost(auxiliaryMaterialCost);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入信息-辅材汇总
	 * 
	 * @param excelImport
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/9 11:09
	 * @return com.hongru.entity.cost.ExcelImpot
	 */
	@Override
	@Transactional(readOnly = false)
	public int addAuxiliaryMaterialSummaryDetailImport(ExcelImport excelImport,
			List<AuxiliaryMaterialSummary> detailList) throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (AuxiliaryMaterialSummary auxiliaryMaterialSummary : detailList) {
				auxiliaryMaterialSummary.setImportId(importId);
				materialSummaryMapper.insertAuxiliaryMaterialSummary(auxiliaryMaterialSummary);
			}
		}
		return importId;
	}

	/**
	 * 删除文件导入信息
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeElectricDetailImport(Integer costId, Short state) {
		electricPriceCostDetailMapper.updateState(costId, state);
	}

	/**
	 * 修改文件导入状态
	 * 
	 * @param importId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/7/27 9:47
	 * @return com.hongru.entity.cost.ExcelImpot
	 */
	@Override
	public void modifyImportState(Integer importId, Integer userId, String userName) {
		// importMapper.modifyImportState(importId,userId,userName);
	}

	/**
	 * 新增文件导入-预定直接部门回收计算表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:50
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addDirectRecyclingImport(ExcelImport excelImport, List<DirectRecycling> detailList) throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (DirectRecycling directRecycling : detailList) {
				directRecycling.setImportId(importId);
				this.addDirectRecycling(directRecycling);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入-预定辅助部门回收计算（补修费、辅材费）
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:50
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addAuxiliaryRecyclingImport(ExcelImport excelImport, List<AuxiliaryRecycling> detailList)
			throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (AuxiliaryRecycling auxiliaryRecycling : detailList) {
				auxiliaryRecycling.setImportId(importId);
				this.addAuxiliaryRecycling(auxiliaryRecycling);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入-预定辅助部门回收计算（人工费）
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:50
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addAuxiliaryRecyclingArtificialImport(ExcelImport excelImport,
			List<AuxiliaryRecyclingArtificial> detailList) throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (AuxiliaryRecyclingArtificial auxiliaryRecyclingArtificial : detailList) {
				auxiliaryRecyclingArtificial.setImportId(importId);
				this.addAuxiliaryRecyclingArtificial(auxiliaryRecyclingArtificial);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入-预定捆包费
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:50
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addBookingBundlingImport(ExcelImport excelImport, List<BookingBundling> detailList) throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (BookingBundling bookingBundling : detailList) {
				bookingBundling.setImportId(importId);
				this.addBookingBundling(bookingBundling);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入-预定线盘回收计算
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:50
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addReservedRecyclingImport(ExcelImport excelImport, List<ReservedRecycling> detailList)
			throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (ReservedRecycling reservedRecycling : detailList) {
				reservedRecycling.setImportId(importId);
				this.addReservedRecycling(reservedRecycling);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入-预定屑使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 11:20
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addReserveCrumbsUsageImport(ExcelImport excelImport, List<ReserveCrumbsUsage> detailList)
			throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (ReserveCrumbsUsage reserveCrumbsUsage : detailList) {
				reserveCrumbsUsage.setImportId(importId);
				this.addReserveCrumbsUsage(reserveCrumbsUsage);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入-预定芯线使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 11:21
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addReserveCoreWireUsageImport(ExcelImport excelImport, List<ReserveCoreWireUsage> detailList)
			throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (ReserveCoreWireUsage reserveCoreWireUsage : detailList) {
				reserveCoreWireUsage.setImportId(importId);
				this.addReserveCoreWireUsage(reserveCoreWireUsage);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入-预定油漆使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 11:21
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addReservePaintUsageImport(ExcelImport excelImport, List<ReservePaintUsage> detailList)
			throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (ReservePaintUsage reservePaintUsage : detailList) {
				reservePaintUsage.setImportId(importId);
				this.addReservePaintUsage(reservePaintUsage);
			}
		}
		return importId;
	}

	/**
	 * 新增文件导入-预订运费
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 10:42
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public int addReserveFreightImport(ExcelImport excelImport, List<ReserveFreight> detailList) throws Exception {
		int importId = this.addExcelImport(excelImport);
		if (!CollectionUtils.isEmpty(detailList)) {
			for (ReserveFreight reserveFreight : detailList) {
				reserveFreight.setImportId(importId);
				this.addReserveFreight(reserveFreight);
			}
		}
		return importId;
	}

	/*
	 * =================================人件工时表======================================
	 */
	/**
	 * 添加人件工时
	 * 
	 * @param humanHourCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addHumanHourCost(HumanHourCost humanHourCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(humanHourCost.getYearMonth())) {
			String[] yearAndMonth = humanHourCost.getYearMonth().split("-");
			humanHourCost.setYear(Integer.valueOf(yearAndMonth[0]));
			humanHourCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		humanHourCostMapper.insertHumanHourCost(humanHourCost);
		return humanHourCost.getCostId();
	}

	/**
	 * 删除人件工时
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeHumanHourCost(Integer costId, Short state) {
		humanHourCostMapper.updateState(costId, state);
	}

	/**
	 * 人件工时明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public HumanHourCost getHumanHourCostById(int costId) throws Exception {
		return humanHourCostMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取人件工时明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<HumanHourCost> listHumanHourCostByYearMonth(String yearMonth) throws Exception {
		return humanHourCostMapper.listHumanHourCostByYearMonth(yearMonth);
	}

	/**
	 * 根据年月批量查询相关人件工时明细信息集合
	 *
	 * @param startYearMonth 起始年月
	 * @param endYearMonth   结束年月
	 * @return 人件工时明细信息集合
	 */
	@Override
	public List<HumanHourCost> getHumanHourCostsByYearMonth(String startYearMonth, String endYearMonth)
			throws Exception {
		return humanHourCostMapper.getHumanHourCostsByYearMonth(startYearMonth, endYearMonth);
	}

	/**
	 * 分页获取获取人件工时列表
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public HumanHourCostDTO listHumanHourCostPage(String yearMonth, String workType, PageInfo pageInfo)
			throws Exception {
		List<HumanHourCost> humanCostList = humanHourCostMapper.listCostPricePage(yearMonth, workType, pageInfo);
		Integer total = humanHourCostMapper.listCostPricePageCount(yearMonth, workType);
		pageInfo.setTotal(total);
		return new HumanHourCostDTO(pageInfo, humanCostList);
	}

	/**
	 * 编辑明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:01
	 * @return
	 */
	@Override
	public void modifyHumanHourCost(HumanHourCost humanHourCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(humanHourCost.getYearMonth())) {
			String[] yearAndMonth = humanHourCost.getYearMonth().split("-");
			humanHourCost.setYear(Integer.valueOf(yearAndMonth[0]));
			humanHourCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		humanHourCostMapper.updateHumanHourCost(humanHourCost);
	}

	/*
	 * =================================人件费用表======================================
	 */
	/**
	 * 添加人件费用
	 * 
	 * @param humanPriceCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addHumanPriceCost(HumanPriceCost humanPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(humanPriceCost.getYearMonth())) {
			String[] yearAndMonth = humanPriceCost.getYearMonth().split("-");
			humanPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			humanPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		humanPriceCostMapper.insertHumanPriceCost(humanPriceCost);
		return humanPriceCost.getCostId();
	}

	/**
	 * 删除人件费用
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeHumanPriceCost(Integer costId, Short state) {
		humanPriceCostMapper.updateState(costId, state);
	}

	/**
	 * 人件费用明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public HumanPriceCost getHumanPriceCostById(int costId) throws Exception {
		return humanPriceCostMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取人件费用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<HumanPriceCost> listHumanPriceCostByYearMonth(String yearMonth) throws Exception {
		return humanPriceCostMapper.selectByYearMonth(yearMonth);
	}

	/**
	 * 根据年月批量查询相关人件费用明细信息集合
	 *
	 * @param startYearMonth 起始年月
	 * @param endYearMonth   结束年月
	 * @return 人件费用明细信息集合
	 */
	@Override
	public List<HumanPriceCost> getHumanPriceCostByYearMonth(String startYearMonth, String endYearMonth)
			throws Exception {
		return humanPriceCostMapper.getHumanPriceCostByYearMonth(startYearMonth, endYearMonth);
	}

	/**
	 * 分页获取获取人件费用列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:14
	 * @return
	 */
	@Override
	public HumanPriceCostDTO listHumanPriceCostPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<HumanPriceCost> humanCostList = humanPriceCostMapper.listCostPricePage(yearMonth, pageInfo);
		Integer total = humanPriceCostMapper.listCostPricePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new HumanPriceCostDTO(pageInfo, humanCostList);
	}

	/*
	 * =================================油漆成本编码表=====================================
	 * =
	 */
	/**
	 * 添加油漆成本编码
	 * 
	 * @param paintCode
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addPaintCode(PaintCode paintCode) throws Exception {
		// 拆分年月存入字段
		// if(StringUtils.isNotEmpty(paintCode.getYearMonth())){
		// String[] yearAndMonth = paintCode.getYearMonth().split("-");
		// paintCode.setYear(Integer.valueOf(yearAndMonth[0]));
		// paintCode.setMonth(Integer.valueOf(yearAndMonth[1]));
		// }
		paintCodeMapper.insertPaintCode(paintCode);
		return paintCode.getCodeId();
	}

	/**
	 * 删除油漆成本编码
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removePaintCode(Integer costId, Short state) {
		paintCodeMapper.updateState(costId, state);
	}

	/**
	 * 油漆成本编码明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintCode getPaintCodeById(int costId) throws Exception {
		return paintCodeMapper.selectByCostId(costId);
	}

	/**
	 * 油漆成本编码明细
	 * 
	 * @param paintName
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintCode getPaintCodeByPaintName(String paintName) throws Exception {
		return paintCodeMapper.getPaintCodeByPaintName(paintName);
	}

	/**
	 * 根据年月获取油漆成本编码明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintCode getPaintCodeByYearMonth(String yearMonth) throws Exception {
		return paintCodeMapper.selectByYearMonth(yearMonth);
	}

	/**
	 * 根据年月获取油漆成本编码明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintCodeDTO listPaintCodePage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<PaintCode> paintCodeList = paintCodeMapper.listPaintCodePage(yearMonth, pageInfo);
		Integer total = paintCodeMapper.listPaintCodePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new PaintCodeDTO(pageInfo, paintCodeList);
	}

	/*
	 * =================================使用区分表======================================
	 */

	/**
	 * 添加使用区分
	 * 
	 * @param paintUseCode
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addPaintUseCode(PaintUseCode paintUseCode) throws Exception {
		paintUseCodeMapper.insertPaintUseCode(paintUseCode);
		return paintUseCode.getCostId();
	}

	/**
	 * 删除使用区分
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removePaintUseCode(Integer costId) {
		paintUseCodeMapper.deletePaintUseCode(costId);
	}

	/**
	 * 使用区分明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintUseCode getPaintUseCodeById(int costId) throws Exception {
		return paintUseCodeMapper.selectByCostId(costId);
	}

	/**
	 * 根据参数获取使用区分详情
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 16:20
	 * @return
	 */
	@Override
	public PaintUseCode getPaintUseCodeByParam(String smallDepartment) throws Exception {
		PaintUseCode paintUseCode = paintUseCodeMapper.selectPaintUseCodeByParam(smallDepartment);
		return paintUseCode;
	}

	/**
	 * 根据参数获取使用区分详情
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 16:20
	 * @return
	 */
	@Override
	public PaintUseCode getPaintMiddleUseCodeByParam(String middleDepartment) throws Exception {
		PaintUseCode paintUseCode = paintUseCodeMapper.selectPaintMiddleUseCodeByParam(middleDepartment);
		return paintUseCode;
	}

	/**
	 * 根据年月获取使用区分明细
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintUseCode getPaintUseCode(String smallCode, String smallDepartment, String midCode, String midDepartment)
			throws Exception {
		return paintUseCodeMapper.getPaintUseCode(smallCode, smallDepartment, midCode, midDepartment);
	}

	/**
	 * 分页获取使用区分
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 16:18
	 * @return
	 */
	@Override
	public PaintUseCodeDTO listPaintUseCodePage(String smallCode, String smallDepartment, String midCode,
			String midDepartment, PageInfo pageInfo) throws Exception {
		List<PaintUseCode> paintUseCodeList = paintUseCodeMapper.listPaintUseCodePage(smallCode, smallDepartment,
				midCode, midDepartment, pageInfo);
		Integer total = paintUseCodeMapper.listPaintUseCodePageCount(smallCode, smallDepartment, midCode,
				midDepartment);
		pageInfo.setTotal(total);
		return new PaintUseCodeDTO(pageInfo, paintUseCodeList);
	}

	/*
	 * =================================油漆价格表======================================
	 */
	/**
	 * 添加油漆价格
	 * 
	 * @param paintPrice
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addPaintPrice(PaintPrice paintPrice) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(paintPrice.getYearMonth())) {
			String[] yearAndMonth = paintPrice.getYearMonth().split("-");
			paintPrice.setYear(Integer.valueOf(yearAndMonth[0]));
			paintPrice.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		paintPriceMapper.insertPaintPrice(paintPrice);
		return paintPrice.getCostId();
	}

	/**
	 * 删除油漆价格
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removePaintPrice(Integer costId, Short state) {
		paintPriceMapper.updateState(costId, state);
	}

	/**
	 * 编辑油漆价格
	 * 
	 * @param paintPrice
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void modifyPaintCostPrice(PaintPrice paintPrice) {
		paintPriceMapper.updatePaintPrice(paintPrice);
	}

	/**
	 * 油漆价格明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintPrice getPaintPriceById(int costId) throws Exception {
		return paintPriceMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取油漆价格明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintPrice getPaintPriceByYearMonth(String yearMonth) throws Exception {
		return paintPriceMapper.selectByYearMonth(yearMonth);
	}

	/**
	 * 根据年月获取油漆价格明细集合
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<PaintPrice> listPaintPriceListByYearMonth(String yearMonth) throws Exception {
		return paintPriceMapper.selectListByYearMonth(yearMonth);
	}

	/**
	 * 分页获取油漆价格列表
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintPriceDTO listPaintPricePage(String yearMonth, String producer, String outPaintName, String paintPrice,
			PageInfo pageInfo) throws Exception {
		List<PaintPrice> paintPriceList = paintPriceMapper.listPaintPricePage(yearMonth, producer, outPaintName,
				paintPrice, pageInfo);
		Integer total = paintPriceMapper.listPaintPricePageCount(yearMonth, producer, outPaintName, paintPrice);
		pageInfo.setTotal(total);
		return new PaintPriceDTO(pageInfo, paintPriceList);
	}

	/**
	 * 根据参数获取油漆价格
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:58
	 * @return
	 */
	@Override
	public PaintPrice getPaintPriceByParam(String outPaintName, String yearMonth) throws Exception {
		PaintPrice paintPrice = paintPriceMapper.selectPaintPriceByParam(outPaintName, yearMonth);
		return paintPrice;
	}

	/**
	 * 根据检索条件获取油漆价格
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/12/22 15:58
	 * @return
	 */
	@Override
	public List<PaintPrice> listPaintPrice(String yearMonthForExcell, String outPaintNameForExcell,
			String producerForExcell) throws Exception {

		return paintPriceMapper.listPaintPrice(yearMonthForExcell, outPaintNameForExcell, producerForExcell);
	}

	/*
	 * =================================油漆品种表======================================
	 */

	/**
	 * 获取油漆品种表详情
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:52
	 * @return
	 */
	@Override
	public PaintVariety getPaintVarietyByLxh(Integer Lxh) throws Exception {
		PaintVariety paintVariety = paintVarietyMapper.selectPaintVarietyByLxh(Lxh);
		return paintVariety;
	}

	/**
	 * 根据参数获取油漆品种表详情
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:55
	 * @return
	 */
	@Override
	public PaintVariety getPaintVarietyByParam(String paintName) throws Exception {
		PaintVariety paintVariety = paintVarietyMapper.selectPaintVarietyByParam(paintName);
		return paintVariety;
	}

	/**
	 * 根据参数获取生产商列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:55
	 * @return
	 */
	@Override
	public List<String> listProducers() throws Exception {
		List<String> paintProducer = paintVarietyMapper.getProducerList();
		return paintProducer;
	}

	/**
	 * 根据参数获取社内油漆名
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:55
	 * @return
	 */
	@Override
	public List<String> getPaintNameByFullPaintName(String fullPaintName) throws Exception {
		List<String> paintName = paintVarietyMapper.selectPaintNameByFullPaintName(fullPaintName);
		return paintName;
	}

	/**
	 * 根据参数获取生产商列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:55
	 * @return
	 */
	@Override
	public List<PaintVariety> listFullPaintNames(String producer) throws Exception {
		List<PaintVariety> fullPaintNameList = paintVarietyMapper.getFullPaintNameList(producer);
		return fullPaintNameList;
	}

	/**
	 * 根据参数获取生产商列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:55
	 * @return
	 */
	@Override
	public List<PaintVariety> listFullPaintNamesV2(String producer) throws Exception {
		List<PaintVariety> fullPaintNameList = paintVarietyMapper.getFullPaintNameListV2(producer);
		return fullPaintNameList;
	}

	/**
	 * 根据参数获取油漆名称
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/1 15:54
	 * @return
	 */
	@Override
	public List<String> listInsidePaintNames() throws Exception {
		List<String> insidePaintNameList = paintVarietyMapper.getInsidePaintNameList();
		return insidePaintNameList;
	}

	/**
	 * 根据参数获取油漆全名
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/1 15:54
	 * @return
	 */
	@Override
	public List<String> getProducerByFullPaintName(String fullPaintName) throws Exception {
		List<String> fullPaintNameList = paintVarietyMapper.selectProducerByFullPaintName(fullPaintName);
		return fullPaintNameList;
	}

	/*
	 * =================================油漆使用明细表=====================================
	 * =
	 */
	/**
	 * 添加油漆使用明细
	 * 
	 * @param paintPriceCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addPaintPriceCost(PaintPriceCost paintPriceCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(paintPriceCost.getYearMonth())) {
			String[] yearAndMonth = paintPriceCost.getYearMonth().split("-");
			paintPriceCost.setYear(Integer.valueOf(yearAndMonth[0]));
			paintPriceCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		paintPriceCostMapper.insertPaintPriceCost(paintPriceCost);
		return paintPriceCost.getCostId();
	}

	/**
	 * 删除油漆使用明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removePaintPriceCost(Integer costId, Short state) {
		paintPriceCostMapper.updateState(costId, state);
	}

	/**
	 * 修改油漆使用明细
	 * 
	 * @param paintPriceCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void editPaintPriceCost(PaintPriceCost paintPriceCost) {
		paintPriceCostMapper.updatePaintPriceCost(paintPriceCost);
	}

	/**
	 * 油漆使用明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintPriceCost getPaintPriceCostById(int costId) throws Exception {
		return paintPriceCostMapper.selectByCostId(costId);
	}

	/**
	 * 根据参数获取油漆使用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<PaintPriceCost> getPaintPriceCostByParms(String yearMonth, String outPaintName) throws Exception {
		return paintPriceCostMapper.listPaintPriceCostByParms(yearMonth, outPaintName);
	}

	/**
	 * 根据年月获取油漆使用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<PaintPriceCost> listPaintPriceCostByYearMonth(String yearMonth) throws Exception {
		return paintPriceCostMapper.listPaintPriceCostByYearMonth(yearMonth);
	}

	/**
	 * 根据年月获取油漆使用明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public PaintPriceCostDTO listPaintPriceCostPage(String yearMonth, String insidePaintName, String paintUseCode,
			PageInfo pageInfo) throws Exception {
		List<PaintPriceCost> paintPriceCostList = paintPriceCostMapper.listPaintPriceCostPage(yearMonth,
				insidePaintName, paintUseCode, pageInfo);
		Integer total = paintPriceCostMapper.listPaintPriceCostPageCount(yearMonth, insidePaintName, paintUseCode);
		pageInfo.setTotal(total);
		return new PaintPriceCostDTO(pageInfo, paintPriceCostList);
	}

	/*
	 * =================================辅材费用表======================================
	 */
	/**
	 * 根据年月获取辅材明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public AuxiliaryMaterialCostDTO listAuxiliaryMaterialPage(String yearMonth, String expenseType, String expenseCode,
			PageInfo pageInfo) throws Exception {
		List<AuxiliaryMaterialCost> materialCostList = materialCostMapper.listMaterialCostPage(yearMonth, expenseType,
				expenseCode, pageInfo);
		Integer total = materialCostMapper.listMaterialCostPageCount(yearMonth, expenseType, expenseCode);
		pageInfo.setTotal(total);
		return new AuxiliaryMaterialCostDTO(pageInfo, materialCostList);
	}

	/**
	 * 添加辅材明细
	 * 
	 * @param auxiliaryMaterialCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addAuxiliaryMaterialCost(AuxiliaryMaterialCost auxiliaryMaterialCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(auxiliaryMaterialCost.getYearMonth())) {
			String[] yearAndMonth = auxiliaryMaterialCost.getYearMonth().split("-");
			auxiliaryMaterialCost.setYear(Integer.valueOf(yearAndMonth[0]));
			auxiliaryMaterialCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		materialCostMapper.insertAuxiliaryMaterialCost(auxiliaryMaterialCost);
		return auxiliaryMaterialCost.getCostId();
	}

	/**
	 * 删除辅材明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeAuxiliaryMaterialCost(Integer costId) {
		materialCostMapper.deleteAuxiliaryMaterial(costId);
	}

	/**
	 * 辅材明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public AuxiliaryMaterialCost getAuxiliaryMaterialCostById(int costId) throws Exception {
		return materialCostMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取辅材明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<AuxiliaryMaterialCost> listAuxiliaryMaterialCostByYearMonth(String yearMonth) throws Exception {
		return materialCostMapper.listAuxiliaryMaterialCostByYearMonth(yearMonth);
	}

	/**
	 * 根据参数获取辅材明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/19 16:37
	 * @return
	 */
	@Override
	public List<AuxiliaryMaterialCost> listAuxiliaryMaterialCostByParam(String timeMin, String timeMax, Integer year,
			Integer month, String yearMonth, String[] expenseTypeStrArr, String[] expenseCodeStrArr) throws Exception {
		return materialCostMapper.listAuxiliaryMaterialCostByParam(timeMin, timeMax, year, month, yearMonth,
				expenseTypeStrArr, expenseCodeStrArr);
	}

	/**
	 * 根据检索条件获取辅材明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/12/21 15:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<AuxiliaryMaterialCost> listAuxiliaryMaterialCost(String yearMonthForExcell, String expenseTypeForExcell)
			throws Exception {
		return materialCostMapper.listAuxiliaryMaterialCost(yearMonthForExcell, expenseTypeForExcell);
	}

	/*
	 * =================================辅材费用汇总表=====================================
	 * =
	 */
	/**
	 * 根据年月获取辅材明细--汇总
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public AuxiliaryMaterialSummaryDTO listAuxiliaryMaterialSummaryPage(String yearMonth, String expenseType,
			String expenseCode, PageInfo pageInfo) throws Exception {
		List<AuxiliaryMaterialSummary> materialSummaryList = materialSummaryMapper.listMaterialSummaryPage(yearMonth,
				expenseType, expenseCode, pageInfo);
		Integer total = materialSummaryMapper.listMaterialSummaryPageCount(yearMonth, expenseType, expenseCode);
		pageInfo.setTotal(total);
		return new AuxiliaryMaterialSummaryDTO(pageInfo, materialSummaryList);
	}

	/**
	 * 添加辅材明细--汇总
	 * 
	 * @param auxiliaryMaterialCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addAuxiliaryMaterialSummary(AuxiliaryMaterialSummary auxiliaryMaterialCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(auxiliaryMaterialCost.getYearMonth())) {
			String[] yearAndMonth = auxiliaryMaterialCost.getYearMonth().split("-");
			auxiliaryMaterialCost.setYear(Integer.valueOf(yearAndMonth[0]));
			auxiliaryMaterialCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		materialSummaryMapper.insertAuxiliaryMaterialSummary(auxiliaryMaterialCost);
		return auxiliaryMaterialCost.getCostId();
	}

	/**
	 * 删除辅材明细--汇总
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeAuxiliaryMaterialSummary(Integer costId, Short state) {
		materialSummaryMapper.updateState(costId, state);
	}

	/**
	 * 辅材明细--汇总
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public AuxiliaryMaterialSummary getAuxiliaryMaterialSummaryById(int costId) throws Exception {
		return materialSummaryMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取辅材明细--汇总
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<AuxiliaryMaterialSummary> listAuxiliaryMaterialSummaryByYearMonth(String yearMonth) throws Exception {
		return materialSummaryMapper.listAuxiliaryMaterialSummaryByYearMonth(yearMonth);
	}

	/**
	 * 编辑明细
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 15:01
	 * @return
	 */
	@Override
	public void modifyAuxiliaryMaterialSummary(AuxiliaryMaterialSummary auxiliaryMaterialSummary) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(auxiliaryMaterialSummary.getYearMonth())) {
			String[] yearAndMonth = auxiliaryMaterialSummary.getYearMonth().split("-");
			auxiliaryMaterialSummary.setYear(Integer.valueOf(yearAndMonth[0]));
			auxiliaryMaterialSummary.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		materialSummaryMapper.updateAuxiliaryMaterialSummary(auxiliaryMaterialSummary);
	}

	/*
	 * =================================线盘费用表======================================
	 */
	/**
	 * 线盘型号列表
	 * 
	 * @param wireDiscCost
	 * @throws
	 * <AUTHOR>
	 * @create 2024/2/20 11:08
	 * @return List<String>
	 */
	@Override
	public List<String> listWireDiscType() {
		return wireDiscSpecificationsMapper.listWireDiscType();
	}

	/**
	 * 根据年月获取线盘明细
	 * 
	 * @param year
	 * @throws
	 * <AUTHOR>
	 * @create 2024/02/20 11:09
	 * @return com.hongru.entity.cost.WireDiscCost
	 */
	@Override
	public List<WireDiscCost> listWireDiscCost(String year, String wireDiscType, String distinguish) throws Exception {
		return wireDiscCostMapper.listWireDiscCost(year, wireDiscType, distinguish);
	}

	/**
	 * 添加线盘明细
	 * 
	 * @param wireDiscCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addWireDiscCost(WireDiscCost wireDiscCost) throws Exception {
		// 选择的年份减1年
		// 统计入库重量、线盘个数用
		int yearInt = Integer.parseInt(wireDiscCost.getYear());
		yearInt--;
		String dateStart = Integer.toString(yearInt) + "-01-01 00:00:00";
		String dateEnd = Integer.toString(yearInt) + "-12-31 23:59:59";

		// 区分0：MW
		if (WireDiscCost.STATE_ZERO.equals(wireDiscCost.getDistinguish())) {
			// 标准回收率
			BigDecimal standardRate = wireDiscCost.getStandardRate();
			// 回収率实绩
			BigDecimal recoveryRate = wireDiscCost.getRecoveryRate();
			// 报废率实绩
			BigDecimal scrapRate = wireDiscCost.getScrapRate();
			// 单重
			BigDecimal singleWeight = wireDiscCost.getSingleWeight();
			// 数字1
			BigDecimal num1 = new BigDecimal(1);
			// 线盘原价计算
			// 回收率实绩>0.98 的场合 ⇒ 线盘价格×（1-标准回收率）+实际报废率]÷线盘单重
			if (recoveryRate.compareTo(standardRate) > 0) {
				wireDiscCost.setWireDiscPrice(
						wireDiscCost.getBuyPrice().multiply(num1.subtract(standardRate).add(scrapRate))
								.divide(singleWeight, 3, RoundingMode.HALF_UP));
			} else {
				// 上记以外的场合⇒ 线盘价格×（1-实际回收率）+实际报废率]÷线盘单重
				wireDiscCost.setWireDiscPrice(
						wireDiscCost.getBuyPrice().multiply(num1.subtract(recoveryRate).add(scrapRate))
								.divide(singleWeight, 3, RoundingMode.HALF_UP));
			}
			// 计算 线盘单价 = 线盘原价*单重
			wireDiscCost.setWireDiscUnitPrice(wireDiscCost.getWireDiscPrice().multiply(singleWeight));
			// 回收费用实绩(默认值0)
			wireDiscCost.setRecoveryCost(BigDecimal.ZERO);
			// // 入库重量(默认值0)
			// wireDiscCost.setStorageWeight(BigDecimal.ZERO);
			// 检索入库表、获取入库重量和线盘个数
			WireDiscUnitWeightUfBean wireDiscUnitWeightUfBean = wireDiscCostMapper
					.selectWireDiscWeightUnit(wireDiscCost.getWireDiscType(), dateStart, dateEnd);
			// 入库重量
			wireDiscCost.setStorageWeight(wireDiscUnitWeightUfBean.getStorageWeight());
			// 线盘个数
			wireDiscCost.setWireDiscNum(wireDiscUnitWeightUfBean.getWireDiscNum());
		} else {
			// 上记以外、超细的场合
			// 检索入库表、获取入库重量和线盘个数
			WireDiscUnitWeightUfBean wireDiscUnitWeightUfBean = wireDiscCostMapper
					.selectWireDiscWeightUnit(wireDiscCost.getWireDiscType(), dateStart, dateEnd);
			// 入库重量
			wireDiscCost.setStorageWeight(wireDiscUnitWeightUfBean.getStorageWeight());
			// 线盘个数
			wireDiscCost.setWireDiscNum(wireDiscUnitWeightUfBean.getWireDiscNum());
			// 单重实绩 =入库重量/线盘个数
			wireDiscCost.setSingleWeight(wireDiscCost.getStorageWeight()
					.divide(new BigDecimal(wireDiscCost.getWireDiscNum()), 3, RoundingMode.HALF_UP));

			BigDecimal wireDiscPriceTemp = BigDecimal.ZERO;
			// 回收费用实绩 > 0的场合
			if (wireDiscCost.getRecoveryCost().compareTo(BigDecimal.ZERO) > 0) {
				// 线盘原价计算=回收费用/线盘个数
				wireDiscPriceTemp = wireDiscCost.getRecoveryCost().divide(new BigDecimal(wireDiscCost.getWireDiscNum()),
						4, RoundingMode.HALF_UP);
			} else {
				// 线盘原价=采购单价
				wireDiscPriceTemp = wireDiscCost.getBuyPrice();
			}
			// 线盘原价>采购单价的场合
			if (wireDiscPriceTemp.compareTo(wireDiscCost.getBuyPrice()) > 0) {
				// 线盘原价= 采购单价
				wireDiscCost.setWireDiscPrice(wireDiscCost.getBuyPrice());
			} else {
				// 线盘原价= 线盘原价
				wireDiscCost.setWireDiscPrice(wireDiscPriceTemp);
			}
			// 线盘单价（KG/个） = 线盘原价/单重
			wireDiscCost.setWireDiscUnitPrice(
					wireDiscCost.getWireDiscPrice().divide(wireDiscCost.getSingleWeight(), 3, RoundingMode.HALF_UP));
			// 回収率实绩(默认值0)
			wireDiscCost.setRecoveryRate(BigDecimal.ZERO);
			// 报废率实绩(默认值0)
			wireDiscCost.setScrapRate(BigDecimal.ZERO);
			// 标准回收率(默认值0)
			wireDiscCost.setStandardRate(BigDecimal.ZERO);
		}
		// 线盘费用表插入数据
		wireDiscCostMapper.insertWireDiscCost(wireDiscCost);
		return wireDiscCost.getCostId();
	}

	/**
	 * 删除线盘明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeWireDiscCost(Integer costId) {
		wireDiscCostMapper.deleteWireDiscCost(costId);
	}

	/**
	 * 根据流水号取得线盘费用明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public WireDiscCost selectWireDiscCostByCostId(Integer costId) throws Exception {
		return wireDiscCostMapper.selectWireDiscCostByCostId(costId);
	}

	/**
	 * 更新线盘费用表
	 * 
	 * @param wireDiscCost
	 * @throws
	 * <AUTHOR>
	 * @create 2024/02/21 11:08
	 * @return void
	 */
	@Override
	public void updateWireDiscCost(WireDiscCost wireDiscCost) throws Exception {
		// 统计入库重量、线盘个数用
		int yearInt = Integer.parseInt(wireDiscCost.getYear());
		yearInt--;
		String dateStart = Integer.toString(yearInt) + "-01-01 00:00:00";
		String dateEnd = Integer.toString(yearInt) + "-12-31 23:59:59";
		// 采购单价
		BigDecimal buyPrice = wireDiscCost.getBuyPrice();
		// 区分0：MW
		if (WireDiscCost.STATE_ZERO.equals(wireDiscCost.getDistinguish())) {
			// 标准回收率
			BigDecimal standardRate = wireDiscCost.getStandardRate();
			// 回収率实绩
			BigDecimal recoveryRate = wireDiscCost.getRecoveryRate();
			// 报废率实绩
			BigDecimal scrapRate = wireDiscCost.getScrapRate();
			// 单重
			BigDecimal singleWeight = wireDiscCost.getSingleWeight();
			// 数字1
			BigDecimal num1 = new BigDecimal(1);
			// 线盘原价计算
			// 回收率实绩>0.98 的场合 ⇒ 采购单价×（1-标准回收率）+实际报废率]÷线盘单重
			if (recoveryRate.compareTo(standardRate) > 0) {
				wireDiscCost.setWireDiscPrice(buyPrice.multiply(num1.subtract(standardRate).add(scrapRate))
						.divide(singleWeight, 3, RoundingMode.HALF_UP));
			} else {
				// 上记以外的场合⇒ 采购单价×（1-实际回收率）+实际报废率]÷线盘单重
				wireDiscCost.setWireDiscPrice(buyPrice.multiply(num1.subtract(recoveryRate).add(scrapRate))
						.divide(singleWeight, 3, RoundingMode.HALF_UP));
			}

			// 线盘单价 = 线盘原价*单重
			wireDiscCost.setWireDiscUnitPrice(wireDiscCost.getWireDiscPrice().multiply(singleWeight));
			// 检索入库表、获取入库重量和线盘个数
			WireDiscUnitWeightUfBean wireDiscUnitWeightUfBean = wireDiscCostMapper
					.selectWireDiscWeightUnit(wireDiscCost.getWireDiscType(), dateStart, dateEnd);
			// 入库重量
			wireDiscCost.setStorageWeight(wireDiscUnitWeightUfBean.getStorageWeight());
			// 线盘个数
			wireDiscCost.setWireDiscNum(wireDiscUnitWeightUfBean.getWireDiscNum());
		} else {
			// 上记以外、超细的场合
			// 检索入库表、获取入库重量和线盘个数
			WireDiscUnitWeightUfBean wireDiscUnitWeightUfBean = wireDiscCostMapper
					.selectWireDiscWeightUnit(wireDiscCost.getWireDiscType(), dateStart, dateEnd);
			// 入库重量
			wireDiscCost.setStorageWeight(wireDiscUnitWeightUfBean.getStorageWeight());
			// 线盘个数
			wireDiscCost.setWireDiscNum(wireDiscUnitWeightUfBean.getWireDiscNum());
			// 单重实绩 =入库重量/线盘个数
			wireDiscCost.setSingleWeight(wireDiscCost.getStorageWeight()
					.divide(new BigDecimal(wireDiscCost.getWireDiscNum()), 4, RoundingMode.HALF_UP));
			BigDecimal wireDiscPriceTemp = BigDecimal.ZERO;
			// 回收费用实绩 > 0的场合
			if (wireDiscCost.getRecoveryCost().compareTo(BigDecimal.ZERO) > 0) {
				// 线盘原价计算=回收费用/线盘个数
				wireDiscPriceTemp = wireDiscCost.getRecoveryCost().divide(new BigDecimal(wireDiscCost.getWireDiscNum()),
						4, RoundingMode.HALF_UP);
			} else {
				// 线盘原价=采购单价
				wireDiscPriceTemp = wireDiscCost.getBuyPrice();
			}
			// 线盘原价>采购单价的场合
			if (wireDiscPriceTemp.compareTo(wireDiscCost.getBuyPrice()) > 0) {
				// 线盘原价= 采购单价
				wireDiscCost.setWireDiscPrice(wireDiscCost.getBuyPrice());
			} else {
				// 线盘原价= 线盘原价
				wireDiscCost.setWireDiscPrice(wireDiscPriceTemp);
			}
			// 线盘单价（KG/个） = 线盘原价/单重
			wireDiscCost.setWireDiscUnitPrice(
					wireDiscCost.getWireDiscPrice().divide(wireDiscCost.getSingleWeight(), 3, RoundingMode.HALF_UP));

		}
		// 更新线盘费用表
		wireDiscCostMapper.updateWireDiscCost(wireDiscCost);
	}

	/*
	 * =================================运输费用表======================================
	 */
	/**
	 * 根据年月获取运输明细
	 * 
	 * @param yearMonth
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public TransportCostDTO listTransportCostPage(String yearMonth, String transportCode, String area,
			PageInfo pageInfo) throws Exception {
		List<TransportCost> transportCost = transportCostMapper.listTransportCostPage(yearMonth, transportCode, area,
				pageInfo);
		Integer total = transportCostMapper.listTransportCostPageCount(yearMonth, transportCode, area);
		pageInfo.setTotal(total);
		return new TransportCostDTO(pageInfo, transportCost);
	}

	/**
	 * 添加运输明细
	 * 
	 * @param transportCost
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addTransportCost(TransportCost transportCost) throws Exception {
		// 拆分年月存入字段
		if (StringUtils.isNotEmpty(transportCost.getYearMonth())) {
			String[] yearAndMonth = transportCost.getYearMonth().split("-");
			transportCost.setYear(Integer.valueOf(yearAndMonth[0]));
			transportCost.setMonth(Integer.valueOf(yearAndMonth[1]));
		}
		// 计算 运输费单价 = 运输费金额/出库量
		BigDecimal transportPrice = new BigDecimal(0);
		if (transportCost.getTransportAmount() != null
				&& transportCost.getTransportAmount().compareTo(BigDecimal.ZERO) == 1
				&& transportCost.getOutNumber() != null
				&& transportCost.getOutNumber().compareTo(BigDecimal.ZERO) == 1) {
			transportPrice = transportCost.getTransportAmount().divide(transportCost.getOutNumber(), 5,
					RoundingMode.HALF_UP);
		}
		transportCost.setTransportPrice(transportPrice);
		transportCostMapper.insertTransportCost(transportCost);
		return transportCost.getCostId();
	}

	/**
	 * 删除运输明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeTransportCost(Integer costId) {
		transportCostMapper.deleteState(costId);
	}

	/**
	 * 运输明细
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public TransportCost getTransportCostById(int costId) throws Exception {
		return transportCostMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取运输明细
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public TransportCost getTransportCostByYearMonth(String yearMonth) throws Exception {
		return transportCostMapper.selectByYearMonth(yearMonth);
	}

	/**
	 * 根据年月获取运输明细
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:09
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public List<TransportCost> getTransportCostListByYearMonth(String yearMonth) throws Exception {
		String year = yearMonth.substring(0, 4);
		String month = yearMonth.replace(year + "-", "");
		return transportCostMapper.selectListByYearMonth(yearMonth);
	}

	/**
	 * 根据参数获取运输费用列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/19 16:46
	 * @return
	 */
	@Override
	public List<TransportCost> listTransportCostByParam(String timeMin, String timeMax, String yearMonth,
			String[] transportCodeStrArr) throws Exception {
		return transportCostMapper.listTransportCostByParam(timeMin, timeMax, yearMonth, transportCodeStrArr);
	}

	/*
	 * =================================补辅比例列表======================================
	 */
	/**
	 * 添加补辅比例列表
	 * 
	 * @param smallDepartment
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public int addSmallDepartment(SmallDepartment smallDepartment) throws Exception {
		smallDepartmentMapper.insertSmallDepartment(smallDepartment);
		return smallDepartment.getCostId();
	}

	/**
	 * 删除补辅比例列表
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/11 11:08
	 * @return int
	 */
	@Override
	public void removeSmallDepartment(Integer costId) {
		smallDepartmentMapper.deleteSmallDepartment(costId);
	}

	/**
	 * 补辅比例列表
	 * 
	 * @param costId
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:08
	 * @return com.hongru.entity.cost.CuProceCost
	 */
	@Override
	public SmallDepartment getSmallDepartmentById(int costId) throws Exception {
		return smallDepartmentMapper.selectByCostId(costId);
	}

	/**
	 * 根据年月获取补辅比例列表
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return com.hongru.entity.cost.SmallDepartment
	 */
	@Override
	public SmallDepartment getSmallDepartmentExist(String departmentCode, String departmentName) throws Exception {
		return smallDepartmentMapper.getSmallDepartmentExist(departmentCode, departmentName);
	}

	/**
	 * 根据年月获取补辅比例列表
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return com.hongru.entity.cost.SmallDepartmentDTO
	 */
	@Override
	public SmallDepartmentDTO listSmallDepartmentPage(String year, String departmentCode, String departmentName,
			String category, PageInfo pageInfo) throws Exception {
		List<SmallDepartment> smallDepartmentList = smallDepartmentMapper.listSmallDepartmentPage(year, departmentCode,
				departmentName, category, pageInfo);
		Integer total = smallDepartmentMapper.listSmallDepartmentPageCount(year, departmentCode, departmentName,
				category);
		pageInfo.setTotal(total);
		return new SmallDepartmentDTO(pageInfo, smallDepartmentList);
	}

	/**
	 * 编辑补辅比例列表
	 * 
	 * @param smallDepartment
	 * @throws
	 * <AUTHOR>
	 * @create 2024/02/02 11:08
	 * @return int
	 */
	@Override
	public void modifySmallDepartment(SmallDepartment smallDepartment) throws Exception {
		smallDepartmentMapper.updateSmallDepartment(smallDepartment);
	}
	/*
	 * =================================预定直接部门回收计算表=================================
	 * =====
	 */

	/**
	 * 添加预定直接部门回收计算表
	 * 
	 * @param directRecycling
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 11:08
	 * @return int
	 */
	@Override
	public int addDirectRecycling(DirectRecycling directRecycling) throws Exception {
		directRecyclingMapper.insertDirectRecycling(directRecycling);
		return directRecycling.getCostId();
	}

	/**
	 * 根据参数获取预定直接部门回收计算列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:44
	 * @return
	 */
	@Override
	public List<DirectRecycling> listDirectRecyclingByYearMonth(String yearMonth) throws Exception {
		List<DirectRecycling> directRecyclingList = directRecyclingMapper.listDirectRecyclingByYearMonth(yearMonth);
		return directRecyclingList;
	}

	/**
	 * 根据参数获取预定直接部门回收计算列表--分页
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:44
	 * @return
	 */
	@Override
	public DirectRecyclingDTO listDirectRecyclingPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<DirectRecycling> directRecyclingList = directRecyclingMapper.listDirectRecyclingPage(yearMonth, pageInfo);
		Integer total = directRecyclingMapper.listDirectRecyclingPageCount(yearMonth);
		pageInfo.setTotal(total);
		return new DirectRecyclingDTO(pageInfo, directRecyclingList);
	}

	/**
	 * 删除预定直接部门回收计算明细
	 * 
	 * @param costId
	 * @throws
	 * @return int
	 */
	@Override
	public void removeDirectRecyclingByCostId(Integer costId) throws Exception {
		directRecyclingMapper.deleteDirectRecyclingByCostId(costId);
	}

	/*
	 * =================================预定辅助部门回收计算（补修费、辅材费）表========================
	 * ==============
	 */

	/**
	 * 添加预定辅助部门回收计算（补修费、辅材费）表
	 * 
	 * @param auxiliaryRecycling
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 11:08
	 * @return int
	 */
	@Override
	public int addAuxiliaryRecycling(AuxiliaryRecycling auxiliaryRecycling) throws Exception {
		auxiliaryRecyclingMapper.insertAuxiliaryRecycling(auxiliaryRecycling);
		return auxiliaryRecycling.getCostId();
	}

	/**
	 * 根据参数获取预定辅助部门回收计算（补修费、辅材费）列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:44
	 * @return
	 */
	@Override
	public List<AuxiliaryRecycling> listAuxiliaryRecyclingByYearMonth(String yearMonth) throws Exception {
		List<AuxiliaryRecycling> auxiliaryRecyclingList = auxiliaryRecyclingMapper
				.listAuxiliaryRecyclingByYearMonth(yearMonth);
		return auxiliaryRecyclingList;
	}

	/**
	 * 根据参数获取预定辅助部门回收计算（补修费、辅材费）列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/19 11:32
	 * @return
	 */
	@Override
	public List<AuxiliaryRecycling> listAuxiliaryRecyclingByParam(String timeMin, String timeMax, Integer year,
			Integer month, String yearMonth, String[] expenseItemStrArr, String[] auxiliaryCodeStrArr)
			throws Exception {
		List<AuxiliaryRecycling> auxiliaryRecyclingList = auxiliaryRecyclingMapper.listAuxiliaryRecyclingByParam(
				timeMin, timeMax, year, month, yearMonth, expenseItemStrArr, auxiliaryCodeStrArr);
		return auxiliaryRecyclingList;
	}

	/**
	 * 根据参数获取预定直接部门回收计算列表--分页
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:44
	 * @return
	 */
	@Override
	public AuxiliaryRecyclingDTO listAuxiliaryRecyclingPage(String yearMonth, String auxiliaryCode, PageInfo pageInfo)
			throws Exception {
		List<AuxiliaryRecycling> auxiliaryRecyclingList = auxiliaryRecyclingMapper.listAuxiliaryRecyclingPage(yearMonth,
				auxiliaryCode, pageInfo);
		Integer total = auxiliaryRecyclingMapper.listAuxiliaryRecyclingPageCount(yearMonth, auxiliaryCode);
		pageInfo.setTotal(total);
		return new AuxiliaryRecyclingDTO(pageInfo, auxiliaryRecyclingList);
	}

	/**
	 * 删除预定辅助部门回收计算（补修费、辅材费）明细
	 * 
	 * @param costId
	 * @throws
	 * @return void
	 */
	@Override
	public void removeAuxiliaryRecyclingByCostId(Integer costId) throws Exception {
		auxiliaryRecyclingMapper.deleteAuxiliaryRecyclingByCostId(costId);
	}
	/*
	 * =================================预定辅助部门回收计算（人工费）表============================
	 * ==========
	 */

	/**
	 * 添加预定辅助部门回收计算（人工费）表
	 * 
	 * @param auxiliaryRecyclingArtificial
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 11:08
	 * @return int
	 */
	@Override
	public int addAuxiliaryRecyclingArtificial(AuxiliaryRecyclingArtificial auxiliaryRecyclingArtificial)
			throws Exception {
		auxiliaryRecyclingArtificialMapper.insertAuxiliaryRecyclingArtificial(auxiliaryRecyclingArtificial);
		return auxiliaryRecyclingArtificial.getCostId();
	}

	/**
	 * 根据参数获取预定辅助部门回收计算（人工费）列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:44
	 * @return
	 */
	@Override
	public List<AuxiliaryRecyclingArtificial> listAuxiliaryRecyclingArtificialByYearMonth(String yearMonth)
			throws Exception {
		List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificialList = auxiliaryRecyclingArtificialMapper
				.listAuxiliaryRecyclingArtificialByYearMonth(yearMonth);
		return auxiliaryRecyclingArtificialList;
	}

	/**
	 * 根据参数获取预定辅助部门回收计算（人工费）列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/19 10:46
	 * @return
	 */
	@Override
	public List<AuxiliaryRecyclingArtificial> listAuxiliaryRecyclingArtificialByParam(String timeMin, String timeMax,
			Integer year, Integer month, String yearMonth, String[] expenseItemStrArr, String[] auxiliaryCodeStrArr)
			throws Exception {
		List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificialList = auxiliaryRecyclingArtificialMapper
				.listAuxiliaryRecyclingArtificialByParam(timeMin, timeMax, year, month, yearMonth, expenseItemStrArr,
						auxiliaryCodeStrArr);
		return auxiliaryRecyclingArtificialList;
	}

	/**
	 * 根据参数获取预定辅助部门回收计算（人工费）列表--分页
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return
	 */
	@Override
	public AuxiliaryRecyclingArtificialDTO listAuxiliaryRecyclingArtificialPage(String yearMonth, String auxiliaryCode,
			PageInfo pageInfo) throws Exception {
		List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificialList = auxiliaryRecyclingArtificialMapper
				.listAuxiliaryRecyclingArtificialPage(yearMonth, auxiliaryCode, pageInfo);
		Integer total = auxiliaryRecyclingArtificialMapper.listAuxiliaryRecyclingArtificialPageCount(yearMonth,
				auxiliaryCode);
		pageInfo.setTotal(total);
		return new AuxiliaryRecyclingArtificialDTO(pageInfo, auxiliaryRecyclingArtificialList);
	}

	/**
	 * 删除预定辅助部门回收计算（人工费）明细
	 * 
	 * @param costId
	 * @throws
	 * @return int
	 */
	@Override
	public void removeAuxiliaryRecyclingArtificialByCostId(Integer costId) throws Exception {
		auxiliaryRecyclingArtificialMapper.deleteAuxiliaryRecyclingArtificialByCostId(costId);
	}

	/*
	 * =================================预定捆包费表======================================
	 */

	/**
	 * 添加预定捆包费表
	 * 
	 * @param bookingBundling
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 11:08
	 * @return int
	 */
	@Override
	public int addBookingBundling(BookingBundling bookingBundling) throws Exception {
		bookingBundlingMapper.insertBookingBundling(bookingBundling);
		return bookingBundling.getCostId();
	}

	/**
	 * 根据参数获取预定捆包费列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:44
	 * @returnb
	 */
	@Override
	public List<BookingBundling> listBookingBundlingByYearMonth(String yearMonth) throws Exception {
		List<BookingBundling> bookingBundlingList = bookingBundlingMapper.listBookingBundlingByYearMonth(yearMonth);
		return bookingBundlingList;
	}

	/**
	 * 根据参数获取预定捆包费列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/19 16:25
	 * @return
	 */
	@Override
	public List<BookingBundling> listBookingBundlingByParam(String timeMin, String timeMax, Integer year, Integer month,
			String yearMonth, String[] bookingCodeStrArr) throws Exception {
		List<BookingBundling> bookingBundlingList = bookingBundlingMapper.listBookingBundlingByParam(timeMin, timeMax,
				year, month, yearMonth, bookingCodeStrArr);
		return bookingBundlingList;
	}

	/**
	 * 根据参数获取预定捆包费列表--分页
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return
	 */
	@Override
	public BookingBundlingDTO listBookingBundlingPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<BookingBundling> bookingBundlingList = bookingBundlingMapper.listBookingBundlingPage(yearMonth, pageInfo);
		Integer total = bookingBundlingMapper.listBookingBundlingPageCount(yearMonth);
		pageInfo.setTotal(total);
		return new BookingBundlingDTO(pageInfo, bookingBundlingList);
	}

	/**
	 * 删除预定辅助部门回收计算（人工费）明细
	 * 
	 * @param costId
	 * @throws
	 * @return int
	 */
	@Override
	public void removeBookingBundlingByCostId(Integer costId) throws Exception {
		bookingBundlingMapper.deleteBookingBundlingByCostId(costId);
	}

	/*
	 * =================================预定线盘回收计算表===================================
	 * ===
	 */

	/**
	 * 添加预定线盘回收计算表
	 * 
	 * @param reservedRecycling
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 11:08
	 * @return int
	 */
	@Override
	public int addReservedRecycling(ReservedRecycling reservedRecycling) throws Exception {
		reservedRecyclingMapper.insertReservedRecycling(reservedRecycling);
		return reservedRecycling.getCostId();
	}

	/**
	 * 根据参数获取预定线盘回收计算列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 14:44
	 * @return
	 */
	@Override
	public List<ReservedRecycling> listReservedRecyclingByYearMonth(String yearMonth) throws Exception {
		List<ReservedRecycling> reservedRecyclingList = reservedRecyclingMapper
				.listReservedRecyclingByYearMonth(yearMonth);
		return reservedRecyclingList;
	}

	/**
	 * 根据参数获取预定线盘回收计算列表--分页
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return
	 */
	@Override
	public ReservedRecyclingDTO listReservedRecyclingPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<ReservedRecycling> reservedRecyclingList = reservedRecyclingMapper.listReservedRecyclingPage(yearMonth,
				pageInfo);
		Integer total = reservedRecyclingMapper.listReservedRecyclingPageCount(yearMonth);
		pageInfo.setTotal(total);
		return new ReservedRecyclingDTO(pageInfo, reservedRecyclingList);
	}

	/**
	 * 删除预定线盘回收明细
	 * 
	 * @param costId
	 * @throws
	 * @return int
	 */
	@Override
	public void removeReservedRecyclingByCostId(Integer costId) throws Exception {
		reservedRecyclingMapper.deleteReservedRecyclingByCostId(costId);
	}

	/*
	 * =================================线盘成本编码表=====================================
	 * =
	 */

	/**
	 * 根据参数获取线盘成本编码列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 9:18
	 * @return
	 */
	@Override
	public List<WireDiscCode> listWireDiscCodeByParam(Short state) throws Exception {
		List<WireDiscCode> wireDiscCodeList = wireDiscCodeMapper.listWireDiscCodeByParam(state);
		return wireDiscCodeList;
	}

	/**
	 * 获取线盘成本编码表详情
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 17:33
	 * @return
	 */
	@Override
	public WireDiscCode getWireDiscCodeByParam(String wireDiscName) throws Exception {
		WireDiscCode wireDiscCode = wireDiscCodeMapper.selectWireDiscCodeByParam(wireDiscName);
		return wireDiscCode;
	}

	/*
	 * =================================预定屑使用量======================================
	 */

	/**
	 * 添加预定屑使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 16:51
	 * @return
	 */
	@Override
	public int addReserveCrumbsUsage(ReserveCrumbsUsage reserveCrumbsUsage) throws Exception {
		reserveCrumbsUsageMapper.insertReserveCrumbsUsage(reserveCrumbsUsage);
		return reserveCrumbsUsage.getReserveCrumbsUsageId();
	}

	/**
	 * 根据参数获取预定屑使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 11:13
	 * @return
	 */
	@Override
	public List<ReserveCrumbsUsage> listReserveCrumbsUsageByYearMonth(String yearMonth) throws Exception {
		List<ReserveCrumbsUsage> reserveCrumbsUsageList = reserveCrumbsUsageMapper
				.listReserveCrumbsUsageByYearMonth(yearMonth);
		return reserveCrumbsUsageList;
	}

	/**
	 * 根据参数获取预定屑使用量--分页
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return
	 */
	@Override
	public ReserveCrumbsUsageDTO listReserveCrumbsUsagePage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<ReserveCrumbsUsage> reserveCrumbsUsageList = reserveCrumbsUsageMapper.listReserveCrumbsUsagePage(yearMonth,
				pageInfo);
		Integer total = reserveCrumbsUsageMapper.listReserveCrumbsUsagePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new ReserveCrumbsUsageDTO(pageInfo, reserveCrumbsUsageList);
	}

	/**
	 * 删除预定屑使用量明细
	 * 
	 * @param costId
	 * @throws
	 * @return int
	 */
	@Override
	public void removeReserveCrumbsUsageByCostId(Integer costId) throws Exception {
		reserveCrumbsUsageMapper.deleteReserveCrumbsUsageByCostId(costId);
	}

	/*
	 * =================================预定芯线使用量=====================================
	 * =
	 */

	/**
	 * 添加预定芯线使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 16:51
	 * @return
	 */
	@Override
	public int addReserveCoreWireUsage(ReserveCoreWireUsage reserveCoreWireUsage) throws Exception {
		reserveCoreWireUsageMapper.insertReserveCoreWireUsage(reserveCoreWireUsage);
		return reserveCoreWireUsage.getReserveCoreWireUsageId();
	}

	/**
	 * 根据参数获取预定芯线使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 11:13
	 * @return
	 */
	@Override
	public List<ReserveCoreWireUsage> listReserveCoreWireUsageByYearMonth(String yearMonth) throws Exception {
		List<ReserveCoreWireUsage> reserveCoreWireUsageList = reserveCoreWireUsageMapper
				.listReserveCoreWireUsageByYearMonth(yearMonth);
		return reserveCoreWireUsageList;
	}

	/**
	 * 根据参数获取预定芯线使用量--分页
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return
	 */
	@Override
	public ReserveCoreWireUsageDTO listReserveCoreWireUsagePage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<ReserveCoreWireUsage> reserveCoreWireUsageList = reserveCoreWireUsageMapper
				.listReserveCoreWireUsagePage(yearMonth, pageInfo);
		Integer total = reserveCoreWireUsageMapper.listReserveCoreWireUsagePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new ReserveCoreWireUsageDTO(pageInfo, reserveCoreWireUsageList);
	}

	/**
	 * 删除预定芯线使用量明细
	 * 
	 * @param costId
	 * @throws
	 * @return int
	 */
	@Override
	public void removeReserveCoreWireUsageByCostId(Integer costId) throws Exception {
		reserveCoreWireUsageMapper.deleteReserveCoreWireUsageByCostId(costId);
	}

	/*
	 * =================================预定油漆使用量=====================================
	 * =
	 */

	/**
	 * 添加预定油漆使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 16:51
	 * @return
	 */
	@Override
	public int addReservePaintUsage(ReservePaintUsage reservePaintUsage) throws Exception {
		reservePaintUsageMapper.insertReservePaintUsage(reservePaintUsage);
		return reservePaintUsage.getReservePaintUsageId();
	}

	/**
	 * 根据参数获取预定油漆使用量
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 11:13
	 * @return
	 */
	@Override
	public List<ReservePaintUsage> listReservePaintUsageByYearMonth(String yearMonth) throws Exception {
		List<ReservePaintUsage> reservePaintUsageList = reservePaintUsageMapper
				.listReservePaintUsageByYearMonth(yearMonth);
		return reservePaintUsageList;
	}

	/**
	 * 根据参数获取预定油漆使用量--分页
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return
	 */
	@Override
	public ReservePaintUsageDTO listReservePaintUsagePage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<ReservePaintUsage> reservePaintUsageList = reservePaintUsageMapper.listReservePaintUsagePage(yearMonth,
				pageInfo);
		Integer total = reservePaintUsageMapper.listReservePaintUsagePageCount(yearMonth);
		pageInfo.setTotal(total);
		return new ReservePaintUsageDTO(pageInfo, reservePaintUsageList);
	}

	/**
	 * 删除预定油漆使用量明细
	 * 
	 * @param costId
	 * @throws
	 * @return int
	 */
	@Override
	public void removeReservePaintUsageByCostId(Integer costId) throws Exception {
		reservePaintUsageMapper.deleteReservePaintUsageByCostId(costId);
	}

	/*
	 * =================================预定运费======================================
	 */

	/**
	 * 添加预定运费
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/21 16:51
	 * @return
	 */
	@Override
	public int addReserveFreight(ReserveFreight reserveFreight) throws Exception {
		reserveFreightMapper.insertReserveFreight(reserveFreight);
		return reserveFreight.getReserveFreightId();
	}

	/**
	 * 根据参数获取预订运费列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/22 11:05
	 * @return
	 */
	@Override
	public List<ReserveFreight> listReserveFreightByYearMonth(String yearMonth) throws Exception {
		List<ReserveFreight> reserveFreightList = reserveFreightMapper.listReserveFreightByYearMonth(yearMonth);
		return reserveFreightList;
	}

	/**
	 * 根据参数获取预订运费列表
	 * 
	 * @throws
	 * <AUTHOR>
	 * @create 2023/9/19 13:13
	 * @return
	 */
	@Override
	public List<ReserveFreight> listReserveFreightByParam(String timeMin, String timeMax, Integer year, Integer month,
			String yearMonth, String[] auxiliaryCodeStrArr) throws Exception {
		List<ReserveFreight> reserveFreightList = reserveFreightMapper.listReserveFreightByParam(timeMin, timeMax, year,
				month, yearMonth, auxiliaryCodeStrArr);
		return reserveFreightList;
	}

	/**
	 * 根据参数获取预订运费列表--分页
	 * 
	 * @param
	 * @throws
	 * <AUTHOR>
	 * @create 2023/8/14 11:09
	 * @return
	 */
	@Override
	public ReserveFreightDTO listReserveFreightPage(String yearMonth, PageInfo pageInfo) throws Exception {
		List<ReserveFreight> reserveFreightList = reserveFreightMapper.listReserveFreightPage(yearMonth, pageInfo);
		Integer total = reserveFreightMapper.listReserveFreightPageCount(yearMonth);
		pageInfo.setTotal(total);
		return new ReserveFreightDTO(pageInfo, reserveFreightList);
	}

	/**
	 * 删除预定油漆使用量明细
	 * 
	 * @param costId
	 * @throws
	 * @return int
	 */
	@Override
	public void removeReserveFreightByCostId(Integer costId) throws Exception {
		reserveFreightMapper.deleteReserveFreightByCostId(costId);
	}

	/*
	 * =================================部门单价表======================================
	 */
	/**
	 * 根据年度、机械类别和页面类型分页查询部门单价表
	 *
	 * @param year        年度
	 * @param machineType 机械类别
	 * @param factoryType 工场区分(MW/UF)
	 * @param pageInfo    分页信息
	 * @return 部门单价表DTO
	 * @throws Exception
	 */
	@Override
	public DepartmentUnitPriceDTO listDepartmentUnitPricePage(String year, String machineType, String factoryType,
			PageInfo pageInfo)
			throws Exception {
		DepartmentUnitPriceDTO departmentUnitPriceDTO = new DepartmentUnitPriceDTO();
		List<DepartmentUnitPrice> departmentUnitPriceList = departmentUnitPriceMapper.listDepartmentUnitPricePage(year,
				machineType, factoryType, pageInfo);
		int total = departmentUnitPriceMapper.listDepartmentUnitPricePageCount(year, machineType, factoryType);
		pageInfo.setTotal(total);
		departmentUnitPriceDTO.setDepartmentUnitPriceList(departmentUnitPriceList);
		departmentUnitPriceDTO.setPageInfo(pageInfo);
		return departmentUnitPriceDTO;
	}

	/**
	 * 新增部门单价表记录
	 * 
	 * @param departmentUnitPrice 部门单价表实体
	 * @return 影响行数
	 * @throws Exception
	 */
	@Override
	public int addDepartmentUnitPrice(DepartmentUnitPrice departmentUnitPrice) throws Exception {
		// 计算项目费用 = SH系数 * 单价
		if (departmentUnitPrice.getShCoefficient() != null && departmentUnitPrice.getUnitPrice() != null) {
			departmentUnitPrice.setProjectCost(
					departmentUnitPrice.getShCoefficient().multiply(departmentUnitPrice.getUnitPrice()));
		}
		return departmentUnitPriceMapper.insertDepartmentUnitPrice(departmentUnitPrice);
	}

	/**
	 * 根据流水号查询部门单价表记录
	 * 
	 * @param serialNumber 流水号
	 * @return 部门单价表实体
	 * @throws Exception
	 */
	@Override
	public DepartmentUnitPrice getDepartmentUnitPriceById(Integer serialNumber) throws Exception {
		return departmentUnitPriceMapper.selectBySerialNumber(serialNumber);
	}

	/**
	 * 更新部门单价表记录
	 * 
	 * @param departmentUnitPrice 部门单价表实体
	 * @throws Exception
	 */
	@Override
	public void modifyDepartmentUnitPrice(DepartmentUnitPrice departmentUnitPrice) throws Exception {
		// 计算项目费用 = SH系数 * 单价
		if (departmentUnitPrice.getShCoefficient() != null && departmentUnitPrice.getUnitPrice() != null) {
			departmentUnitPrice.setProjectCost(
					departmentUnitPrice.getShCoefficient().multiply(departmentUnitPrice.getUnitPrice()));
		}
		departmentUnitPriceMapper.updateDepartmentUnitPrice(departmentUnitPrice);
	}

	/**
	 * 部门汇总查询
	 *
	 * @param startDate   开始日期
	 * @param endDate     结束日期
	 * @throws Exception
	 */
	@Override
	public List<SummaryOfDepartmentalUnitPricesBean> getDepartmentCostTotal(String startDate, String endDate) throws Exception {
		List<SummaryOfDepartmentalUnitPricesBean> departmentCostTotalList = new ArrayList<>();

		if (StringUtils.isAnyBlank(startDate, endDate)) {
			// 时间范围必传，不然数据量过大，会卡死
			return departmentCostTotalList;
		}

		// 构造时间范围字符串 (格式: yyyy-MM~yyyy-MM)x`
		String time = startDate + "~" + endDate;

		// 获取MW部门单价汇总数据
		List<SummaryOfDepartmentalUnitPricesBean> summaryOfMwList = Optional.ofNullable(
				yearReviseService.getSummaryOfDepartmentalUnitPricesList(time, null, "MW")).orElse(new ArrayList<>());

		// 获取UF部门单价汇总数据
		List<SummaryOfDepartmentalUnitPricesBean> summaryOfUfList = Optional.ofNullable(
				yearReviseService.getSummaryOfDepartmentalUnitPricesList(time, null, "UF")).orElse(new ArrayList<>());

		// 合并MW和UF部门单价汇总数据
		summaryOfMwList.addAll(summaryOfUfList);

		// 计算每个部门的直接及辅助部门费用合计
		if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(summaryOfMwList)) {
			// 根据机械类别分组
			Map<String, List<SummaryOfDepartmentalUnitPricesBean>> summaryMap = summaryOfMwList.stream()
					.collect(Collectors.groupingBy(SummaryOfDepartmentalUnitPricesBean::getMachineType));

			for (Map.Entry<String, List<SummaryOfDepartmentalUnitPricesBean>> entry : summaryMap.entrySet()) {
				String currentMachineType = entry.getKey();
				List<SummaryOfDepartmentalUnitPricesBean> currentDepartmentSummaryList = entry.getValue();

				SummaryOfDepartmentalUnitPricesBean departmentCostTotal = new SummaryOfDepartmentalUnitPricesBean();
				departmentCostTotal.setMachineType(currentMachineType);
				// departmentCostTotal.setDepartmentCode(currentDepartmentSummaryList.get(0).getDepartmentCode());
				departmentCostTotal.setDirectDeptCostTotal(BigDecimal.ZERO);
				departmentCostTotal.setAuxDeptAllocTotal(BigDecimal.ZERO);
				departmentCostTotal.setDeptCostTotal(BigDecimal.ZERO);

				for (SummaryOfDepartmentalUnitPricesBean summary : currentDepartmentSummaryList) {
					// 获取当前项目费用
					BigDecimal projectCost = Optional.ofNullable(summary.getProjectCost()).orElse(BigDecimal.ZERO);

					if ("直接".equals(summary.getAttribute())) {
						// 获取历史直接部门费用合计
						BigDecimal directDeptCostTotal = Optional.ofNullable(departmentCostTotal.getDirectDeptCostTotal()).orElse(BigDecimal.ZERO);

						// 计算直接部门费用合计
						departmentCostTotal.setDirectDeptCostTotal(directDeptCostTotal.add(projectCost));
					} else {
						// 获取历史辅助部门费用合计
						BigDecimal auxDeptAllocTotal = Optional.ofNullable(departmentCostTotal.getAuxDeptAllocTotal()).orElse(BigDecimal.ZERO);

						// 计算辅助部门费用合计
						departmentCostTotal.setAuxDeptAllocTotal(auxDeptAllocTotal.add(projectCost));
					}

					// 获取历史部门费用合计
					BigDecimal deptCostTotal = Optional.ofNullable(departmentCostTotal.getDeptCostTotal()).orElse(BigDecimal.ZERO);
					// 计算部门费用合计
					departmentCostTotal.setDeptCostTotal(deptCostTotal.add(projectCost));
				}

				departmentCostTotalList.add(departmentCostTotal);
			}
		}

		return departmentCostTotalList;
	}

	/**
	 * 从年度改订部门单价汇总导入数据到部门单价表
	 *
	 * @param startDate   开始日期
	 * @param endDate     结束日期
	 * @param factoryType 工场区分(MW/UF)
	 * @param userName    当前登录用户名
	 * @return 导入记录数
	 * @throws Exception
	 */
	@Override
	@Transactional
	public int importDepartmentUnitPriceFromSummary(String startDate, String endDate, String factoryType,
			String userName)
			throws Exception {
		// 根据开始日期获取年度
		String year = YearUtil.getYear(startDate);

		// 删除该年度指定工场区分的现有数据
		departmentUnitPriceMapper.deleteDepartmentUnitPriceByYearAndFactoryType(year, factoryType);

		// 构造时间范围字符串 (格式: yyyy-MM~yyyy-MM)
		String time = startDate + "~" + endDate;

		// 根据工场区分获取对应的部门单价汇总数据
		List<SummaryOfDepartmentalUnitPricesBean> summaryList = yearReviseService
				.getSummaryOfDepartmentalUnitPricesList(time, null, factoryType);

		// 转换为部门单价表实体并批量插入
		List<DepartmentUnitPrice> departmentUnitPriceList = new ArrayList<>();
		String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");

		// 处理指定工场区分的数据
		if (summaryList != null) {
			for (SummaryOfDepartmentalUnitPricesBean summary : summaryList) {
				DepartmentUnitPrice departmentUnitPrice = new DepartmentUnitPrice();
				departmentUnitPrice.setYear(year);
				departmentUnitPrice.setMachineType(summary.getMachineType());
				departmentUnitPrice.setAttribute(summary.getAttribute().equals("直接") ? "01" : "02");
				departmentUnitPrice.setExpenseItem(summary.getExpenseItem());
				departmentUnitPrice.setFactoryType(factoryType); // 设置工场区分
				departmentUnitPrice.setShCoefficient(summary.getShCoefficient());
				departmentUnitPrice.setUnitPrice(summary.getUnitPrice());
				departmentUnitPrice.setProjectCost(summary.getProjectCost());
				departmentUnitPrice.setCreatorName(userName);
				departmentUnitPrice.setCreatedTime(currentTime);
				departmentUnitPrice.setUpdaterName(userName);
				departmentUnitPrice.setUpdatedTime(currentTime);

				departmentUnitPriceList.add(departmentUnitPrice);
			}
		}

		// 批量插入
		if (!departmentUnitPriceList.isEmpty()) {
			// 分批处理，每50条更新一次
			if (departmentUnitPriceList.size() <= 50) {
				return departmentUnitPriceMapper.batchInsertDepartmentUnitPrice(departmentUnitPriceList);
			} else {
				int totalSize = departmentUnitPriceList.size();
				int batchSize = 50;
				// 向上取整计算批次数
				int totalBatches = (totalSize + batchSize - 1) / batchSize;
				int successCount = 0;

				for (int i = 0; i < totalBatches; i++) {
					int fromIndex = i * batchSize;
					int toIndex = Math.min((i + 1) * batchSize, totalSize);
					List<DepartmentUnitPrice> batchList = departmentUnitPriceList.subList(fromIndex, toIndex);
					successCount += departmentUnitPriceMapper.batchInsertDepartmentUnitPrice(batchList);
				}

				return successCount;
			}
		}

		return 0;
	}

	/**
	 * 成本参数复制功能
	 * 将费用项目单价表、原料项目表、导体单价明细表、油漆单价明细表、线盘单价明细表
	 * 从源年度复制到目标年度
	 *
	 * @param fromYear 源年度
	 * @param toYear   目标年度
	 * @return 复制的总记录数
	 * @throws Exception
	 */
	@Override
	@Transactional
	public int copyCostParameters(String fromYear, String toYear) throws Exception {
		int totalCopiedCount = 0;
		String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");

		// 设置默认用户信息
		String currentUserName = "系统";

		// 1. 复制费用项目单价表
		// 1-1. 删除目标年度的数据
		expenseItemUnitPriceMapper.deleteExpenseItemUnitPriceByYear(toYear);

		// 1-2. 复制源年度的数据到目标年度
		List<ExpenseItemUnitPrice> expenseItemUnitPrices = expenseItemUnitPriceMapper
				.selectExpenseItemUnitPriceByYear(fromYear);
		if (expenseItemUnitPrices != null && !expenseItemUnitPrices.isEmpty()) {
			for (ExpenseItemUnitPrice item : expenseItemUnitPrices) {
				item.setSerialNumber(0); // 重置流水号，让数据库自动生成
				item.setYear(toYear); // 更新年度
				item.setCreatorName(currentUserName); // 更新创建人
				item.setCreatedTime(currentTime); // 更新创建时间
				item.setUpdaterName(currentUserName); // 更新更新人
				item.setUpdatedTime(currentTime); // 更新更新时间
			}
			expenseItemUnitPriceMapper.batchInsertExpenseItemUnitPrice(expenseItemUnitPrices);
			totalCopiedCount += expenseItemUnitPrices.size();
		}

		// 2. 复制原料项目表
		// 2-1. 删除目标年度的数据
		rawMaterialItemMapper.deleteRawMaterialItemByYear(toYear);

		// 2-2. 复制源年度的数据到目标年度
		List<RawMaterialItem> rawMaterialItems = rawMaterialItemMapper.selectRawMaterialItemByYear(fromYear);
		if (rawMaterialItems != null && !rawMaterialItems.isEmpty()) {
			Date currentDate = new Date(); // 创建Date对象
			for (RawMaterialItem item : rawMaterialItems) {
				item.setSerialNumber(0); // 重置流水号，让数据库自动生成
				item.setYear(toYear); // 更新年度
				item.setCreatorName(currentUserName); // 更新创建人
				item.setCreatedTime(currentDate); // 更新创建时间
				item.setUpdaterName(currentUserName); // 更新更新人
				item.setUpdatedTime(currentDate); // 更新更新时间
			}
			rawMaterialItemMapper.batchInsertRawMaterialItem(rawMaterialItems);
			totalCopiedCount += rawMaterialItems.size();
		}

		// 3. 复制导体单价明细表（铜加工費用明细表）
		// 3-1. 删除目标年度的数据
		cuPriceCostMapper.deleteCuPriceCostByYear(toYear);

		// 3-2. 复制源年度的数据到目标年度
		List<CuPriceCost> cuPriceCosts = cuPriceCostMapper.selectCuPriceCostByYear(fromYear);
		if (cuPriceCosts != null && !cuPriceCosts.isEmpty()) {
			for (CuPriceCost item : cuPriceCosts) {
				item.setCostId(0); // 重置流水号，让数据库自动生成
				item.setYear(Integer.parseInt(toYear)); // 更新年度
				item.setCreatorName(currentUserName); // 更新创建人
				item.setCreatedTime(currentTime); // 更新创建时间
			}
			cuPriceCostMapper.batchInsertCuPriceCost(cuPriceCosts);
			totalCopiedCount += cuPriceCosts.size();
		}

		// 4. 复制油漆单价明细表（油漆价格表）
		// 4-1. 删除目标年度的数据
		paintPriceMapper.deletePaintPriceByYear(toYear);

		// 4-2. 复制源年度的数据到目标年度
		List<PaintPrice> paintPrices = paintPriceMapper.selectPaintPriceByYear(fromYear);
		if (paintPrices != null && !paintPrices.isEmpty()) {
			for (PaintPrice item : paintPrices) {
				item.setCostId(0); // 重置流水号，让数据库自动生成
				item.setYear(Integer.parseInt(toYear)); // 更新年度
				item.setCreatorName(currentUserName); // 更新创建人
				item.setCreatedTime(currentTime); // 更新创建时间
			}
			paintPriceMapper.batchInsertPaintPrice(paintPrices);
			totalCopiedCount += paintPrices.size();
		}

		// 5. 复制线盘单价明细表（线盘费用表）
		// 5-1. 删除目标年度的数据
		wireDiscCostMapper.deleteWireDiscCostByYear(toYear);

		// 5-2. 复制源年度的数据到目标年度
		List<WireDiscCost> wireDiscCosts = wireDiscCostMapper.selectWireDiscCostByYear(fromYear);
		if (wireDiscCosts != null && !wireDiscCosts.isEmpty()) {
			for (WireDiscCost item : wireDiscCosts) {
				item.setCostId(0); // 重置流水号，让数据库自动生成
				item.setYear(toYear); // 更新年度
				item.setCreatorName(currentUserName); // 更新创建人
				item.setCreatedTime(currentTime); // 更新创建时间
			}
			wireDiscCostMapper.batchInsertWireDiscCost(wireDiscCosts);
			totalCopiedCount += wireDiscCosts.size();
		}

		return totalCopiedCount;
	}

	/*
	 * =================================费用项目单价表=====================================
	 * =
	 */
	/**
	 * 根据年度分页查询费用项目单价表
	 *
	 * @param year     年度
	 * @param pageInfo 分页信息
	 * @return 费用项目单价表DTO
	 * @throws Exception
	 */
	@Override
	public ExpenseItemUnitPriceDTO listExpenseItemUnitPricePage(String year, PageInfo pageInfo) throws Exception {
		ExpenseItemUnitPriceDTO expenseItemUnitPriceDTO = new ExpenseItemUnitPriceDTO();
		List<ExpenseItemUnitPrice> expenseItemUnitPriceList = expenseItemUnitPriceMapper
				.listExpenseItemUnitPricePage(year, pageInfo);
		int total = expenseItemUnitPriceMapper.listExpenseItemUnitPricePageCount(year);
		pageInfo.setTotal(total);
		expenseItemUnitPriceDTO.setExpenseItemUnitPriceList(expenseItemUnitPriceList);
		expenseItemUnitPriceDTO.setPageInfo(pageInfo);
		return expenseItemUnitPriceDTO;
	}

	/**
	 * 新增费用项目单价表记录
	 *
	 * @param expenseItemUnitPrice 费用项目单价表实体
	 * @return 影响行数
	 * @throws Exception
	 */
	@Override
	public int addExpenseItemUnitPrice(ExpenseItemUnitPrice expenseItemUnitPrice) throws Exception {
		return expenseItemUnitPriceMapper.insertExpenseItemUnitPrice(expenseItemUnitPrice);
	}

	/**
	 * 根据流水号查询费用项目单价表记录
	 *
	 * @param serialNumber 流水号
	 * @return 费用项目单价表实体
	 * @throws Exception
	 */
	@Override
	public ExpenseItemUnitPrice getExpenseItemUnitPriceById(Integer serialNumber) throws Exception {
		return expenseItemUnitPriceMapper.selectBySerialNumber(serialNumber);
	}

	/**
	 * 更新费用项目单价表记录
	 *
	 * @param expenseItemUnitPrice 费用项目单价表实体
	 * @throws Exception
	 */
	@Override
	public void modifyExpenseItemUnitPrice(ExpenseItemUnitPrice expenseItemUnitPrice) throws Exception {
		expenseItemUnitPriceMapper.updateExpenseItemUnitPrice(expenseItemUnitPrice);

		// 根据需求，当编辑费用单价后，需要更新部门单价表
		// 根据「费用项目名称」、「年度」更新[部门单价表]
		if (expenseItemUnitPrice.getExpenseItemName() != null && expenseItemUnitPrice.getYear() != null) {
			// 查询部门单价表中对应的记录并更新
			departmentUnitPriceMapper.updateDepartmentUnitPriceByExpenseItem(
					expenseItemUnitPrice.getYear(),
					expenseItemUnitPrice.getExpenseItemName(),
					expenseItemUnitPrice.getExpenseItemUnitPrice());
		}
	}

	/* ======================原料项目表====================== */
	/**
	 * 根据年度分页查询原料项目表
	 *
	 * @param year     年度
	 * @param pageInfo 分页信息
	 * @return 原料项目表DTO
	 * @throws Exception
	 */
	@Override
	public RawMaterialItemDTO listRawMaterialItemPage(String year, PageInfo pageInfo) throws Exception {
		RawMaterialItemDTO rawMaterialItemDTO = new RawMaterialItemDTO();

		// 查询总数
		int totalCount = rawMaterialItemMapper.listRawMaterialItemPageCount(year);
		pageInfo.setTotal(totalCount);

		// 查询列表
		List<RawMaterialItem> rawMaterialItemList = rawMaterialItemMapper.listRawMaterialItemPage(year, pageInfo);
		rawMaterialItemDTO.setRawMaterialItemList(rawMaterialItemList);
		rawMaterialItemDTO.setPageInfo(pageInfo);

		return rawMaterialItemDTO;
	}

	@Override
	public RawMaterialItemDTO listRawMaterialItemPageByMaterialType(String year, String materialType, PageInfo pageInfo)
			throws Exception {
		RawMaterialItemDTO rawMaterialItemDTO = new RawMaterialItemDTO();

		// 查询总数
		int totalCount = rawMaterialItemMapper.listRawMaterialItemPageCountByMaterialType(year, materialType);
		pageInfo.setTotal(totalCount);

		// 查询列表
		List<RawMaterialItem> rawMaterialItemList = rawMaterialItemMapper.listRawMaterialItemPageByMaterialType(year,
				materialType, pageInfo);
		rawMaterialItemDTO.setRawMaterialItemList(rawMaterialItemList);
		rawMaterialItemDTO.setPageInfo(pageInfo);

		return rawMaterialItemDTO;
	}

	/**
	 * 新增原料项目表记录
	 *
	 * @param rawMaterialItem 原料项目表实体
	 * @return 影响行数
	 * @throws Exception
	 */
	@Override
	public int addRawMaterialItem(RawMaterialItem rawMaterialItem) throws Exception {
		rawMaterialItemMapper.insertRawMaterialItem(rawMaterialItem);
		return 1;
	}

	/**
	 * 根据流水号查询原料项目表记录
	 *
	 * @param serialNumber 流水号
	 * @return 原料项目表实体
	 * @throws Exception
	 */
	@Override
	public RawMaterialItem getRawMaterialItemById(Integer serialNumber) throws Exception {
		return rawMaterialItemMapper.selectBySerialNumber(serialNumber);
	}

	@Override
	public List<ConductorCode> getConductorCodeList() throws Exception {
		return conductorCodeMapper.listConductorCodeByParam((short) 0);
	}

	@Override
	@Transactional
	public void addRawMaterialItemWithDetail(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
		RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();

		// 1. 新增原料项目表
		rawMaterialItemMapper.insertRawMaterialItem(rawMaterialItem);

		// 2. 根据原料区分新增对应的明细表
		String materialType = rawMaterialItem.getMaterialType();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		if ("01".equals(materialType)) {
			// 导体
			ConductorUnitPriceDetail detail = rawMaterialItemDTO.getConductorUnitPriceDetail();
			if (detail != null) {
				detail.setYear(rawMaterialItem.getYear());
				detail.setItemCode(rawMaterialItem.getItemCode());
				detail.setItemName(rawMaterialItem.getItemName());
				detail.setMaterialType(rawMaterialItem.getMaterialType());
				detail.setCreatorName(rawMaterialItem.getCreatorName());
				detail.setCreatedTime(
						rawMaterialItem.getCreatedTime() != null ? sdf.format(rawMaterialItem.getCreatedTime()) : null);
				detail.setUpdaterName(rawMaterialItem.getUpdaterName());
				detail.setUpdatedTime(
						rawMaterialItem.getUpdatedTime() != null ? sdf.format(rawMaterialItem.getUpdatedTime()) : null);
				conductorUnitPriceDetailMapper.insertConductorUnitPriceDetail(detail);
			}
		} else if ("02".equals(materialType)) {
			// 油漆
			PaintUnitPriceDetail detail = rawMaterialItemDTO.getPaintUnitPriceDetail();
			if (detail != null) {
				detail.setYear(rawMaterialItem.getYear());
				detail.setItemCode(rawMaterialItem.getItemCode());
				detail.setItemName(rawMaterialItem.getItemName());
				detail.setMaterialType(rawMaterialItem.getMaterialType());
				detail.setCreatorName(rawMaterialItem.getCreatorName());
				detail.setCreatedTime(
						rawMaterialItem.getCreatedTime() != null ? sdf.format(rawMaterialItem.getCreatedTime()) : null);
				detail.setUpdaterName(rawMaterialItem.getUpdaterName());
				detail.setUpdatedTime(
						rawMaterialItem.getUpdatedTime() != null ? sdf.format(rawMaterialItem.getUpdatedTime()) : null);
				paintUnitPriceDetailMapper.insertPaintUnitPriceDetail(detail);
			}
		} else if ("03".equals(materialType)) {
			// 线盘
			WireDiscUnitPriceDetail detail = rawMaterialItemDTO.getWireDiscUnitPriceDetail();
			if (detail != null) {
				detail.setYear(rawMaterialItem.getYear());
				detail.setItemCode(rawMaterialItem.getItemCode());
				detail.setItemName(rawMaterialItem.getItemName());
				detail.setMaterialType(rawMaterialItem.getMaterialType());
				detail.setCreatorName(rawMaterialItem.getCreatorName());
				detail.setCreatedTime(
						rawMaterialItem.getCreatedTime() != null ? sdf.format(rawMaterialItem.getCreatedTime()) : null);
				detail.setUpdaterName(rawMaterialItem.getUpdaterName());
				detail.setUpdatedTime(
						rawMaterialItem.getUpdatedTime() != null ? sdf.format(rawMaterialItem.getUpdatedTime()) : null);
				wireDiscUnitPriceDetailMapper.insertWireDiscUnitPriceDetail(detail);
			}
		}
	}

	@Override
	@Transactional
	public void updateRawMaterialItemWithDetail(RawMaterialItemDTO rawMaterialItemDTO) throws Exception {
		RawMaterialItem rawMaterialItem = rawMaterialItemDTO.getRawMaterialItem();

		// 1. 更新原料项目表
		rawMaterialItemMapper.updateRawMaterialItem(rawMaterialItem);

		// 2. 根据原料区分更新对应的明细表
		String materialType = rawMaterialItem.getMaterialType();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		if ("01".equals(materialType)) {
			// 导体
			ConductorUnitPriceDetail detail = rawMaterialItemDTO.getConductorUnitPriceDetail();
			if (detail != null) {
				detail.setYear(rawMaterialItem.getYear());
				detail.setItemCode(rawMaterialItem.getItemCode());
				detail.setItemName(rawMaterialItem.getItemName());
				detail.setMaterialType(rawMaterialItem.getMaterialType());
				detail.setUpdaterName(rawMaterialItem.getUpdaterName());
				detail.setUpdatedTime(
						rawMaterialItem.getUpdatedTime() != null ? sdf.format(rawMaterialItem.getUpdatedTime()) : null);
				conductorUnitPriceDetailMapper.updateConductorUnitPriceDetail(detail);
			}
		} else if ("02".equals(materialType)) {
			// 油漆
			PaintUnitPriceDetail detail = rawMaterialItemDTO.getPaintUnitPriceDetail();
			if (detail != null) {
				detail.setYear(rawMaterialItem.getYear());
				detail.setItemCode(rawMaterialItem.getItemCode());
				detail.setItemName(rawMaterialItem.getItemName());
				detail.setMaterialType(rawMaterialItem.getMaterialType());
				detail.setUpdaterName(rawMaterialItem.getUpdaterName());
				detail.setUpdatedTime(
						rawMaterialItem.getUpdatedTime() != null ? sdf.format(rawMaterialItem.getUpdatedTime()) : null);
				paintUnitPriceDetailMapper.updatePaintUnitPriceDetail(detail);
			}
		} else if ("03".equals(materialType)) {
			// 线盘
			WireDiscUnitPriceDetail detail = rawMaterialItemDTO.getWireDiscUnitPriceDetail();
			if (detail != null) {
				detail.setYear(rawMaterialItem.getYear());
				detail.setItemCode(rawMaterialItem.getItemCode());
				detail.setItemName(rawMaterialItem.getItemName());
				detail.setMaterialType(rawMaterialItem.getMaterialType());
				detail.setUpdaterName(rawMaterialItem.getUpdaterName());
				detail.setUpdatedTime(
						rawMaterialItem.getUpdatedTime() != null ? sdf.format(rawMaterialItem.getUpdatedTime()) : null);
				wireDiscUnitPriceDetailMapper.updateWireDiscUnitPriceDetail(detail);
			}
		}
	}

	@Override
	public ConductorUnitPriceDetail getConductorUnitPriceDetailByYearAndItemCode(String year, String itemCode)
			throws Exception {
		return conductorUnitPriceDetailMapper.selectByYearAndItemCode(year, itemCode);
	}

	@Override
	public PaintUnitPriceDetail getPaintUnitPriceDetailByYearAndItemCode(String year, String itemCode)
			throws Exception {
		return paintUnitPriceDetailMapper.selectByYearAndItemCode(year, itemCode);
	}

	@Override
	public WireDiscUnitPriceDetail getWireDiscUnitPriceDetailByYearAndItemCode(String year, String itemCode)
			throws Exception {
		return wireDiscUnitPriceDetailMapper.selectByYearAndItemCode(year, itemCode);
	}

	/**
	 * 更新原料项目表记录
	 *
	 * @param rawMaterialItem 原料项目表实体
	 * @throws Exception
	 */
	@Override
	public void modifyRawMaterialItem(RawMaterialItem rawMaterialItem) throws Exception {
		rawMaterialItemMapper.updateRawMaterialItem(rawMaterialItem);
	}

	/* ======================导体成本编码表====================== */
	/**
	 * 根据参数获取导体成本编码列表
	 *
	 * @param state 状态
	 * @return 导体成本编码列表
	 * @throws Exception
	 */
	@Override
	public List<ConductorCode> listConductorCodeByParam(Short state) throws Exception {
		return conductorCodeMapper.listConductorCodeByParam(state);
	}

	/**
	 * 获取导体成本编码表详情
	 *
	 * @param conductorName 导体名称
	 * @return 导体成本编码
	 * @throws Exception
	 */
	@Override
	public ConductorCode getConductorCodeByParam(String conductorName) throws Exception {
		return conductorCodeMapper.selectConductorCodeByParam(conductorName);
	}

	/* ======================导体单价明细表====================== */
	/**
	 * 根据年度和品目查询导体单价明细表
	 *
	 * @param year     年度
	 * @param itemCode 品目
	 * @return 导体单价明细表
	 * @throws Exception
	 */
	@Override
	public ConductorUnitPriceDetail getConductorUnitPriceDetail(String year, String itemCode) throws Exception {
		return conductorUnitPriceDetailMapper.selectByYearAndItemCode(year, itemCode);
	}

	/**
	 * 新增导体单价明细表
	 *
	 * @param conductorUnitPriceDetail 导体单价明细表实体
	 * @throws Exception
	 */
	@Override
	public void addConductorUnitPriceDetail(ConductorUnitPriceDetail conductorUnitPriceDetail) throws Exception {
		conductorUnitPriceDetailMapper.insertConductorUnitPriceDetail(conductorUnitPriceDetail);
	}

	/**
	 * 更新导体单价明细表
	 *
	 * @param conductorUnitPriceDetail 导体单价明细表实体
	 * @throws Exception
	 */
	@Override
	public void modifyConductorUnitPriceDetail(ConductorUnitPriceDetail conductorUnitPriceDetail) throws Exception {
		conductorUnitPriceDetailMapper.updateConductorUnitPriceDetail(conductorUnitPriceDetail);
	}

	/* ======================油漆单价明细表====================== */
	/**
	 * 根据年度和品目查询油漆单价明细表
	 *
	 * @param year     年度
	 * @param itemCode 品目
	 * @return 油漆单价明细表
	 * @throws Exception
	 */
	@Override
	public PaintUnitPriceDetail getPaintUnitPriceDetail(String year, String itemCode) throws Exception {
		return paintUnitPriceDetailMapper.selectByYearAndItemCode(year, itemCode);
	}

	/**
	 * 新增油漆单价明细表
	 *
	 * @param paintUnitPriceDetail 油漆单价明细表实体
	 * @throws Exception
	 */
	@Override
	public void addPaintUnitPriceDetail(PaintUnitPriceDetail paintUnitPriceDetail) throws Exception {
		paintUnitPriceDetailMapper.insertPaintUnitPriceDetail(paintUnitPriceDetail);
	}

	/**
	 * 更新油漆单价明细表
	 *
	 * @param paintUnitPriceDetail 油漆单价明细表实体
	 * @throws Exception
	 */
	@Override
	public void modifyPaintUnitPriceDetail(PaintUnitPriceDetail paintUnitPriceDetail) throws Exception {
		paintUnitPriceDetailMapper.updatePaintUnitPriceDetail(paintUnitPriceDetail);
	}

	/* ======================线盘单价明细表====================== */
	/**
	 * 根据年度和品目查询线盘单价明细表
	 *
	 * @param year     年度
	 * @param itemCode 品目
	 * @return 线盘单价明细表
	 * @throws Exception
	 */
	@Override
	public WireDiscUnitPriceDetail getWireDiscUnitPriceDetail(String year, String itemCode) throws Exception {
		return wireDiscUnitPriceDetailMapper.selectByYearAndItemCode(year, itemCode);
	}

	/**
	 * 新增线盘单价明细表
	 *
	 * @param wireDiscUnitPriceDetail 线盘单价明细表实体
	 * @throws Exception
	 */
	@Override
	public void addWireDiscUnitPriceDetail(WireDiscUnitPriceDetail wireDiscUnitPriceDetail) throws Exception {
		wireDiscUnitPriceDetailMapper.insertWireDiscUnitPriceDetail(wireDiscUnitPriceDetail);
	}

	/**
	 * 更新线盘单价明细表
	 *
	 * @param wireDiscUnitPriceDetail 线盘单价明细表实体
	 * @throws Exception
	 */
	@Override
	public void modifyWireDiscUnitPriceDetail(WireDiscUnitPriceDetail wireDiscUnitPriceDetail) throws Exception {
		wireDiscUnitPriceDetailMapper.updateWireDiscUnitPriceDetail(wireDiscUnitPriceDetail);
	}

	/* ======================运费单价表====================== */
	/**
	 * 根据年度分页查询运费单价表
	 *
	 * @param year     年度
	 * @param pageInfo 分页信息
	 * @return 运费单价表DTO
	 * @throws Exception
	 */
	@Override
	public FreightUnitPriceDTO listFreightUnitPricePage(String year, PageInfo pageInfo) throws Exception {
		List<FreightUnitPrice> freightUnitPriceList = freightUnitPriceMapper.listFreightUnitPricePage(year, pageInfo);
		Integer total = freightUnitPriceMapper.listFreightUnitPricePageCount(year);
		pageInfo.setTotal(total);

		FreightUnitPriceDTO freightUnitPriceDTO = new FreightUnitPriceDTO();
		freightUnitPriceDTO.setFreightUnitPriceList(freightUnitPriceList);
		freightUnitPriceDTO.setPageInfo(pageInfo);
		return freightUnitPriceDTO;
	}

	/**
	 * 新增运费单价表记录
	 *
	 * @param freightUnitPrice 运费单价表实体
	 * @return 影响行数
	 * @throws Exception
	 */
	@Override
	public int addFreightUnitPrice(FreightUnitPrice freightUnitPrice) throws Exception {
		return freightUnitPriceMapper.insertFreightUnitPrice(freightUnitPrice);
	}

	/**
	 * 根据流水号查询运费单价表记录
	 *
	 * @param serialNumber 流水号
	 * @return 运费单价表实体
	 * @throws Exception
	 */
	@Override
	public FreightUnitPrice getFreightUnitPriceById(Integer serialNumber) throws Exception {
		return freightUnitPriceMapper.selectBySerialNumber(serialNumber);
	}

	/**
	 * 更新运费单价表记录
	 *
	 * @param freightUnitPrice 运费单价表实体
	 * @throws Exception
	 */
	@Override
	public void modifyFreightUnitPrice(FreightUnitPrice freightUnitPrice) throws Exception {
		freightUnitPriceMapper.updateFreightUnitPrice(freightUnitPrice);
	}

	/**
	 * 从运费汇总导入数据到运费单价表
	 *
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @return 导入记录数
	 * @throws Exception
	 */
	@Override
	@Transactional
	public int importFreightUnitPriceFromSummary(String startDate, String endDate) throws Exception {
		// 获取年度
		String year = YearUtil.getYear(startDate);
		String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");
		String currentUserName = "系统";

		// 1. 删除指定年度的运费单价表数据
		freightUnitPriceMapper.deleteByYear(year);

		// 2. 从运输费汇总查询数据
		List<TransportationCostBean> transportationCostBeanList = yearReviseService
				.selectTransportationCostBeanList(startDate, endDate);

		int importCount = 0;
		if (transportationCostBeanList != null && !transportationCostBeanList.isEmpty()) {
			for (TransportationCostBean bean : transportationCostBeanList) {
				FreightUnitPrice freightUnitPrice = new FreightUnitPrice();
				freightUnitPrice.setYear(year);
				freightUnitPrice.setArea(bean.getArea());
				// 使用综合运费(含回收)作为运费
				if (bean.getComprehensiveFreight() != null) {
					freightUnitPrice.setFreight(new BigDecimal(bean.getComprehensiveFreight()));
				} else if (bean.getTransportationCostUnit() != null) {
					freightUnitPrice.setFreight(new BigDecimal(bean.getTransportationCostUnit()));
				}
				freightUnitPrice.setCreatorName(currentUserName);
				freightUnitPrice.setCreatedTime(currentTime);
				freightUnitPrice.setUpdaterName(currentUserName);
				freightUnitPrice.setUpdatedTime(currentTime);

				freightUnitPriceMapper.insertFreightUnitPrice(freightUnitPrice);
				importCount++;
			}
		}

		return importCount;
	}

	/* ======================导体成本编码表====================== */
	/**
	 * 分页查询导体成本编码列表
	 *
	 * @param conductorName 导体名称
	 * @param pageInfo      分页信息
	 * @return 分页结果
	 * @throws Exception
	 */
	@Override
	public ConductorCodeDTO listConductorCodePage(String conductorName, PageInfo pageInfo) throws Exception {
		List<ConductorCode> conductorCodeList = conductorCodeMapper.listConductorCodePage(conductorName, pageInfo);
		Integer total = conductorCodeMapper.listConductorCodePageCount(conductorName);
		pageInfo.setTotal(total);
		return new ConductorCodeDTO(pageInfo, conductorCodeList);
	}

	/**
	 * 新增导体成本编码
	 *
	 * @param conductorCode 导体成本编码实体
	 * @return 流水号
	 * @throws Exception
	 */
	@Override
	public int addConductorCode(ConductorCode conductorCode) throws Exception {
		conductorCodeMapper.insertConductorCode(conductorCode);
		return conductorCode.getCodeId();
	}

	/**
	 * 修改导体成本编码
	 *
	 * @param conductorCode 导体成本编码实体
	 * @throws Exception
	 */
	@Override
	public void modifyConductorCode(ConductorCode conductorCode) throws Exception {
		conductorCodeMapper.updateConductorCode(conductorCode);
	}

	/**
	 * 删除导体成本编码
	 *
	 * @param codeId 流水号
	 * @throws Exception
	 */
	@Override
	public void deleteConductorCode(int codeId) throws Exception {
		ConductorCode conductorCode = new ConductorCode();
		conductorCode.setCodeId(codeId);
		conductorCode.setState(ConductorCode.STATE_DELETED);
		conductorCodeMapper.updateConductorCode(conductorCode);
	}

	/**
	 * 根据流水号获取导体成本编码
	 *
	 * @param codeId 流水号
	 * @return 导体成本编码
	 * @throws Exception
	 */
	@Override
	public ConductorCode getConductorCodeById(int codeId) throws Exception {
		return conductorCodeMapper.selectConductorCodeById(codeId);
	}

	/**
	 * 生成下一个导体成本编码
	 *
	 * @return 成本编码
	 * @throws Exception
	 */
	@Override
	public String generateNextConductorCode() throws Exception {
		String maxCode = conductorCodeMapper.selectMaxCostCode();
		if (maxCode == null || maxCode.isEmpty()) {
			return "C1001";
		}
		// 提取数字部分并递增
		String numberPart = maxCode.substring(2);
		int nextNumber = Integer.parseInt(numberPart) + 1;
		return String.format("C1%03d", nextNumber);
	}

	/* ======================油漆成本编码表====================== */
	/**
	 * 分页查询油漆成本编码列表（按社内油漆名）
	 *
	 * @param insidePaintName 社内油漆名
	 * @param pageInfo        分页信息
	 * @return 分页结果
	 * @throws Exception
	 */
	@Override
	public PaintCodeDTO listPaintCodePageByName(String insidePaintName, PageInfo pageInfo) throws Exception {
		List<PaintCode> paintCodeList = paintCodeMapper.listPaintCodePageByName(insidePaintName, pageInfo);
		Integer total = paintCodeMapper.listPaintCodePageCountByName(insidePaintName);
		pageInfo.setTotal(total);
		return new PaintCodeDTO(pageInfo, paintCodeList);
	}

	/**
	 * 修改油漆成本编码
	 *
	 * @param paintCode 油漆成本编码实体
	 * @throws Exception
	 */
	@Override
	public void modifyPaintCode(PaintCode paintCode) throws Exception {
		paintCodeMapper.updatePaintCode(paintCode);
	}

	/**
	 * 删除油漆成本编码
	 *
	 * @param codeId 流水号
	 * @throws Exception
	 */
	@Override
	public void deletePaintCode(int codeId) throws Exception {
		paintCodeMapper.updateState(codeId, PaintCode.STATE_DELETED);
	}

	/**
	 * 根据流水号获取油漆成本编码详情
	 *
	 * @param codeId 流水号
	 * @return 油漆成本编码
	 * @throws Exception
	 */
	@Override
	public PaintCode getPaintCodeByCodeId(int codeId) throws Exception {
		return paintCodeMapper.selectPaintCodeById(codeId);
	}

	/**
	 * 生成下一个油漆成本编码
	 *
	 * @return 成本编码
	 * @throws Exception
	 */
	@Override
	public String generateNextPaintCode() throws Exception {
		String maxCode = paintCodeMapper.selectMaxPaintCode();
		if (maxCode == null || maxCode.isEmpty()) {
			return "C2001";
		}
		// 提取数字部分并递增
		String numberPart = maxCode.substring(2);
		int nextNumber = Integer.parseInt(numberPart) + 1;
		return String.format("C2%03d", nextNumber);
	}

	/**
	 * 获取油漆品种下拉列表
	 *
	 * @return 油漆品种列表
	 * @throws Exception
	 */
	@Override
	public List<String> listPaintVarieties() throws Exception {
		return paintCodeMapper.listPaintVarieties();
	}

	/* ======================线盘成本编码表====================== */
	/**
	 * 分页查询线盘成本编码列表
	 *
	 * @param wireDiscName 线盘名称
	 * @param pageInfo     分页信息
	 * @return 分页结果
	 * @throws Exception
	 */
	@Override
	public WireDiscCodeDTO listWireDiscCodePage(String wireDiscName, PageInfo pageInfo) throws Exception {
		List<WireDiscCode> wireDiscCodeList = wireDiscCodeMapper.listWireDiscCodePage(wireDiscName, pageInfo);
		Integer total = wireDiscCodeMapper.listWireDiscCodePageCount(wireDiscName);
		pageInfo.setTotal(total);
		return new WireDiscCodeDTO(pageInfo, wireDiscCodeList);
	}

	/**
	 * 新增线盘成本编码
	 *
	 * @param wireDiscCode 线盘成本编码实体
	 * @return 成功标识（1表示成功）
	 * @throws Exception
	 */
	@Override
	public int addWireDiscCode(WireDiscCode wireDiscCode) throws Exception {
		wireDiscCodeMapper.insertWireDiscCode(wireDiscCode);
		// 由于使用子查询生成ID，直接返回1表示成功
		return 1;
	}

	/**
	 * 修改线盘成本编码
	 *
	 * @param wireDiscCode 线盘成本编码实体
	 * @throws Exception
	 */
	@Override
	public void modifyWireDiscCode(WireDiscCode wireDiscCode) throws Exception {
		wireDiscCodeMapper.updateWireDiscCode(wireDiscCode);
	}

	/**
	 * 删除线盘成本编码
	 *
	 * @param codeId 流水号
	 * @throws Exception
	 */
	@Override
	public void deleteWireDiscCode(int codeId) throws Exception {
		WireDiscCode wireDiscCode = new WireDiscCode();
		wireDiscCode.setCodeId(codeId);
		wireDiscCode.setState(WireDiscCode.STATE_DELETED);
		wireDiscCodeMapper.updateWireDiscCode(wireDiscCode);
	}

	/**
	 * 根据流水号获取线盘成本编码
	 *
	 * @param codeId 流水号
	 * @return 线盘成本编码
	 * @throws Exception
	 */
	@Override
	public WireDiscCode getWireDiscCodeById(int codeId) throws Exception {
		return wireDiscCodeMapper.selectWireDiscCodeById(codeId);
	}

	/**
	 * 生成下一个线盘成本编码
	 *
	 * @return 成本编码
	 * @throws Exception
	 */
	@Override
	public String generateNextWireDiscCode() throws Exception {
		String maxCode = wireDiscCodeMapper.selectMaxWireDiscCode();
		if (maxCode == null || maxCode.isEmpty()) {
			return "C3001";
		}
		// 提取数字部分并递增
		String numberPart = maxCode.substring(2);
		int nextNumber = Integer.parseInt(numberPart) + 1;
		return String.format("C3%03d", nextNumber);
	}

}