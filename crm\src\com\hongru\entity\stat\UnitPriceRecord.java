package com.hongru.entity.stat;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("预定单价表")//UnitPriceRecord
public class UnitPriceRecord {

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int statId;

	/* 年度 */
	@TableField("年度")
	protected String year;
	/* 电力单价 */
	@TableField("电力单价")
	protected BigDecimal electricUnitPrice;
	/* 一般社员单价 */
	@TableField("一般社员单价")
	protected BigDecimal generalUnitPrice;
	/* 临时工单价 */
	@TableField("临时工单价")
	protected BigDecimal temporaryUnitPrice;
	/* 保全单价 */
	@TableField("保全单价")
	protected BigDecimal saveDamageUnitPrice;
	/* WR加工费EM */
	@TableField("WR加工费EM")
	protected BigDecimal wrEMUnitPrice;
	/* WR加工费EF */
	@TableField("WR加工费EF")
	protected BigDecimal wrEFUnitPrice;
	/* WR加工费ER */
	@TableField("WR加工费ER")
	protected BigDecimal wrERUnitPrice;
	/* WR加工费EH */
	@TableField("WR加工费EH")
	protected BigDecimal wrEHUnitPrice;
	/* 运费单价 */
	@TableField("运费单价")
	protected BigDecimal tranUnitPrice;
	/* 线盘单价 */
	@TableField("线盘单价")
	protected BigDecimal wireUnitPrice;
	/* 线盘回收率 */
	@TableField("线盘回收率")
	protected BigDecimal wireRecycling;
	/* 铜屑单价 */
	@TableField("铜屑单价")
	protected BigDecimal cuCrumbsUnitPrice;
	/* 天然气单价 */
	@TableField("天然气单价")
	protected BigDecimal gasUnitPrice;
	/* 窒素单价 */
	@TableField("窒素单价")
	protected BigDecimal nitrogenUnitPrice;

	public int getStatId() {
		return statId;
	}

	public void setStatId(int statId) {
		this.statId = statId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public BigDecimal getElectricUnitPrice() {
		return electricUnitPrice;
	}

	public void setElectricUnitPrice(BigDecimal electricUnitPrice) {
		this.electricUnitPrice = electricUnitPrice;
	}

	public BigDecimal getGeneralUnitPrice() {
		return generalUnitPrice;
	}

	public void setGeneralUnitPrice(BigDecimal generalUnitPrice) {
		this.generalUnitPrice = generalUnitPrice;
	}

	public BigDecimal getTemporaryUnitPrice() {
		return temporaryUnitPrice;
	}

	public void setTemporaryUnitPrice(BigDecimal temporaryUnitPrice) {
		this.temporaryUnitPrice = temporaryUnitPrice;
	}

	public BigDecimal getSaveDamageUnitPrice() {
		return saveDamageUnitPrice;
	}

	public void setSaveDamageUnitPrice(BigDecimal saveDamageUnitPrice) {
		this.saveDamageUnitPrice = saveDamageUnitPrice;
	}

	public BigDecimal getWrEMUnitPrice() {
		return wrEMUnitPrice;
	}

	public void setWrEMUnitPrice(BigDecimal wrEMUnitPrice) {
		this.wrEMUnitPrice = wrEMUnitPrice;
	}

	public BigDecimal getWrEFUnitPrice() {
		return wrEFUnitPrice;
	}

	public void setWrEFUnitPrice(BigDecimal wrEFUnitPrice) {
		this.wrEFUnitPrice = wrEFUnitPrice;
	}

	public BigDecimal getWrERUnitPrice() {
		return wrERUnitPrice;
	}

	public void setWrERUnitPrice(BigDecimal wrERUnitPrice) {
		this.wrERUnitPrice = wrERUnitPrice;
	}

	public BigDecimal getWrEHUnitPrice() {
		return wrEHUnitPrice;
	}

	public void setWrEHUnitPrice(BigDecimal wrEHUnitPrice) {
		this.wrEHUnitPrice = wrEHUnitPrice;
	}

	public BigDecimal getTranUnitPrice() {
		return tranUnitPrice;
	}

	public void setTranUnitPrice(BigDecimal tranUnitPrice) {
		this.tranUnitPrice = tranUnitPrice;
	}

	public BigDecimal getWireUnitPrice() {
		return wireUnitPrice;
	}

	public void setWireUnitPrice(BigDecimal wireUnitPrice) {
		this.wireUnitPrice = wireUnitPrice;
	}

	public BigDecimal getWireRecycling() {
		return wireRecycling;
	}

	public void setWireRecycling(BigDecimal wireRecycling) {
		this.wireRecycling = wireRecycling;
	}

	public BigDecimal getCuCrumbsUnitPrice() {
		return cuCrumbsUnitPrice;
	}

	public void setCuCrumbsUnitPrice(BigDecimal cuCrumbsUnitPrice) {
		this.cuCrumbsUnitPrice = cuCrumbsUnitPrice;
	}

	public BigDecimal getGasUnitPrice() {
		return gasUnitPrice;
	}

	public void setGasUnitPrice(BigDecimal gasUnitPrice) {
		this.gasUnitPrice = gasUnitPrice;
	}

	public BigDecimal getNitrogenUnitPrice() {
		return nitrogenUnitPrice;
	}

	public void setNitrogenUnitPrice(BigDecimal nitrogenUnitPrice) {
		this.nitrogenUnitPrice = nitrogenUnitPrice;
	}
}