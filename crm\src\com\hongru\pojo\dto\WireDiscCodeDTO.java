package com.hongru.pojo.dto;

import com.hongru.entity.cost.WireDiscCode;
import com.hongru.support.page.PageInfo;

import java.util.List;

/**
 * 线盘成本编码DTO
 * <AUTHOR>
 */
public class WireDiscCodeDTO {
    
    private PageInfo pageInfo;
    private List<WireDiscCode> wireDiscCodeList;
    
    public WireDiscCodeDTO() {
    }
    
    public WireDiscCodeDTO(PageInfo pageInfo, List<WireDiscCode> wireDiscCodeList) {
        this.pageInfo = pageInfo;
        this.wireDiscCodeList = wireDiscCodeList;
    }
    
    public PageInfo getPageInfo() {
        return pageInfo;
    }
    
    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
    
    public List<WireDiscCode> getWireDiscCodeList() {
        return wireDiscCodeList;
    }
    
    public void setWireDiscCodeList(List<WireDiscCode> wireDiscCodeList) {
        this.wireDiscCodeList = wireDiscCodeList;
    }
}
