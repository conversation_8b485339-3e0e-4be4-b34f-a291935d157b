package com.hongru.controller.stat;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.*;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.stat.*;
import com.hongru.pojo.dto.ReportFormsDTO;
import com.hongru.service.business.IReportFormsBusinessService;
import com.hongru.service.stat.IStatService;
import com.hongru.support.page.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping(value = "/stat")
public class StatController extends BaseController {
    @Autowired
    private IReportFormsBusinessService reportFormsService;
    @Autowired
    private IStatService statService;
    /*=================================reportForms======================================*/

    /**
     * 获取月度原价CR实绩列表页面
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 10:28
     * @return
     */
    @GetMapping(value = "/reportForms/monthCostPrice/list/view")
    public String getReportFormsMonthCostPriceListView(Model model) throws Exception {
        return "/modules/stat/reportForms_monthCostPrice_list";
    }

    /**
     * 获取月度原价CR实绩列表
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 10:29
     * @return
     */
    @PostMapping(value = "/reportForms/monthCostPrice/list")
    @ResponseBody
    public Object getReportFormsMonthCostPriceList(Short reportType, String title, String year, String yearAndMonth,PageInfo pageInfo) throws Exception {
        //获取当前登录用户信息
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        String userName =  authorizingUser.getUserName();
        
        ReportFormsDTO reportFormsDTO = reportFormsService.listReportFormsByPage( reportType, title, year, yearAndMonth, pageInfo,userName);
        return new HrPageResult(reportFormsDTO.getReportFormsList(),reportFormsDTO.getPageInfo().getTotal());
    }

    /**
    * 添加月度原价CR实绩页面
    * @throws
    * <AUTHOR>
    * @create 2023/9/20 16:08
    * @return
    */
    @GetMapping("/reportForms/monthCostPrice/add/view")
    public String reportFormsMonthCostPriceAddView(Model model) throws Exception{
        return "/modules/stat/reportForms_monthCostPrice_add";
    }

    /**
    * 添加月度原价CR实绩
    * @throws
    * <AUTHOR>
    * @create 2023/9/20 16:08
    * @return
    */
    @PostMapping("/reportForms/monthCostPrice/add")
    @ResponseBody
    public Object reportFormsMonthCostPriceAdd(/*MultipartFile filename,*/ String yearAndMonth, String title, HttpServletRequest request){
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }
            Integer year = null;
            Integer month = null;
            if(!StringUtil.isStringEmpty(yearAndMonth)){
                String[] timeArr = yearAndMonth.split("-");
                if(timeArr != null && timeArr.length > 0){
                    year = Integer.parseInt(timeArr[0]);
                }
                if(timeArr != null && timeArr.length > 1){
                    month = Integer.parseInt(timeArr[1]);
                }
            }

            //项目下路径
            String tempFilePath = request.getScheme() + "://" + request.getServerName();
            if(request.getServerPort() != 80 && request.getServerPort() != 443){
                tempFilePath+= ":" +request.getServerPort();
            }
            tempFilePath+=request.getContextPath()+"/static/hongru/downloadfile/report1.xls";
            String extensionName = tempFilePath.substring(tempFilePath.lastIndexOf(".")+1);
            String newFileName = "月度原价CR实绩("+year+")"+DateUtils.getCurrentTime()+"."+extensionName;
            FileUtils.download( tempFilePath, newFileName,FileUtils.getSavePath()+"/"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM")+"/");
            //获取新下载的文件
            String filePath = "/"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM")+"/"+newFileName;

            ReportForms reportForms = new ReportForms();
            reportForms.setReportType(ReportForms.REPORTTYPE_MONTHCOSTPRICE);
            reportForms.setTitle(title);
            reportForms.setCreatorId(authorizingUser.getUserId().intValue());
            reportForms.setCreatorName(authorizingUser.getUserName());
            reportForms.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            reportForms.setYearAndMonth(yearAndMonth);
            reportForms.setYear(year + "");

            BoundQuantity boundQuantity = statService.selectBoundQuantityByYearMonth(yearAndMonth);

           String flag = reportFormsService.addReportFormsForMain(reportForms,filePath,boundQuantity);
           // 判断报表参与计算相关数据是否有缺失
          if("0".equals(flag)) {
        	  ;
          }else if("1".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少电力数据！");
          }else if("2".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少水费数据！");
          }else if("3".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少电力使用明细数据！");
          }else if("4".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), " 缺少燃气费用数据！");
          }else if("5".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定直接部门回收计算数据！");
          }else if("6".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定辅助部门回收计算人工数据！");
          }else if("7".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少人件工时数据！");
          }else if("8".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少人件费用数据！");
          }else if("9".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定辅助部门回收计算补修辅材数据！");
          }else if("10".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少辅材費用汇总数据！");
          }else if("11".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少运输費用数据！");
          }else if("12".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定运费数据！");
          }else if("13".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少辅材費用数据！");
          }else if("14".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定屑使用量数据！");
          }else if("15".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定油漆使用量数据！");
          }else if("16".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定捆包费数据！");
          }else if("17".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定线盘回收计算数据！");
          }else if("18".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少油漆使用明细数据！");
          }else if("19".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定单价数据！");
          }else if("20".equals(flag)){
        	  return new HrResult(CommonReturnCode.FAILED.getCode(), " 缺少入库数据！");
          }
          
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 删除月度原价CR实绩
     * @throws
     * <AUTHOR>
     * @create 2025/04/13 10:28s
     * @return
     */
    @PostMapping(value = "/reportForms/monthCostPrice/remove")
    @ResponseBody
    public Object reportFormsMonthCostPriceRemove(Integer reportFormsId) throws Exception {
    	 try {
    		 reportFormsService.removeReportForms(reportFormsId);
    	 }catch (Exception e){
             e.printStackTrace();
             logger.error("报表删除异常信息：", e);
             return new HrResult(0,e.getMessage());
         }
         return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 获取CR計画列表页面
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 10:28
     * @return
     */
    @GetMapping(value = "/reportForms/crPlan/list/view")
    public String getReportFormsCrPlanListView(Model model) throws Exception {
        return "/modules/stat/reportForms_crPlan_list";
    }

    /**
     * 获取CR計画列表
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 10:28
     * @return
     */
    @PostMapping(value = "/reportForms/crPlan/list")
    @ResponseBody
    public Object getReportFormsCrPlanList(Short reportType, String title, String year, String yearAndMonth,PageInfo pageInfo) throws Exception {
        //获取当前登录用户信息
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        String userName =  authorizingUser.getUserName();
        
        ReportFormsDTO reportFormsDTO = reportFormsService.listReportFormsByPage( reportType, title, year, yearAndMonth, pageInfo,userName);
        return new HrPageResult(reportFormsDTO.getReportFormsList(),reportFormsDTO.getPageInfo().getTotal());
    }

    /**
    * CR計画添加页面
    * @throws
    * <AUTHOR>
    * @create 2023/9/19 14:49
    * @return
    */
    @GetMapping("/reportForms/crPlan/add/view")
    public String reportFormsCrPlanAddView(Model model) throws Exception{
        return "/modules/stat/reportForms_crPlan_add";
    }

    /**
    * CR計画添加
    * @throws 
    * <AUTHOR>
    * @create 2023/9/19 15:35
    * @return 
    */
    @PostMapping("/reportForms/crPlan/add")
    @ResponseBody
    public Object reportFormsCrPlanAdd(MultipartFile filename, String yearAndMonth, String endYearAndMonth, String title, HttpServletRequest request){
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }
            Integer year = null;
            if(!StringUtil.isStringEmpty(yearAndMonth)){
                String[] timeArr = yearAndMonth.split("-");
                if(timeArr != null && timeArr.length > 0){
                    year = Integer.parseInt(timeArr[0]);
                }
            }
            String newFileName = "CR計画("+year+")"+DateUtils.getCurrentTime();
            String filePath = FileUtils.downloadV2(filename, newFileName,"/"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM")+"/");//本地处理文件
            ReportForms reportForms = new ReportForms();
            reportForms.setReportType(ReportForms.REPORTTYPE_CRPLAN);
            reportForms.setTitle(title);
            reportForms.setCreatorId(authorizingUser.getUserId().intValue());
            reportForms.setCreatorName(authorizingUser.getUserName());
            reportForms.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            reportForms.setYearAndMonth(yearAndMonth);
            reportForms.setYear(year+"");
            reportForms.setStartYearAndMonth(yearAndMonth);
            reportForms.setEndYearAndMonth(endYearAndMonth);
            reportForms.setFilePath("/"+yearAndMonth+"/"+newFileName+".xls");
            String flag = reportFormsService.addReportFormsForMain(reportForms,filePath,null);
            // 判断报表参与计算相关数据是否有缺失
            if("0".equals(flag)) {
          	  ;
            }else if("1".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少电力数据！");
            }else if("2".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少水费数据！");
            }else if("3".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少电力使用明细数据！");
            }else if("4".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), " 缺少燃气费用数据！");
            }else if("5".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定直接部门回收计算数据！");
            }else if("6".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定辅助部门回收计算人工数据！");
            }else if("7".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少人件工时数据！");
            }else if("8".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少人件费用数据！");
            }else if("9".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定辅助部门回收计算补修辅材数据！");
            }else if("10".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少辅材費用汇总数据！");
            }else if("11".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少运输費用数据！");
            }else if("12".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定运费数据！");
            }else if("13".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少辅材費用数据！");
            }else if("14".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定屑使用量数据！");
            }else if("15".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定油漆使用量数据！");
            }else if("16".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定捆包费数据！");
            }else if("17".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定线盘回收计算数据！");
            }else if("18".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少油漆使用明细数据！");
            }else if("19".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定单价数据！");
            }else if("20".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), " 缺少入库数据！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除CR計画
     * @throws
     * <AUTHOR>
     * @create 2025/04/13 10:28s
     * @return
     */
    @PostMapping(value = "/reportForms/CrPlan/remove")
    @ResponseBody
    public Object reportFormsCrPlanRemove(Integer reportFormsId) throws Exception {
    	 try {
    		 reportFormsService.removeReportForms(reportFormsId);
    	 }catch (Exception e){
             e.printStackTrace();
             logger.error("CR計画删除异常信息：", e);
             return new HrResult(0,e.getMessage());
         }
         return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 获取管财差列表页面
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 10:28
     * @return
     */
    @GetMapping(value = "/reportForms/guanCaiCha/list/view")
    public String getReportFormsGuanCaiChaListView(Model model) throws Exception {
        return "/modules/stat/reportForms_guanCaiCha_list";
    }

    /**
     * 获取管财差列表
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 10:28
     * @return
     */
    @PostMapping(value = "/reportForms/guanCaiCha/list")
    @ResponseBody
    public Object getReportFormsGuanCaiChaList(Short reportType, String title, String year, String yearAndMonth,PageInfo pageInfo) throws Exception {
        //获取当前登录用户信息
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        String userName =  authorizingUser.getUserName();
        
        ReportFormsDTO reportFormsDTO = reportFormsService.listReportFormsByPage( reportType, title, year, yearAndMonth, pageInfo,userName);
        return new HrPageResult(reportFormsDTO.getReportFormsList(),reportFormsDTO.getPageInfo().getTotal());
    }

    /**
     * 添加管财差页面
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 13:59
     * @return
     */
    @GetMapping("/reportForms/guanCaiCha/add/view")
    public String reportFormsGuanCaiChaAddView(Model model) throws Exception{
        return "/modules/stat/reportForms_guanCaiCha_add";
    }

    /**
     * 添加管财差
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 14:04
     * @return
     */
    @PostMapping("/reportForms/guanCaiCha/add")
    @ResponseBody
    public Object reportFormsGuanCaiChaAdd(/*MultipartFile filename,*/String yearAndMonth, String title, HttpServletRequest request) throws Exception{
        try {
            //获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            Integer year = null;
            if(!StringUtil.isStringEmpty(yearAndMonth)){
                String[] timeArr = yearAndMonth.split("-");
                if(timeArr != null && timeArr.length > 0){
                    year = Integer.parseInt(timeArr[0]);
                }
            }

            //项目下路径
            String tempFilePath = request.getScheme() + "://" + request.getServerName();
            if(request.getServerPort() != 80 && request.getServerPort() != 443){
                tempFilePath+= ":" +request.getServerPort();
            }
            tempFilePath+=request.getContextPath()+"/static/hongru/downloadfile/report3.xls";
            String extensionName = tempFilePath.substring(tempFilePath.lastIndexOf(".")+1);
            String newFileName = "管财差("+year+")"+DateUtils.getCurrentTime()+"."+extensionName;
            FileUtils.download( tempFilePath, newFileName,FileUtils.getSavePath()+"/"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM")+"/");
            //获取新下载的文件
            String filePath = "/"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM")+"/"+newFileName;

            ReportForms reportForms = new ReportForms();
            reportForms.setReportType(ReportForms.REPORTTYPE_GUANCAICHA);
            reportForms.setTitle(title);
            reportForms.setCreatorId(authorizingUser.getUserId().intValue());
            reportForms.setCreatorName(authorizingUser.getUserName());
            reportForms.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            reportForms.setYearAndMonth(yearAndMonth);
            reportForms.setYear(year+"");
            String flag = reportFormsService.addReportFormsForMain(reportForms,filePath,null);
         // 判断报表参与计算相关数据是否有缺失
            if("0".equals(flag)) {
          	  ;
            }else if("1".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少电力数据！");
            }else if("2".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少水费数据！");
            }else if("3".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少电力使用明细数据！");
            }else if("4".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), " 缺少燃气费用数据！");
            }else if("5".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定直接部门回收计算数据！");
            }else if("6".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定辅助部门回收计算人工数据！");
            }else if("7".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少人件工时数据！");
            }else if("8".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少人件费用数据！");
            }else if("9".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定辅助部门回收计算补修辅材数据！");
            }else if("10".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少辅材費用汇总数据！");
            }else if("11".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少运输費用数据！");
            }else if("12".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定运费数据！");
            }else if("13".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少辅材費用数据！");
            }else if("14".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定屑使用量数据！");
            }else if("15".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定油漆使用量数据！");
            }else if("16".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定捆包费数据！");
            }else if("17".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定线盘回收计算数据！");
            }else if("18".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少油漆使用明细数据！");
            }else if("19".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少预定单价数据！");
            }else if("20".equals(flag)){
          	  return new HrResult(CommonReturnCode.FAILED.getCode(), " 缺少入库数据！");
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("管财差新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 删除管财差
     * @throws
     * <AUTHOR>
     * @create 2025/04/13 10:28s
     * @return
     */
    @PostMapping(value = "/reportForms/guanCaiCha/remove")
    @ResponseBody
    public Object reportFormsGuanCaiChaRemove(Integer reportFormsId) throws Exception {
    	 try {
    		 reportFormsService.removeReportForms(reportFormsId);
    	 }catch (Exception e){
             e.printStackTrace();
             logger.error("管财差删除异常信息：", e);
             return new HrResult(0,e.getMessage());
         }
         return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 获取WIN-W製造CR実績列表页面
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 10:28
     * @return
     */
    @GetMapping(value = "/reportForms/win/list/view")
    public String getReportFormsWinListView(Model model) throws Exception {
        return "/modules/stat/reportForms_win_list";
    }

    /**
     * 获取WIN-W製造CR実績列表
     * @throws
     * <AUTHOR>
     * @create 2023/9/18 10:28
     * @return
     */
    @PostMapping(value = "/reportForms/win/list")
    @ResponseBody
    public Object getReportFormsWinList(Short reportType, String title, String year, String yearAndMonth,PageInfo pageInfo) throws Exception {
        //获取当前登录用户信息
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        String userName =  authorizingUser.getUserName();
        
        ReportFormsDTO reportFormsDTO = reportFormsService.listReportFormsByPage( reportType, title, year, yearAndMonth, pageInfo,userName);
        return new HrPageResult(reportFormsDTO.getReportFormsList(),reportFormsDTO.getPageInfo().getTotal());
    }

    /**
    * 添加WIN-W製造CR実績页面
    * @throws
    * <AUTHOR>
    * @create 2023/9/26 13:22
    * @return
    */
    @GetMapping("/reportForms/win/add/view")
    public String reportFormsWinAddView(Model model) throws Exception{
        return "/modules/stat/reportForms_win_add";
    }

    /**
    * 添加WIN-W製造CR実績
    * @throws
    * <AUTHOR>
    * @create 2023/9/26 13:22
    * @return
    */
    @PostMapping("/reportForms/win/add")
    @ResponseBody
    public Object reportFormsWinAdd(String yearAndMonth, String title, HttpServletRequest request) throws Exception{
        try {
            //获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            ReportForms reportForms = new ReportForms();
            reportForms.setReportType(ReportForms.REPORTTYPE_WIN);
            reportForms.setTitle(title);
            reportForms.setCreatorId(authorizingUser.getUserId().intValue());
            reportForms.setCreatorName(authorizingUser.getUserName());
            reportForms.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            if(!StringUtil.isStringEmpty(yearAndMonth)){
                reportForms.setYearAndMonth(yearAndMonth);
                String[] timeArr = yearAndMonth.split("-");
                reportForms.setYear(timeArr[0]);
            }
            //绝对路径
            //String downloadPath = ServletUtils.getRequest().getSession().getServletContext().getRealPath("/")+"static"+ File.separator+"hongru"+File.separator+"downloadfile"+File.separator+"report3.xls";
            //项目下路径
            String tempFilePath = request.getScheme() + "://" + request.getServerName();
            if(request.getServerPort() != 80 && request.getServerPort() != 443){
                tempFilePath+= ":" +request.getServerPort();
            }
            tempFilePath+=request.getContextPath()+"/static/hongru/downloadfile/report4.xls";
            String extensionName = tempFilePath.substring(tempFilePath.lastIndexOf(".")+1);
            String newFileName = "WIN-W製造CR実績("+reportForms.getYearAndMonth()+")"+DateUtils.getCurrentTime()+"."+extensionName;
            FileUtils.download( tempFilePath, newFileName,FileUtils.getSavePath()+"/"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM")+"/");
            //获取新下载的文件
            String filePath = "/"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM")+"/"+newFileName;
            String flag = reportFormsService.addReportFormsForMain(reportForms,filePath,null);
            // 判断报表参与计算相关数据是否有缺失
            if("0".equals(flag)) {
             	  ;
            }else if("9".equals(flag)){
             	return new HrResult(CommonReturnCode.FAILED.getCode(), "缺少CR实绩相关数据！");
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("WIN-W製造CR実績新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除CR計画
     * @throws
     * <AUTHOR>
     * @create 2025/04/13 10:28s
     * @return
     */
    @PostMapping(value = "/reportForms/win/remove")
    @ResponseBody
    public Object reportFormsWinRemove(Integer reportFormsId) throws Exception {
    	 try {
    		 reportFormsService.removeReportForms(reportFormsId);
    	 }catch (Exception e){
             e.printStackTrace();
             logger.error("WIN-W製造CR実績删除异常信息：", e);
             return new HrResult(0,e.getMessage());
         }
         return new HrResult(CommonReturnCode.SUCCESS);
    }
}