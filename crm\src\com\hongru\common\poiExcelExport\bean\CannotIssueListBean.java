package com.hongru.common.poiExcelExport.bean;

public class CannotIssueListBean {
	/*木托号*/
	protected String palletNo;
	/*Lot*/
	protected String batchNo;
	/*尺寸*/
	protected String size;
	/*重量*/
	protected String weight;
	/*线盘*/
	protected String coil;
	/*原因*/
	protected String reason;
	/*入库日期*/
	protected String stockInTime;
	/*产品代码*/
	protected String productCode;
	/*客户简称*/
	protected String customerAbbreviation;

	public String getPalletNo() {
		return palletNo;
	}

	public void setPalletNo(String palletNo) {
		this.palletNo = palletNo;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getSize() {
		return size;
	}

	public void setSize(String size) {
		this.size = size;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getCoil() {
		return coil;
	}

	public void setCoil(String coil) {
		this.coil = coil;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getStockInTime() {
		return stockInTime;
	}

	public void setStockInTime(String stockInTime) {
		this.stockInTime = stockInTime;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getCustomerAbbreviation() {
		return customerAbbreviation;
	}

	public void setCustomerAbbreviation(String customerAbbreviation) {
		this.customerAbbreviation = customerAbbreviation;
	}
}
