package com.hongru.pojo.dto;

import com.hongru.entity.cost.FreightUnitPrice;
import com.hongru.support.page.PageInfo;

import java.util.List;

/**
 * 运费单价表DTO
 * <AUTHOR>
 */
public class FreightUnitPriceDTO {
    
    private List<FreightUnitPrice> freightUnitPriceList;
    private PageInfo pageInfo;

    public List<FreightUnitPrice> getFreightUnitPriceList() {
        return freightUnitPriceList;
    }

    public void setFreightUnitPriceList(List<FreightUnitPrice> freightUnitPriceList) {
        this.freightUnitPriceList = freightUnitPriceList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
}
