
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    laydate.render({
        elem: '#time'
        ,type: 'month'
        ,range: '~'
        ,format: 'yyyy-MM'
      });

    //执行一个 table 实例
    var url = baselocation+'/yearRevise/machineTimePreUF/list';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '列表'
        ,page: false //开启分页
//		,limits: [1000]
//		,limit: 1000   // 每页条数
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            ,{field: 'department',title: '部门',align:'center', width:100}
            ,{field: 'machineTimePreAvg',title: '机械时间予定(平均)',align:'center', width:200}
            ,{field: 'newYearMachineTimePre',title: '新年度机械时间予定',align:'center', width:200}
        ]]
    });

});

function search() {
	var time=$("#time").val();
	if(time == null || time.trim() == ''){
		layer.alert("请选择日期范围！");
		return;
	}else{
	    $("#isSearch").val(1);
	    var temp = $("#formSearch").serializeJsonObject();
	    console.info(temp);
	    //执行重载
	    layui.table.reload('demo', {
	    	where: temp
	    }, 'data');
	}
}
// MW、UF一览明细选择
function toPage(orders) {
    if(orders == 1){
        window.location.href = baselocation+'/yearRevise/machineTimePre/list/view';
    }else if(orders == 2){
        window.location.href = baselocation+'/yearRevise/machineTimePreUF/list/view';
    }
}
