package com.hongru.common.poiExcelExport.bean;

public class DayInTheLibraryBean {
	/*客户简称*/
	protected String userName;
	/*产品型号*/
	protected String productMode;
	/*产品尺寸*/
	protected String size;
	/*线盘名称*/
	protected String coil;
	/*上日在库*/
	protected String lastWeekInTheLibrary;
	/*本日入库*/
	protected String warehousingThisWeek;
	/*本日出库*/
	protected String issueThisWeek;
	/*本日在库*/
	protected String inLibraryThisWeek;
	/*不可出库品*/
	protected String nonDeliverableProducts;
	/*SEW*/
	protected String sew;

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getProductMode() {
		return productMode;
	}

	public void setProductMode(String productMode) {
		this.productMode = productMode;
	}

	public String getSize() {
		return size;
	}

	public void setSize(String size) {
		this.size = size;
	}

	public String getCoil() {
		return coil;
	}

	public void setCoil(String coil) {
		this.coil = coil;
	}

	public String getLastWeekInTheLibrary() {
		return lastWeekInTheLibrary;
	}

	public void setLastWeekInTheLibrary(String lastWeekInTheLibrary) {
		this.lastWeekInTheLibrary = lastWeekInTheLibrary;
	}

	public String getWarehousingThisWeek() {
		return warehousingThisWeek;
	}

	public void setWarehousingThisWeek(String warehousingThisWeek) {
		this.warehousingThisWeek = warehousingThisWeek;
	}

	public String getIssueThisWeek() {
		return issueThisWeek;
	}

	public void setIssueThisWeek(String issueThisWeek) {
		this.issueThisWeek = issueThisWeek;
	}

	public String getInLibraryThisWeek() {
		return inLibraryThisWeek;
	}

	public void setInLibraryThisWeek(String inLibraryThisWeek) {
		this.inLibraryThisWeek = inLibraryThisWeek;
	}

	public String getNonDeliverableProducts() {
		return nonDeliverableProducts;
	}

	public void setNonDeliverableProducts(String nonDeliverableProducts) {
		this.nonDeliverableProducts = nonDeliverableProducts;
	}

	public String getSew() {
		return sew;
	}

	public void setSew(String sew) {
		this.sew = sew;
	}
}
