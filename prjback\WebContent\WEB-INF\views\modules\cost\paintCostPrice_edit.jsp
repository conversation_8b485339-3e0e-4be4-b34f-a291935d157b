<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>油漆单价编辑</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/paintCostPrice/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value="${paintPrice.yearMonth}"/>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 100px"><span class="star">*</span>供应商油漆名:</label>
                        <div class="layui-input-block" style="margin-left: 130px">
                            <select class="layui-select" id="outPaintName" name="outPaintName" lay-filter="outPaintNameFun" lay-search="true">
                                <c:forEach items="${fullPaintNameList}" var="paintName">
                                    <option value="${paintName.fullPaintName}" <c:if test="${paintName.fullPaintName eq paintPrice.outPaintName}">selected</c:if>>${paintName.fullPaintName}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 100px"><span class="star">*</span>供应商:</label>
                        <div class="layui-input-block" style="margin-left: 130px">
                            <select class="layui-select" id="producer" name="producer" lay-search="true">
                                <option value="">${paintPrice.producer}</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>购入单价:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="paintPrice" name="paintPrice" value="${paintPrice.paintPrice}" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>存货单价:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="inventoryPrice" name="inventoryPrice" value="${paintPrice.inventoryPrice}" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="costId" name="costId" value="${paintPrice.costId}"/>
                            <button type="button" class="layui-btn" lay-submit lay-filter="formDemo"><i class="layui-icon layui-icon-ok"></i>确定</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/paintCostPrice_edit.js?time=7"></script>
</myfooter>
</body>
</html>
