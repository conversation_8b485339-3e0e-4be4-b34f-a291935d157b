package com.hongru.entity.check;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("his_UQC复绕检查表")//QC
public class HisUqcFrCheck {
	public static final String CHECK_QCDR_NORMAL  = "0";//正常
	public static final String CHECK_QCDR_ERROR   = "1";//超出规格

	/* 流水号 */
	@TableId(value="checkId", type= IdType.AUTO)
	protected Integer checkId;//流水号
	protected String dateTime;//日期
	protected String productCodeA;//产品代码A
	protected String barCodeA;//条码A
	protected String batchNumberA;//批号A (lot)
	protected String serialNumberA;//序列号A (sn)
	protected String customerCodeA;//客户代码A
	protected String productCodeB;//产品代码B
	protected String barCodeB;//条码B
	protected String batchNumberB;//批号B (lot)
	protected String serialNumberB;//序列号B (sn)
	protected String customerCodeB;//客户代码B
	protected String checkItem;//检查项目
	protected String itemCode;//项目编号
	protected String lineNumber;//线头号
	protected String frequentness;//频度
	protected String condition;//试验条件
	protected String unit;//单位
	protected String lsl;//规格下限
	protected String direction1;//方向1
	protected String usl;//规格上限
	protected String direction2;//方向2
	protected String weight;//荷重
	protected String entry1;//输入值
	protected String entry2;//输入值2
	protected String entry3;//输入值3
	protected String entry4;//输入值4
	protected String entry5;//输入值5
	protected String entry6;//输入值6
	protected String entry7;//输入值7
	protected String entry8;//输入值8
	protected String entry9;//输入值9
	protected String entry10;//输入值10
	protected String entry11;//输入值11
	protected String entry12;//输入值12
	protected String fileNumber;//文件号
	protected String recordNumber;//记录号
	protected String modifyNumber;//修改号
	protected Integer count;//数量
	protected String qcdr;//QCDR
	protected String sa;//SA
	protected String fuRaoDate;//复绕日期
	protected String machineNumber;//设备代码
	protected String shift;//班（早，中，晚）
	protected String creatorName;//作业员
	protected String comment;//备注
	protected String queryConfirm;//查询确认
	protected String entryMin;//最小值
	protected String entryAverage;//平均值
	protected String entryMax;//最大值
	protected String minLsl;//最小值下限
	protected String minUsl;//最小值上限
	protected String averageLsl;//平均值下限
	protected String averageUsl;//平均值上限
	protected String maxLsl;//最大值下限
	protected String maxUsl;//最大值上限
	protected String confirmTime;//确认时间
	protected String confirmName;//确认者

	public static String getCheckQcdrNormal() {
		return CHECK_QCDR_NORMAL;
	}

	public static String getCheckQcdrError() {
		return CHECK_QCDR_ERROR;
	}

	public Integer getCheckId() {
		return checkId;
	}

	public void setCheckId(Integer checkId) {
		this.checkId = checkId;
	}

	public String getDateTime() {
		return dateTime;
	}

	public void setDateTime(String dateTime) {
		this.dateTime = dateTime;
	}

	public String getProductCodeA() {
		return productCodeA;
	}

	public void setProductCodeA(String productCodeA) {
		this.productCodeA = productCodeA;
	}

	public String getBarCodeA() {
		return barCodeA;
	}

	public void setBarCodeA(String barCodeA) {
		this.barCodeA = barCodeA;
	}

	public String getBatchNumberA() {
		return batchNumberA;
	}

	public void setBatchNumberA(String batchNumberA) {
		this.batchNumberA = batchNumberA;
	}

	public String getSerialNumberA() {
		return serialNumberA;
	}

	public void setSerialNumberA(String serialNumberA) {
		this.serialNumberA = serialNumberA;
	}

	public String getCustomerCodeA() {
		return customerCodeA;
	}

	public void setCustomerCodeA(String customerCodeA) {
		this.customerCodeA = customerCodeA;
	}

	public String getProductCodeB() {
		return productCodeB;
	}

	public void setProductCodeB(String productCodeB) {
		this.productCodeB = productCodeB;
	}

	public String getBarCodeB() {
		return barCodeB;
	}

	public void setBarCodeB(String barCodeB) {
		this.barCodeB = barCodeB;
	}

	public String getBatchNumberB() {
		return batchNumberB;
	}

	public void setBatchNumberB(String batchNumberB) {
		this.batchNumberB = batchNumberB;
	}

	public String getSerialNumberB() {
		return serialNumberB;
	}

	public void setSerialNumberB(String serialNumberB) {
		this.serialNumberB = serialNumberB;
	}

	public String getCustomerCodeB() {
		return customerCodeB;
	}

	public void setCustomerCodeB(String customerCodeB) {
		this.customerCodeB = customerCodeB;
	}

	public String getCheckItem() {
		return checkItem;
	}

	public void setCheckItem(String checkItem) {
		this.checkItem = checkItem;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public String getLineNumber() {
		return lineNumber;
	}

	public void setLineNumber(String lineNumber) {
		this.lineNumber = lineNumber;
	}

	public String getFrequentness() {
		return frequentness;
	}

	public void setFrequentness(String frequentness) {
		this.frequentness = frequentness;
	}

	public String getCondition() {
		return condition;
	}

	public void setCondition(String condition) {
		this.condition = condition;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getLsl() {
		return lsl;
	}

	public void setLsl(String lsl) {
		this.lsl = lsl;
	}

	public String getDirection1() {
		return direction1;
	}

	public void setDirection1(String direction1) {
		this.direction1 = direction1;
	}

	public String getUsl() {
		return usl;
	}

	public void setUsl(String usl) {
		this.usl = usl;
	}

	public String getDirection2() {
		return direction2;
	}

	public void setDirection2(String direction2) {
		this.direction2 = direction2;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getEntry1() {
		return entry1;
	}

	public void setEntry1(String entry1) {
		this.entry1 = entry1;
	}

	public String getEntry2() {
		return entry2;
	}

	public void setEntry2(String entry2) {
		this.entry2 = entry2;
	}

	public String getEntry3() {
		return entry3;
	}

	public void setEntry3(String entry3) {
		this.entry3 = entry3;
	}

	public String getEntry4() {
		return entry4;
	}

	public void setEntry4(String entry4) {
		this.entry4 = entry4;
	}

	public String getEntry5() {
		return entry5;
	}

	public void setEntry5(String entry5) {
		this.entry5 = entry5;
	}

	public String getEntry6() {
		return entry6;
	}

	public void setEntry6(String entry6) {
		this.entry6 = entry6;
	}

	public String getEntry7() {
		return entry7;
	}

	public void setEntry7(String entry7) {
		this.entry7 = entry7;
	}

	public String getEntry8() {
		return entry8;
	}

	public void setEntry8(String entry8) {
		this.entry8 = entry8;
	}

	public String getEntry9() {
		return entry9;
	}

	public void setEntry9(String entry9) {
		this.entry9 = entry9;
	}

	public String getEntry10() {
		return entry10;
	}

	public void setEntry10(String entry10) {
		this.entry10 = entry10;
	}

	public String getEntry11() {
		return entry11;
	}

	public void setEntry11(String entry11) {
		this.entry11 = entry11;
	}

	public String getEntry12() {
		return entry12;
	}

	public void setEntry12(String entry12) {
		this.entry12 = entry12;
	}

	public String getFileNumber() {
		return fileNumber;
	}

	public void setFileNumber(String fileNumber) {
		this.fileNumber = fileNumber;
	}

	public String getRecordNumber() {
		return recordNumber;
	}

	public void setRecordNumber(String recordNumber) {
		this.recordNumber = recordNumber;
	}

	public String getModifyNumber() {
		return modifyNumber;
	}

	public void setModifyNumber(String modifyNumber) {
		this.modifyNumber = modifyNumber;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getQcdr() {
		return qcdr;
	}

	public void setQcdr(String qcdr) {
		this.qcdr = qcdr;
	}

	public String getSa() {
		return sa;
	}

	public void setSa(String sa) {
		this.sa = sa;
	}

	public String getFuRaoDate() {
		return fuRaoDate;
	}

	public void setFuRaoDate(String fuRaoDate) {
		this.fuRaoDate = fuRaoDate;
	}

	public String getMachineNumber() {
		return machineNumber;
	}

	public void setMachineNumber(String machineNumber) {
		this.machineNumber = machineNumber;
	}

	public String getShift() {
		return shift;
	}

	public void setShift(String shift) {
		this.shift = shift;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getQueryConfirm() {
		return queryConfirm;
	}

	public void setQueryConfirm(String queryConfirm) {
		this.queryConfirm = queryConfirm;
	}

	public String getEntryMin() {
		return entryMin;
	}

	public void setEntryMin(String entryMin) {
		this.entryMin = entryMin;
	}

	public String getEntryAverage() {
		return entryAverage;
	}

	public void setEntryAverage(String entryAverage) {
		this.entryAverage = entryAverage;
	}

	public String getEntryMax() {
		return entryMax;
	}

	public void setEntryMax(String entryMax) {
		this.entryMax = entryMax;
	}

	public String getMinLsl() {
		return minLsl;
	}

	public void setMinLsl(String minLsl) {
		this.minLsl = minLsl;
	}

	public String getMinUsl() {
		return minUsl;
	}

	public void setMinUsl(String minUsl) {
		this.minUsl = minUsl;
	}

	public String getAverageLsl() {
		return averageLsl;
	}

	public void setAverageLsl(String averageLsl) {
		this.averageLsl = averageLsl;
	}

	public String getAverageUsl() {
		return averageUsl;
	}

	public void setAverageUsl(String averageUsl) {
		this.averageUsl = averageUsl;
	}

	public String getMaxLsl() {
		return maxLsl;
	}

	public void setMaxLsl(String maxLsl) {
		this.maxLsl = maxLsl;
	}

	public String getMaxUsl() {
		return maxUsl;
	}

	public void setMaxUsl(String maxUsl) {
		this.maxUsl = maxUsl;
	}

	public String getConfirmTime() {
		return confirmTime;
	}

	public void setConfirmTime(String confirmTime) {
		this.confirmTime = confirmTime;
	}

	public String getConfirmName() {
		return confirmName;
	}

	public void setConfirmName(String confirmName) {
		this.confirmName = confirmName;
	}
}