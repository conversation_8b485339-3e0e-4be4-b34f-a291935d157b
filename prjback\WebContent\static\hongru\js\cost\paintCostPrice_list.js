
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#yearMonth', //指定元素
        btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
    });

    //类别选择
    form.on('select(outPaintNameFun)', function(data){
        listOutPaintName();
    });

    //获取列表
    function listOutPaintName(){
        var outPaintName = $("#outPaintName option:checked").val();
        var index = layer.load(2,{
            shade:[0.1,'#fff']
        });
        $.ajax({
            url : baselocation+'/costPrice/listByFullPaintNameList/json',
            type : 'post',
            data : {"outPaintName":outPaintName},
            success : function(result) {
                layer.closeAll();
                var html ='<option value="">全部</option>';
                if(result.code == 1){
                    var fullPaintNameList = result.data;
                    if(fullPaintNameList != null && fullPaintNameList.length>0){
                        for(var i=0;i<fullPaintNameList.length;i++) {
                            var fullPaintName = fullPaintNameList[i];
                            html+='<option value="'+fullPaintName+'">'+fullPaintName+'</option>';
                        }
                    }
                }else{
                    layer.alert(result.message);
                }
                $("#producer").html(html);
                form.render();//重新渲染
            }
        });
    }


    //执行一个 table 实例
    var url = baselocation+'/costPrice/paintCostPrice/listPage';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '油漆单价列表'
        ,page: true //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'orderNo', title: '',width:50,fixed: 'left',type:"numbers",align:'center'}
            ,{field: 'yearMonth',title: '年月',align:'center', width:150}
            ,{field: 'producer',title: '供应商',align:'center', width:150}
            ,{field: 'outPaintName',title: '供应商油漆名',align:'center', width:150}
            ,{field: 'paintPrice',title: '购入单价',align:'center', width:150}
            ,{field: 'inventoryPrice',title: '存货单价',align:'center', width:150}
            ,{title: '操作',minWidth:150, align:'left',fixed: 'right', toolbar: '#barDemo',width: 150}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                layer_show('添加', baselocation+"/costPrice/paintCostPrice/add/view", 750, document.body.clientHeight-100)
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
			case 'toExport':
				var yearMonth = $("#yearMonth").val();
				var outPaintName = $("#outPaintName").val();
				var producer = $("#producer").val();
				$("#yearMonthForExcell").val(yearMonth);
				$("#outPaintNameForExcell").val(outPaintName);
				$("#producerForExcell").val(producer);
				$("#formExcell").submit();
			break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值
        if(layEvent === 'edit'){
            layer_show('编辑',baselocation+"/costPrice/paintCostPrice/modify/view?costId="+data.costId,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'remove'){
            layer.confirm('确认要删除吗？', {
                btn : [ '确定', '取消' ] //按钮
            }, function() {
                $.ajax({
                    type : 'post',
                    dataType : 'json',
                    data: {"costId":data.costId,"state":9},
                    url : baselocation + '/costPrice/paintCostPrice/remove',
                    success : function(result) {
                        if (result.code == 1) {
                            layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {
                                search();
                                layer.closeAll();
                            });
                        } else {
                            layer.alert(result.message, {
                                icon : 2
                            });
                        }
                    }
                })
            });
        }
    });
});

function search() {
    debugger;
    var arr = new Array();
    $("input:checkbox[name='processIds']:checked").each(function(i){
        arr[i] = $(this).val();
    });
    $("#processIdStr").val(arr.join(","));
    $("#isSearch").val(1);
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    //执行重载
    layui.table.reload('demo', {
        page: {
            curr: 1 //重新从第 1 页开始${ctx}
        }
        ,where: temp
    }, 'data');
}

function toExport(){
	var yearMonth = $("#yearMonth").val();
	var outPaintName = $("#outPaintName").val();
	var producer = $("#producer").val();
	
	$("#yearMonthForExcell").val(yearMonth);
	$("#outPaintNameForExcell").val(outPaintName);
	$("#producerForExcell").val(producer);
	$("#formExcell").submit();
}
