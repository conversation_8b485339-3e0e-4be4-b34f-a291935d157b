package com.hongru.controller.cost;

import com.hongru.base.BaseController;
import com.hongru.common.poiExcelExport.bean.ImportBean;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.*;
import com.hongru.common.util.poiExcelExport.ExcelExp;
import com.hongru.common.util.poiExcelExport.ExcelExportUtil;
import com.hongru.common.util.poiExcelExport.ServletUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.cost.*;
import com.hongru.entity.stat.BoundQuantity;
import com.hongru.entity.stat.UnitPriceRecord;
import com.hongru.entity.yearRevise.SummaryOfDepartmentalUnitPricesBean;
import com.hongru.pojo.dto.*;
import com.hongru.service.admin.IUserService;
import com.hongru.service.cost.ICostService;
import com.hongru.service.stat.IStatService;
import com.hongru.support.page.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping(value = "/costPrice")
public class CostPriceController extends BaseController {
    @Autowired
    private ICostService costService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IStatService statService;

    /*=================================铜加工費用明细表======================================*/
    /**
     * 铜加工費用列表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/list/view")
    public String costPriceListView(Model model) throws Exception{
        return "/modules/cost/costPrice_list";
    }

    /**
     * 添加铜加工費用页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/add/view")
    public String costPriceAddView(Model model) throws Exception{
        //获取铜供应商列表
        List<CuSupplier> supplierList = costService.listCuSupplier();
        model.addAttribute("supplierList",supplierList);
        return "/modules/cost/costPrice_add";
    }

    /**
     * 添加铜加工費用
     * @param costPrice
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    public Object costPriceAdd(CuPriceCost costPrice) throws Exception{
        try {
            //获取当前登录用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());
            //将用户信息放入铜供应商对象中
            costPrice.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            costPrice.setCreatorName(user.getUserName());
            costPrice.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            costPrice.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
            costPrice.setLastModifierName(user.getUserName());
            costPrice.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            //根据供应商id查询供应商编码和名称
            if (costPrice.getSupplierId() != null){
                CuSupplier cuSupplier = costService.getSupplierById(costPrice.getSupplierId());
                if(cuSupplier != null){
                    costPrice.setSupplierCode(cuSupplier.getSupplierCode());
                    costPrice.setSupplierName(cuSupplier.getSupplierName());
                }
            }
            //设置状态为 0：正常
            costPrice.setState(CuPriceCost.STATE_NORMAL);
            costPrice.setComProcePrice(costPrice.getProcePrice().add(costPrice.getExchangeRate().multiply(costPrice.getAscendingWater())));
            //插入铜供应商表中
            costService.addCuProceCost(costPrice);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("铜加工费用新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 查询铜加工費用
     * @param yearMonth
     * @param lineType
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/listPage")
    @ResponseBody
    public Object costPriceListPage(PageInfo pageInfo,short isSearch,String yearMonth,Integer lineType) throws Exception{
        //新建页面对象
        if(isSearch == 1){
            //根据条件过滤查询铜供应商列表
            CuPriceCostDTO cuPriceCostDTO = costService.listCostPricePage(yearMonth,lineType,pageInfo);
            return new HrPageResult(cuPriceCostDTO.getCuPriceCostList(), cuPriceCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<CuPriceCost>(), 0);
        }
    }

    /**
     * 查询铜加工費用
     * @param yearMonth
     * @param lineType
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
	@PostMapping("/listPageV2")
    @ResponseBody
    public Object costPriceList(short isSearch,String yearMonth,Integer lineType) throws Exception{
        HashMap hashMap = new HashMap();
        BigDecimal avg1 = new BigDecimal(0);
        BigDecimal avg2 = new BigDecimal(0);
        //新建页面对象
        if(isSearch == 1){
            //根据条件过滤查询铜供应商列表
            List<CuPriceCost> cuPriceCostList = costService.listCostPrice(yearMonth,lineType);
            BigDecimal purchasNum1 = new BigDecimal(0);
            BigDecimal procePrice1 = new BigDecimal(0);
            BigDecimal purchasNum2 = new BigDecimal(0);
            BigDecimal procePrice2 = new BigDecimal(0);
            for(CuPriceCost cu : cuPriceCostList){
                //圆线
                if(cu.getLineType() == 1){
                    procePrice1 = procePrice1.add(cu.getComProcePrice().multiply(cu.getPurchasNum()));
                    purchasNum1 = purchasNum1.add(cu.getPurchasNum());
                }
                //平角线（排除掉'加工费(不含税)'为0的数据）
                if(cu.getLineType() == 2){
                    if(cu.getProcePrice() != null && cu.getProcePrice().compareTo(BigDecimal.ZERO) == 1){
                        procePrice2 = procePrice2.add(cu.getComProcePrice().multiply(cu.getPurchasNum()));
                        purchasNum2 = purchasNum2.add(cu.getPurchasNum());
                    }
                }
            }
            if(procePrice1.compareTo(BigDecimal.ZERO) == 1 && purchasNum1.compareTo(BigDecimal.ZERO) == 1){
                avg1 = procePrice1.divide(purchasNum1.multiply(new BigDecimal(1000)),3, RoundingMode.HALF_UP);

            }
            if(procePrice2.compareTo(BigDecimal.ZERO) == 1 && purchasNum2.compareTo(BigDecimal.ZERO) == 1){
                avg2 = procePrice2.divide(purchasNum2.multiply(new BigDecimal(1000)),3, RoundingMode.HALF_UP);

            }
        }
        hashMap.put("avg1",avg1);
        hashMap.put("avg2",avg2);
        return new HrResult(CommonReturnCode.SUCCESS,hashMap);
    }

    /**
     * 修改铜加工費用页面
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/edit/view")
    public String costPriceEditView(Model model,Integer costId) throws Exception{
        //根据流水号查询铜供应商
        CuPriceCost cuPriceCost = costService.getCuProceCostById(costId);
        model.addAttribute("costPrice",cuPriceCost);
        //获取所有的铜供应商
        List<CuSupplier> supplierList = costService.listCuSupplier();
        model.addAttribute("supplierList",supplierList);
        return "/modules/cost/costPrice_modify";
    }

    /**
     * 修改铜加工費用
     * @param costPrice
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/edit")
    @ResponseBody
    public Object costPriceEdit(CuPriceCost costPrice) throws Exception{
        try {
            //获取当前登录对象
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id查询用户信息
            User user = userService.getById(authorizingUser.getUserId());
            //修改表中‘最后修改人id name time’字段
            costPrice.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
            costPrice.setLastModifierName(user.getUserName());
            costPrice.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            costService.modifyCuPriceCost(costPrice);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("铜加工费用修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除铜加工費用
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/remove")
    @ResponseBody
    public Object costPriceRemove(Integer costId) throws Exception{
        try {
            //根据流水号删除铜供应商 状态9：删除
            costService.removeCuPriceCost(costId,CuPriceCost.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("铜加工费用修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================电费用表============================================*/
    /**
     * 电费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/electric/list/view")
    public String costElecPriceListView(Model model) throws Exception{
        return "/modules/cost/electricCostPrice_list";
    }

    /**
     * 根据条件获取电费用表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/electric/listPage")
    @ResponseBody
    public Object electricCostPriceListPage(short isSearch,String yearMonth,PageInfo pageInfo,Short types) throws Exception{
        if(isSearch == 1){
            //根据条件过滤获取列表信息
            ElectricPriceCostDTO electricPriceCostDTO = costService.listElectricPriceCostPage(yearMonth,pageInfo);
            return new HrPageResult(electricPriceCostDTO.getElectricPriceCostList(), electricPriceCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<ElectricPriceCost>(), 0);
        }
    }

    /**
     * 添加电费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/electric/add/view")
    public String electircCostPriceAddView(Model model) throws Exception{
        return "/modules/cost/electricCostPrice_add";
    }

    /**
     * 添加电费用表
     * @param electricPriceCost
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/electric/add")
    @ResponseBody
    public Object electricCostPriceAdd(ElectricPriceCost electricPriceCost) throws Exception{
        try {
            //获取当前用户信息
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id获取用户信息
            User user = userService.getById(authorizingUser.getUserId());
            electricPriceCost.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            electricPriceCost.setCreatorName(user.getUserName());
            electricPriceCost.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
//            //查询是否已有重复的数据
//            ElectricPriceCost isExsit =  costService.getElectricPriceCostByYearMonth(electricPriceCost.getYearMonth(),electricPriceCost.getCalculateTag());
//            if(isExsit != null){
//                return new HrResult(CommonReturnCode.FAILED.getCode(),"已有相同数据，请检查!");
//            }
            //设置状态0：正常
            electricPriceCost.setState(CuPriceCost.STATE_NORMAL);
            //插入数据
            costService.addElectricPriceCost(electricPriceCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("电费新增新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除电费用表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/electric/remove")
    @ResponseBody
    public Object electricCostPriceRemove(Integer costId) throws Exception{
        try {
            //根据流水号删除 状态9：删除
            costService.removeElectricCostPrice(costId,ElectricPriceCost.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 修改电费用表页面
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/electric/edit/view")
    public String electricCostPriceEditView(Model model,Integer costId) throws Exception{
        ElectricPriceCost electricPriceCost = costService.getElectricPriceCostById(costId);
        model.addAttribute("electricPriceCost",electricPriceCost);
        return "/modules/cost/electricCostPrice_modify";
    }

    /**
     * 修改电费用表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/electric/edit")
    @ResponseBody
    public Object electricCostPriceEdit(ElectricPriceCost electricPriceCost) throws Exception{
        try {
            //获取当前登录对象
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id查询用户信息
            User user = userService.getById(authorizingUser.getUserId());
            //修改表中‘最后修改人id name time’字段
            electricPriceCost.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
            electricPriceCost.setLastModifierName(user.getUserName());
            electricPriceCost.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            costService.modifyElectricPriceCost(electricPriceCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("铜加工费用修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }


    /*=================================水费用表======================================*/
    /**
     * 水费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/water/list/view")
    public String waterCostPriceListView(Model model) throws Exception{
        return "/modules/cost/waterCostPrice_list";
    }

    /**
     * 水费用表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/water/listPage")
    @ResponseBody
    public Object waterCostPriceListPage(short isSearch,String yearMonth,PageInfo pageInfo,Short types) throws Exception{
        if(isSearch == 1){
            WaterPriceCostDTO waterPriceCostDTO = costService.listWaterPriceCostPage(yearMonth,pageInfo);
            return new HrPageResult(waterPriceCostDTO.getWaterPriceCostList(), waterPriceCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<WaterPriceCost>(), 0);
        }
    }

    /**
     * 导入水费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/water/add/view")
    public String waterCostPriceAddView(Model model) throws Exception{
        return "/modules/cost/waterCostPrice_add";
    }

    /**
     * 导入水费用表
     * @param waterPriceCost
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/water/add")
    @ResponseBody
    public Object waterCostPriceAdd(WaterPriceCost waterPriceCost) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            waterPriceCost.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            waterPriceCost.setCreatorName(user.getUserName());
            waterPriceCost.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
//            //查询是否已有重复的数据
//            WaterPriceCost isExsit =  costService.getWaterPriceCostByYearMonth(waterPriceCost.getYearMonth());
//            if(isExsit != null){
//                return new HrResult(CommonReturnCode.FAILED.getCode(),"已有相同数据，请检查!");
//            }
            waterPriceCost.setState(CuPriceCost.STATE_NORMAL);
            costService.addWaterPriceCost(waterPriceCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("水费新增新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除水费用表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/water/remove")
    @ResponseBody
    public Object waterCostPriceRemove(Integer costId) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            costService.removeWaterCostPrice(costId,GasPriceCost.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 修改水费用表页面
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/water/edit/view")
    public String waterCostPriceEditView(Model model,Integer costId) throws Exception{
        WaterPriceCost waterPriceCost = costService.getWaterPriceCostById(costId);
        model.addAttribute("waterPriceCost",waterPriceCost);
        return "/modules/cost/waterCostPrice_modify";
    }

    /**
     * 修改水费用表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/water/edit")
    @ResponseBody
    public Object waterCostPriceEdit(WaterPriceCost waterPriceCost) throws Exception{
        try {
            //获取当前登录对象
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id查询用户信息
            User user = userService.getById(authorizingUser.getUserId());
            //修改表中‘最后修改人id name time’字段
            waterPriceCost.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
            waterPriceCost.setLastModifierName(user.getUserName());
            waterPriceCost.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            costService.modifyWaterPriceCost(waterPriceCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("铜加工费用修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================电力使用明细表======================================*/
    /**
     * 电力使用明细表列表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/electric/detail/list/view")
    public String electricDetailCostPriceAddView(Model model) throws Exception{
        return "/modules/cost/electricDetailCostPrice_list";
    }

    /**
     * 根据条件过滤电力使用明细表页面
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/electric/detail/listPage")
    @ResponseBody
    public Object electricDetailListPage(short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //根据条件过滤获取电力使用信息
            ElectricPriceCostDetailDTO electricPriceCostDetailDTO = costService.listElectricPriceCostDetailPage(yearMonth,pageInfo);
            return new HrPageResult(electricPriceCostDetailDTO.getElectricPriceCostDetailList(), electricPriceCostDetailDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<ElectricPriceCostDetail>(), 0);
        }
    }

    /**
     * 导入电力使用明细表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/electric/detail/import/view")
    public String elecDetailCostPriceDetailImportView(Model model) throws Exception{
        return "/modules/cost/electricDetail_import";
    }

    /**
     * 导入电力使用明细表
     * @param filename
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/electric/detail/import")
    @ResponseBody
    public Object electricDetailImport(MultipartFile filename, String dateStr){
        int importId = 0;
        try {
            //获取当前登录用户
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }
            //根据年月获取电力使用信息
//            List<ElectricPriceCostDetail> electricPriceCostDetailList = costService.listElectricPriceCostDetailByYearMonth(dateStr);
//            if(electricPriceCostDetailList != null  && electricPriceCostDetailList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"电费明细已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<ElectricPriceCostDetail> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                ElectricPriceCostDetail detail= new ElectricPriceCostDetail();
                String departmentCode = null;
                String eleCost = null;
                if (i <= 1) {//去掉第一行 第二行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    departmentCode = strArr.get(0).toString();//部门编号
                }
                if (strArr.size() > 1) {
                    eleCost = strArr.get(1).toString();//用电量
                }
                //判断部门列表是否为空
                if(StringUtil.isStringEmpty(departmentCode)){
                    continue;
                }
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setDepartment(departmentCode);
                if(!StringUtil.isStringEmpty(eleCost)){
                    detail.setEleCost(new BigDecimal(eleCost).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                    detail.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setLastModifierName(user.getUserName());
                    detail.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if(detailList == null || detailList.size() == 0){
                throw new Exception();
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addElectricDetailImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 删除电力使用明细表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/electric/detail/remove")
    @ResponseBody
    public Object electricDetailCostPriceRemove(Integer costId) throws Exception{
        try {
            costService.removeDetailCostPriceByCostId(costId);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 批量删除电力使用明细表
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/electric/detail/delBatch")
    @ResponseBody
    public Object electricDetailCostPriceDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeDetailCostPriceByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================燃气费用表======================================*/
    /**
     * 燃气费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/gas/list/view")
    public String gasCostPriceListView(Model model) throws Exception{
        return "/modules/cost/gasCostPrice_list";
    }

    /**
     * 根据顾虑条件获取燃气费用表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/gas/listPage")
    @ResponseBody
    public Object gasCostPriceListPage(short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            GasPriceCostDTO gasPriceCostDTO = costService.listGasPriceCostPage(yearMonth,pageInfo);
            return new HrPageResult(gasPriceCostDTO.getGasPriceCostList(), gasPriceCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<GasPriceCost>(), 0);
        }
    }

    /**
     * 添加获取燃气费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/gas/add/view")
    public String gasCostPriceAddView(Model model) throws Exception{
        List<PaintUseCode> departmentList = costService.listPowerDepartment();
        model.addAttribute("departmentList",departmentList);
        return "/modules/cost/gasCostPrice_add";
    }

    /**
     * 添加获取燃气费用表
     * @param gasPriceCost
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/gas/add")
    @ResponseBody
    public Object gasCostPriceAdd(GasPriceCost gasPriceCost) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            gasPriceCost.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            gasPriceCost.setCreatorName(user.getUserName());
            gasPriceCost.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
//            //查询是否已有重复的数据
//            GasPriceCost isExsit =  costService.getGasPriceCostByYearMonth(gasPriceCost.getYearMonth(),gasPriceCost.getDepartment());
//            if(isExsit != null){
//                return new HrResult(CommonReturnCode.FAILED.getCode(),"已有相同数据，请检查!");
//            }
            gasPriceCost.setState(GasPriceCost.STATE_NORMAL);
            costService.addGasPriceCost(gasPriceCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("燃气费新增新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除燃气费用表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/gas/remove")
    @ResponseBody
    public Object gasCostPriceRemove(Integer costId) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            costService.removeGasCostPrice(costId,GasPriceCost.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 修改燃气费用表页面
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/gas/edit/view")
    public String gasCostPriceEditView(Model model,Integer costId) throws Exception{
        List<PaintUseCode> departmentList = costService.listPowerDepartment();
        model.addAttribute("departmentList",departmentList);
        GasPriceCost gasPriceCost = costService.getGasPriceCostById(costId);
        model.addAttribute("gasPriceCost",gasPriceCost);
        return "/modules/cost/gasCostPrice_modify";
    }

    /**
     * 修改燃气费用表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/gas/edit")
    @ResponseBody
    public Object gasCostPriceEdit(GasPriceCost gasPriceCost) throws Exception{
        try {
            //获取当前登录对象
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id查询用户信息
            User user = userService.getById(authorizingUser.getUserId());
            //修改表中‘最后修改人id name time’字段
            gasPriceCost.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
            gasPriceCost.setLastModifierName(user.getUserName());
            gasPriceCost.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            costService.modifyGasPriceCost(gasPriceCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("铜加工费用修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================人件部门表======================================*/
    /**
     * 人件部门表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @GetMapping("/humanCost/list/view")
    public String humanDepartmentCostListView(Model model) throws Exception{
        return "/modules/cost/humanDepartmentCost_list";
    }

    /**
     * 人件部门表列表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanCost/listPage")
    @ResponseBody
    public Object humanDepartmentListPage(short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            HumanDepartmentCostDTO humanDepartmentCostDTO = costService.listHumanDepartmentCostPage(yearMonth,pageInfo);
            return new HrPageResult(humanDepartmentCostDTO.getHumanDepartmentCostList(), humanDepartmentCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<HumanDepartmentCost>(), 0);
        }
    }

    /**
     * 新增人件部门表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @GetMapping("/humanCost/add/view")
    public String humanDepartmentAddView(Model model) throws Exception{
        return "/modules/cost/humanDepartmentCost_add";
    }

    /**
     * 新增人件部门表
     * @param humanDepartmentCost
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanCost/add")
    @ResponseBody
    public Object humanDepartmentAdd(HumanDepartmentCost humanDepartmentCost) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            humanDepartmentCost.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            humanDepartmentCost.setCreatorName(user.getUserName());
            humanDepartmentCost.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
//            //查询是否已有重复的数据
//            HumanDepartmentCost isExsit =  costService.getHumanDepartmentCostByYearMonth(humanDepartmentCost.getYearMonth());
//            if(isExsit != null){
//                return new HrResult(CommonReturnCode.FAILED.getCode(),"已有相同数据，请检查!");
//            }
            humanDepartmentCost.setState(HumanDepartmentCost.STATE_NORMAL);
            costService.addHumanDepartmentCost(humanDepartmentCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("人件部门新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除人件部门表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanCost/remove")
    @ResponseBody
    public Object humanDepartmentRemove(Integer costId) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            costService.removeHumanDepartmentCost(costId,HumanDepartmentCost.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================人件工时登录======================================*/
    /**
     * 人件工时表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @GetMapping("/humanWorkTime/list/view")
    public String humanDepartmentHourListView(Model model) throws Exception{
        return "/modules/cost/humanDepartmentWorkTime_list";
    }

    /**
     * 人件工时表列表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanWorkTime/listPage")
    @ResponseBody
    public Object humanWorkTimeListPage(short isSearch,String yearMonth, String workType, PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            HumanHourCostDTO humanHourCostDTO = costService.listHumanHourCostPage(yearMonth,workType,pageInfo);
            return new HrPageResult(humanHourCostDTO.getHumanHourCostList(), humanHourCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<HumanHourCost>(), 0);
        }
    }

    /**
     * 导入人件工时表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @GetMapping("/humanWorkTime/detail/import/view")
    public String humanWorkTimeDetailImportView(Model model) throws Exception{
        return "/modules/cost/humanDepartmentWorkTime_import";
    }

    /**
     * 导入人件工时表
     * @param filename
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/human/workTime/import")
    @ResponseBody
    public Object humanWorkTimeDetailImport(MultipartFile filename, String dateStr){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

//            List<HumanHourCost> humanHourCostList = costService.listHumanHourCostByYearMonth(dateStr);
//            if(humanHourCostList !=null && humanHourCostList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"人件工时明细已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<HumanHourCost> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                if (i < 1) {//去掉第一行
                    continue;
                }
                HumanHourCost detail= new HumanHourCost();
                String workTypeStr = null;
                String departmentCodeStr = null;
                String peopleNumStr = null;
                String workTimeStr = null;
                String workClassStr = null;
                strArr = list.get(i);
                errMsg = "序号：" + (i-1) + ",";
                if (strArr.size() > 0) {
                    departmentCodeStr = strArr.get(0).toString();//部门编号
                    //判断部门编号是否为空
                    if(StringUtil.isStringEmpty(departmentCodeStr)){
                        continue;
                    }
                }
                if (strArr.size() > 1) {
                    workClassStr = strArr.get(1).toString();//性质
                }
                if (strArr.size() > 2) {
                    workTypeStr = strArr.get(2).toString();//工种
                }
                if (strArr.size() > 3) {
                    peopleNumStr = strArr.get(3).toString();//人数
                }
                if (strArr.size() > 4) {
                    workTimeStr = strArr.get(4).toString();//MH工时
                }

                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setDepartmentCode(departmentCodeStr);
                detail.setWorkType(workTypeStr);
                //判断人数是否为空
                if(!StringUtil.isStringEmpty(peopleNumStr)){
                    detail.setHumanNumber(Double.valueOf(peopleNumStr).intValue());
                }
                detail.setWorkClass(workClassStr);
                //判断工时是否为空
                if(!StringUtil.isStringEmpty(workTimeStr)){
                    detail.setWorkHour(new BigDecimal(workTimeStr).setScale(3,BigDecimal.ROUND_HALF_UP));
                }
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                    detail.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setLastModifierName(user.getUserName());
                    detail.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if(detailList == null || detailList.size() == 0){
                throw new Exception();
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addHumanHourDetailImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 删除人件工时表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanWorkTime/remove")
    @ResponseBody
    public Object humanWorkTimeRemove(Integer costId) throws Exception{
        try {
            costService.removeHumanHourCost(costId,HumanHourCost.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 批量删除人件工时表
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanWorkTime/delBatch")
    @ResponseBody
    public Object humanWorkTimeDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeHumanHourCost(Integer.valueOf(costId),HumanHourCost.STATE_DELETED);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 修改页面
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/humanWorkTime/edit/view")
    public String humanWorkTimeEditView(Model model,Integer costId) throws Exception{
        //根据流水号查询人件工时的实体类
        HumanHourCost humanHourCost = costService.getHumanHourCostById(costId);
        model.addAttribute("humanHourCost",humanHourCost);
        return "/modules/cost/humanDepartmentWorkTime_modify";
    }

    /**
     * 修改
     * @param humanHourCost
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/humanWorkTime/edit")
    @ResponseBody
    public Object humanWorkTimeEdit(HumanHourCost humanHourCost) throws Exception{
        try {
            //获取当前登录对象
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            //根据用户id查询用户信息
            User user = userService.getById(authorizingUser.getUserId());
            //修改表中‘最后修改人id name time’字段
            humanHourCost.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
            humanHourCost.setLastModifierName(user.getUserName());
            humanHourCost.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            costService.modifyHumanHourCost(humanHourCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("铜加工费用修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================人件费用登录-导入======================================*/
    /**
     * 人件工时表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @GetMapping("/humanWorkPrice/list/view")
    public String humanDepartmentPriceListView(Model model) throws Exception{
        return "/modules/cost/humanDepartmentWorkPrice_list";
    }

    /**
     * 人件费表列表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanWorkPrice/listPage")
    @ResponseBody
    public Object humanWorkPriceListPage(short isSearch,String yearMonth, PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            HumanPriceCostDTO humanPriceCostDTO = costService.listHumanPriceCostPage(yearMonth,pageInfo);
            return new HrPageResult(humanPriceCostDTO.getHumanPriceCostList(), humanPriceCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<HumanPriceCost>(), 0);
        }
    }

    /**
     * 导入人件费表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @GetMapping("/humanWorkPrice/detail/import/view")
    public String humanWorkPriceDetailImportView(Model model) throws Exception{
        return "/modules/cost/humanDepartmentWorkPrice_import";
    }

    /**
     * 导入人件费表
     * @param filename
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/human/workPrice/import")
    @ResponseBody
    public Object humanWorkPriceDetailImport(MultipartFile filename, String dateStr){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

//            HumanPriceCost humanHourCostList = costService.getHumanPriceCostByYearMonth(dateStr);
//            if(humanHourCostList !=null && humanHourCostList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"人件费明细已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<HumanPriceCost> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                if (i < 1) {//去掉第一行
                    continue;
                }
                HumanPriceCost detail= new HumanPriceCost();
                String workTypeStr = null;
                String humanPriceCode = null;
                String humanPrice = null;

                strArr = list.get(i);
                errMsg = "序号：" + (i-1) + ",";
                if (strArr.size() > 0) {
                    humanPriceCode = strArr.get(0).toString();//人件费区分
                    //判断人件费区分是否为空
                    if(StringUtil.isStringEmpty(humanPriceCode)){
                        continue;
                    }
                }
                if (strArr.size() > 1) {
                    humanPrice = strArr.get(1).toString();//人件费
                }
                if (strArr.size() > 2) {
                    workTypeStr = strArr.get(2).toString();//工种
                }

                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setHumanPriceCode(humanPriceCode);
                detail.setWorkType(workTypeStr);
                //判断金额是否为0
                if(!StringUtil.isStringEmpty(humanPrice)){
                    detail.setHumanPrice(new BigDecimal(humanPrice).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                    detail.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setLastModifierName(user.getUserName());
                    detail.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if(detailList == null || detailList.size() == 0){
                throw new Exception();
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addHumanPriceDetailImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 删除人件费表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanWorkPrice/remove")
    @ResponseBody
    public Object humanWorkPriceRemove(Integer costId) throws Exception{
        try {
            costService.removeHumanPriceCost(costId,HumanPriceCost.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 批量删除人件费表
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/7 17:43
     * @return
     */
    @PostMapping("/humanWorkPrice/delBatch")
    @ResponseBody
    public Object humanWorkPriceDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeHumanPriceCost(Integer.valueOf(costId),HumanPriceCost.STATE_DELETED);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================油漆单价登录======================================*/
    /**
     * 油漆单价表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/paintCostPrice/list/view")
    public String paintPriceListView(Model model,String producer) throws Exception{
        //获取油漆品种表的油漆全名（供应商油漆名）集合
        List<PaintVariety> fullPaintNameList = costService.listFullPaintNamesV2(null);
        model.addAttribute("fullPaintNameList",fullPaintNameList);
        return "/modules/cost/paintCostPrice_list";
    }

    /**
     * 根据类别获取生产商
     * @param
     * @throws
     * <AUTHOR>
     * @create 2023/6/16 20:36
     * @return java.lang.Object
     */
    @PostMapping(value = "/listByFullPaintNameList/json")
    @ResponseBody
    public Object listByFullPaintNameList(String outPaintName) throws Exception {
        try{
            //获取油漆品种表的油漆全名（供应商油漆名）集合
            List<String> fullPaintNameList = costService.getProducerByFullPaintName(outPaintName);
            return new HrResult(CommonReturnCode.SUCCESS,fullPaintNameList);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
        }
    }

    /**
     * 编辑油漆单价表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/paintCostPrice/modify/view")
    public Object paintCostPriceEdit(Model model,int costId) throws Exception{
        PaintPrice paintPrice = costService.getPaintPriceById(costId);
        model.addAttribute("paintPrice",paintPrice);
        //获取油漆品种表的油漆全名（供应商油漆名）集合
        List<PaintVariety> fullPaintNameList = costService.listFullPaintNamesV2(null);
        model.addAttribute("fullPaintNameList",fullPaintNameList);
        return "/modules/cost/paintCostPrice_edit";
    }

    /**
     * 油漆单价表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/paintCostPrice/listPage")
    @ResponseBody
    public Object paintCostPriceListPage(short isSearch,String yearMonth, String producer, String outPaintName, String paintPrice,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            PaintPriceDTO paintPriceDTO = costService.listPaintPricePage(yearMonth,producer,outPaintName ,paintPrice,pageInfo);
            return new HrPageResult(paintPriceDTO.getPaintPriceList(), paintPriceDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<PaintPrice>(), 0);
        }
    }

    /**
     * 新增油漆单价表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/paintCostPrice/add/view")
    public String paintCostPriceAddView(Model model) throws Exception{
        //获取油漆品种表的油漆全名（供应商油漆名）集合
        List<PaintVariety> fullPaintNameList = costService.listFullPaintNamesV2(null);
        model.addAttribute("fullPaintNameList",fullPaintNameList);
        return "/modules/cost/paintCostPrice_add";
    }

    /**
     * 新增油漆单价表
     * @param paintPrice
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/paintCostPrice/add")
    @ResponseBody
    public Object paintCostPriceAdd(PaintPrice paintPrice) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            paintPrice.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            paintPrice.setCreatorName(user.getUserName());
            paintPrice.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            paintPrice.setInventoryPrice(BigDecimal.ZERO);
            paintPrice.setState(PaintPrice.STATE_NORMAL);
            costService.addPaintPrice(paintPrice);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("油漆单价新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除油漆单价表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/paintCostPrice/remove")
    @ResponseBody
    public Object paintCostPriceRemove(Integer costId) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            costService.removePaintPrice(costId,PaintPrice.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑油漆单价表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/paintCostPrice/edit")
    @ResponseBody
    public Object paintCostPriceEdit(String yearMonth,String producer,String outPaintName,String paintPrice, String inventoryPrice, Integer costId) throws Exception{
        try{
            PaintPrice paintPriceBean = new PaintPrice();
            //拆分年月存入字段
            if(StringUtils.isNotEmpty(yearMonth)){
                String[] yearAndMonth = yearMonth.split("-");
                paintPriceBean.setYearMonth(yearMonth);
                paintPriceBean.setYear(Integer.valueOf(yearAndMonth[0]));
                paintPriceBean.setMonth(Integer.valueOf(yearAndMonth[1]));
            }
            paintPriceBean.setProducer(producer);
            paintPriceBean.setOutPaintName(outPaintName);
            paintPriceBean.setCostId(costId);
//            //根据coseId获取油漆单价表 ，然后判断油漆单价是否有变动，如果有变动，就去更新油漆使用明细表中相关数据
//            PaintPrice paintPriceOld = costService.getPaintPriceById(costId);
           // 定义变量变更价格
            BigDecimal changePrice = new BigDecimal(0);
            // 变更前的存货单价与变更后的存货单价不相等的场合
			if (StringUtils.isNotEmpty(inventoryPrice) && new BigDecimal(inventoryPrice).compareTo(BigDecimal.ZERO)  > 0) {
            	changePrice=new BigDecimal(inventoryPrice);
            // 购入单价为空的场合取购入单价
            }else {
            	changePrice=new BigDecimal(paintPrice);
            }
			// 购入单价或存货单价有变动的场合、根据‘年月’‘供应商油漆名’去更改数据
			// 需要根据‘年月’‘油漆全名’去获取所有复合条件的集合
			List<PaintPriceCost> paintPriceCostList = costService.getPaintPriceCostByParms(yearMonth, outPaintName);
			if (paintPriceCostList != null && paintPriceCostList.size() > 0) {
				for (PaintPriceCost pp : paintPriceCostList) {
					// 获取当前[油漆使用明细表].[油漆使用量]，再乘以输入的新[单价]，获取到新的 [油漆使用金额]
					if (pp.getPaintUsage() != null && pp.getPaintUsage().compareTo(BigDecimal.ZERO) > 0) {
						pp.setPaintUsageAmount(changePrice.multiply(pp.getPaintUsage()).setScale(2, BigDecimal.ROUND_HALF_UP));
						// 根据主键id去更新[油漆使用金额]
						costService.editPaintPriceCost(pp);
					}
				}
			}

            // 购入单价
            paintPriceBean.setPaintPrice(new BigDecimal(paintPrice));
            // 存货单价
            paintPriceBean.setInventoryPrice(new BigDecimal(inventoryPrice));
            costService.modifyPaintCostPrice(paintPriceBean);
            return new HrResult(CommonReturnCode.SUCCESS);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
        }
    }
    
    /**
     * 油漆单价表导出
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/12/22 13:43
     * @return
     */
    @PostMapping("/paintCostPrice/export")
    @ResponseBody
    public Object paintPriceExport(String yearMonthForExcell, String outPaintNameForExcell, String producerForExcell, HttpServletRequest req, HttpServletResponse resp) throws Exception{
        try {
        	List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
            //拼接需要导出的数据
            List<String[]> dataset = new ArrayList<String[]>();
        	List<PaintPrice> paintPriceList = costService.listPaintPrice(yearMonthForExcell, outPaintNameForExcell, producerForExcell);
        	
        	for(PaintPrice paintPrice:paintPriceList){
        		  String[] arr = new String[5];
        		  arr[0] = paintPrice.getYearMonth();
        		  arr[1] = paintPrice.getProducer();
        		  arr[2] = paintPrice.getOutPaintName();
        		  arr[3] = paintPrice.getPaintPrice().toString();
        		  if(paintPrice.getInventoryPrice() != null) {
        			  arr[4] = paintPrice.getInventoryPrice().toString();
        		  }else {
        			  arr[4] ="0";
        		  }
        			  
        		  dataset.add(arr);
        	}
        	 //表头
            String[] handers = new String[5];
            handers[0] = "年月";
            handers[1] = "供应商";
            handers[2] = "供应商油漆名";
            handers[3] = "购入单价";
            handers[4] = "存货单价";

            //设置数字列
            Integer[] numericColumnArr = new Integer[0];
            //对象
            ExcelExp e = new ExcelExp("油漆单价明细", handers, dataset,numericColumnArr);
            mysheet.add(e);
            String fileName = "油漆单价明细_"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),  "yyyyMMddHHmmss");
            ServletUtil su = new ServletUtil(fileName, req, resp);
            su.poiExcelServlet();
            ExcelExportUtil.exportManySheetExcel(su.getOut(), mysheet); //生成sheet
        }catch (Exception e){
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================油漆费用登录======================================*/
    /**
     * 油漆费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/paintCost/list/view")
    public String paintCostListView(Model model) throws Exception{
        //获取油漆品种表的油漆名称集合
        List<String> insidePaintNameList = costService.listInsidePaintNames();
        model.addAttribute("insidePaintNameList",insidePaintNameList);
        return "/modules/cost/paintCost_list";
    }

    /**
     * 油漆费用表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/paintCost/listPage")
    @ResponseBody
    public Object paintCostListPage(short isSearch,String yearMonth, String insidePaintName, String paintUseCode,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            PaintPriceCostDTO paintPriceCostDTO = costService.listPaintPriceCostPage(yearMonth,insidePaintName,paintUseCode,pageInfo);
            return new HrPageResult(paintPriceCostDTO.getPaintPriceCostList(), paintPriceCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<PaintPriceCost>(), 0);
        }
    }

    /**
     * 导入油漆费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/paintCost/import/view")
    public String paintCostDetailImportView(Model model) throws Exception{
        return "/modules/cost/paintCost_import";
    }

    /**
     * 导入油漆费用表
     * @param filename
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/paintCost/import")
    @ResponseBody
    public Object paintCostDetailImport(MultipartFile filename, String dateStr){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<PaintPriceCost> detailList = new ArrayList<>();//添加数据集合
            int stopNum = list.size();
            for (int i = 0; i < list.size(); i++) {
                //PaintPriceCost detail= new PaintPriceCost();
                String paintNameStr = null;
                Map<String,String> useCodeMap = new HashMap<>();

                if (i <= 14 || i >= stopNum) {//去掉前面多余的无用行 第15开始是数据
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i - 14) + ",";
                if (strArr.size() > 0) {
                    paintNameStr = strArr.get(0).toString();//油漆名称(社内油漆名)
                }
                if (strArr.size() > 22 && (!"0".equals( strArr.get(22).toString())) && (!"0.0".equals( strArr.get(22).toString())) && (!"0.000".equals( strArr.get(22).toString()))) {
                    useCodeMap.put("EM", strArr.get(22).toString());//使用区分(小分类部门EM)
                }
                if (strArr.size() > 23 && (!"0".equals( strArr.get(23).toString())) && (!"0.0".equals( strArr.get(23).toString())) && (!"0.000".equals( strArr.get(23).toString()))) {
                    useCodeMap.put("EF", strArr.get(23).toString());//使用区分(小分类部门EF)
                }
                if (strArr.size() > 24 && (!"0".equals( strArr.get(24).toString())) && (!"0.0".equals( strArr.get(24).toString())) && (!"0.000".equals( strArr.get(24).toString()))) {
                    useCodeMap.put("ER01", strArr.get(24).toString());//使用区分(小分类部门ER01)
                }
                if (strArr.size() > 25 && (!"0".equals( strArr.get(25).toString())) && (!"0.0".equals( strArr.get(25).toString())) && (!"0.000".equals( strArr.get(25).toString()))) {
                    useCodeMap.put("ER02", strArr.get(25).toString());//使用区分(小分类部门ER02)
                }
                if (strArr.size() > 26 && (!"0".equals( strArr.get(26).toString())) && (!"0.0".equals( strArr.get(26).toString())) && (!"0.000".equals( strArr.get(26).toString()))) {
                    useCodeMap.put("ER03", strArr.get(26).toString());//使用区分(小分类部门ER03)
                }
                if (strArr.size() > 27 && (!"0".equals( strArr.get(27).toString())) && (!"0.0".equals( strArr.get(27).toString())) && (!"0.000".equals( strArr.get(27).toString()))) {
                    useCodeMap.put("ER04", strArr.get(27).toString());//使用区分(小分类部门ER04)
                }
                if (strArr.size() > 28 && (!"0".equals( strArr.get(28).toString())) && (!"0.0".equals( strArr.get(28).toString())) && (!"0.000".equals( strArr.get(28).toString()))) {
                    useCodeMap.put("ER05", strArr.get(28).toString());//使用区分(小分类部门ER05)
                }
                if (strArr.size() > 29 && (!"0".equals( strArr.get(29).toString())) && (!"0.0".equals( strArr.get(29).toString())) && (!"0.000".equals( strArr.get(29).toString()))) {
                    useCodeMap.put("EF09", strArr.get(29).toString());//使用区分(小分类部门EF09)
                }
                if (strArr.size() > 30 && (!"0".equals( strArr.get(30).toString())) && (!"0.0".equals( strArr.get(30).toString())) && (!"0.000".equals( strArr.get(30).toString()))) {
                    useCodeMap.put("UF", strArr.get(30).toString());//使用区分(小分类部门UF)
                }
                if(StringUtil.isStringEmpty(paintNameStr)){
                    continue;
                }
                //如果第一列 油漆名称是‘合计’，计算当前int i 的值
                if("合计".equals(paintNameStr)){
                    stopNum = i;
                    continue;
                }

                //根据「油漆使用明细表」的「社内油漆名」检索 [油漆品种表]更新「油漆使用明细表」.「供应商油漆名」
                PaintVariety paintVariety = costService.getPaintVarietyByParam(paintNameStr.trim());
                
                //使用区分
                for(Map.Entry<String,String> map :useCodeMap.entrySet()){
                    //新建一个对象 避免第二次对象会覆盖掉第一个对象
                    PaintPriceCost detail= new PaintPriceCost();
                    PaintPrice paintPrice = new PaintPrice();
                    //判断油漆品种表中有无数据
                    if(paintVariety != null ){
                        detail.setOutPaintName(paintVariety.getFullPaintName());
                        //根据「供应商油漆名」查询「油漆价格表」获取「油漆单价」
                        paintPrice = costService.getPaintPriceByParam(paintVariety.getFullPaintName(),dateStr);
                    }else{
                        detail.setOutPaintName("");
                        //return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"[油漆品种表]中没有相应的油漆名称数据！");
                    }
                    //插入数据
                    detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                    detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                    detail.setYearMonth(dateStr);
                    //社内油漆名
                    detail.setInsidePaintName(paintNameStr);
                    //用户信息
                    User user = userService.getById(authorizingUser.getUserId());
                    if(user != null){
                        detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                        detail.setCreatorName(user.getUserName());
                        detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                        detail.setLastModifierId(Integer.valueOf(String.valueOf(user.getUserId())));
                        detail.setLastModifierName(user.getUserName());
                        detail.setLastModifiedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                    }

                    //mapKey是useCode   mapVal是使用量
                    String mapKey = map.getKey();
                    String mapVal = map.getValue();
                    detail.setPaintUseCode(mapKey);
                    //根据使用区分（小分类部门）查询设定小分类编号
                    PaintUseCode paintUseCode = costService.getPaintUseCodeByParam(mapKey);
                    if(paintUseCode != null){
                        detail.setSmallCode(paintUseCode.getSmallCode());
                    }else{
                        detail.setSmallCode("");
                        //return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"[使用区分表]中没有相应的分类部门！");
                    }
                    if(!StringUtil.isStringEmpty(mapVal)){
                        //油漆使用量
                        detail.setPaintUsage(new BigDecimal(mapVal).setScale(2,BigDecimal.ROUND_HALF_UP));
                    }else{
                        continue;
                    }

                    if(paintVariety != null ){
                        //「油漆单价」乘以「油漆使用量」获得「油漆使用金额」
                        if(paintPrice != null && detail.getPaintUsage() != null){
                        	
                        	if(paintPrice.getInventoryPrice().compareTo(BigDecimal.ZERO) > 0 ) {
                                BigDecimal paintUsageAmount = (paintPrice.getInventoryPrice().multiply(detail.getPaintUsage()).setScale(2,BigDecimal.ROUND_HALF_UP));
                                detail.setPaintUsageAmount(paintUsageAmount);
                        	} else {
                                BigDecimal paintUsageAmount = (paintPrice.getPaintPrice().multiply(detail.getPaintUsage()).setScale(2,BigDecimal.ROUND_HALF_UP));
                                detail.setPaintUsageAmount(paintUsageAmount);
                        	}
                        }
                    }else{
                        detail.setOutPaintName("");
                        //return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"[油漆品种表]中没有相应的油漆名称数据！");
                    }

                    detailList.add(detail);
                }
            }
            if(detailList == null || detailList.size() == 0){
                throw new Exception();
            }
            if (errMsgList != null && errMsgList.size() > 0){
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addPaintDetailImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 删除油漆费用表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/paintCost/remove")
    @ResponseBody
    public Object paintCostRemove(Integer costId) throws Exception{
        try {
            costService.removePaintPriceCost(costId,PaintPriceCost.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 批量删除油漆费用表
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/paintCost/delBatch")
    @ResponseBody
    public Object paintCostDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removePaintPriceCost(Integer.valueOf(costId),PaintPriceCost.STATE_DELETED);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================辅材·补修·包装·线盘费用登录======================================*/
    /**
     * 辅材·补修·包装费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/materialCost/list/view")
    public String materialCostListView(Model model) throws Exception{
        return "/modules/cost/materialCost_list";
    }

    /**
     * 辅材·补修·包装费用表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialCost/listPage")
    @ResponseBody
    public Object materialCostListPage(short isSearch,String yearMonth, String expenseType, String expenseCode,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            AuxiliaryMaterialCostDTO auxiliaryMaterialCostDTO = costService.listAuxiliaryMaterialPage(yearMonth,expenseType,expenseCode,pageInfo);
            return new HrPageResult(auxiliaryMaterialCostDTO.getMaterialCostList(), auxiliaryMaterialCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<AuxiliaryMaterialCost>(), 0);
        }
    }

    /**
     * 导入辅材·补修·包装费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/materialCost/import/view")
    public String materialCostDetailImportView(Model model) throws Exception{
        return "/modules/cost/materialCost_import";
    }

    /**
     * 导入辅材·补修·包装费用表
     * @param filename
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialCost/import")
    @ResponseBody
    public Object materialCostDetailImport(MultipartFile filename, String dateStr){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<AuxiliaryMaterialCost> auxiliaryMaterialCostList = costService.listAuxiliaryMaterialCostByYearMonth(dateStr);
//            if(auxiliaryMaterialCostList !=null  && auxiliaryMaterialCostList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"辅材明细已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> bxlist ;
            List<List<Object>> fclist ;
            List<List<Object>> bzlist ;
            List<List<Object>> xplist ;
            List<Object> strArr;//读取出的每一行
            String expenseType = null;
            //读取sheet的sheetName
            String sheetName1 = ReadExcelUtil.readExcelSheetName(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1).trim();
            String sheetName2 = ReadExcelUtil.readExcelSheetName(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet2).trim();
            String sheetName3 = ReadExcelUtil.readExcelSheetName(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet3).trim();
            String sheetName4 = ReadExcelUtil.readExcelSheetName(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet4).trim();
            int bxSheetIndex = 0;
            int fcSheetIndex = 0;
            int bzSheetIndex = 0;
            int xpSheetIndex = 0;

            if(!StringUtil.isStringEmpty(sheetName4)){
                if("补修".equals(sheetName1)){
                    bxSheetIndex = 0;
                }else if("补修".equals(sheetName2)){
                    bxSheetIndex = 1;
                }else if("补修".equals(sheetName3)){
                    bxSheetIndex = 2;
                }else if("补修".equals(sheetName4)){
                    bxSheetIndex = 3;
                }
                if("辅材".equals(sheetName1)){
                    fcSheetIndex = 0;
                }else if("辅材".equals(sheetName2)){
                    fcSheetIndex = 1;
                }else if("辅材".equals(sheetName3)){
                    fcSheetIndex = 2;
                }else if("辅材".equals(sheetName4)){
                    fcSheetIndex = 3;
                }
                if("包装".equals(sheetName1)){
                    bzSheetIndex = 0;
                }else if("包装".equals(sheetName2)){
                    bzSheetIndex = 1;
                }else if("包装".equals(sheetName3)){
                    bzSheetIndex = 2;
                }else if("包装".equals(sheetName4)){
                    bzSheetIndex = 3;
                }
                if("线盘".equals(sheetName1)){
                    xpSheetIndex = 0;
                }else if("线盘".equals(sheetName2)){
                    xpSheetIndex = 1;
                }else if("线盘".equals(sheetName3)){
                    xpSheetIndex = 2;
                }else if("线盘".equals(sheetName4)){
                    xpSheetIndex = 3;
                }
            }

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息
            List<AuxiliaryMaterialCost> detailList = new ArrayList<>();//添加数据集合
            File file = new File(FileUtils.getSavePath() + "//" + temp_file);
            bxlist = ReadExcelUtil.readExcelSheet(file,bxSheetIndex);
            fclist = ReadExcelUtil.readExcelSheet(file,fcSheetIndex);
            bzlist = ReadExcelUtil.readExcelSheet(file,bzSheetIndex);
            xplist = ReadExcelUtil.readExcelSheet(file,xpSheetIndex);


            /***************************************补修****************************************************/
            for (int i = 0; i < bxlist.size(); i++) {
                expenseType = "补修费";
                AuxiliaryMaterialCost detail= new AuxiliaryMaterialCost();
                //费用名称
                String expenseNameStr = null;
                //部门编码
                String departmentCodeStr = null;
                //部门名称
                String departmentNameStr = null;
                //摘要
                String notesStr = null;
                //金额
                String amountStr = null;
                if (i <= 4) {//去掉前5行
                    continue;
                }
                strArr = bxlist.get(i);
                errMsg = "序号：" + (i - 4) + ",";
                if (strArr.size() > 1) {
                    //第一列用来判:月份字符长度大于2 跳过（用来跳过最后一行）
                    if(strArr.get(0).toString().length() > 2 || (!StringUtil.isStringEmpty(strArr.get(0).toString()) &&"月".equals(strArr.get(0).toString()))){
                        continue;
                    }
                    //第一列用来判:月份为空 跳过
                    if(StringUtil.isStringEmpty(strArr.get(0).toString())){
                        continue;
                    }
                }
                if (strArr.size() > 3) {
                    expenseNameStr = strArr.get(3).toString();//费用名称
                }
                if (strArr.size() > 4) {
                    departmentCodeStr = strArr.get(4).toString();//部门编码
                }
                if (strArr.size() > 5) {
                    departmentNameStr = strArr.get(5).toString();//部门名称
                }
                if (strArr.size() > 7) {
                    notesStr = strArr.get(7).toString();//摘要
                }
                if (strArr.size() > 9) {
                    amountStr = strArr.get(9).toString();//金额
                }

//                if(StringUtil.isStringEmpty(departmentNameStr)){
//                    errMsg = errMsg +"部门名称未填写；";
//                    errMsgList.add(errMsg);
//                    continue;
//                }
                if(StringUtil.isStringEmpty(amountStr)){
                    amountStr = "0";
                }

                //把字段放到对象中，然后再插入表
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setExpenseType(expenseType);
                detail.setExpenseName(expenseNameStr);
                detail.setDepartmentCode(departmentCodeStr);
                detail.setDepartmentName(departmentNameStr);
                detail.setNotes(notesStr);
                if(amountStr != null){
                    detail.setAmount(new BigDecimal(amountStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                //根据用户id获取到用户信息
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                //把每个对象放在list集合中
                detailList.add(detail);
            }

            /***************************************辅材****************************************************/
            for (int i = 0; i < fclist.size(); i++) {
                expenseType = "辅材费";
                AuxiliaryMaterialCost detail= new AuxiliaryMaterialCost();
                //费用名称
                String expenseNameStr = null;
                //部门编码
                String departmentCodeStr = null;
                //部门名称
                String departmentNameStr = null;
                //摘要
                String notesStr = null;
                //金额
                String amountStr = null;
                if (i <= 4) {//去掉前5行
                    continue;
                }
                strArr = fclist.get(i);
                errMsg = "序号：" + (i - 4) + ",";
                if (strArr.size() > 1) {//第一列用来判空
                    //第一列用来判:月份字符长度大于2 跳过（用来跳过最后一行）
                    if(strArr.get(0).toString().length() > 2 || (!StringUtil.isStringEmpty(strArr.get(0).toString()) &&"月".equals(strArr.get(0).toString()))){
                        continue;
                    }
                    //第一列用来判:月份为空 跳过
                    if(StringUtil.isStringEmpty(strArr.get(0).toString())){
                        continue;
                    }
                }
                if (strArr.size() > 3) {
                    expenseNameStr = strArr.get(3).toString();//费用名称
                }
                if (strArr.size() > 4) {
                    departmentCodeStr = strArr.get(4).toString();//部门编码
                }
                if (strArr.size() > 5) {
                    departmentNameStr = strArr.get(5).toString();//部门名称
                }
                if (strArr.size() > 7) {
                    notesStr = strArr.get(7).toString();//摘要
                }
                if (strArr.size() > 9) {
                    amountStr = strArr.get(9).toString();//金额
                }

                if(StringUtil.isStringEmpty(amountStr)){
                    amountStr = "0";
                }

                //把字段放到对象中，然后再插入表
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setExpenseType(expenseType);
                detail.setExpenseName(expenseNameStr);
                detail.setDepartmentCode(departmentCodeStr);
                detail.setDepartmentName(departmentNameStr);
                detail.setNotes(notesStr);
                if(amountStr != null){
                    detail.setAmount(new BigDecimal(amountStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                //根据用户id获取到用户信息
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                //把每个对象放在list集合中
                detailList.add(detail);
            }

            /***************************************包装****************************************************/
            for (int i = 0; i < bzlist.size(); i++) {
                expenseType = "包装费";
                AuxiliaryMaterialCost detail= new AuxiliaryMaterialCost();
                //费用名称
                String expenseNameStr = null;
                //部门编码
                String departmentCodeStr = null;
                //部门名称
                String departmentNameStr = null;
                //摘要
                String notesStr = null;
                //金额
                String amountStr = null;
                if (i <= 4) {//去掉前5行
                    continue;
                }
                strArr = bzlist.get(i);
                errMsg = "序号：" + (i - 4) + ",";
                if (strArr.size() > 1) {//第一列用来判空
                    //第一列用来判:月份字符长度大于2 跳过（用来跳过最后一行）
                    if(strArr.get(0).toString().length() > 2 || (!StringUtil.isStringEmpty(strArr.get(0).toString()) &&"月".equals(strArr.get(0).toString()))){
                        continue;
                    }
                    //第一列用来判:月份为空 跳过
                    if(StringUtil.isStringEmpty(strArr.get(0).toString())){
                        continue;
                    }
                }
                if (strArr.size() > 3) {
                    expenseNameStr = strArr.get(3).toString();//费用名称
                }
                if (strArr.size() > 4) {
                    departmentCodeStr = strArr.get(4).toString();//部门编码
                }
                if (strArr.size() > 5) {
                    departmentNameStr = strArr.get(5).toString();//部门名称
                }
                if (strArr.size() > 7) {
                    notesStr = strArr.get(7).toString();//摘要
                }
                if (strArr.size() > 9) {
                    amountStr = strArr.get(9).toString();//金额
                }

                if(StringUtil.isStringEmpty(amountStr)){
                    amountStr = "0";
                }

                //把字段放到对象中，然后再插入表
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setExpenseType(expenseType);
                detail.setExpenseName(expenseNameStr);
                detail.setDepartmentCode(departmentCodeStr);
                detail.setDepartmentName(departmentNameStr);
                detail.setNotes(notesStr);
                if(amountStr != null){
                    detail.setAmount(new BigDecimal(amountStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                //根据用户id获取到用户信息
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                //把每个对象放在list集合中
                detailList.add(detail);
            }

            /***************************************线盘****************************************************/
            for (int i = 0; i < xplist.size(); i++) {
                expenseType = "线盘费";
                AuxiliaryMaterialCost detail= new AuxiliaryMaterialCost();
                //费用名称
                String expenseNameStr = null;
                //部门编码
                String departmentCodeStr = null;
                //部门名称
                String departmentNameStr = null;
                //摘要
                String notesStr = null;
                //金额
                String amountStr = null;
                if (i <= 4) {//去掉前5行
                    continue;
                }
                strArr = xplist.get(i);
                errMsg = "序号：" + (i - 4) + ",";
                if (strArr.size() > 1) {//第一列用来判空
                    //第一列用来判:月份字符长度大于2 跳过（用来跳过最后一行）
                    if(strArr.get(0).toString().length() > 2 || (!StringUtil.isStringEmpty(strArr.get(0).toString()) &&"月".equals(strArr.get(0).toString()))){
                        continue;
                    }
                    //第一列用来判:月份为空 跳过
                    if(StringUtil.isStringEmpty(strArr.get(0).toString())){
                        continue;
                    }
                }
                if (strArr.size() > 3) {
                    expenseNameStr = strArr.get(3).toString();//费用名称
                }
                if (strArr.size() > 4) {
                    departmentCodeStr = strArr.get(4).toString();//部门编码
                }
                if (strArr.size() > 5) {
                    departmentNameStr = strArr.get(5).toString();//部门名称
                }
                if (strArr.size() > 7) {
                    notesStr = strArr.get(7).toString();//摘要
                }
                if (strArr.size() > 9) {
                    amountStr = strArr.get(9).toString();//金额
                }

                if(StringUtil.isStringEmpty(amountStr)){
                    amountStr = "0";
                }

                //把字段放到对象中，然后再插入表
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setExpenseType(expenseType);
                detail.setExpenseName(expenseNameStr);
                detail.setDepartmentCode(departmentCodeStr);
                detail.setDepartmentName(departmentNameStr);
                detail.setNotes(notesStr);
                if(amountStr != null){
                    detail.setAmount(new BigDecimal(amountStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                //根据用户id获取到用户信息
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                //把每个对象放在list集合中
                detailList.add(detail);
            }
            //判断是否有报错信息
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addAuxiliaryMaterialDetailImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 删除辅材·补修·包装费用表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialCost/remove")
    @ResponseBody
    public Object materialCostRemove(Integer costId) throws Exception{
        try {
            costService.removeAuxiliaryMaterialCost(costId);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 批量辅材·补修·包装费用表
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialCost/delBatch")
    @ResponseBody
    public Object materialCostDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeAuxiliaryMaterialCost(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 批量辅材·补修·包装Excell导出
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialCost/export")
    @ResponseBody
    public Object materialCostExport(String yearMonthForExcell, String expenseTypeForExcell, HttpServletRequest req, HttpServletResponse resp) throws Exception{
        try {
        	List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
            //拼接需要导出的数据
            List<String[]> dataset = new ArrayList<String[]>();
            
        	List<AuxiliaryMaterialCost> auxiliaryMaterialCostList = costService.listAuxiliaryMaterialCost(yearMonthForExcell, expenseTypeForExcell);
        	
        	for(AuxiliaryMaterialCost auxiliaryMaterialCost:auxiliaryMaterialCostList){
        		  String[] arr = new String[7];
        		  arr[0] = auxiliaryMaterialCost.getYearMonth();
        		  arr[1] = auxiliaryMaterialCost.getExpenseType();
        		  arr[2] = auxiliaryMaterialCost.getExpenseName();
        		  arr[3] = auxiliaryMaterialCost.getDepartmentCode();
        		  arr[4] = auxiliaryMaterialCost.getDepartmentName();
        		  arr[5] = auxiliaryMaterialCost.getNotes();
        		  arr[6] = auxiliaryMaterialCost.getAmount().toString();   
        		  dataset.add(arr);
        	}
        	 //表头
            String[] handers = new String[7];
            handers[0] = "年月";
            handers[1] = "费用种类";
            handers[2] = "费用名称";
            handers[3] = "部门编码";
            handers[4] = "部门名称";
            handers[5] = "备注";
            handers[6] = "金额";
            //设置数字列
            Integer[] numericColumnArr = new Integer[0];
            //对象
            ExcelExp e = new ExcelExp("辅材_补修_包装明细", handers, dataset,numericColumnArr);
            mysheet.add(e);
            String fileName = "辅材_补修_包装明细_"+DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),  "yyyyMMddHHmmss");
            ServletUtil su = new ServletUtil(fileName, req, resp);
            su.poiExcelServlet();
            ExcelExportUtil.exportManySheetExcel(su.getOut(), mysheet); //生成sheet
        }catch (Exception e){
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    /*=================================辅材·补修·包装·线盘汇总计算登录======================================*/
    /**
     * 辅材·补修·包装汇总计算页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/materialSummary/list/view")
    public String materialSummaryListView(Model model) throws Exception{
        return "/modules/cost/materialSummary_list";
    }

    /**
     * 辅材·补修·包装汇总计算表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialSummary/listPage")
    @ResponseBody
    public Object materialSummaryListPage(short isSearch,String yearMonth, String nature, String abstracts,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            AuxiliaryMaterialSummaryDTO auxiliaryMaterialSummaryDTO = costService.listAuxiliaryMaterialSummaryPage(yearMonth,nature,abstracts,pageInfo);
            return new HrPageResult(auxiliaryMaterialSummaryDTO.getMaterialSummaryList(), auxiliaryMaterialSummaryDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<AuxiliaryMaterialSummary>(), 0);
        }
    }

    /**
     * 导入辅材·补修·包装汇总计算表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/materialSummary/import/view")
    public String materialSummaryDetailImportView(Model model) throws Exception{
        return "/modules/cost/materialSummary_import";
    }

    /**
     * 导入辅材·补修·包装汇总计算表
     * @param filename
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialSummary/import")
    @ResponseBody
    public Object materialSummaryDetailImport(MultipartFile filename, String dateStr){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

//            List<AuxiliaryMaterialSummary> auxiliaryMaterialSummaryList = costService.listAuxiliaryMaterialSummaryByYearMonth(dateStr);
//            if(auxiliaryMaterialSummaryList !=null  && auxiliaryMaterialSummaryList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"辅材汇总已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list;
            List<Object> strArr;//读取出的每一行

            list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<AuxiliaryMaterialSummary> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                strArr = list.get(i);
                //判断摘要是否为‘补修’ ‘辅材’ ‘线盘’，如果不是 就跳过
                if (strArr.size() > 3) {
                    String abstracts = strArr.get(3).toString();//摘要
                    if(!"补修".equals(abstracts) && !"辅材".equals(abstracts) && !"线盘".equals(abstracts)
                    && !"模具".equals(abstracts) && !"润滑剂".equals(abstracts) && !"运输费".equals(abstracts)){
                        continue;
                    }
                }else{
                    continue;
                }

                AuxiliaryMaterialSummary detail= new AuxiliaryMaterialSummary();
                //性质
                String natureStr = null;
                //摘要
                String abstractsStr = null;
                //金额
                String amountStr = null;
                //EM
                String emStr = null;
                //EF
                String efStr = null;
                //UF
                String ufStr = null;
                //ER
                String erStr = null;
                //EH
                String ehStr = null;

                errMsg = "序号：" + (i - 1) + ",";
                if (strArr.size() > 1) {
                    natureStr = strArr.get(1).toString();//性质
                }
                if (strArr.size() > 3) {
                    abstractsStr = strArr.get(3).toString();//摘要
                }
                if (strArr.size() > 4) {
                    amountStr = strArr.get(4).toString();//金额
                }
                if (strArr.size() > 5) {
                    emStr = strArr.get(5).toString();//EM
                }
                if (strArr.size() > 6) {
                    efStr = strArr.get(6).toString();//EF
                }
                if (strArr.size() > 7) {
                    ufStr = strArr.get(7).toString();//UF
                }
                if (strArr.size() > 8) {
                    erStr = strArr.get(8).toString();//ER
                }
                if (strArr.size() > 9) {
                    ehStr = strArr.get(9).toString();//EH
                }

                //把字段放到对象中，然后再插入表
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setNature(natureStr);
                detail.setAbstracts(abstractsStr);
                if(!StringUtil.isStringEmpty(amountStr)){
                    detail.setAmount(new BigDecimal(amountStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                if(!StringUtil.isStringEmpty(emStr)){
                    detail.setEm(new BigDecimal(emStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                if(!StringUtil.isStringEmpty(efStr)){
                    detail.setEf(new BigDecimal(efStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                if(!StringUtil.isStringEmpty(ufStr)){
                    detail.setUf(new BigDecimal(ufStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                if(!StringUtil.isStringEmpty(erStr)){
                    detail.setEr(new BigDecimal(erStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                if(!StringUtil.isStringEmpty(ehStr)){
                    detail.setEh(new BigDecimal(ehStr).setScale(2,BigDecimal.ROUND_HALF_UP));
                }

                //根据用户id获取到用户信息
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                //把每个对象放在list集合中
                detailList.add(detail);
            }
            if(detailList == null || detailList.size() == 0){
                throw new Exception();
            }
            //判断是否有报错信息
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addAuxiliaryMaterialSummaryDetailImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 删除辅材·补修·包装汇总计算表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialSummary/remove")
    @ResponseBody
    public Object materialSummaryRemove(Integer costId) throws Exception{
        try {
            costService.removeAuxiliaryMaterialSummary(costId,AuxiliaryMaterialSummary.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 批量删除辅材·补修·包装汇总计算表
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/materialSummary/delBatch")
    @ResponseBody
    public Object materialSummaryDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeAuxiliaryMaterialSummary(Integer.valueOf(costId),AuxiliaryMaterialSummary.STATE_DELETED);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 修改页面
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/materialSummary/edit/view")
    public String materialSummaryEditView(Model model,Integer costId) throws Exception{
        //根据流水号查询实体类
        AuxiliaryMaterialSummary auxiliaryMaterialSummary = costService.getAuxiliaryMaterialSummaryById(costId);
        model.addAttribute("auxiliaryMaterialSummary",auxiliaryMaterialSummary);
        return "/modules/cost/materialSummary_modify";
    }

    /**
     * 修改
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/materialSummary/edit")
    @ResponseBody
    public Object materialSummaryEdit(AuxiliaryMaterialSummary auxiliaryMaterialSummary) throws Exception{
        try {
            //获取当前登录对象
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            costService.modifyAuxiliaryMaterialSummary(auxiliaryMaterialSummary);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("铜加工费用修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }



    /*=================================线盘予定登录======================================*/
    /**
     * 线盘费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/wireDiscCost/list/view")
    public String wireDiscCostListView(Model model) throws Exception{
    	// 线盘型号
        List<String> wireDiscTypeList= costService.listWireDiscType();
        model.addAttribute("wireDiscTypeList",wireDiscTypeList);
        return "/modules/cost/wireDiscCost_list";
    }

    /**
     * 线盘费用表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/wireDiscCost/list")
    @ResponseBody
    public Object wireDiscCostList(short isSearch,String year, String wireDiscType) throws Exception{
        if(isSearch == 1){
        	List<WireDiscCost> wireDiscCostList = costService.listWireDiscCost(year, wireDiscType, WireDiscCost.STATE_ZERO);
            return new HrPageResult(wireDiscCostList, wireDiscCostList.size());
        }else{
            return new HrPageResult(new ArrayList<WireDiscCost>(), 0);
        }
    }

    /**
     * 新增线盘费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/wireDiscCost/add/view")
    public String wireDiscCostAddView(Model model) throws Exception{
    	// 线盘型号
        List<String> wireDiscTypeList= costService.listWireDiscType();
        model.addAttribute("wireDiscTypeList",wireDiscTypeList);
        return "/modules/cost/wireDiscCost_add";
    }

    /**
     * 新增线盘费用表
     * @param wireDiscCost
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/wireDiscCost/add")
    @ResponseBody
    public Object wireDiscCostAdd(WireDiscCost wireDiscCost) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            wireDiscCost.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            wireDiscCost.setCreatorName(user.getUserName());
            wireDiscCost.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
    		// 区分(0:MW)
    		wireDiscCost.setDistinguish(WireDiscCost.STATE_ZERO);
            
            // 线盘费用表新增记录
           costService.addWireDiscCost(wireDiscCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("线盘费用新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑线盘费用画面
     * @param model
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @GetMapping("/wireDiscCost/modify/view")
    public String wireDiscCostModifyView(Model model,Integer costId) throws Exception{
    	WireDiscCost wireDiscCost = costService.selectWireDiscCostByCostId(costId);
        model.addAttribute("wireDiscCost", wireDiscCost);
    	// 线盘型号List
        List<String> wireDiscTypeList= costService.listWireDiscType();
        model.addAttribute("wireDiscTypeList",wireDiscTypeList);
    	return "/modules/cost/wireDiscCost_modify";
    }
    
    /**
     * 编辑线盘费用
     * @param wireDiscCost
     * @throws Exception
      * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @PostMapping("/wireDiscCost/modify")
    @ResponseBody
    public Object wireDiscCostModify(WireDiscCost wireDiscCost) throws Exception{
        try {
    		// 区分(0:MW)
    		wireDiscCost.setDistinguish(WireDiscCost.STATE_ZERO);
            // 更新线盘费用表
            costService.updateWireDiscCost(wireDiscCost);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("人件比例明细编辑异常信息：", e);
			return new HrResult(0, e.getMessage());
		}
		return new HrResult(CommonReturnCode.SUCCESS);
	}
    
    /**
     * 删除线盘费用表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/wireDiscCost/remove")
    @ResponseBody
    public Object wireDiscCostRemove(Integer costId) throws Exception{
        try {
            costService.removeWireDiscCost(costId);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 超细线盘费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/22 10:43
     * @return
     */
    @GetMapping("/wireDiscCostUF/list/view")
    public String wireDiscCostUFListView(Model model) throws Exception{
    	// 线盘型号
        List<String> wireDiscTypeList= costService.listWireDiscType();
        model.addAttribute("wireDiscTypeList",wireDiscTypeList);
        return "/modules/cost/wireDiscCostUF_list";
    }

    /**
     * 超细线盘费用表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/22 10:43
     * @return
     */
    @PostMapping("/wireDiscCostUF/list")
    @ResponseBody
    public Object wireDiscCostUFList(short isSearch,String year, String wireDiscType) throws Exception{
        if(isSearch == 1){
        	List<WireDiscCost> wireDiscCostList = costService.listWireDiscCost(year, wireDiscType, WireDiscCost.STATE_ONE);
            return new HrPageResult(wireDiscCostList, wireDiscCostList.size());
        }else{
            return new HrPageResult(new ArrayList<WireDiscCost>(), 0);
        }
    }
    
    /**
     * 超细新增线盘费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/22 10:43
     * @return
     */
    @GetMapping("/wireDiscCostUF/add/view")
    public String wireDiscCostUFAddView(Model model) throws Exception{
    	// 线盘型号
        List<String> wireDiscTypeList= costService.listWireDiscType();
        model.addAttribute("wireDiscTypeList",wireDiscTypeList);
        return "/modules/cost/wireDiscCostUF_add";
    }

    /**
     * 超细新增线盘费用表
     * @param wireDiscCost
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/22 10:43
     * @return
     */
    @PostMapping("/wireDiscCostUF/add")
    @ResponseBody
    public Object wireDiscCostUFAdd(WireDiscCost wireDiscCost) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            wireDiscCost.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            wireDiscCost.setCreatorName(user.getUserName());
            wireDiscCost.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
    		// 区分(1:UF)
    		wireDiscCost.setDistinguish(WireDiscCost.STATE_ONE);
    		// 线盘费用表新增记录
            costService.addWireDiscCost(wireDiscCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("线盘费用新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 编辑线盘费用画面
     * @param model
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @GetMapping("/wireDiscCostUF/modify/view")
    public String wireDiscCostUFModifyView(Model model,Integer costId) throws Exception{
    	WireDiscCost wireDiscCost = costService.selectWireDiscCostByCostId(costId);
        model.addAttribute("wireDiscCost", wireDiscCost);
    	// 线盘型号List
        List<String> wireDiscTypeList= costService.listWireDiscType();
        model.addAttribute("wireDiscTypeList",wireDiscTypeList);
    	return "/modules/cost/wireDiscCostUF_modify";
    }
    
    /**
     * 编辑线盘费用
     * @param wireDiscCost
     * @throws Exception
      * <AUTHOR>
     * @create 2024/02/22 11:43
     * @return
     */
    @PostMapping("/wireDiscCostUF/modify")
    @ResponseBody
    public Object wireDiscCostUFModify(WireDiscCost wireDiscCost) throws Exception{
        try {
    		// 区分(1:UF)
    		wireDiscCost.setDistinguish(WireDiscCost.STATE_ONE);
            // 更新线盘费用表
            costService.updateWireDiscCost(wireDiscCost);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("人件比例明细编辑异常信息：", e);
			return new HrResult(0, e.getMessage());
		}
		return new HrResult(CommonReturnCode.SUCCESS);
	}
    
    /**
     * 删除超细线盘费用表
     * @param costId
     * @throws Exception
      * <AUTHOR>
     * @create 2024/02/22 11:43
     * @return
     */
    @PostMapping("/wireDiscCostUF/remove")
    @ResponseBody
    public Object wireDiscCostUFRemove(Integer costId) throws Exception{
        try {
            costService.removeWireDiscCost(costId);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    /*=================================运输费用登录======================================*/
    /**
     * 运输费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/transportCost/list/view")
    public String transportCostListView(Model model) throws Exception{
        return "/modules/cost/transportCost_list";
    }

    /**
     * 运输费用表
     * @param yearMonth
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/transportCost/listPage")
    @ResponseBody
    public Object transportCostListPage(short isSearch,String yearMonth, String transportCode,String area, PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            TransportCostDTO transportCostDTO = costService.listTransportCostPage(yearMonth,transportCode, area,pageInfo);
            return new HrPageResult(transportCostDTO.getTransportCostList(), transportCostDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<TransportCost>(), 0);
        }
    }

    /**
     * 新增运输费用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/transportCost/add/view")
    public String transportCostAddView(Model model) throws Exception{
        return "/modules/cost/transportCost_add";
    }

    /**
     * 新增运输费用表
     * @param transportCost
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/transportCost/add")
    @ResponseBody
    public Object transportCostAdd(TransportCost transportCost) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            transportCost.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            transportCost.setCreatorName(user.getUserName());
            transportCost.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            // 插入数据运输费用表
            costService.addTransportCost(transportCost);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("运输费用新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除运输费用表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/transportCost/remove")
    @ResponseBody
    public Object transportCostRemove(Integer costId) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            costService.removeTransportCost(costId);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

//    /*=================================部门区分列表======================================*/
//    /**
//     * 使用区分表页面
//     * @param model
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @GetMapping("/paintUseCode/list/view")
//    public String paintUseCodeListView(Model model) throws Exception{
//        return "/modules/cost/paintUseCode_list";
//    }
//
//    /**
//     * 使用区分表
//     * @param
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @PostMapping("/paintUseCode/listPage")
//    @ResponseBody
//    public Object paintUseCodeListPage(short isSearch,String smallCode, String smallDepartment,String midCode, String midDepartment,PageInfo pageInfo) throws Exception{
//        if(isSearch == 1){
//            PaintUseCodeDTO paintUseCodeDTO = costService.listPaintUseCodePage(smallCode,smallDepartment,midCode,midDepartment,pageInfo);
//            return new HrPageResult(paintUseCodeDTO.getPaintUseCodeList(), paintUseCodeDTO.getPageInfo().getTotal());
//        }else{
//            return new HrPageResult(new ArrayList<PaintUseCode>(), 0);
//        }
//    }
//
//    /**
//     * 新增使用区分表页面
//     * @param model
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @GetMapping("/paintUseCode/add/view")
//    public String paintUseCodeAddView(Model model) throws Exception{
//        return "/modules/cost/paintUseCode_add";
//    }
//
//    /**
//     * 新增使用区分表
//     * @param paintUseCode
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @PostMapping("/paintUseCode/add")
//    @ResponseBody
//    public Object paintUseCodeAdd(PaintUseCode paintUseCode) throws Exception{
//        try {
//            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
//            if(authorizingUser == null ) {
//                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
//            }
//            User user = userService.getById(authorizingUser.getUserId());
//            paintUseCode.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
//            paintUseCode.setCreatorName(user.getUserName());
//            paintUseCode.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
//            costService.addPaintUseCode(paintUseCode);
//        }catch (Exception e){
//            e.printStackTrace();
//            logger.error("使用区分新增异常信息：", e);
//            return new HrResult(0,e.getMessage());
//        }
//        return new HrResult(CommonReturnCode.SUCCESS);
//    }
//
//    /**
//     * 删除使用区分表
//     * @param costId
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @PostMapping("/paintUseCode/remove")
//    @ResponseBody
//    public Object paintUseCodeRemove(Integer costId) throws Exception{
//        try {
//            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
//            if(authorizingUser == null ) {
//                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
//            }
//            costService.removePaintUseCode(costId);
//        }catch (Exception e){
//            e.printStackTrace();
//            return new HrResult(0,e.getMessage());
//        }
//        return new HrResult(CommonReturnCode.SUCCESS);
//    }
    /*=================================人件比例用表======================================*/
//    /**
//     * 人件比例用表页面
//     * @param model
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2024/02/05 17:43
//     * @return
//     */
//    @GetMapping("/proportion/list/view")
//    public String proportionListView(Model model) throws Exception{
//        return "/modules/cost/proportion_list";
//    }
//    
//    /**
//     * 人件比例用表明细
//     * @param
//     * @throws Exception
//      * <AUTHOR>
//     * @create 2024/02/05 17:43
//     * @return
//     */
//    @PostMapping("/proportion/list")
//    @ResponseBody
//    public Object  proportionList(short isSearch, String year) throws Exception{
//        if(isSearch == 1){
//        	List<Proportion> proportionList = statService.listProportion(year);
//            return new HrPageResult(proportionList, proportionList.size());
//        }else{
//            return new HrPageResult(new ArrayList<Proportion>(), 0);
//        }
//    }  
//
//    /**
//     * 添加人件比例用表画面
//     * @param model
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2024/02/05 17:43
//     * @return
//     */
//    @GetMapping("proportion/add/view")
//    public String proportionAddView(Model model) throws Exception{
//    	return "/modules/cost/proportion_add";
//    }
//    
//    /**
//     * 添加人件比例用表
//     * @param proportion
//     * @throws Exception
//      * <AUTHOR>
//     * @create 2024/02/05 17:43
//     * @return
//     */
//    @PostMapping("/proportion/add")
//    @ResponseBody
//    public Object proportionAdd(Proportion proportion) throws Exception{
//		try {
//			statService.addProportion(proportion);
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error("人件比例明细新增异常信息：", e);
//			return new HrResult(0, e.getMessage());
//		}
//		return new HrResult(CommonReturnCode.SUCCESS);
//	}
//
//    /**
//     * 编辑人件比例用表画面
//     * @param model
//     * @param statId
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2024/02/05 17:43
//     * @return
//     */
//    @GetMapping("proportion/modify/view")
//    public String proportionModifyView(Model model,Integer statId) throws Exception{
//    	Proportion proportion = statService.selectBystatId(statId);
//        model.addAttribute("proportion", proportion);
//    	return "/modules/cost/proportion_modify";
//    }
//    
//    /**
//     * 编辑人件比例用表
//     * @param proportion
//     * @throws Exception
//      * <AUTHOR>
//     * @create 2024/02/05 17:43
//     * @return
//     */
//    @PostMapping("/proportion/modify")
//    @ResponseBody
//    public Object proportionModify(Proportion proportion) throws Exception{
//		try {
//			statService.modifyProportion(proportion);
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error("人件比例明细编辑异常信息：", e);
//			return new HrResult(0, e.getMessage());
//		}
//		return new HrResult(CommonReturnCode.SUCCESS);
//	}
//    
//    /**
//     * 删除人件比例用表
//     * @param costId
//      * <AUTHOR>
//     * @create 2024/02/05 17:43
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @PostMapping("/proportion/remove")
//    @ResponseBody
//    public Object proportionRemove(Integer statId) throws Exception{
//        try {
//        	statService.removeProportion(statId);
//        }catch (Exception e){
//            e.printStackTrace();
//            return new HrResult(0,e.getMessage());
//        }
//        return new HrResult(CommonReturnCode.SUCCESS);
//    }

//    /*=================================补辅比例列表======================================*/
//    /**
//     * 补辅比例列表页面
//     * @param model
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @GetMapping("/smallDepartment/list/view")
//    public String smallDepartmentListView(Model model) throws Exception{
//        return "/modules/cost/smallDepartment_list";
//    }
//
//    
//    /**
//     * 新增补辅比例列表页面
//     * @param model
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @GetMapping("/smallDepartment/add/view")
//    public String smallDepartmentAddView(Model model) throws Exception{
//        return "/modules/cost/smallDepartment_add";
//    }
//
//    /**
//     * 新增补辅比例列表
//     * @param smallDepartment
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @PostMapping("/smallDepartment/add")
//    @ResponseBody
//    public Object smallDepartmentAdd(SmallDepartment smallDepartment) throws Exception{
//        try {
//            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
//            if(authorizingUser == null ) {
//                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
//            }
//            User user = userService.getById(authorizingUser.getUserId());
//            smallDepartment.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
//            smallDepartment.setCreatorName(user.getUserName());
//            smallDepartment.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
//            costService.addSmallDepartment(smallDepartment);
//        }catch (Exception e){
//            e.printStackTrace();
//            logger.error("小部门明细新增异常信息：", e);
//            return new HrResult(0,e.getMessage());
//        }
//        return new HrResult(CommonReturnCode.SUCCESS);
//    }
//    
//    /**
//     * 编辑补辅比例列表页面
//     * @param model
//     * @param costId
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/7/5 17:43
//     * @return
//     */
//    @GetMapping("/smallDepartment/edit/view")
//    public String smallDepartmentEditView(Model model,Integer costId) throws Exception{
//    	SmallDepartment smallDepartment = costService.getSmallDepartmentById(costId);
//        model.addAttribute("smallDepartment",smallDepartment);
//        return "/modules/cost/smallDepartment_modify";
//    }
//    /**
//     * 编辑补辅比例列表
//     * @param model
//     * @param costId
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/7/5 17:43
//     * @return
//     */
//    @PostMapping("/smallDepartment/edit")
//    @ResponseBody
//    public Object smallDepartmentEdit(SmallDepartment smallDepartment) throws Exception{
//        try {
////        	smallDepartment.setUfShareRateOfAuxiliary(new BigDecimal(smallDepartment.getUfShareRateOfAuxiliary()));
////        	smallDepartment.setUfShareRateOfRepaire(new BigDecimal(smallDepartment.getUfShareRateOfRepaire()));
//            costService.modifySmallDepartment(smallDepartment);
//        }catch (Exception e){
//            e.printStackTrace();
//            logger.error("铜加工费用修改异常信息：", e);
//            return new HrResult(0,e.getMessage());
//        }
//        return new HrResult(CommonReturnCode.SUCCESS);
//    }
//   
//    /**
//     * 删除补辅比例列表
//     * @param costId
//     * @throws Exception
//     * <AUTHOR>
//     * @create 2023/8/10 17:43
//     * @return
//     */
//    @PostMapping("/smallDepartment/remove")
//    @ResponseBody
//    public Object smallDepartmentRemove(Integer costId) throws Exception{
//        try {
//            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
//            if(authorizingUser == null ) {
//                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
//            }
//            costService.removeSmallDepartment(costId);
//        }catch (Exception e){
//            e.printStackTrace();
//            return new HrResult(0,e.getMessage());
//        }
//        return new HrResult(CommonReturnCode.SUCCESS);
//    }

    /*=================================================予定回收导入=====================================================*/

    /**
     * 予定回收导入页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/scheduledRecovery/import/view")
    public String scheduledRecoveryImportView(Model model) throws Exception{
        return "/modules/cost/scheduledRecovery_import";
    }

    /**
     * 导入予定回收（预定直接部门回收计算）
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 14:34
     * @return
     */
    @PostMapping("/scheduledRecovery/directRecycling/import")
    @ResponseBody
    public Object scheduledRecoveryDirectRecyclingImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<DirectRecycling> directRecyclingList = costService.listDirectRecyclingByYearMonth(dateStr);
//            if(directRecyclingList != null && directRecyclingList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预定直接部门回收计算已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<DirectRecycling> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                DirectRecycling detail = new DirectRecycling();

                //部门
                String departmentCode = null;
                //SMCH
                String sMCHNum = null;
                //操作工人件费SH
                String operatorCostSH = null;
                //操作工人件费费用
                String operatorCost = null;
                //劳务工人件费SH
                String laborCostSH = null;
                //劳务工人件费费用
                String laborCost = null;
                //电费SH
                String electricCostSH = null;
                //电费费用
                String electricCost = null;
                //煤气SH
                String gasCostSH = null;
                //煤气费费用
                String gasCost = null;
                //水费SH
                String waterCostSH = null;
                //水费费用
                String waterCost = null;
                //氮气费SH
                String nitrogenCostSH = null;
                //氮气费费用
                String nitrogenCost = null;
                //保全操作工人件费SH
                String securityCostSH = null;
                //保全操作工人件费费用
                String securityCost = null;
                //补修费SH
                String repairCostSH = null;
                //补修费费用
                String repairCost = null;
                //辅材费SH
                String materialCostSH = null;
                //辅材费费用
                String materialCost = null;

                if (i == 0 || i == 1) {//去掉第一行和第二行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";

                if (strArr.size() > 0) {
                    departmentCode = strArr.get(0).toString();//部门
                }
                if (strArr.size() > 1) {
                    sMCHNum = strArr.get(1).toString();//SMCH
                }
                if (strArr.size() > 2) {
                    operatorCostSH = strArr.get(2).toString();//操作工人件费SH
                }
                if (strArr.size() > 3) {
                    operatorCost = strArr.get(3).toString();//操作工人件费费用
                }
                if (strArr.size() > 4) {
                    laborCostSH = strArr.get(4).toString();//劳务工人件费SH
                }
                if (strArr.size() > 5) {
                    laborCost = strArr.get(5).toString();//劳务工人件费费用
                }
                if (strArr.size() > 6) {
                    electricCostSH = strArr.get(6).toString();//电费SH
                }
                if (strArr.size() > 7) {
                    electricCost = strArr.get(7).toString();//电费费用
                }
                if (strArr.size() > 8) {
                    gasCostSH = strArr.get(8).toString();//煤气费SH
                }
                if (strArr.size() > 9) {
                    gasCost = strArr.get(9).toString();//煤气费费用
                }
                if (strArr.size() > 10) {
                    waterCostSH = strArr.get(10).toString();//水费SH
                }
                if (strArr.size() > 11) {
                    waterCost = strArr.get(11).toString();//水费费用
                }
                if (strArr.size() > 12) {
                    nitrogenCostSH = strArr.get(12).toString();//氮气费SH
                }
                if (strArr.size() > 13) {
                    nitrogenCost = strArr.get(13).toString();//氮气费费用
                }
                if (strArr.size() > 14) {
                    securityCostSH = strArr.get(14).toString();//保全操作工人件费SH
                }
                if (strArr.size() > 15) {
                    securityCost = strArr.get(15).toString();//保全操作工人件费费用
                }
                if (strArr.size() > 16) {
                    repairCostSH = strArr.get(16).toString();//补修费SH
                }
                if (strArr.size() > 17) {
                    repairCost = strArr.get(17).toString();//补修费费用
                }
                if (strArr.size() > 18) {
                    materialCostSH = strArr.get(18).toString();//辅材费SH
                }
                if (strArr.size() > 19) {
                    materialCost = strArr.get(19).toString();//辅材费费用
                }

                if(StringUtil.isStringEmpty(departmentCode)){
                    continue;
                }

                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);

                detail.setDepartmentCode(departmentCode);
                if(StringUtils.isNotEmpty(sMCHNum)){
                    detail.setsMCHNum(new BigDecimal(sMCHNum).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(operatorCostSH)){
                    detail.setOperatorCostSH(new BigDecimal(operatorCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(operatorCost)){
                    detail.setOperatorCost(new BigDecimal(operatorCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(laborCostSH)){
                    detail.setLaborCostSH(new BigDecimal(laborCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(laborCost)){
                    detail.setLaborCost(new BigDecimal(laborCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(electricCostSH)){
                    detail.setElectricCostSH(new BigDecimal(electricCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(electricCost)){
                    detail.setElectricCost(new BigDecimal(electricCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(gasCostSH)){
                    detail.setGasCostSH(new BigDecimal(gasCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(gasCost)){
                    detail.setGasCost(new BigDecimal(gasCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(waterCostSH)){
                    detail.setWaterCostSH(new BigDecimal(waterCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(waterCost)){
                    detail.setWaterCost(new BigDecimal(waterCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(nitrogenCostSH)){
                    detail.setNitrogenCostSH(new BigDecimal(nitrogenCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(nitrogenCost)){
                    detail.setNitrogenCost(new BigDecimal(nitrogenCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(securityCostSH)){
                    detail.setSecurityCostSH(new BigDecimal(securityCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(securityCost)){
                    detail.setSecurityCost(new BigDecimal(securityCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(repairCostSH)){
                    detail.setRepairCostSH(new BigDecimal(repairCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(repairCost)){
                    detail.setRepairCost(new BigDecimal(repairCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(materialCostSH)){
                    detail.setMaterialCostSH(new BigDecimal(materialCostSH).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(materialCost)){
                    detail.setMaterialCost(new BigDecimal(materialCost).setScale(8,BigDecimal.ROUND_HALF_UP));
                }

                detail.setDirectCode(distinguish);
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addDirectRecyclingImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导入予定回收（预定辅助部门回收计算（补修费、辅材费））
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 14:34
     * @return
     */
    @PostMapping("/scheduledRecovery/auxiliaryRecycling/import")
    @ResponseBody
    public Object scheduledRecoveryAuxiliaryRecyclingImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<AuxiliaryRecycling> auxiliaryRecyclingList = costService.listAuxiliaryRecyclingByYearMonth(dateStr);
//            if(auxiliaryRecyclingList != null && auxiliaryRecyclingList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预定辅助部门回收计算（补修费、辅材费）已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<AuxiliaryRecycling> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                AuxiliaryRecycling detail = new AuxiliaryRecycling();

                //直接部门
                String directDepartmentCode = null;
                //辅助部门
                String auxiliaryDepartmentCode = null;
                //费用项目
                String expenseItem = null;
                //SMCH
                String sMCHNum = null;
                //金额
                String amount = null;

                if (i == 0) {//去掉第一行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    directDepartmentCode = strArr.get(0).toString();//直接部门
                }
                if (strArr.size() > 1) {
                    auxiliaryDepartmentCode = strArr.get(1).toString();//辅助部门
                }
                if (strArr.size() > 2) {
                    expenseItem = strArr.get(2).toString();//费用项目
                }
                if (strArr.size() > 3) {
                    sMCHNum = strArr.get(3).toString();//SMCH
                }
                if (strArr.size() > 4) {
                    amount = strArr.get(4).toString();//金额
                }

                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setDirectDepartmentCode(directDepartmentCode);
                detail.setAuxiliaryDepartmentCode(auxiliaryDepartmentCode);
                detail.setExpenseItem(expenseItem);
                if(StringUtils.isNotEmpty(sMCHNum)){
                    detail.setsMCHNum(new BigDecimal(sMCHNum).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(amount)){
                    detail.setAmount(new BigDecimal(amount).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                detail.setAuxiliaryCode(distinguish);
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addAuxiliaryRecyclingImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导入予定回收（预定辅助部门回收计算（人工费））
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 14:34
     * @return
     */
    @PostMapping("/scheduledRecovery/auxiliaryRecyclingArtificial/import")
    @ResponseBody
    public Object scheduledRecoveryAuxiliaryRecyclingArtificialImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificialList = costService.listAuxiliaryRecyclingArtificialByYearMonth(dateStr);
//            if(auxiliaryRecyclingArtificialList != null && auxiliaryRecyclingArtificialList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预定辅助部门回收计算（人工费）已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<AuxiliaryRecyclingArtificial> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                AuxiliaryRecyclingArtificial detail = new AuxiliaryRecyclingArtificial();

                //直接部门
                String directDepartmentCode = null;
                //辅助部门
                String auxiliaryDepartmentCode = null;
                //费用项目
                String expenseItem = null;
                //SMCH
                String sMCHNum = null;
                //SH
                String sHNum = null;
                //单价
                String unitPrice = null;
                //金额
                String amount = null;

                if (i == 0) {//去掉第一行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    directDepartmentCode = strArr.get(0).toString();//直接部门
                }
                if (strArr.size() > 1) {
                    auxiliaryDepartmentCode = strArr.get(1).toString();//辅助部门
                }
                if (strArr.size() > 2) {
                    expenseItem = strArr.get(2).toString();//费用项目
                }
                if (strArr.size() > 3) {
                    sMCHNum = strArr.get(3).toString();//SMCH
                }
                if (strArr.size() > 4) {
                    sHNum = strArr.get(4).toString();//SH
                }
                if (strArr.size() > 5) {
                    unitPrice = strArr.get(5).toString();//单价
                }
                if (strArr.size() > 6) {
                    amount = strArr.get(6).toString();//金额
                }

                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setDirectDepartmentCode(directDepartmentCode);
                detail.setAuxiliaryDepartmentCode(auxiliaryDepartmentCode);
                detail.setExpenseItem(expenseItem);
                if(StringUtils.isNotEmpty(sMCHNum)){
                    detail.setsMCHNum(new BigDecimal(sMCHNum).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(sHNum)){
                    detail.setsHNum(new BigDecimal(sHNum).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(unitPrice)){
                    detail.setUnitPrice(new BigDecimal(unitPrice).setScale(3,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(amount)){
                    detail.setAmount(new BigDecimal(amount).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                detail.setAuxiliaryCode(distinguish);
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addAuxiliaryRecyclingArtificialImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导入予定回收（预定捆包费）
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 14:34
     * @return
     */
    @PostMapping("/scheduledRecovery/bookingBundling/import")
    @ResponseBody
    public Object scheduledRecoveryBookingBundlingImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<BookingBundling> bookingBundlingList = costService.listBookingBundlingByYearMonth(dateStr);
//            if(bookingBundlingList != null && bookingBundlingList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预定捆包费已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<BookingBundling> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                BookingBundling detail = new BookingBundling();

                //捆包方法
                String bundlingMethodStr = null;
                //捆包费
                String bundlingCostStr = null;

                if (i == 0) {//去掉第一行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    bundlingMethodStr = strArr.get(0).toString();//捆包方法
                }
                if (strArr.size() > 1) {
                    bundlingCostStr = strArr.get(1).toString();//捆包费
                }
                if(StringUtil.isStringEmpty(bundlingMethodStr)){
                    continue;
                }
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setBundlingMethod(bundlingMethodStr);
                if(StringUtils.isNotEmpty(bundlingCostStr)){
                    detail.setBundlingCost(new BigDecimal(bundlingCostStr).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                detail.setBookingCode(distinguish);
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addBookingBundlingImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导入予定回收（预定线盘回收计算）
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 14:34
     * @return
     */
    @PostMapping("/scheduledRecovery/reservedRecycling/import")
    @ResponseBody
    public Object scheduledRecoveryReservedRecyclingImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<ReservedRecycling> reservedRecyclingList = costService.listReservedRecyclingByYearMonth(dateStr);
//            if(reservedRecyclingList != null && reservedRecyclingList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预定线盘回收计算已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<ReservedRecycling> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                ReservedRecycling detail = new ReservedRecycling();

                //原料
                String rawMaterialStr = null;
                //线盘名
                String wireDiscNameStr = null;
                //预定使用量
                String scheduledUsageStr = null;
                //金额
                String amountStr = null;

                if (i == 0) {//去掉第一行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    rawMaterialStr = strArr.get(0).toString();//原料
                }
                if (strArr.size() > 1) {
                    wireDiscNameStr = strArr.get(1).toString();//线盘名
                }
                if (strArr.size() > 2) {
                    scheduledUsageStr = strArr.get(2).toString();//预定使用量
                }
                if (strArr.size() > 3) {
                    amountStr = strArr.get(3).toString();//金额
                }
                if(StringUtil.isStringEmpty(rawMaterialStr)){
                    continue;
                }
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setRawMaterial(rawMaterialStr);
                detail.setWireDiscName(wireDiscNameStr);
                if(StringUtils.isNotEmpty(scheduledUsageStr)){
                    detail.setScheduledUsage(new BigDecimal(scheduledUsageStr).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(amountStr)){
                    detail.setAmount(new BigDecimal(amountStr).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                detail.setReservedCode(distinguish);
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addReservedRecyclingImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导入予定回收（预定屑使用量）
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 14:34
     * @return
     */
    @PostMapping("/scheduledRecovery/reserveCrumbsUsage/import")
    @ResponseBody
    public Object scheduledRecoveryReserveCrumbsUsageImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<ReserveCrumbsUsage> reserveCrumbsUsageList = costService.listReserveCrumbsUsageByYearMonth(dateStr);
//            if(reserveCrumbsUsageList != null && reserveCrumbsUsageList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预定屑使用量已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<ReserveCrumbsUsage> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                ReserveCrumbsUsage detail = new ReserveCrumbsUsage();

                //部门
                String departmentStr = null;
                //预定屑量
                String reserveCrumbsAmountStr = null;

                if (i == 0) {//去掉第一行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    departmentStr = strArr.get(0).toString();//部门
                }
                if (strArr.size() > 1) {
                    reserveCrumbsAmountStr = strArr.get(1).toString();//预定屑量
                }
                if(StringUtil.isStringEmpty(departmentStr)){
                    continue;
                }
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setDistinguish(distinguish);
                detail.setDepartment(departmentStr);
                if(StringUtils.isNotEmpty(reserveCrumbsAmountStr)){
                    detail.setReserveCrumbsAmount(new BigDecimal(reserveCrumbsAmountStr).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addReserveCrumbsUsageImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导入予定回收（预定芯线使用量）
     * @throws
     * <AUTHOR>
     * @create 2023/8/22 14:34
     * @return
     */
    @PostMapping("/scheduledRecovery/reserveCoreWireUsage/import")
    @ResponseBody
    public Object scheduledRecoveryReserveCoreWireUsageImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

//            List<ReserveCoreWireUsage> reserveCoreWireUsageList = costService.listReserveCoreWireUsageByYearMonth(dateStr);
//            if(reserveCoreWireUsageList != null && reserveCoreWireUsageList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预定芯线使用量已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<ReserveCoreWireUsage> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                ReserveCoreWireUsage detail = new ReserveCoreWireUsage();

                //部门
                String departmentStr = null;
                //提供尺寸
                String sizeStr = null;
                //芯线使用量
                String reserveCrumbsAmountStr = null;

                if (i == 0) {//去掉第一行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    departmentStr = strArr.get(0).toString();//部门
                }
                if (strArr.size() > 1) {
                    sizeStr = strArr.get(1).toString();//提供尺寸
                }
                if (strArr.size() > 2) {
                    reserveCrumbsAmountStr = strArr.get(2).toString();//芯线使用量
                }
                if(StringUtil.isStringEmpty(departmentStr)){
                    continue;
                }

                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setDistinguish(distinguish);
                detail.setDepartment(departmentStr);
                if(StringUtils.isNotEmpty(sizeStr)){
                    detail.setSize(new BigDecimal(sizeStr).setScale(3,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(reserveCrumbsAmountStr)){
                    detail.setReserveCrumbsAmount(new BigDecimal(reserveCrumbsAmountStr).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addReserveCoreWireUsageImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
    * 导入予定回收（预定油漆使用量）
    * @throws
    * <AUTHOR>
    * @create 2023/8/22 14:34
    * @return
    */
    @PostMapping("/scheduledRecovery/reservePaintUsage/import")
    @ResponseBody
    public Object scheduledRecoveryReservePaintUsageImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<ReservePaintUsage> reservePaintUsageList = costService.listReservePaintUsageByYearMonth(dateStr);
//            if(reservePaintUsageList != null && reservePaintUsageList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预定油漆使用量已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<ReservePaintUsage> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                ReservePaintUsage detail = new ReservePaintUsage();
                //原料
                String rawMaterialStr = null;
                //油漆品名
                String paintNameStr = null;
                //预定使用量
                String reserveUsageStr = null;
                //金额
                String reserveamountOfMoneyStr = null;
                if (i == 0) {//去掉第一行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    rawMaterialStr = strArr.get(0).toString();//原料
                }
                if (strArr.size() > 1) {
                    paintNameStr = strArr.get(1).toString();//油漆品名
                }
                if (strArr.size() > 2) {
                    reserveUsageStr = strArr.get(2).toString();//预定使用量
                }
                if (strArr.size() > 3) {
                    reserveamountOfMoneyStr = strArr.get(3).toString();//金额
                }
                if(StringUtil.isStringEmpty(rawMaterialStr)){
                    continue;
                }
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setDistinguish(distinguish);
                detail.setRawMaterial(rawMaterialStr);
                detail.setPaintName(paintNameStr);
                if(StringUtils.isNotEmpty(reserveUsageStr)){
                    detail.setReserveUsage(new BigDecimal(reserveUsageStr).setScale(7,BigDecimal.ROUND_HALF_UP));
                }
                if(StringUtils.isNotEmpty(reserveamountOfMoneyStr)){
                    detail.setReserveamountOfMoney(new BigDecimal(reserveamountOfMoneyStr).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addReservePaintUsageImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导入予定回收（预订运费）
     * @param filename
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/scheduledRecovery/reserveFreight/import")
    @ResponseBody
    public Object scheduledRecoveryReserveFreightImport(MultipartFile filename, String dateStr,String distinguish){
        int importId = 0;
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED.getCode(), "登录超时，请重新登录！");
            }

            List<ReserveFreight> reserveFreightList = costService.listReserveFreightByYearMonth(dateStr);
//            if(reserveFreightList != null && reserveFreightList.size() > 0){
//                return new HrResult(CommonReturnCode.FAILED.getCode(), dateStr+"预订运费已存在，请勿重复导入！");
//            }
            int successImport = 0;
            String temp_file = FileUtils.copyFile(filename);
            List<List<Object>> list = ReadExcelUtil.readExcelSheet(new File(FileUtils.getSavePath() + "//" + temp_file),ReadExcelUtil.sheet1);
            List<Object> strArr;//读取出的每一行

            ArrayList<String> errMsgList = new ArrayList<>();//错误集合
            String errMsg = null;//错误信息

            List<ReserveFreight> detailList = new ArrayList<>();//添加数据集合
            for (int i = 0; i < list.size(); i++) {
                ReserveFreight detail = new ReserveFreight();
                String shippingMethodStr = null;
                String freightStr = null;
                if (i == 0) {//去掉第一行
                    continue;
                }
                strArr = list.get(i);
                errMsg = "序号：" + (i + 1) + ",";
                if (strArr.size() > 0) {
                    shippingMethodStr = strArr.get(0).toString();//运送方法
                }
                if (strArr.size() > 1) {
                    freightStr = strArr.get(1).toString();//运费
                }
                if(StringUtil.isStringEmpty(shippingMethodStr)){
                    continue;
                }
                detail.setYear(Integer.parseInt(dateStr.split("-")[0]));
                detail.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                detail.setYearMonth(dateStr);
                detail.setDistinguish(distinguish);
                detail.setShippingMethod(shippingMethodStr);
                if(StringUtils.isNotEmpty(freightStr)){
                    detail.setFreight(new BigDecimal(freightStr).setScale(8,BigDecimal.ROUND_HALF_UP));
                }
                User user = userService.getById(authorizingUser.getUserId());
                if(user != null){
                    detail.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
                    detail.setCreatorName(user.getUserName());
                    detail.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                detailList.add(detail);
            }
            if (errMsgList != null && errMsgList.size() > 0) {
                ImportBean importBean = new ImportBean();
                importBean.setSuccessImport(successImport);
                importBean.setErrMsgList(errMsgList);
                return new HrResult(CommonReturnCode.FAILED, importBean);
            } else {
                ExcelImport record = new ExcelImport();
//                record.setState(ExcelImport.STATE_NORMAL);
                record.setYearMonth(dateStr);
                record.setYear(Integer.parseInt(dateStr.split("-")[0]));
                record.setMonth(Integer.parseInt(dateStr.split("-")[1]));
                record.setCreatorId(authorizingUser.getUserId().intValue());
                record.setCreatorName(authorizingUser.getUserName());
                record.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                importId = costService.addReserveFreightImport(record,detailList);
                return new HrResult(CommonReturnCode.SUCCESS);
            }
        } catch (Exception e) {
//            if(importId !=0){
//                try {
//                    costService.modifyImportState(importId, ExcelImport.STATE_DELETED,
//                            SingletonLoginUtils.getUser().getUserId().intValue(),
//                            SingletonLoginUtils.getUser().getUserName());
//                } catch (Exception exception) {
//                    exception.printStackTrace();
//                }
//            }
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }


    /*=================================报表1单价维护登录======================================*/
    /**
     * 单价列表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/unitPrice/list/view")
    public String unitPriceListView(Model model) throws Exception{
        return "/modules/cost/unitPrice_list";
    }
    
    /**
     * 单价列表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/unitPrice/listPage")
    @ResponseBody
    public Object unitPriceListPage(short isSearch,String year,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            UnitPriceRecordDTO unitPriceRecordDTO = statService.listUnitPriceRecordPage(year, pageInfo);
            return new HrPageResult(unitPriceRecordDTO.getUnitPriceRecordList(), unitPriceRecordDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<PaintPrice>(), 0);
        }
    }
    
    /**
     * 新增单价页面
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/unitPrice/add/view")
    public Object unitPriceAdd(Model model) throws Exception{
        return "/modules/cost/unitPrice_add";
    }

    
    /**
     * 编辑单价页面
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/unitPrice/modify/view")
    public Object unitPriceEdit(Model model,int statId) throws Exception{

        UnitPriceRecord unitPriceRecord = statService.selectUnitPriceRecord(statId);
        model.addAttribute("unitPriceRecord",unitPriceRecord);
        return "/modules/cost/unitPrice_edit";
    }
    
    /**
     * 新增单价
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/unitPrice/add")
    @ResponseBody
    public Object unitPriceAdd(String year,String electricUnitPrice,String generalUnitPrice,String temporaryUnitPrice,String saveDamageUnitPrice
            ,String wrEMUnitPrice,String wrEFUnitPrice,String wrERUnitPrice,String wrEHUnitPrice,String tranUnitPrice,String wireUnitPrice
            ,String wireRecycling,String cuCrumbsUnitPrice,String gasUnitPrice,String nitrogenUnitPrice,Integer statId) throws Exception{
        try{
            UnitPriceRecord unitPriceRecord = new UnitPriceRecord();
            if(StringUtils.isNotEmpty(year)){
                unitPriceRecord.setYear(year);
            }
            if(StringUtils.isNotEmpty(electricUnitPrice)){
            	unitPriceRecord.setElectricUnitPrice(new BigDecimal(electricUnitPrice));
            }
            if(StringUtils.isNotEmpty(generalUnitPrice)){
                unitPriceRecord.setGeneralUnitPrice(new BigDecimal(generalUnitPrice));
            }
            if(StringUtils.isNotEmpty(temporaryUnitPrice)){
                unitPriceRecord.setTemporaryUnitPrice(new BigDecimal(temporaryUnitPrice));
            }
            if(StringUtils.isNotEmpty(saveDamageUnitPrice)){
                unitPriceRecord.setSaveDamageUnitPrice(new BigDecimal(saveDamageUnitPrice));
            }
            if(StringUtils.isNotEmpty(wrEMUnitPrice)){
                unitPriceRecord.setWrEMUnitPrice(new BigDecimal(wrEMUnitPrice));
            }
            if(StringUtils.isNotEmpty(wrEFUnitPrice)){
                unitPriceRecord.setWrEFUnitPrice(new BigDecimal(wrEFUnitPrice));
            }
            if(StringUtils.isNotEmpty(wrERUnitPrice)){
                unitPriceRecord.setWrERUnitPrice(new BigDecimal(wrERUnitPrice));
            }
            if(StringUtils.isNotEmpty(wrEHUnitPrice)){
                unitPriceRecord.setWrEHUnitPrice(new BigDecimal(wrEHUnitPrice));
            }
            if(StringUtils.isNotEmpty(tranUnitPrice)){
                unitPriceRecord.setTranUnitPrice(new BigDecimal(tranUnitPrice));
            }
            if(StringUtils.isNotEmpty(wireUnitPrice)){
                unitPriceRecord.setWireUnitPrice(new BigDecimal(wireUnitPrice));
            }
            if(StringUtils.isNotEmpty(wireRecycling)){
                unitPriceRecord.setWireRecycling(new BigDecimal(wireRecycling));
            }
            if(StringUtils.isNotEmpty(cuCrumbsUnitPrice)){
                unitPriceRecord.setCuCrumbsUnitPrice(new BigDecimal(cuCrumbsUnitPrice));
            }
            if(StringUtils.isNotEmpty(gasUnitPrice)){
                unitPriceRecord.setGasUnitPrice(new BigDecimal(gasUnitPrice));
            }
            if(StringUtils.isNotEmpty(nitrogenUnitPrice)){
                unitPriceRecord.setNitrogenUnitPrice(new BigDecimal(nitrogenUnitPrice));
            }

            statService.addUnitPriceRecord(unitPriceRecord);
            return new HrResult(CommonReturnCode.SUCCESS);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
        }
    }
    
    /**
     * 编辑单价
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/unitPrice/edit")
    @ResponseBody
    public Object unitPriceEdit(String year,String electricUnitPrice,String generalUnitPrice,String temporaryUnitPrice,String saveDamageUnitPrice
            ,String wrEMUnitPrice,String wrEFUnitPrice,String wrERUnitPrice,String wrEHUnitPrice,String tranUnitPrice,String wireUnitPrice
            ,String wireRecycling,String cuCrumbsUnitPrice,String gasUnitPrice,String nitrogenUnitPrice,Integer statId) throws Exception{
        try{
            UnitPriceRecord unitPriceRecord = new UnitPriceRecord();
            unitPriceRecord.setStatId(statId);
            if(StringUtils.isNotEmpty(year)){
                unitPriceRecord.setYear(year);
            }
            if(StringUtils.isNotEmpty(electricUnitPrice)){
            	unitPriceRecord.setElectricUnitPrice(new BigDecimal(electricUnitPrice));
            }
            if(StringUtils.isNotEmpty(generalUnitPrice)){
                unitPriceRecord.setGeneralUnitPrice(new BigDecimal(generalUnitPrice));
            }
            if(StringUtils.isNotEmpty(temporaryUnitPrice)){
                unitPriceRecord.setTemporaryUnitPrice(new BigDecimal(temporaryUnitPrice));
            }
            if(StringUtils.isNotEmpty(saveDamageUnitPrice)){
                unitPriceRecord.setSaveDamageUnitPrice(new BigDecimal(saveDamageUnitPrice));
            }
            if(StringUtils.isNotEmpty(wrEMUnitPrice)){
                unitPriceRecord.setWrEMUnitPrice(new BigDecimal(wrEMUnitPrice));
            }
            if(StringUtils.isNotEmpty(wrEFUnitPrice)){
                unitPriceRecord.setWrEFUnitPrice(new BigDecimal(wrEFUnitPrice));
            }
            if(StringUtils.isNotEmpty(wrERUnitPrice)){
                unitPriceRecord.setWrERUnitPrice(new BigDecimal(wrERUnitPrice));
            }
            if(StringUtils.isNotEmpty(wrEHUnitPrice)){
                unitPriceRecord.setWrEHUnitPrice(new BigDecimal(wrEHUnitPrice));
            }
            if(StringUtils.isNotEmpty(tranUnitPrice)){
                unitPriceRecord.setTranUnitPrice(new BigDecimal(tranUnitPrice));
            }
            if(StringUtils.isNotEmpty(wireUnitPrice)){
                unitPriceRecord.setWireUnitPrice(new BigDecimal(wireUnitPrice));
            }
            if(StringUtils.isNotEmpty(wireRecycling)){
                unitPriceRecord.setWireRecycling(new BigDecimal(wireRecycling));
            }
            if(StringUtils.isNotEmpty(cuCrumbsUnitPrice)){
                unitPriceRecord.setCuCrumbsUnitPrice(new BigDecimal(cuCrumbsUnitPrice));
            }
            if(StringUtils.isNotEmpty(gasUnitPrice)){
                unitPriceRecord.setGasUnitPrice(new BigDecimal(gasUnitPrice));
            }
            if(StringUtils.isNotEmpty(nitrogenUnitPrice)){
                unitPriceRecord.setNitrogenUnitPrice(new BigDecimal(nitrogenUnitPrice));
            }

            statService.modifyUnitPriceRecord(unitPriceRecord);
            return new HrResult(CommonReturnCode.SUCCESS);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
        }
    }

    /*=================================报表1出库量/入库量 维护登录======================================*/
    /**
     * 入库量页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/boundQuantity/list/view")
    public String boundQuantityListView(Model model) throws Exception{
        return "/modules/cost/boundQuantity_list";
    }

    /**
     * 入库量
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/boundQuantity/listPage")
    @ResponseBody
    public Object boundQuantityListPage(short isSearch,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            BoundQuantityDTO boundQuantityDTO = statService.getBoundQuantityList(pageInfo);
            return new HrPageResult(boundQuantityDTO.getBoundQuantitys(), boundQuantityDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<PaintPrice>(), 0);
        }
    }

    /**
     * 编辑页面
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/boundQuantity/modify/view")
    public Object boundQuantityEdit(Model model,int statId) throws Exception{

        BoundQuantity boundQuantity = statService.selectBoundQuantityById(statId);
        model.addAttribute("boundQuantity",boundQuantity);
        return "/modules/cost/boundQuantity_modify";
    }

    /**
     * 编辑
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/boundQuantity/edit")
    @ResponseBody
    public Object boundQuantityEdit(String yearMonth,BigDecimal emInboundQuantity,BigDecimal emOutboundQuantity,BigDecimal efInboundQuantity
            ,BigDecimal efOutboundQuantity,BigDecimal ef09InboundQuantity,BigDecimal ef09OutboundQuantity,BigDecimal erInboundQuantity,BigDecimal erOutboundQuantity
            ,BigDecimal ehInboundQuantity1,BigDecimal ehOutboundQuantity1,BigDecimal ehInboundQuantity2,BigDecimal ehOutboundQuantity2,BigDecimal uftxInboundQuantity
            ,BigDecimal uftxOutboundQuantity,BigDecimal ufxxInboundQuantity,BigDecimal ufxxOutboundQuantity,Integer statId) throws Exception{
        try{
            BoundQuantity boundQuantity = new BoundQuantity(statId,yearMonth,emInboundQuantity,efInboundQuantity,ef09InboundQuantity
            ,erInboundQuantity,ehInboundQuantity1,ehInboundQuantity2,uftxInboundQuantity,ufxxInboundQuantity,emOutboundQuantity,efOutboundQuantity
                    ,ef09OutboundQuantity,erOutboundQuantity,ehOutboundQuantity1,ehOutboundQuantity2,uftxOutboundQuantity,ufxxOutboundQuantity);

            statService.modifyBoundQuantity(boundQuantity);
            return new HrResult(CommonReturnCode.SUCCESS);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(),"系统异常");
        }
    }

    /**
     * 新增页面
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/boundQuantity/add/view")
    public Object boundQuantityAddView(Model model) throws Exception{
        return "/modules/cost/boundQuantity_add";
    }

    /**
     * 新增
     * @param boundQuantity
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/boundQuantity/add")
    @ResponseBody
    public Object boundQuantityAdd(BoundQuantity boundQuantity) throws Exception{
        try {
            //插入铜供应商表中
            statService.addBoundQuantity(boundQuantity);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("出入库新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/boundQuantity/remove")
    @ResponseBody
    public Object boundQuantityRemove(Integer statId) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            statService.removeBoundQuantity(statId,BoundQuantity.STATE_DELETED);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*=================================预定回收列表======================================*/
    /**
     * DirectRecycling 预定直接部门回收计算表 页面1
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/directRecycling/list/view")
    public String directRecyclingListView(Model model) throws Exception{
        return "/modules/cost/directRecycling_list";
    }
    /**
     * DirectRecycling 预定直接部门回收计算表列表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/directRecycling/list")
    @ResponseBody
    public Object directRecyclingListPage(Short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //DirectRecycling 预定直接部门回收计算表
            DirectRecyclingDTO directRecyclingDTO = costService.listDirectRecyclingPage(yearMonth,pageInfo);
            return new HrPageResult(directRecyclingDTO.getDirectRecyclings(), directRecyclingDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<DirectRecycling>(), 0);
        }
    }

    /**
     * 批量删除预定直接部门回收计算明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/directRecyclingl/delBatch")
    @ResponseBody
    public Object directRecyclingDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeDirectRecyclingByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 预定辅助部门回收计算补修辅材表 页面2
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/auxiliaryRecycling/list/view")
    public String auxiliaryRecyclingListView(Model model) throws Exception{
        return "/modules/cost/auxiliaryRecycling_list";
    }
    /**
     * 预定辅助部门回收计算补修辅材表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/auxiliaryRecycling/list")
    @ResponseBody
    public Object auxiliaryRecyclingListPage(Short isSearch, String yearMonth, String  auxiliaryCode, PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //AuxiliaryRecycling 预定辅助部门回收计算补修辅材表
            AuxiliaryRecyclingDTO auxiliaryRecyclingDTO = costService.listAuxiliaryRecyclingPage(yearMonth, auxiliaryCode, pageInfo);
            return new HrPageResult(auxiliaryRecyclingDTO.getAuxiliaryRecyclings(), auxiliaryRecyclingDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<AuxiliaryRecycling>(), 0);
        }
    }

    /**
     * 批量删除预定直接部门回收计算明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/auxiliaryRecycling/delBatch")
    @ResponseBody
    public Object auxiliaryRecyclingDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeAuxiliaryRecyclingByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 予定回收-预定辅助部门回收计算（人工费）表 页面3
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/auxiliaryRecyclingArtificial/list/view")
    public String auxiliaryRecyclingArtificialListView(Model model) throws Exception{
        return "/modules/cost/auxiliaryRecyclingArtificial_list";
    }
    /**
     * 予定回收-预定辅助部门回收计算（人工费）表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/auxiliaryRecyclingArtificial/list")
    @ResponseBody
    public Object auxiliaryRecyclingArtificialListPage(Short isSearch,String yearMonth, String  auxiliaryCode, PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //AuxiliaryRecyclingArtificial 预定辅助部门回收计算人工表
            AuxiliaryRecyclingArtificialDTO auxiliaryRecyclingArtificialDTO = costService.listAuxiliaryRecyclingArtificialPage(yearMonth, auxiliaryCode,pageInfo);
            return new HrPageResult(auxiliaryRecyclingArtificialDTO.getAuxiliaryRecyclingArtificials(), auxiliaryRecyclingArtificialDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<AuxiliaryRecyclingArtificial>(), 0);
        }
    }

    /**
     * 批量删除预定直接部门回收计算明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/auxiliaryRecyclingArtificial/delBatch")
    @ResponseBody
    public Object auxiliaryRecyclingArtificialDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeAuxiliaryRecyclingArtificialByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 予定回收-预定捆包费表 页面4
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/bookingBundling/list/view")
    public String bookingBundlingListView(Model model) throws Exception{
        return "/modules/cost/bookingBundling_list";
    }
    /**
     * 予定回收-预定捆包费表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/bookingBundling/list")
    @ResponseBody
    public Object bookingBundlingListPage(Short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //BookingBundling 预定捆包费表
            BookingBundlingDTO bookingBundlingDTO = costService.listBookingBundlingPage(yearMonth,pageInfo);
            return new HrPageResult(bookingBundlingDTO.getBookingBundlings(), bookingBundlingDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<BookingBundling>(), 0);
        }
    }

    /**
     * 批量删除预定直接部门回收计算明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/bookingBundling/delBatch")
    @ResponseBody
    public Object bookingBundlingDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeBookingBundlingByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 予定回收-预定线盘回收计算表 页面5
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/reservedRecycling/list/view")
    public String reservedRecyclingListView(Model model) throws Exception{
        return "/modules/cost/reservedRecycling_list";
    }
    /**
     * 予定回收-预定线盘回收计算表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/reservedRecycling/list")
    @ResponseBody
    public Object reservedRecyclingListPage(Short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //ReservedRecycling 预定线盘回收计算表
            ReservedRecyclingDTO reservedRecyclingDTO = costService.listReservedRecyclingPage(yearMonth,pageInfo);
            return new HrPageResult(reservedRecyclingDTO.getReservedRecyclings(), reservedRecyclingDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<ReservedRecycling>(), 0);
        }
    }

    /**
     * 批量删除预定线盘回收明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/reservedRecycling/delBatch")
    @ResponseBody
    public Object reservedRecyclingDelBatch(String costIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(costIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeReservedRecyclingByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 予定回收-预定屑使用量 页面6
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/reserveCrumbsUsage/list/view")
    public String reserveCrumbsUsageListView(Model model) throws Exception{
        return "/modules/cost/reserveCrumbsUsage_list";
    }
    /**
     * 予定回收-预定屑使用量
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/reserveCrumbsUsage/list")
    @ResponseBody
    public Object reserveCrumbsUsageListPage(Short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //ReserveCrumbsUsage 预定屑使用量
            ReserveCrumbsUsageDTO reserveCrumbsUsageDTO = costService.listReserveCrumbsUsagePage(yearMonth,pageInfo);
            return new HrPageResult(reserveCrumbsUsageDTO.getReserveCrumbsUsages(), reserveCrumbsUsageDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<ReserveCrumbsUsage>(), 0);
        }
    }

    /**
     * 批量删除预定线盘回收明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/reserveCrumbsUsage/delBatch")
    @ResponseBody
    public Object reserveCrumbsUsageDelBatch(String reserveCrumbsUsageIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(reserveCrumbsUsageIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeReserveCrumbsUsageByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 予定回收-预定芯线使用量 页面7
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/reserveCoreWireUsage/list/view")
    public String reserveCoreWireUsageListView(Model model) throws Exception{
        return "/modules/cost/reserveCoreWireUsage_list";
    }
    /**
     * 予定回收-预定芯线使用量
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/reserveCoreWireUsage/list")
    @ResponseBody
    public Object reserveCoreWireUsageListPage(Short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //ReserveCoreWireUsage 预定芯线使用量
            ReserveCoreWireUsageDTO reserveCoreWireUsageDTO = costService.listReserveCoreWireUsagePage(yearMonth,pageInfo);
            return new HrPageResult(reserveCoreWireUsageDTO.getReserveCoreWireUsages(), reserveCoreWireUsageDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<ReserveCoreWireUsage>(), 0);
        }
    }

    /**
     * 批量删除预定芯线使用量明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2024/6/07 10:43
     * @return
     */
    @PostMapping("/reserveCoreWireUsage/delBatch")
    @ResponseBody
    public Object reserveCoreWireUsageDelBatch(String reserveCoreWireUsageIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(reserveCoreWireUsageIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeReserveCoreWireUsageByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    
    /**
     * 予定回收-预定油漆使用量 页面8
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/reservePaintUsage/list/view")
    public String reservePaintUsageListView(Model model) throws Exception{
        return "/modules/cost/reservePaintUsage_list";
    }
    /**
     * 予定回收-预定油漆使用量
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/reservePaintUsage/list")
    @ResponseBody
    public Object reservePaintUsageListPage(Short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //ReserveCoreWireUsage 预定油漆使用量
            ReservePaintUsageDTO reservePaintUsageDTO = costService.listReservePaintUsagePage(yearMonth,pageInfo);
            return new HrPageResult(reservePaintUsageDTO.getReservePaintUsages(), reservePaintUsageDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<ReserveCoreWireUsage>(), 0);
        }
    }

    /**
     * 批量删除预定油漆使用量明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2024/6/07 10:43
     * @return
     */
    @PostMapping("/reservePaintUsage/delBatch")
    @ResponseBody
    public Object reservePaintUsageDelBatch(String reservePaintUsageIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(reservePaintUsageIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeReservePaintUsageByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 予定回收-预定运费 页面9
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/reserveFreight/list/view")
    public String reserveFreightListView(Model model) throws Exception{
        return "/modules/cost/reserveFreight_list";
    }
    /**
     * 予定回收-预定运费
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/reserveFreight/list")
    @ResponseBody
    public Object reserveFreightListPage(Short isSearch,String yearMonth,PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            //ReserveFreight 预定运费
            ReserveFreightDTO reserveFreightDTO = costService.listReserveFreightPage(yearMonth,pageInfo);
            return new HrPageResult(reserveFreightDTO.getReserveFreights(), reserveFreightDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<ReserveFreight>(), 0);
        }
    }

    /**
     * 批量删除预定油漆使用量明细
     * @param costIds
     * @throws Exception
     * <AUTHOR>
     * @create 2024/6/07 10:43
     * @return
     */
    @PostMapping("/reserveFreight/delBatch")
    @ResponseBody
    public Object reserveFreightDelBatch(String reserveFreightIds) throws Exception{
        try {
            List<String> costIdList = Arrays.asList(reserveFreightIds.split(","));
            if (!CollectionUtils.isEmpty(costIdList)){
                for(String costId : costIdList){
                    costService.removeReserveFreightByCostId(Integer.valueOf(costId));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }


}
