package com.hongru.pojo.dto;

import com.hongru.entity.cost.ReservePaintUsage;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class ReservePaintUsageDTO {

    private PageInfo pageInfo;

    private List<ReservePaintUsage> reservePaintUsages;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<ReservePaintUsage> getReservePaintUsages() {
        return reservePaintUsages;
    }

    public void setReservePaintUsages(List<ReservePaintUsage> reservePaintUsages) {
        this.reservePaintUsages = reservePaintUsages;
    }

    public ReservePaintUsageDTO(PageInfo pageInfo, List<ReservePaintUsage> reservePaintUsages) {
        this.pageInfo = pageInfo;
        this.reservePaintUsages = reservePaintUsages;
    }
}
