<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.xieFenXi.ScrapAmountMapper">

    <select id="listDailyScrapAmountForMonth" resultType="com.hongru.entity.xieFenXi.EMScrapAmountReport">
        select
        [设备号] as machineId,
        [产生原因] as causeBy,
        sum(屑量) as scrapAmounts
        from [屑分析].[dbo].[屑量表]
        <where>
            1 = 1
            <if test="machineId != null and machineId != ''">
                and CONVERT (VARCHAR (3),[设备号],121)  = #{machineId}
            </if>
            <if test="productDate != null and productDate != ''">
                AND CONVERT (VARCHAR(7), [日期], 120) = #{productDate}
            </if>
            AND [产生原因] !=''
        </where>
        GROUP BY [设备号],[产生原因]
        ORDER BY [设备号] ASC
    </select>
    
	<select id="listEFbreakNumInfo" resultType="com.hongru.entity.xieFenXi.XieFenXiBean">
		SELECT Convert(varchar(10),a.[日期] ,120) AS date,a.[设备号] AS machineId,
		sum(DATEDIFF(MINUTE,Convert(varchar(19),a.[停机时间],121),Convert(varchar(19),a.[恢复时间],121))) AS diffDate 
		FROM  [屑分析].[dbo].[稼动率表] a
    <where>
		a.[停机原因]='断线' AND Convert(varchar(7),a.日期 ,120) &gt;=#{timeStartStr} AND Convert(varchar(7),a.日期 ,120)  &lt;= #{timeEndStr} 
	</where>
		GROUP BY Convert(varchar(10),a.[日期] ,120),a.[设备号] 
		ORDER BY a.[设备号],Convert(varchar(10),a.[日期] ,120) ASC
	</select>
	
	<select id="listProductParametricInfo"  resultType="com.hongru.entity.xieFenXi.ProductParametricBean">
		SELECT
			p.[设备名称] as equipmentNo,
			p.[线头数] as lineNum
		FROM
			[PIMS].[dbo].[设备参数表] p
	</select>

	<select id="listDiffDate"  resultType="com.hongru.entity.xieFenXi.XieFenXiBean">
		SELECT Convert(varchar(10),a.[日期] ,120) as date,a.[设备号] AS machineId,sum(b.[机械时间]) AS machineTime,
		sum(DATEDIFF(MINUTE,Convert(varchar(19),b.[停机时间],121),Convert(varchar(19),b.[恢复时间],121))) AS diffDate 
		FROM [屑分析].[dbo].[产量表] a LEFT JOIN [屑分析].[dbo].[稼动率表] b ON
		a.[设备号]=b.[设备号] AND
		Convert(varchar(10),a.[日期] ,120) = Convert(varchar(10),b.[日期] ,120) AND
		a.[产品型号] = b.[产品型号] 
    <where>
    	Convert(varchar(7),a.日期 ,120) &gt;=#{timeStartStr} AND Convert(varchar(7),a.日期 ,120)  &lt;= #{timeEndStr} 
		AND a.[计算标记] = 1
	</where>
		GROUP BY Convert(varchar(10),a.[日期] ,120),a.[设备号]
		ORDER BY a.[设备号] ASC
	</select>
</mapper>