<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8" %>
<%@ include file="/WEB-INF/layouts/base.jsp" %>
<html>
<head>
    <title></title>
</head>
<body>
<form class="layui-form hr-form-add" action="" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label"><span class="star">*</span>日期:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="dateStr" name="dateStr"
                                   placeholder="" autocomplete="off" lay-verify="required">
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"><span class="star">*</span>文件:</label>
                        <div class="layui-input-block">
                            <input type="file" class="form-control" name="filename" id="excelFile">
                        </div>
                    </div>
                    <%--<div class="layui-inline layui-col-md12">
                        <label class="layui-form-label">导入注意事项：</label>
                        <div class="layui-input-block">
                            请下载最新的<a href="${ctxsta}/hongru/downloadfile/humanDepartmentWorkTimeImport.xlsx" style="color:red;">
                            《导入模板》</a>进行导入，否则将导致数据错乱!<br/>
                        </div>
                    </div>--%>
                    <div class="layui-inline layui-col-md12" id="errMsTr" style="display: none;">
                        <label class="layui-form-label">出错内容:</label>
                        <div class="layui-input-block">
                            <textarea class="layui-textarea" style="resize: none;" id="errmsg" readonly></textarea>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script>
        function closeAll() {
            parent.layer.closeAll();
        }

        layui.use(['form','laydate'], function () {
            var form = layui.form;
            var laydate = layui.laydate; //日期
            laydate.render({
                trigger: 'click',
                type: 'month',
                elem: '#dateStr', //指定元素
                btns: ['clear','confirm'],//默认的按钮为：清空、现在、确定,现在按钮取消，否则和时间范围限制冲突
            });
            //监听提交
            form.on('submit(formDemo)', function (data) {
                var excelFile = $("#excelFile").val();
                var excelFileRex = /\.(xls|xlsx)$/;
                if (excelFile == null || excelFile == "") {
                    layer.alert("请上传文件");
                    return false;
                } else {
                    if (!excelFileRex.test(excelFile)) {
                        layer.alert("文件必须是.xlsx");
                        return false;
                    }
                }
                var index = layer.load(2, {
                    shade: [0.1, '#fff']
                });
                $("#errMsTr").hide();
                $("#errmsg").text("")
                var form = document.querySelector("#submitForm");
                var formData = new FormData(form);
                formData.append('filename', $('input[type="file"]')[0].files[0]);
                $.ajax({
                    url: "${ctx}/costPrice/human/workPrice/import",
                    type: 'post',
                    cache: false,
                    data: formData,
                    processData: false, //因为data值是FormData对象,不需要对数据做处理
                    contentType: false, //因为是由<form>表单构造的FormData对象,且已经声明了属性enctype="multipart/form-data",所以这里设置为false
                    success: function (result) {
                        layer.closeAll();
                        if (result.code == 1) {
                            parent.layer.msg("操作成功!", {
                                shade: 0.3,
                                time: 1500
                            }, function () {
                                parent.search();
                                parent.layer.closeAll();
                            });
                        } else {
                            var importBean = result.data;
                            if (importBean != null) {
                                var errMsgList = importBean.errMsgList;
                                var errmsg = "";
                                if (errMsgList.length > 0) {
                                    errmsg = "有" + errMsgList.length + "条出错,出错信息为:" + errMsgList;
                                }
                                $("#errmsg").text(errmsg)
                                $("#errMsTr").show();
                                $("#excelFile").val("");
                            } else {
                                layer.alert(result.message);
                            }
                        }
                    }
                });
                return false;
            });
        });
    </script>
</myfooter>
</body>
</html>