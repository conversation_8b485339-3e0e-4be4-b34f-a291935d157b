package com.hongru.pojo.dto;

import com.hongru.entity.cost.WireDiscCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class WireDiscCostDTO {

    private PageInfo pageInfo;

    private List<WireDiscCost> wireDiscCostList;

    public WireDiscCostDTO(PageInfo pageInfo, List<WireDiscCost> wireDiscCostList) {
        super();
        this.pageInfo = pageInfo;
        this.wireDiscCostList = wireDiscCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<WireDiscCost> getWireDiscCostList() {
        return wireDiscCostList;
    }

    public void setWireDiscCost(List<WireDiscCost> wireDiscCostList) {
        this.wireDiscCostList = wireDiscCostList;
    }
}
