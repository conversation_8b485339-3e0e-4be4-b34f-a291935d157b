<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ page import="com.hongru.common.util.Constants" %>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <title>个人信息</title>
  <link rel="stylesheet" href="${ctxsta}/common/layui/admin.css?time=20220112" media="all">
  <link rel="stylesheet" href="${ctxsta}/hongru/css/userinfo.css" media="all">
</head>
<body >
<div class="layui-fluid hr-layui-fluid">
<div class="layui-row layui-col-space15">
  <div class="layui-col-sm12 layui-col-md3">
    <div class="layui-card" style="min-height: 462px;">
      <div class="layui-card-body" style="padding: 25px;">
        <div class="text-center layui-text">
          <%--<div class="user-info-head" id="userInfoHead" >
            <c:if test="${user.picImg != null  && user.picImg != '' }">
              <img src="<%=Constants.IMAGE_OSS_PATH_READ%>${user.picImg}" alt=""/>
            </c:if>
            <c:if test="${user.picImg == null  || user.picImg == '' }">
              <img src="../../static/hongru/images/default.png" alt=""/>
            </c:if>
          </div>--%>
          <div class="user-info-head">
            <img src="../../static/hongru/images/default.png" alt=""/>
          </div>
          <h2 style="padding-top: 20px;">${user.realName}</h2>
          <p style="padding-top: 8px;">${user.userName}</p>
        </div>
        <div class="layui-text" style="padding-top: 30px;">
          <div class="user-info-list-item">
            <i class="layui-icon layui-icon-username"></i>
            <p>${user.roleName}</p>
          </div>
          <div class="user-info-list-item">
            <i class="layui-icon layui-icon-release"></i>
            <p>${user.organizationName}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="layui-col-sm12 layui-col-md9">
    <div class="layui-card">
      <div class="layui-tab layui-tab-brief" lay-filter="userInfoTab">
        <ul class="layui-tab-title">
          <li class="layui-this">基本信息</li>
          <li>修改密码</li>
        </ul>
        <div class="layui-tab-content" style="min-height: 400px;">
          <div class="layui-tab-item layui-show">
            <form class="layui-form" id="userInfoForm" lay-filter="userInfoForm"
                  style="max-width: 400px;padding: 25px 10px 0 0;">
              <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">姓名：</label>
                <div class="layui-input-block">
                  <input name="realName" value="${user.realName}" class="layui-input"
                         lay-verify="required" required/>
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">移动电话：</label>
                <div class="layui-input-block">
                  <input name="telephone" value="${user.telephone}" class="layui-input" />
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">电子邮箱：</label>
                <div class="layui-input-block">
                  <input name="email" value="${user.email}" class="layui-input" />
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">性别：</label>
                <div class="layui-input-block">
                  <input type="radio" name="sex" value="1" title="男" ${user.sex eq '1'?'checked':''}>
                  <input type="radio" name="sex" value="2" title="女" ${user.sex eq '2'?'checked':''}>
                  <input type="radio" name="sex" value="0" title="保密" ${user.sex eq '0'?'checked':''}>
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <input type="hidden" id="picImg" name="picImg" value="${user.picImg}" />
                  <button class="layui-btn" lay-filter="userInfoSubmit" lay-submit>
                  	<i class="layui-icon layui-icon-ok"></i>更新基本信息
                  </button>
                </div>
              </div>
            </form>
          </div>
          <div class="layui-tab-item">
            <form class="layui-form" id="pwdForm" lay-filter="pwdForm"
                  style="max-width: 400px;padding: 25px 10px 0 0;">
              <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">原密码</label>
                <div class="layui-input-block">
                  <input type="password" id="nowPassword" name="nowPassword" value="" class="layui-input"
                         lay-verify="required" required/>
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">新密码</label>
                <div class="layui-input-block">
                  <input type="password" id="newPassword" name="newPassword" value="" class="layui-input"
                         lay-verify="required" required/>
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">确认密码</label>
                <div class="layui-input-block">
                  <input type="password" id="confirmPwd" name="confirmPwd" value="" class="layui-input"
                         lay-verify="required" required/>
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">

                  <button class="layui-btn" lay-filter="pwdSubmit" lay-submit>
                  	<i class="layui-icon layui-icon-ok"></i>修改密码</button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>

<script type="text/javascript" src="${ctxsta}/common/layui/layui.js"></script>
<script type="text/javascript" src="${ctxsta}/common/layui/common.js?v=318"></script>
<script src="${ctxsta}/hongru/js/admin/admin_user_info.js?timer = 2"></script>
</body>
</html>
