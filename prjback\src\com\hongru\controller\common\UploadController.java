package com.hongru.controller.common;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.HtmlUtils;

import com.hongru.base.BaseController;
import com.hongru.common.enums.CloudServiceEnum;
import com.hongru.common.enums.WebSiteFileBelongEnum;
import com.hongru.common.result.HrResult;
import com.hongru.common.util.Constants;
import com.hongru.common.util.ServletUtils;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.ImageLog;
import com.hongru.factory.CloudStorageFactory;
import com.hongru.service.IBaseCloudStorageService;
import com.hongru.support.upload.UploadManager;
import com.hongru.support.upload.UploadResult;

import sun.misc.BASE64Decoder;

/**
 * 
* 类名称：UploadController   
* 类描述：文件上传表示层控制器   
* 创建人：hongru   
* 创建时间：2017年4月10日 上午3:20:21   
*
 */
@Controller
@RequestMapping(value="/uploads")
public class UploadController extends BaseController{
	
	private static final Logger logger = LoggerFactory.getLogger(UploadController.class);

	@Autowired
	private CloudStorageFactory cloudStorageFactory;

	/**
	 * POST 广告图片上传
	 * @return
	 */
	@PostMapping(value = "/shopOss", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadShopImg(MultipartFile shop_file) {
		if (!shop_file.isEmpty()) {
			try {
				// 判断文件的MIMEtype
				String type = shop_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}
				IBaseCloudStorageService baseCloudStorageService = cloudStorageFactory
						.getBaseCloudStorageService(CloudServiceEnum.ALIYUN);
				ImageLog imageLog = baseCloudStorageService.upload(shop_file.getBytes(), "Shop",
						shop_file.getOriginalFilename());
				if(imageLog != null && imageLog.getImageUrl() != null && !imageLog.getImageUrl().equals("")) {
					String imageUrl = imageLog.getImageUrl();
					String finalImageUrl= imageUrl.replaceAll("http://", "http://"+imageLog.getBucket()+".");
					return new HrResult(CommonReturnCode.SUCCESS, finalImageUrl);
				}

			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}

	/**
	 * POST 广告图片上传
	 * @return
	 */
	@PostMapping(value = "/advert", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadAdvert(MultipartFile advert_file) {
		if (!advert_file.isEmpty()) {
			try {
				// 判断文件的MIMEtype
				String type = advert_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}

				UploadResult uploadResult = UploadManager.upload(ServletUtils.getRequest(), advert_file,
						WebSiteFileBelongEnum.IMAGES.getBelong(), WebSiteFileBelongEnum.ADVERT.getBelong());

				if (uploadResult.getResult()) {
					return new HrResult(CommonReturnCode.SUCCESS, uploadResult.getSavaPath());
				}
			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}	
	
	
	/**
	 * POST 用户头像上传
	 * @return
	 */
	@PostMapping(value = "/avatar", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadAvatar(MultipartFile multipartFile, String avatar_src, String avatar_data) {
		if (!multipartFile.isEmpty()) {
			try {

				// 判断文件的MIMEtype
				String type = multipartFile.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}

				UploadResult uploadResult = UploadManager.upload(ServletUtils.getRequest(), multipartFile,
						HtmlUtils.htmlUnescape(avatar_data), WebSiteFileBelongEnum.IMAGES.getBelong(),
						WebSiteFileBelongEnum.AVATAR.getBelong());

				if (uploadResult.getResult()) {
					return new HrResult(CommonReturnCode.SUCCESS, uploadResult.getSavaPath());
				}
			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}
	
	/**
	 * POST 门店图片上传
	 * @return
	 */
	@PostMapping(value = "/shop", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadShop(MultipartFile shop_file) {
		if (!shop_file.isEmpty()) {
			try {
				// 判断文件的MIMEtype
				String type = shop_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}

				UploadResult uploadResult = UploadManager.upload(ServletUtils.getRequest(), shop_file,
						WebSiteFileBelongEnum.IMAGES.getBelong(), WebSiteFileBelongEnum.SHOP.getBelong());

				if (uploadResult.getResult()) {
					return new HrResult(CommonReturnCode.SUCCESS, uploadResult.getSavaPath());
				}
			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}
	
	@PostMapping(value = "/coachOss", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadCoachImg(MultipartFile coach_file) {
		if (!coach_file.isEmpty()) {
			try {
				// 判断文件的MIMEtype
				String type = coach_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}
				IBaseCloudStorageService baseCloudStorageService = cloudStorageFactory
						.getBaseCloudStorageService(CloudServiceEnum.ALIYUN);
				ImageLog imageLog = baseCloudStorageService.upload(coach_file.getBytes(), "Coach",
						coach_file.getOriginalFilename());
				if(imageLog != null && imageLog.getImageUrl() != null && !imageLog.getImageUrl().equals("")) {
					String imageUrl = imageLog.getImageUrl();
					String finalImageUrl= imageUrl.replaceAll("http://", "http://"+imageLog.getBucket()+".");
					return new HrResult(CommonReturnCode.SUCCESS, finalImageUrl);
				}

			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}
	
	@PostMapping(value = "/shopPicOss", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadshopPicImg(MultipartFile shopPic_file) {
		if (!shopPic_file.isEmpty()) {
			try {
				// 判断文件的MIMEtype
				String type = shopPic_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}
				IBaseCloudStorageService baseCloudStorageService = cloudStorageFactory
						.getBaseCloudStorageService(CloudServiceEnum.ALIYUN);
				ImageLog imageLog = baseCloudStorageService.upload(shopPic_file.getBytes(), "ShopPic",
						shopPic_file.getOriginalFilename());
				if(imageLog != null && imageLog.getImageUrl() != null && !imageLog.getImageUrl().equals("")) {
					String imageUrl = imageLog.getImageUrl();
					String finalImageUrl= imageUrl.replaceAll("http://", "http://"+imageLog.getBucket()+".");
					return new HrResult(CommonReturnCode.SUCCESS, finalImageUrl);
				}

			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}
	
	@PostMapping(value = "/adOss", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadadImg(MultipartFile ad_file) {
		if (!ad_file.isEmpty()) {
			if(ad_file.getSize() >=512000) {
				return new  HrResult(CommonReturnCode.FILE_SIZE_OVER);
			}
			try {
				// 判断文件的MIMEtype
				String type = ad_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}
				IBaseCloudStorageService baseCloudStorageService = cloudStorageFactory
						.getBaseCloudStorageService(CloudServiceEnum.ALIYUN);
				ImageLog imageLog = baseCloudStorageService.upload(ad_file.getBytes(), "Ad",
						ad_file.getOriginalFilename());
				if(imageLog != null && imageLog.getImageUrl() != null && !imageLog.getImageUrl().equals("")) {
					String imageUrl = imageLog.getImageUrl();
					String finalImageUrl= imageUrl.replaceAll("http://", "http://"+imageLog.getBucket()+".");
					return new HrResult(CommonReturnCode.SUCCESS, finalImageUrl);
				}

			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}
	
	@PostMapping(value = "/mapOss", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadMapImg(MultipartFile map_file) {
		if (!map_file.isEmpty()) {
			try {
				// 判断文件的MIMEtype
				String type = map_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}
				IBaseCloudStorageService baseCloudStorageService = cloudStorageFactory
						.getBaseCloudStorageService(CloudServiceEnum.ALIYUN);
				ImageLog imageLog = baseCloudStorageService.upload(map_file.getBytes(), "Map",
						map_file.getOriginalFilename());
				if(imageLog != null && imageLog.getImageUrl() != null && !imageLog.getImageUrl().equals("")) {
					String imageUrl = imageLog.getImageUrl();
					String finalImageUrl= imageUrl.replaceAll("http://", "http://"+imageLog.getBucket()+".");
					return new HrResult(CommonReturnCode.SUCCESS, finalImageUrl);
				}

			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}
	
	@PostMapping(value = "/articleOss", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadArticleImg(MultipartFile article_file) {
		if (!article_file.isEmpty()) {
			try {
				// 判断文件的MIMEtype
				String type = article_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}
				IBaseCloudStorageService baseCloudStorageService = cloudStorageFactory
						.getBaseCloudStorageService(CloudServiceEnum.ALIYUN);
				ImageLog imageLog = baseCloudStorageService.upload(article_file.getBytes(), "Map",
						article_file.getOriginalFilename());
				if(imageLog != null && imageLog.getImageUrl() != null && !imageLog.getImageUrl().equals("")) {
					String imageUrl = imageLog.getImageUrl();
					String finalImageUrl= imageUrl.replaceAll("http://", "http://"+imageLog.getBucket()+".");
					return new HrResult(CommonReturnCode.SUCCESS, finalImageUrl);
				}

			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}
	
	@PostMapping(value = "/mobileSumOss", produces = "application/json;charset=utf-8")
	@ResponseBody
	public Object uploadMobileShopImg(MultipartFile mobile_file) {
		if (!mobile_file.isEmpty()) {
			try {
				// 判断文件的MIMEtype
				String type = mobile_file.getContentType();
				if (type == null || !type.toLowerCase().startsWith("image/")) {
					return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "不支持的文件类型，仅支持图片!");
				}
				IBaseCloudStorageService baseCloudStorageService = cloudStorageFactory
						.getBaseCloudStorageService(CloudServiceEnum.ALIYUN);
				ImageLog imageLog = baseCloudStorageService.upload(mobile_file.getBytes(), "Map",
						mobile_file.getOriginalFilename());
				if(imageLog != null && imageLog.getImageUrl() != null && !imageLog.getImageUrl().equals("")) {
					String imageUrl = imageLog.getImageUrl();
					String finalImageUrl= imageUrl.replaceAll("http://", "http://"+imageLog.getBucket()+".");
					return new HrResult(CommonReturnCode.SUCCESS, finalImageUrl);
				}

			} catch (Exception e) {
				logger.error("ImageUploadController.uploadHeadPortrait={}", e);
				return new HrResult(CommonReturnCode.UNKNOWN_ERROR);
			}
		}
		return new HrResult(CommonReturnCode.BAD_REQUEST.getCode(), "图片不存在,请确认图片地址重新上传!");
	}

	/**
	 * 上传base64
	 * @param imgBase64 图片base64
	 * @return
	 */
	@PostMapping(value = "/img/cropper/oss")
	@ResponseBody
	public Object uploadCropperImg(String imgBase64){
		try {
			String fileName="裁剪文件.png";
			BASE64Decoder decoder = new BASE64Decoder();
			imgBase64 = imgBase64.substring(30);
			imgBase64 = URLDecoder.decode(imgBase64,"UTF-8");
			byte[] decodedBytes = decoder.decodeBuffer(imgBase64);// 将字符串格式的imagedata转为二进制流（biye[])的decodedBytes
			for(int i=0;i<decodedBytes.length;++i){
				if(decodedBytes[i]<0) {
					//调整异常数据
					decodedBytes[i]+=256;
				}
			}
			IBaseCloudStorageService baseCloudStorageService = cloudStorageFactory
					.getBaseCloudStorageService(CloudServiceEnum.ALIYUN);
			String uploadPath = Constants.IMAGE_UPLOAD_PATH;//wechat_img
			//上传后可直接下载
			ImageLog imageLog = baseCloudStorageService.upload(decodedBytes, uploadPath,
					fileName);
			String finalImageUrl ="";
			if(imageLog != null && imageLog.getImageUrl() != null && !imageLog.getImageUrl().equals("")) {
				String imageUrl = imageLog.getImageUrl();
				//截取成/69f131b962b.png
				finalImageUrl= imageUrl.substring(imageUrl.lastIndexOf("/"),imageUrl.length());
			}
			HashMap<String,String> resultMap = new HashMap<>();
			resultMap.put("finalImageUrl",finalImageUrl);
			resultMap.put("fileName",fileName);
			return new HrResult(CommonReturnCode.SUCCESS, resultMap);
		} catch (IOException e) {
			e.printStackTrace();
			return new HrResult(CommonReturnCode.FAILED.getCode(),"操作失败");
		}
	}
}
