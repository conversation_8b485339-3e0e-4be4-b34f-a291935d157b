package com.hongru.entity.system;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("hr_system_option")
public class SystemOption {
	/* 标识 */
	@TableId(value="systemOptionId", type= IdType.AUTO)
	private int systemOptionId;
	private String cnName;
	private String enName;
	private String content;
	private int createdTimestamp;
	private int creatorId;
	private String creatorName;
	private int lastModified;
	private int lastModifierId;
	private String lastModifierName;
	
	public int getSystemOptionId() {
		return systemOptionId;
	}
	public void setSystemOptionId(int systemOptionId) {
		this.systemOptionId = systemOptionId;
	}
	public String getCnName() {
		return cnName;
	}
	public void setCnName(String cnName) {
		this.cnName = cnName;
	}
	public String getEnName() {
		return enName;
	}
	public void setEnName(String enName) {
		this.enName = enName;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public int getCreatedTimestamp() {
		return createdTimestamp;
	}
	public void setCreatedTimestamp(int createdTimestamp) {
		this.createdTimestamp = createdTimestamp;
	}
	public int getCreatorId() {
		return creatorId;
	}
	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public int getLastModified() {
		return lastModified;
	}
	public void setLastModified(int lastModified) {
		this.lastModified = lastModified;
	}
	public int getLastModifierId() {
		return lastModifierId;
	}
	public void setLastModifierId(int lastModifierId) {
		this.lastModifierId = lastModifierId;
	}
	public String getLastModifierName() {
		return lastModifierName;
	}
	public void setLastModifierName(String lastModifierName) {
		this.lastModifierName = lastModifierName;
	}
}
