package com.hongru.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class YearUtil{

	/**
	 * 年度取得（月度用，新年度从每年的4月份开始）
	 * 
	 * @param
	 * @return String
	 */
	public static String getYear(String param) throws Exception {
		if (param == null) {
			return null;
		} else {
	    	// 根据选取的年月获取年度
	    	String year= param.substring(0, 4);
	    	String month= param.substring(param.length()-2);
	    	if(Integer.valueOf(month) < 4) {
	    		year= String.valueOf(Integer.valueOf(year)-1);
	    	}
			return year;
		}
	}
	
    /**
     * 根据时间范围取得下一年度(非年度从4月份开始算)
     * @param timeStartStr
     * @param timeEndStr
     * @throws Exception
     * <AUTHOR>
     * @create 2024/01/04 09:43
     * @return int
     * @throws ParseException 
     */
    public static int getNextYear(String timeStartStr) throws ParseException {
    	String yearStr = timeStartStr.substring(0, 4); // 截取开始日期的年度
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy"); // 定义格式化模式为"yyyy"，只保留四位数的年份部分
    	Date date = sdf.parse(yearStr); // 将字符串转换成Date对象
    	Calendar calendar = Calendar.getInstance(); // 创建Calendar对象
    	calendar.setTime(date); // 设置Calendar对象的时间为指定的Date对象
    	calendar.add(Calendar.YEAR, 1); // 添加一年到当前时间
    	int nextYear = calendar.get(Calendar.YEAR); // 获取新的年份值
    	return nextYear;
    }
}

