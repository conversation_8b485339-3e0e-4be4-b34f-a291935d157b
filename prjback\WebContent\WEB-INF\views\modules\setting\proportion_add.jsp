<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>人件比例添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/setting/proportion/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                   <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" ><span class="star">*</span>年度:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="year"  name="year" value=""   required=""  lay-verify="required"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>部门:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="departmentCode" name="departmentCode" value=""/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EM:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="em" name="em" value="0"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EF:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="ef" name="ef" value="0"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>UF:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="uf" name="uf" value="0"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>ER:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="er" name="er" value="0"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>EH:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="eh" name="eh" value="0"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/setting/proportion_add.js?time=1"></script>
</myfooter>
</body>
</html>
