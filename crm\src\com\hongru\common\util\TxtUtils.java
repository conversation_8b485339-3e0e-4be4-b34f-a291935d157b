package com.hongru.common.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

import com.hongru.common.poiExcelExport.bean.ReadTxtBean;
import com.hongru.common.poiExcelExport.bean.ReadTxtBeanForProductSite;

/**
* Text工具类
* <AUTHOR>
* @create 2022/11/09 10:41
*
*/
public class TxtUtils {

	/**传入txt路径读取txt文件
	 * @param txtPath
	 * @return 返回读取到的内容
	 */
	public static String readTxt(String txtPath) {
		File file = new File(txtPath);
		if(file.isFile() && file.exists()){
			FileInputStream fileInputStream = null;
			InputStreamReader inputStreamReader = null;
			BufferedReader bufferedReader = null;
			try {
				fileInputStream = new FileInputStream(file);
				inputStreamReader = new InputStreamReader(fileInputStream);
				bufferedReader = new BufferedReader(inputStreamReader);
				StringBuffer sb = new StringBuffer();
				String text = null;
				while((text = bufferedReader.readLine()) != null){
					sb.append(text);
				}
				return sb.toString();
			} catch (Exception e) {
				e.printStackTrace();
			}finally {
				try {
					if(fileInputStream !=null){
						fileInputStream.close();
					}
					if(inputStreamReader !=null){
						inputStreamReader.close();
					}
					if(bufferedReader !=null){
						bufferedReader.close();
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return null;
	}

	/**使用FileOutputStream来写入txt文件
	 * @param txtPath txt文件路径
	 * @param content 需要写入的文本
	 */
	public static void writeTxt(String txtPath,String content){
		FileOutputStream fileOutputStream = null;
		File file = new File(txtPath);
		try {
			if(file.exists()){
				//判断文件是否存在，如果不存在就新建一个txt
				file.createNewFile();
			}
			fileOutputStream = new FileOutputStream(file);
			fileOutputStream.write(content.getBytes());
			fileOutputStream.flush();
			fileOutputStream.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	* 读取量少品txt文件
	* @param file
	* @throws
	* <AUTHOR>
	* @create 2022/11/9 11:01
	* @return java.util.List<com.hongru.common.poiExcelExport.bean.ReadTextBean>
	*/
	public static List<ReadTxtBean> readFewProductTxt(File file) {
		List<ReadTxtBean> beanList = new ArrayList<>();
		if(file.isFile() && file.exists()){
			FileInputStream fileInputStream = null;
			InputStreamReader inputStreamReader = null;
			BufferedReader bufferedReader = null;
			try {
				fileInputStream = new FileInputStream(file);
				inputStreamReader = new InputStreamReader(fileInputStream);
				bufferedReader = new BufferedReader(inputStreamReader);
//				StringBuffer sb = new StringBuffer();
				String text = null;
				while((text = bufferedReader.readLine()) != null){
					if(!StringUtil.isStringEmpty(text)){
						String[] strArr = text.split(",");
						if(strArr.length>2){
							if(strArr[0].length()==14){
								ReadTxtBean bean = new ReadTxtBean();
								bean.setCode(strArr[0].trim());
								bean.setBarCode(strArr[0].trim().substring(1,6));
								bean.setBatchNo(strArr[0].trim().substring(6,9));
								bean.setSerialNo(strArr[0].trim().substring(9,13));
								bean.setWeight(strArr[1].trim());
								bean.setDate(strArr[2].trim());
								beanList.add(bean);
							}
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}finally {
				try {
					if(fileInputStream !=null){
						fileInputStream.close();
					}
					if(inputStreamReader !=null){
						inputStreamReader.close();
					}
					if(bufferedReader !=null){
						bufferedReader.close();
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return beanList;
	}

	/**
	* 读取未入库品txt文件
	* @param file
	* @throws
	* <AUTHOR>
	* @create 2022/11/9 11:08
	* @return java.util.List<com.hongru.common.poiExcelExport.bean.ReadTxtBean>
	*/
	public static List<ReadTxtBean> readUnStoreProductTxt(File file) {
		List<ReadTxtBean> beanList = new ArrayList<>();
		if(file.isFile() && file.exists()){
			FileInputStream fileInputStream = null;
			InputStreamReader inputStreamReader = null;
			BufferedReader bufferedReader = null;
			try {
				fileInputStream = new FileInputStream(file);
				inputStreamReader = new InputStreamReader(fileInputStream);
				bufferedReader = new BufferedReader(inputStreamReader);
//				StringBuffer sb = new StringBuffer();
				String text = null;
				while((text = bufferedReader.readLine()) != null){
					if(!StringUtil.isStringEmpty(text)){
						if(!StringUtil.isStringEmpty(text)&&  text.length()>=13){
							String code = text.replace(",","").trim();
							ReadTxtBean bean = new ReadTxtBean();
							bean.setCode(code);
							bean.setBarCode(code.substring(1,6));
							bean.setBatchNo(code.substring(6,9));
							bean.setSerialNo(code.substring(9,13));
							beanList.add(bean);
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}finally {
				try {
					if(fileInputStream !=null){
						fileInputStream.close();
					}
					if(inputStreamReader !=null){
						inputStreamReader.close();
					}
					if(bufferedReader !=null){
						bufferedReader.close();
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return beanList;
	}

	/**
	* 读取产品库位txt文件
	* @param file
	* @throws
	* <AUTHOR>
	* @create 2022/12/13 13:28
	* @return java.util.List<com.hongru.common.poiExcelExport.bean.ReadTxtBeanForProductSite>
	*/
	public static List<ReadTxtBeanForProductSite> readProductSiteTxt(File file) {
		List<ReadTxtBeanForProductSite> beanList = new ArrayList<>();
		if(file.isFile() && file.exists()){
			FileInputStream fileInputStream = null;
			InputStreamReader inputStreamReader = null;
			BufferedReader bufferedReader = null;
			try {
				fileInputStream = new FileInputStream(file);
				inputStreamReader = new InputStreamReader(fileInputStream);
				bufferedReader = new BufferedReader(inputStreamReader);
				String text = null;
				while((text = bufferedReader.readLine()) != null){
					if(!StringUtil.isStringEmpty(text)){
						String[] strArr = text.split(",");
						if(strArr.length==2){
							ReadTxtBeanForProductSite bean = new ReadTxtBeanForProductSite();
							bean.setContent(text.trim());
							bean.setPlace(strArr[0].trim());
							bean.setPalletNo(strArr[1].trim());
							beanList.add(bean);
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}finally {
				try {
					if(fileInputStream !=null){
						fileInputStream.close();
					}
					if(inputStreamReader !=null){
						inputStreamReader.close();
					}
					if(bufferedReader !=null){
						bufferedReader.close();
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return beanList;
	}
}
