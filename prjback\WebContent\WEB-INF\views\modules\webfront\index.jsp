<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ page import="com.hongru.common.util.Constants" %>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>管理后台 - 原价</title>
	<link rel="shortcut icon" href="${ctxsta}/hongru/ico/favicon.ico" type="image/x-icon" />
	
	<link rel="stylesheet" href="${ctxsta}/common/layui/css/layui.css?time=5"  media="all">
	<link rel="stylesheet" href="${ctxsta}/common/layui/admin.css?time=20220112"  media="all">
    <link rel="stylesheet" href="${ctxsta}/hongru/css/style.css?time=3"  media="all">
  <style>
    .layui-layout-left {
      left: -10px !important;
    }
    .layui-layout-admin .layui-side .layui-nav {
      width: 220px !important;
    }
    .layui-nav .layui-nav-item {
      line-height: 30px !important;
    }
  </style>
</head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
  <div class="layui-side" style="top:0">
    <div class="layui-side-scroll">
      <div>
        <ul class="layui-nav layui-layout-left">
          <li class="layui-nav-item" lay-unselect>
            <a ew-event="flexible" title="侧边伸缩"><i class="layui-icon layui-icon-shrink-right"></i></a>
          </li>
         <%-- <li class="layui-nav-item" lay-unselect>
            <a ew-event="refresh" title="刷新"><i class="layui-icon layui-icon-refresh-3"></i></a>
          </li>--%>
        </ul>
        <ul class="layui-nav layui-layout-right" style="z-index: 999">
          <li class="layui-nav-item" lay-unselect>
            <a>
              <cite>${user.userName} 您好！</cite>
            </a>
            <dl class="layui-nav-child">
              <dd lay-unselect><a ew-href="${ctx}/administrator/info/view"><i class="layui-icon layui-icon-form"></i>个人信息</a></dd>
              <hr>
              <dd lay-unselect><a ew-event="logout" data-url="${ctx}/logout"><i class="layui-icon layui-icon-logout"></i>安全退出</a></dd>
            </dl>
          </li>
        </ul>
      </div>
      <ul class="layui-nav layui-nav-tree arrow2" nav-id="xt1" lay-filter="admin-side-nav" lay-shrink="_all" style="top:50px;">
        <c:forEach items="${menus}" var="menu">
          <c:choose>
            <c:when test="${menu.childMenus != null && menu.childMenus.size()>0}">
              <li class="layui-nav-item ">
                <a><i class="layui-icon ${menu.icon}"></i>&emsp;<cite>${menu.menuName}</cite><span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child">
                  <c:forEach items="${menu.childMenus}" var="childMenu">
                    <dd><a lay-href="${ctx}${childMenu.href}">${childMenu.menuName}</a></dd>
                  </c:forEach>
                </dl>
              </li>
            </c:when>
            <c:otherwise>
              <li class="layui-nav-item layui-nav-itemed">
                <a lay-href="${ctx}${menu.href}"><i class="layui-icon ${menu.icon}"></i>&emsp;<cite>${menu.menuName}</cite></a>
              </li>
            </c:otherwise>
          </c:choose>
        </c:forEach>
      </ul>
      <ul class="layui-nav layui-nav-tree layui-hide" nav-id="xt2" lay-filter="admin-side-nav" style="margin: 15px 0;">
        <li class="layui-nav-item layui-nav-itemed">
          <a><i class="layui-icon layui-icon-table"></i>&emsp;<cite>表格页</cite><span class="layui-nav-more"></span></a>
          <dl class="layui-nav-child" style="">
            <dd class=""><a lay-href="../../page/template/table/table-basic.html">数据表格</a></dd>
            <dd><a lay-href="../../page/template/table/table-advance.html">复杂查询</a></dd>
          </dl>
        </li>
        <span class="layui-nav-bar" style="top: 27.5px; height: 0px; opacity: 0;"></span>
      </ul>
      <ul class="layui-nav layui-nav-tree layui-hide" nav-id="xt3" lay-filter="admin-side-nav" style="margin: 15px 0;">
        <li class="layui-nav-item layui-nav-itemed">
          <a><i class="layui-icon layui-icon-table"></i>&emsp;<cite>表格页</cite><span class="layui-nav-more"></span></a>
        </li>
        <span class="layui-nav-bar" style="top: 27.5px; height: 0px; opacity: 0;"></span>
      </ul>
    </div>
  </div>
  <div class="layui-body" style="bottom: 0;top:0" ></div>
  <div class="layui-footer layui-text"
       style="left: 0;width: 235px;z-index: 1009;color: #666;height: 36px;line-height: 30px;background: #000;">
    <div class="layui-logo" style="line-height: 30px">
      <img src="${ctxsta}/hongru/ico/favicon.png"/>
      <cite>&nbsp;原价管理系统</cite>
    </div>
  </div>
</div>
<input type="hidden" id="ctxsta" value="${ctxsta }">
<input type="hidden" id="baselocation" name="baselocation" value="${ctx}"/>

<script type="text/javascript" src="${ctxsta}/common/layui/layui.js"></script>
<script type="text/javascript" src="${ctxsta}/common/layui/common.js?time=1"></script>
<script type="text/javascript" src="${ctxsta}/common/layui/util.js?time=1"></script>

<script src="${ctxsta}/hongru/js/webfront/index.js?timer=2"></script>
</body>
</html>
