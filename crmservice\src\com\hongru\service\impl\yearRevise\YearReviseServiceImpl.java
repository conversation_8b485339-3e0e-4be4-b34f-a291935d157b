package com.hongru.service.impl.yearRevise;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.hongru.common.util.*;
import com.hongru.entity.cost.*;
import com.hongru.entity.stat.Proportion;
import com.hongru.entity.yearRevise.*;
import com.hongru.mapper.cost.*;
import com.hongru.mapper.stat.BoundQuantityMapper;
import com.hongru.mapper.yearRevise.MachineTimePreMapper;
import com.hongru.mapper.yearRevise.NewYearStoragePreMapper;
import com.hongru.pojo.dto.NewYearStoragePreDTO;
import com.hongru.service.cost.ICostService;
import com.hongru.service.setting.ISettingService;
import com.hongru.service.stat.IStatService;
import com.hongru.service.xieFenXi.IXieFenXiService;
import com.hongru.service.yearRevise.IYearReviseService;
import com.hongru.support.page.PageInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class YearReviseServiceImpl implements IYearReviseService {

	@Autowired
	private NewYearStoragePreMapper newYearStoragePreMapper;
	@Autowired
	private MachineTimePreMapper machineTimePreMapper;
	@Autowired
	private BoundQuantityMapper boundQuantityMapper;
	@Autowired
	private ElectricPriceCostMapper electricPriceCostMapper;
	@Autowired
	private ElectricPriceCostDetailMapper electricPriceCostDetailMapper;
	@Autowired
	private WaterPriceCostMapper waterPriceCostMapper;
	@Autowired
	private GasPriceCostMapper gasPriceCostMapper;
	@Autowired
	private DirectRecyclingMapper directRecyclingMapper;
	@Autowired
	private HumanHourCostMapper humanHourCostMapper;
	@Autowired
	private AuxiliaryRecyclingArtificialMapper auxiliaryRecyclingArtificialMapper;
	@Autowired
	private MaterialCostMapper materialCostMapper;
	@Autowired
	private SmallDepartmentMapper smallDepartmentMapper;
	@Autowired
	private AuxiliaryRecyclingMapper auxiliaryRecyclingMapper;
	@Autowired
	private TransportCostMapper transportCostMapper;

	// 移除对自身的注入
	@Autowired
	private ISettingService settingService;
	@Autowired
	private IStatService statService;
	@Autowired
	private ICostService costService;
	@Autowired
	private IXieFenXiService xieFenXiService;

	// Setter methods for Spring dependency injection
	public void setNewYearStoragePreMapper(NewYearStoragePreMapper newYearStoragePreMapper) {
		this.newYearStoragePreMapper = newYearStoragePreMapper;
	}

	public void setMachineTimePreMapper(MachineTimePreMapper machineTimePreMapper) {
		this.machineTimePreMapper = machineTimePreMapper;
	}

	public void setBoundQuantityMapper(BoundQuantityMapper boundQuantityMapper) {
		this.boundQuantityMapper = boundQuantityMapper;
	}

	public void setElectricPriceCostMapper(ElectricPriceCostMapper electricPriceCostMapper) {
		this.electricPriceCostMapper = electricPriceCostMapper;
	}

	public void setElectricPriceCostDetailMapper(ElectricPriceCostDetailMapper electricPriceCostDetailMapper) {
		this.electricPriceCostDetailMapper = electricPriceCostDetailMapper;
	}

	public void setWaterPriceCostMapper(WaterPriceCostMapper waterPriceCostMapper) {
		this.waterPriceCostMapper = waterPriceCostMapper;
	}

	public void setGasPriceCostMapper(GasPriceCostMapper gasPriceCostMapper) {
		this.gasPriceCostMapper = gasPriceCostMapper;
	}

	public void setDirectRecyclingMapper(DirectRecyclingMapper directRecyclingMapper) {
		this.directRecyclingMapper = directRecyclingMapper;
	}

	public void setHumanHourCostMapper(HumanHourCostMapper humanHourCostMapper) {
		this.humanHourCostMapper = humanHourCostMapper;
	}

	public void setAuxiliaryRecyclingArtificialMapper(
			AuxiliaryRecyclingArtificialMapper auxiliaryRecyclingArtificialMapper) {
		this.auxiliaryRecyclingArtificialMapper = auxiliaryRecyclingArtificialMapper;
	}

	public void setMaterialCostMapper(MaterialCostMapper materialCostMapper) {
		this.materialCostMapper = materialCostMapper;
	}

	public void setSmallDepartmentMapper(SmallDepartmentMapper smallDepartmentMapper) {
		this.smallDepartmentMapper = smallDepartmentMapper;
	}

	public void setAuxiliaryRecyclingMapper(AuxiliaryRecyclingMapper auxiliaryRecyclingMapper) {
		this.auxiliaryRecyclingMapper = auxiliaryRecyclingMapper;
	}

	public void setTransportCostMapper(TransportCostMapper transportCostMapper) {
		this.transportCostMapper = transportCostMapper;
	}

	/*
	 * =================================年度改订======================================
	 */

	/*
	 * =================================入库量实际======================================
	 */
	/**
	 * 年度予定入库量分页列表
	 *
	 * @param year
	 * @throws
	 * <AUTHOR>
	 * @create 2024/01/02 13:43
	 * @return com.hongru.entity.cost.NewYearStoragePre
	 */
	@Override
	public NewYearStoragePreDTO listNewYearStoragePrePage(String year, PageInfo pageInfo) throws Exception {
		List<NewYearStoragePre> newYearStoragePreList = newYearStoragePreMapper.listNewYearStoragePrePage(year,
				pageInfo);
		Integer total = newYearStoragePreMapper.listNewYearStoragePreCount(year);
		pageInfo.setTotal(total);
		return new NewYearStoragePreDTO(pageInfo, newYearStoragePreList);
	}

	/**
	 * 添加年度予定入库量
	 *
	 * @param newYearStoragePre
	 * @throws
	 * <AUTHOR>
	 * @create 2024/01/02 13:43
	 * @return
	 */
	@Override
	public void addNewYearStoragePre(NewYearStoragePre newYearStoragePre) throws Exception {
		newYearStoragePreMapper.insertNewYearStoragePre(newYearStoragePre);
	}

	/**
	 * 根据年度查询年度予定入库量
	 *
	 * @param year
	 * @throws
	 * <AUTHOR>
	 * @create 2024/01/02 13:43
	 * @return
	 */
	@Override
	public NewYearStoragePre selectNewYearStoragePreByYear(String year) throws Exception {
		return newYearStoragePreMapper.selectNewYearStoragePreByYear(year);
	}

	/**
	 * 编辑年度予定入库量
	 *
	 * @param newYearStoragePre
	 * @throws
	 * <AUTHOR>
	 * @create 2024/01/02 13:43
	 * @return
	 */
	@Override
	public void updateNewYearStoragePreInfo(NewYearStoragePre newYearStoragePre) throws Exception {
		newYearStoragePreMapper.updateNewYearStoragePreInfo(newYearStoragePre);
	}

	/**
	 * 删除年度予定入库量
	 *
	 * @param year
	 * @throws
	 * <AUTHOR>
	 * @create 2024/01/02 13:43
	 * @return int
	 */
	@Override
	public void deleteNewYearStoragePreByYear(String year) throws Exception {
		newYearStoragePreMapper.deleteNewYearStoragePreByYear(year);
	}

	/**
	 * 取得入库量实际
	 *
	 * @param monthInterval
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws
	 * <AUTHOR>
	 * @create 2024/01/08 13:53
	 * @return java.util.List<com.hongru.entity.yearRevise.StorageInActBean>
	 */
	public List<StorageInActBean> selectStorageInActForMWList(int monthInterval, String timeStartStr,
			String timeEndStr) {
		return boundQuantityMapper.selectStorageInActBeanForMWList(monthInterval, timeStartStr, timeEndStr);
	}

	/*
	 * =================================机械时间予定======================================
	 */

	/**
	 * 根据时间范围获取机械时间予定明细(MW)
	 *
	 * @param monthInterval
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/04 09:43
	 * @return
	 */
	@Override
	public List<MachineTimePreBean> listMachineTimePreBeanForMW(int monthInterval, String timeStartStr,
			String timeEndStr) throws Exception {

		return machineTimePreMapper.listMachineTimePreBeanForMW(monthInterval, timeStartStr, timeEndStr);
	}

	/**
	 * 根据时间范围获取机械时间予定明细(UF)
	 *
	 * @param monthInterval
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/04 09:43
	 * @return
	 */
	@Override
	public List<MachineTimePreBean> listMachineTimePreBeanForUF(int monthInterval, String timeStartStr,
			String timeEndStr) throws Exception {
		return machineTimePreMapper.listMachineTimePreBeanForUF(monthInterval, timeStartStr, timeEndStr);
	}

	/*
	 * =================================动力SH系数-电燃氮==================================
	 * ====
	 */
	/**
	 * 根据时间范围获取电力相关信息
	 *
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/04 09:43
	 * @return
	 */
	@Override
	public PowerShCoefficientBean selectPowerElectricByDateRange(String timeStartStr, String timeEndStr)
			throws Exception {
		return electricPriceCostMapper.selectPowerElectricByDateRange(timeStartStr, timeEndStr);
	}

	/**
	 * 根据时间范围获取水相关信息
	 *
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/04 09:43
	 * @return
	 */
	@Override
	public PowerShCoefficientBean selectPowerWaterByDateRange(String timeStartStr, String timeEndStr) throws Exception {
		return waterPriceCostMapper.selectPowerWaterByDateRange(timeStartStr, timeEndStr);
	}

	/**
	 * 根据时间范围获取天然气、氮气相关信息
	 *
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/04 09:43
	 * @return
	 */
	@Override
	public List<PowerShCoefficientBean> selectPowerOtherByDateRange(String timeStartStr, String timeEndStr)
			throws Exception {
		return gasPriceCostMapper.selectPowerOtherByDateRange(timeStartStr, timeEndStr);
	}

	/**
	 * 根据部门获取平均实际电量
	 *
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/10 09:43
	 * @return
	 */
	@Override
	public List<NewYearEleCoeffucientBean> selectElectricActAvgByDepart(String[] departArr, String timeStartStr,
			String timeEndStr) throws Exception {
		return electricPriceCostDetailMapper.selectElectricActAvgByDepart(departArr, timeStartStr, timeEndStr);
	}

	/**
	 * 预定直接部门回收计算表取得[电费SH]、[SMCH]（加权平均）
	 *
	 * @param departArr
	 * @param monthInterval
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/11 11:43
	 * @return
	 */
	@Override
	public List<NewYearEleCoeffucientBean> listDirectRecyclingForUF(String[] departArr, int monthInterval,
			String timeStartStr, String timeEndStr) throws Exception {
		return directRecyclingMapper.listDirectRecyclingForUF(departArr, monthInterval, timeStartStr, timeEndStr);
	}

	/**
	 * 燃气费用表取得[氮气费]（平均）
	 *
	 * @param depart
	 * @param monthInterval
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/11 13:43
	 * @return
	 */
	@Override
	public BigDecimal selectNitrogenActAvgByDateRange(String department, String timeStartStr, String timeEndStr)
			throws Exception {
		return gasPriceCostMapper.selectNitrogenActAvgByDateRange(department, timeStartStr, timeEndStr);
	}

	/*
	 * =================================人件费SH系数=====================================
	 * =
	 */
	/**
	 * 根据时间范围获取人件工时实绩
	 *
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/22 10:43
	 * @return
	 */
	@Override
	public List<HumanHourCost> listHumanHourCostByDateRange(String timeStartStr, String timeEndStr,
			String departmentCode) throws Exception {
		String departmentCodeLIKE = null;
		if (!StringUtil.isStringEmpty(departmentCode)) {
			departmentCodeLIKE = "%" + departmentCode + "%";
		}
		return humanHourCostMapper.listHumanHourCostByDateRange(timeStartStr, timeEndStr, departmentCodeLIKE);
	}

	/**
	 * 预定直接部门回收计算表mw
	 *
	 * @param departArr
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/11 11:43
	 * @return
	 */
	@Override
	public List<DirectRecycling> listDirectRecyclingForMW(String[] departArr, int monthInterval, String timeStartStr,
			String timeEndStr) throws Exception {
		return directRecyclingMapper.listDirectRecyclingForMW(departArr, monthInterval, timeStartStr, timeEndStr);
	}

	/**
	 * 预定直接部门回收计算表
	 *
	 * @param departArr
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/11 11:43
	 * @return
	 */
	@Override
	public List<DirectRecycling> listDirectRecyclingByDateRange(String[] departArr, int monthInterval, int flag,
			String timeStartStr, String timeEndStr) throws Exception {
		return directRecyclingMapper.listDirectRecyclingByDateRange(departArr, monthInterval, flag, timeStartStr,
				timeEndStr);
	}

	/**
	 * 预定辅助部门回收计算表取得[SH]]（加权平均）
	 *
	 * @param departArr
	 * @param supportDepartArr
	 * @param expenseItemArr
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/26 11:43
	 * @return
	 */
	@Override
	public List<AuxiliaryRecyclingArtificial> listMwAuxiliaryRecyclingArtificialByDateRange(String[] departArr,
			String[] supportDepartArr, String[] expenseItemArr, int monthInterval, String timeStartStr,
			String timeEndStr) throws Exception {
		return auxiliaryRecyclingArtificialMapper.listMwAuxiliaryRecyclingArtificialByDateRange(departArr,
				supportDepartArr, expenseItemArr, monthInterval, timeStartStr, timeEndStr);
	}

	/**
	 * 预定辅助部门回收计算表取得[SH]]（加权平均）
	 *
	 * @param departArr
	 * @param supportDepartArr
	 * @param expenseItemArr
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/26 11:43
	 * @return
	 */
	@Override
	public List<AuxiliaryRecyclingArtificial> listAuxiliaryRecyclingArtificialByDateRange(String[] departArr,
			String[] supportDepartArr, String[] expenseItemArr, int monthInterval, String timeStartStr,
			String timeEndStr) throws Exception {
		return auxiliaryRecyclingArtificialMapper.listAuxiliaryRecyclingArtificialByDateRange(departArr,
				supportDepartArr, expenseItemArr, monthInterval, timeStartStr, timeEndStr);
	}

	/*
	 * =================================补修辅材分配额SH系数=================================
	 * =====
	 */
	/**
	 * 根据参数获取金额（加权平均）
	 *
	 * @param monthInterval
	 * @param timeStartStr
	 * @param timeEndStr
	 * @param expenseType
	 * @param department
	 * <AUTHOR>
	 * @create 2024/01/30 13:37
	 * @return
	 */
	@Override
	public List<AuxiliaryMaterialCost> listAuxiliaryMaterialCostByDateRange(int monthInterval, String timeStartStr,
			String timeEndStr, String expenseType, String department, String category, String year) throws Exception {
		return materialCostMapper.listAuxiliaryMaterialCostByDateRange(monthInterval, timeStartStr, timeEndStr,
				expenseType, department, category, year);
	}

	/**
	 * 根据部门编号取得补修超细分摊比例
	 *
	 * @param departmentCode
	 * @param year
	 * <AUTHOR>
	 * @create 2024/01/30 13:37
	 * @return
	 */
	@Override
	public BigDecimal selectRepaireUfShareRate(String departmentCode, String year) {
		return smallDepartmentMapper.selectRepaireUfShareRate(departmentCode, year);
	}

	/**
	 * 根据部门编号取得辅材超细分摊比例
	 * @param departmentCode
	 * @param year
	 * <AUTHOR>
	 * @create 2024/01/30 13:37
	 * @return
	 */
	@Override
	public BigDecimal selectAuxiliaryUfShareRate(String departmentCode, String year) {
		return smallDepartmentMapper.selectAuxiliaryUfShareRate(departmentCode, year);
	}

	/**
	 * 取得含有UF分摊比例的小部门编号
	 *
	 * @param expenseType
	 * @param year
	 * <AUTHOR>
	 * @create 2024/01/30 13:37
	 * @return
	 */
	@Override
	public List<SmallDepartment> listShareRateForUF(String expenseType, String year) {
		return smallDepartmentMapper.listShareRateForUF(expenseType, year);
	}

	/**
	 * 根据条件检索「预定辅助部门回收计算补修辅材表」取得各部门金额明细（加权平均）
	 *
	 * @param monthInterval
	 * @param flag          0:MW / 1:UF
	 * @param expenseItem   0080:补修 /
	 * @param timeStartStr
	 * @param timeEndStr
	 * @param departArr
	 * <AUTHOR>
	 * @create 2024/01/30 13:37
	 * @return
	 */
	@Override
	public List<AuxiliaryRecycling> listAuxiliaryRecyclingByDateRange(int monthInterval, int flag, String expenseItem,
			String timeStartStr, String timeEndStr, String[] departArr) {
		return auxiliaryRecyclingMapper.listAuxiliaryRecyclingByDateRange(monthInterval, flag, expenseItem,
				timeStartStr, timeEndStr, departArr);
	}

	/**
	 * 根据条件检索「预定辅助部门回收计算补修辅材表」取得各部门金额总计（加权平均）
	 *
	 * @param monthInterval
	 * @param flag          0:MW / 1:UF
	 * @param expenseItem
	 * @param timeStartStr
	 * @param timeEndStr
	 * @param departArr
	 * <AUTHOR>
	 * <AUTHOR>
	 * @create 2024/01/30 13:37
	 * @return
	 */
	@Override
	public List<AuxiliaryRecycling> listAuxiliaryRecyclingAmountTotal(int monthInterval, int flag, String expenseItem,
			String timeStartStr, String timeEndStr, String[] departArr) {
		return auxiliaryRecyclingMapper.listAuxiliaryRecyclingAmountTotal(monthInterval, flag, expenseItem,
				timeStartStr, timeEndStr, departArr);
	}

	/**
	 * 预定直接部门回收计算表取得[补修费费用]、[辅材费费用]（加权平均）
	 *
	 * @param departArr
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/11 11:43
	 * @return
	 */
	@Override
	public List<DirectRecycling> listDirectRecyclingUFByDateRange(String[] departArr, int monthInterval,
			String timeStartStr, String timeEndStr) {
		return directRecyclingMapper.listDirectRecyclingUFByDateRange(departArr, monthInterval, timeStartStr,
				timeEndStr);
	}

	/**
	 * 根据条件检索「辅材费用表」取得实际金额合计
	 *
	 * @param timeStartStr
	 * @param timeEndStr
	 * @param expenseType    费用种类
	 * @param departmentCode 部门编码
	 * <AUTHOR>
	 * @create 2024/01/30 13:37
	 * @return
	 */
	@Override
	public BigDecimal selectAuxMaterialCostSum(String timeStartStr, String timeEndStr, String expenseType,
			String departmentCode, String department, String category) {
		return materialCostMapper.selectAuxMaterialCostSum(timeStartStr, timeEndStr, expenseType, departmentCode,
				department, category);
	}

	/**
	 * 根据年月范围取得回收运输费平均单价
	 *
	 * @param yearMonthStart
	 * @param yearMonthEnd
	 * <AUTHOR>
	 * @create 2024/02/23 13:37
	 * @return
	 */
	@Override
	public String selectRecycleTransportationCostAvgUnit(String yearMonthStart, String yearMonthEnd) {
		return transportCostMapper.selectRecycleTransportationCostAvgUnit(yearMonthStart, yearMonthEnd);
	}
	/**
	 * 根据年月范围取得运输费用汇总
	 * @param yearMonthStart
	 * @param yearMonthEnd
	 * <AUTHOR>
	 * @create 2024/02/23 13:37
	 * @return
	 */
	@Override
	public List<TransportationCostBean> selectTransportationCostBeanList(String yearMonthStart, String yearMonthEnd) {
		return transportCostMapper.selectTransportationCostBeanList(yearMonthStart, yearMonthEnd);
	}

	/*
	 * =================================部门单价汇总======================================
	 */
	/**
	 * 获取部门单价汇总数据集合
	 * @param time 日期
	 * @param machineType 机械类别
	 * @param pageType 页面类型(MW/UF)
	 * @throws Exception
	 * <AUTHOR>
	 * @return
	 */
	@Override
	public List<SummaryOfDepartmentalUnitPricesBean> getSummaryOfDepartmentalUnitPricesList(String time, String machineType, String pageType) throws Exception{
		// 部门单价汇总集合
		List<SummaryOfDepartmentalUnitPricesBean> summaryOfDepartmentalUnitPricesBeanList = new ArrayList<>();
		// 直接部门集合
		List<String> directDepartments = new ArrayList<>();
		// 截取时间范围
		String yearMonthStart = null;
		String yearMonthEnd = null;
		if (org.apache.commons.lang3.StringUtils.isNotBlank(time)) {
			yearMonthStart = time.substring(0, 7);
			yearMonthEnd = time.substring(time.length()-7);

			// 根据开始年月取得年度
			String year = YearUtil.getYear(yearMonthStart);

			// 检索[补辅部门列表] 获取所有直接部门（部门属性非「2：辅助部门」的其它部门都是直接部门）
			directDepartments = settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_2);
		}
		// 获取两个日期相差的月数(加权平均用)
		int monthInterval =  DateUtils.getMonthDiff(yearMonthStart,yearMonthEnd);

		// 获取部门「电力」汇总信息
		List<SummaryOfDepartmentalUnitPricesBean> electricitySummaryInfos = getElectricitySummaryInfos(monthInterval, yearMonthStart, yearMonthEnd, machineType, pageType);
		if (CollectionUtils.isNotEmpty(electricitySummaryInfos)) {
			summaryOfDepartmentalUnitPricesBeanList.addAll(electricitySummaryInfos);
		}

		// 获取部门「天然气/氮气」汇总信息
		List<SummaryOfDepartmentalUnitPricesBean> naturalGasSummaryInfos = getNaturalGasSummaryInfos(monthInterval, yearMonthStart, yearMonthEnd, machineType, pageType);
		if (CollectionUtils.isNotEmpty(naturalGasSummaryInfos)) {
			summaryOfDepartmentalUnitPricesBeanList.addAll(naturalGasSummaryInfos);
		}

		// 获取部门「人件」汇总信息
		List<SummaryOfDepartmentalUnitPricesBean> personManHourSummaryInfos = getPersonManHourSummaryInfos(monthInterval, yearMonthStart, yearMonthEnd, machineType, pageType, directDepartments);
		if (CollectionUtils.isNotEmpty(personManHourSummaryInfos)) {
			summaryOfDepartmentalUnitPricesBeanList.addAll(personManHourSummaryInfos);
		}

		// 获取部门「补修、辅材」汇总信息
		List<SummaryOfDepartmentalUnitPricesBean> repairAndAuxiliarySummaryInfos = getRepairAndAuxiliarySummaryInfos(monthInterval, yearMonthStart, yearMonthEnd, machineType, pageType, directDepartments);
		if (CollectionUtils.isNotEmpty(repairAndAuxiliarySummaryInfos)) {
			summaryOfDepartmentalUnitPricesBeanList.addAll(repairAndAuxiliarySummaryInfos);
		}

		return summaryOfDepartmentalUnitPricesBeanList;
	}

	/**
	 * 根据条件获取部门「电力」汇总信息
	 * @param monthInterval 日期相差的月数
	 * @param yearMonthStart 开始日期
	 * @param yearMonthEnd 结束日期
	 * @param machineType 机械类别
	 * @param pageType 页面类型
	 * @return 电力汇总信息集合
	 * @throws Exception
	 * <AUTHOR>
	 */
	@Override
	public List<SummaryOfDepartmentalUnitPricesBean> getElectricitySummaryInfos(int monthInterval, String yearMonthStart, String yearMonthEnd, String machineType, String pageType) throws Exception {
		List<SummaryOfDepartmentalUnitPricesBean> electricitySummaryInfos = new ArrayList<>();
		//根据日期范围检索[电费用表]取得「电力使用量实绩合计」、「电力费合计」
		PowerShCoefficientBean powerElectric = Optional.ofNullable(this.selectPowerElectricByDateRange(yearMonthStart, yearMonthEnd)).orElse(new PowerShCoefficientBean());
		//根据日期范围检索[水费用表]取得「水费合计」
		PowerShCoefficientBean powerWater = Optional.ofNullable(this.selectPowerWaterByDateRange(yearMonthStart, yearMonthEnd)).orElse(new PowerShCoefficientBean());

		// 电力、水费合计
		BigDecimal tempPriceAct = Optional.ofNullable(powerElectric.getElePriceTotal()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(powerWater.getWaterPriceTotal()).orElse(BigDecimal.ZERO));
		// 电力使用量实绩合计
		BigDecimal eleUsedActTotal = Optional.ofNullable(powerElectric.getEleUsedActTotal()).orElse(BigDecimal.ZERO);
		// 电力单价实绩=(电力费合计+水费合计)/电力使用量实绩合计
		BigDecimal electricityUnitPrice = BigDecimal.ZERO;
		if (eleUsedActTotal.compareTo(BigDecimal.ZERO) > 0) {
			electricityUnitPrice = tempPriceAct.divide(eleUsedActTotal, 3,  RoundingMode.HALF_UP);
		}

		if ("MW".equals(pageType)) {
			// 可查询相关数据的部门
			List<String> queryDepartments = Arrays.asList("DH总", "DM总", "EM总", "EF总", "EF09总", "ER总", "EH总");
			// 检索条件部门
			String[] departArr = {"DH总", "DM总", "EM总", "EF总", "EF09总", "ER总", "EH总", "公用设施 总", "食堂","其他"} ;
			// 直接平均电量总计
			BigDecimal directEleAvgTotal = new BigDecimal(0);
			// 间接接平均电量总计
			BigDecimal indirectEleAvgTotal = new BigDecimal(0);

			if (org.apache.commons.lang3.StringUtils.isNotBlank(machineType)) {
				queryDepartments = Arrays.asList(machineType + "总");
			}

			// 检索[电力使用明细表] 获取电量实绩平均
			List<NewYearEleCoeffucientBean> newYearEleCoeffucientBeanList = this.selectElectricActAvgByDepart(departArr, yearMonthStart, yearMonthEnd);
			if(CollectionUtils.isNotEmpty(newYearEleCoeffucientBeanList)) {
				// 根据条件获「部门」为key，「单位机械时间」为value（如果「新年度机械时间予定」不为空，则value为「新年度机械时间予定」）的Map
				Map<String, String> machineTimePreBeanMap = getMachineTimePreBeanMap(monthInterval, yearMonthStart, yearMonthEnd);

				for(NewYearEleCoeffucientBean newYearEleCoeffucient:newYearEleCoeffucientBeanList) {
					// 间接的场合
					if (
							"公用设施 总".equals(newYearEleCoeffucient.getDepartment())
									|| "食堂".equals(newYearEleCoeffucient.getDepartment())
									|| "其他".equals(newYearEleCoeffucient.getDepartment())
					) {
						indirectEleAvgTotal = indirectEleAvgTotal.add(newYearEleCoeffucient.getElectricityActAvg());
					} else {
						// 直接的场合
						directEleAvgTotal = directEleAvgTotal.add(newYearEleCoeffucient.getElectricityActAvg());
					}
				}

				for(NewYearEleCoeffucientBean newYearEleCoeffucientBean : newYearEleCoeffucientBeanList) {
					// 电量实绩平均
					BigDecimal electricityActAvg = Optional.ofNullable(newYearEleCoeffucientBean.getElectricityActAvg()).orElse(BigDecimal.ZERO);
					// 实绩比例
					BigDecimal proportionAct = BigDecimal.ZERO;

					if(directEleAvgTotal.compareTo(BigDecimal.ZERO) > 0) {
						// 实绩比例 = 电量实绩平均/直接平均电量总计
						proportionAct = electricityActAvg.divide(directEleAvgTotal, 4, RoundingMode.HALF_UP);
					}

					// 间接分摊 = 间接接平均电量总计*实绩比例
					BigDecimal indirectEleAvg = indirectEleAvgTotal.multiply(proportionAct).setScale(3, RoundingMode.HALF_UP);
					// 实绩总电量(分配电量) = 电量实绩平均 + 间接分摊
					BigDecimal actualTotalElectricityConsumption = electricityActAvg.add(indirectEleAvg);
					// 机械时间
					String machineTimePre = machineTimePreBeanMap.get(newYearEleCoeffucientBean.getDepartment().replace("总", ""));
					// SH系数=分配电量/机械时间
					BigDecimal shCoefficient = BigDecimal.ZERO;
					if(org.apache.commons.lang3.StringUtils.isNotBlank(machineTimePre)) {
						try {
							shCoefficient = actualTotalElectricityConsumption.divide(new BigDecimal(machineTimePre), 4, RoundingMode.HALF_UP);
						} catch (Exception e) {
							shCoefficient = BigDecimal.ZERO;
						}
					}

					// MW电力费=电力单价实绩*SH系数
					BigDecimal mwElectricityCost = electricityUnitPrice.multiply(shCoefficient).setScale(3,  RoundingMode.HALF_UP);

					if (queryDepartments.contains(newYearEleCoeffucientBean.getDepartment())) {
						SummaryOfDepartmentalUnitPricesBean electricitySummaryInfo = new SummaryOfDepartmentalUnitPricesBean();
						electricitySummaryInfo.setDepartmentCode(newYearEleCoeffucientBean.getDepartment());
						electricitySummaryInfo.setExpenseItem("电力");
						electricitySummaryInfo.setAttribute("直接");
						electricitySummaryInfo.setUnitPrice(electricityUnitPrice);
						electricitySummaryInfo.setShCoefficient(shCoefficient);
						electricitySummaryInfo.setProjectCost(mwElectricityCost);
						electricitySummaryInfo.setMachineType(newYearEleCoeffucientBean.getDepartment().replace("总", ""));

						electricitySummaryInfos.add(electricitySummaryInfo);
					}
				}
			}
		} else {
			// 检索条件部门
			String[] departUFArr = {"超细"} ;
			// 检索[电力使用明细表] 获取电量实绩平均
			List<NewYearEleCoeffucientBean> electricActAvgList=this.selectElectricActAvgByDepart(departUFArr, yearMonthStart, yearMonthEnd);

			// 超细实际电量平均
			BigDecimal ufEleActAvg = BigDecimal.ZERO;
			if (CollectionUtils.isNotEmpty(electricActAvgList)) {
				for(NewYearEleCoeffucientBean electricActAvg:electricActAvgList) {
					ufEleActAvg = ufEleActAvg.add(electricActAvg.getElectricityActAvg());
				}
			}

			// 可查询相关数据的部门
			List<String> queryDepartments = Arrays.asList("DE", "DFA", "DS", "DU", "EE", "ES", "EU", "EW");
			// 检索预定直接部门回收计算表超细不包含下记部门
			String[] departArr = {"UFINS", "DH", "DM"} ;

			if (org.apache.commons.lang3.StringUtils.isNotBlank(machineType)) {
				queryDepartments = Arrays.asList(machineType);
			}

			List<NewYearEleCoeffucientBean> newYearEleCoeffucientBeanList = this.listDirectRecyclingForUF(departArr, monthInterval, yearMonthStart, yearMonthEnd);

			if (CollectionUtils.isNotEmpty(newYearEleCoeffucientBeanList)) {
				// 予定电量合计
				BigDecimal elePreTotal = BigDecimal.ZERO;
				for (NewYearEleCoeffucientBean elePreTemp : newYearEleCoeffucientBeanList) {
					elePreTotal = elePreTotal.add(elePreTemp.getElectricityPreAvg());
				}

				// 新年度予定电量系数查询返回结果实体类
				for (NewYearEleCoeffucientBean newYearEleCoeffucientBean : newYearEleCoeffucientBeanList) {
					if (queryDepartments.contains(newYearEleCoeffucientBean.getDepartment())) {
						// 新年度予定电量系数
						BigDecimal newYearPreCoefficient = BigDecimal.ZERO;
						if (elePreTotal.compareTo(BigDecimal.ZERO) > 0) {
							// 予定比例 = 电量予定平均/予定电量合计
							BigDecimal proportionPre = newYearEleCoeffucientBean.getElectricityPreAvg().divide(elePreTotal, 8, RoundingMode.HALF_UP);
							// 实绩电量分配 = 予定比例*超细实际电量平均
							BigDecimal eleActAllocation = proportionPre.multiply(ufEleActAvg).setScale(4, RoundingMode.HALF_UP);
							// 新年度予定SYS系数 = 实绩电量分配/机械时间予定
							newYearPreCoefficient = eleActAllocation.divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP);
						}

						// UF电力费=电力单价实绩*SH系数(新年度予定SYS系数)
						BigDecimal ufElectricityCost = electricityUnitPrice.multiply(newYearPreCoefficient).setScale(3,  RoundingMode.HALF_UP);

						SummaryOfDepartmentalUnitPricesBean electricitySummaryInfo = new SummaryOfDepartmentalUnitPricesBean();
						electricitySummaryInfo.setDepartmentCode(newYearEleCoeffucientBean.getDepartment());
						electricitySummaryInfo.setExpenseItem("电力");
						electricitySummaryInfo.setAttribute("直接");
						electricitySummaryInfo.setUnitPrice(electricityUnitPrice);
						electricitySummaryInfo.setShCoefficient(newYearPreCoefficient);
						electricitySummaryInfo.setProjectCost(ufElectricityCost);
						electricitySummaryInfo.setMachineType(newYearEleCoeffucientBean.getDepartment());

						electricitySummaryInfos.add(electricitySummaryInfo);
					}

				}
			}
		}

		return electricitySummaryInfos;
	}

	/**
	 * 根据条件获取机械时间予定明细Map
	 * @param monthInterval 日期相差的月数
	 * @param yearMonthStart 开始日期
	 * @param yearMonthEnd 结束日期
	 * @return
	 */
	@Override
	public Map<String, String> getMachineTimePreBeanMap(int monthInterval, String yearMonthStart, String yearMonthEnd) throws Exception {
		// 根据条件获取机械时间予定明细信息
		List<MachineTimePreBean> machineTimePreBeanList = Optional.ofNullable(this.listMachineTimePreBeanForMW(monthInterval, yearMonthStart, yearMonthEnd)).orElse(new ArrayList<>());
		// 检索入库表、取得各入库量实际
		List<StorageInActBean> storageInActBeanList = this.selectStorageInActForMWList(monthInterval, yearMonthStart, yearMonthEnd);

		// 实际入库量总计
		BigDecimal storageInActTotal = new BigDecimal(0);
		// EM实际入库量总计
		BigDecimal storageInActEM = new BigDecimal(0);
		// EF实际入库量总计
		BigDecimal storageInActEF = new BigDecimal(0);
		// EF09实际入库量总计
		BigDecimal storageInActEF09= new BigDecimal(0);
		// ER实际入库量总计
		BigDecimal storageInActER = new BigDecimal(0);
		// EH实际入库量总计
		BigDecimal storageInActEH = new BigDecimal(0);

		if(CollectionUtils.isNotEmpty(storageInActBeanList)) {
			for(StorageInActBean storageInActBean:storageInActBeanList) {
				// 实际入库量总计  =EM+EF+EF09+ER+EH
				storageInActTotal = storageInActBean.getStorageInActEM()
						.add(storageInActBean.getStorageInActEF().add(storageInActBean.getStorageInActEF09().add(
								storageInActBean.getStorageInActER().add(storageInActBean.getStorageInActEH()))));
				// EM实际入库量
				storageInActEM = Optional.ofNullable(storageInActBean.getStorageInActEM()).orElse(BigDecimal.ZERO);
				// EF实际入库量
				storageInActEF = Optional.ofNullable(storageInActBean.getStorageInActEF()).orElse(BigDecimal.ZERO);
				// EF09实际入库量
				storageInActEF09 = Optional.ofNullable(storageInActBean.getStorageInActEF09()).orElse(BigDecimal.ZERO);
				// ER实际入库量
				storageInActER = Optional.ofNullable(storageInActBean.getStorageInActER()).orElse(BigDecimal.ZERO);
				// EH实际入库量
				storageInActEH = Optional.ofNullable(storageInActBean.getStorageInActEH()).orElse(BigDecimal.ZERO);
			}
		}
		// 新年度入库量予定
		int nextYear = YearUtil.getNextYear(yearMonthStart); // 获取新的年份值

		// 根据条件「年度」检索需要编辑的年度予定入库量
		NewYearStoragePre newYearStoragePre = this.selectNewYearStoragePreByYear(String.valueOf(nextYear));
		BigDecimal newYearStoragePreTotal = null;

		// 新年度入库量予定总计= EM+EF+EF09+ER+EH
		if(newYearStoragePre != null) {
			newYearStoragePreTotal = newYearStoragePre.getEmNewYearStoragePre().add(newYearStoragePre.getEfNewYearStoragePre()
					.add(newYearStoragePre.getEf09NewYearStoragePre().add(newYearStoragePre.getErNewYearStoragePre().add(newYearStoragePre.getEhNewYearStoragePre()))));
		}

		// 机械时间实际
		HashMap<String,Float> totalDepartScrapAmountMap = xieFenXiService.listxieFenXiBeanForMachineTime(monthInterval, yearMonthStart, yearMonthEnd);
		if(CollectionUtils.isNotEmpty(machineTimePreBeanList)){
			for(MachineTimePreBean machineTimePreBean:machineTimePreBeanList) {
				machineTimePreBean.setNewYearUpRate("0");
				switch(machineTimePreBean.getDepartment()) {
					case "DH":
						// 入库量实绩总计
						machineTimePreBean.setStorageActAvg(storageInActTotal.toString());
						// 单位机械时间=机械时间予定(平均)/入库量实绩(平均)
						machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimePreAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
						// 新年度入库量予定:EM+EF+EF09+ER+EH
						if(newYearStoragePreTotal != null) {
							machineTimePreBean.setNewYearStoragePre(newYearStoragePreTotal.toString());
							// 新年度UP率=新年度入库量予定/入库量实绩
							machineTimePreBean.setNewYearUpRate("0");
							if (storageInActTotal.compareTo(BigDecimal.ZERO) > 0) {
								machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
							}
							// 新年度机械时间予定 = 机械时间予定*新年度UP率
							machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimePreAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
						}
						break;
					case "DM":
						// 入库量实绩  DM
						machineTimePreBean.setStorageActAvg(storageInActEF.toString());
						//单位机械时间=机械时间予定(平均)/入库量实绩(平均)
						machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimePreAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
						if(newYearStoragePre != null) {
							//新年度入库量予定:DM
							machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEfNewYearStoragePre().toString());
							//新年度UP率=新年度入库量予定/入库量实绩
							machineTimePreBean.setNewYearUpRate("0");
							if (storageInActEF.compareTo(BigDecimal.ZERO) > 0) {
								machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
							}
							//新年度机械时间予定 = 机械时间予定*新年度UP率
							machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimePreAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
						}
						break;
					case "EM":
						// 入库量实绩  EM
						machineTimePreBean.setStorageActAvg(storageInActEM.toString());
						// 机械时间实绩(平均) = EM
						machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("EM").toString());
						//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
						machineTimePreBean.setUnitMechanicalTime("0");
						if (storageInActEM.compareTo(BigDecimal.ZERO) > 0) {
							machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
						}

						if(newYearStoragePre != null) {
							//新年度入库量予定
							machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEmNewYearStoragePre().toString());
							//新年度UP率=新年度入库量予定/入库量实绩
							machineTimePreBean.setNewYearUpRate("0");
							if (storageInActEM.compareTo(BigDecimal.ZERO) > 0) {
								machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
							}
							//新年度机械时间予定 = 机械时间实绩*新年度UP率
							machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
						}
						break;
					case "EF":
						// 入库量实绩  EF
						machineTimePreBean.setStorageActAvg(storageInActEF.toString());
						// 机械时间实绩(平均) =  EF
						machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("EF").toString());
						//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
						machineTimePreBean.setUnitMechanicalTime("0");
						if (storageInActEF.compareTo(BigDecimal.ZERO) > 0) {
							machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
						}
						if(newYearStoragePre != null) {
							//新年度入库量予定
							machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEfNewYearStoragePre().toString());
							//新年度UP率=新年度入库量予定/入库量实绩
							machineTimePreBean.setNewYearUpRate("0");
							if (storageInActEF.compareTo(BigDecimal.ZERO) > 0) {
								machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
							}
							//新年度机械时间予定 = 机械时间实绩*新年度UP率
							machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
						}
						break;
					case "EF09":
						// 入库量实绩  EF09
						machineTimePreBean.setStorageActAvg(storageInActEF09.toString());
						// 机械时间实绩(平均)
						machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("EF09").toString());
						//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
						machineTimePreBean.setUnitMechanicalTime("0");
						if (storageInActEF09.compareTo(BigDecimal.ZERO) > 0) {
							machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()),3)));
						}

						if(newYearStoragePre != null) {
							//新年度入库量予定
							machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEf09NewYearStoragePre().toString());
							//新年度UP率=新年度入库量予定/入库量实绩
							machineTimePreBean.setNewYearUpRate("0");
							if (storageInActEF09.compareTo(BigDecimal.ZERO) > 0) {
								machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
							}
							//新年度机械时间予定 = 机械时间实绩*新年度UP率
							machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
						}
						break;
					case "ER":
						// 入库量实绩  ER
						machineTimePreBean.setStorageActAvg(storageInActER.toString());
						// 机械时间实绩(平均)
						machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("ER").toString());
						//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
						machineTimePreBean.setUnitMechanicalTime("0");
						if (storageInActER.compareTo(BigDecimal.ZERO) > 0) {
							machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
						}
						if(newYearStoragePre != null) {
							//新年度入库量予定
							machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getErNewYearStoragePre().toString());
							//新年度UP率=新年度入库量予定/入库量实绩
							machineTimePreBean.setNewYearUpRate("0");
							if (storageInActER.compareTo(BigDecimal.ZERO) > 0) {
								machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
							}
							//新年度机械时间予定 = 机械时间实绩*新年度UP率
							machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
						}
						break;
					case "EH":
						// 入库量实绩  EH
						machineTimePreBean.setStorageActAvg(storageInActEH.toString());
						// 机械时间实绩(平均)
						machineTimePreBean.setMachineTimeActAvg(totalDepartScrapAmountMap.get("EH").toString());
						//单位机械时间=机械时间实绩(平均)/入库量实绩(平均)
						machineTimePreBean.setUnitMechanicalTime("0");
						if (storageInActEH.compareTo(BigDecimal.ZERO) > 0) {
							machineTimePreBean.setUnitMechanicalTime(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getMachineTimeActAvg()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
						}

						if(newYearStoragePre != null) {
							//新年度入库量予定
							machineTimePreBean.setNewYearStoragePre(newYearStoragePre.getEhNewYearStoragePre().toString());
							//新年度UP率=新年度入库量予定/入库量实绩
							machineTimePreBean.setNewYearUpRate("0");
							if (storageInActEH.compareTo(BigDecimal.ZERO) > 0) {
								machineTimePreBean.setNewYearUpRate(Float.toString(FloatUtil.IntForFloat(Float.valueOf(machineTimePreBean.getNewYearStoragePre()), Float.valueOf(machineTimePreBean.getStorageActAvg()), 3)));
							}
							//新年度机械时间予定 = 机械时间实绩*新年度UP率
							machineTimePreBean.setNewYearMachineTimePre(String.valueOf(DoubleUtil.DoubleForInt(Double.parseDouble(machineTimePreBean.getMachineTimeActAvg()), Double.parseDouble(machineTimePreBean.getNewYearUpRate()))));
						}
						break;
					default:
						break;
				}
			}
		}

		// 转成「部门」为key，「单位机械时间」为value（如果「新年度机械时间予定」不为空，则value为「新年度机械时间予定」）的map
		// 部门为DH、DM的场合如果「新年度机械时间予定」为空的场合取「机械时间予定(平均)」、不为空则取「新年度机械时间予定」
		// 部门为DH、DM以外的场合「新年度机械时间予定」为空的场合取「机械时间实绩(平均)」、不为空则取「新年度机械时间予定」
		Map<String, String> machineTimePreBeanMap = new HashMap<>();

		for(MachineTimePreBean machineTimePreBean:machineTimePreBeanList) {
			if("DH".equals(machineTimePreBean.getDepartment()) || "DM".equals(machineTimePreBean.getDepartment())) {
				//「新年度机械时间予定」不为空的场合、取「新年度机械时间予定」
				if(org.apache.commons.lang3.StringUtils.isNotEmpty(machineTimePreBean.getNewYearMachineTimePre())) {
					machineTimePreBeanMap.put(machineTimePreBean.getDepartment(), machineTimePreBean.getNewYearMachineTimePre());
				}else {
					// 新年度机械时间予定」为空的场合取「机械时间予定(平均)」
					machineTimePreBeanMap.put(machineTimePreBean.getDepartment(), machineTimePreBean.getMachineTimePreAvg());
				}
			}else {
				//「新年度机械时间予定」不为空的场合、取「新年度机械时间予定」
				if(org.apache.commons.lang3.StringUtils.isNotEmpty(machineTimePreBean.getNewYearMachineTimePre())) {
					machineTimePreBeanMap.put(machineTimePreBean.getDepartment(), machineTimePreBean.getNewYearMachineTimePre());
				}else {
					// 新年度机械时间予定」为空的场合取「机械时间实绩(平均)」
					machineTimePreBeanMap.put(machineTimePreBean.getDepartment(), machineTimePreBean.getMachineTimeActAvg());
				}
			}
		}
		return machineTimePreBeanMap;
	}

	/**
	 * 根据条件获取部门「天然气/氮气」汇总信息
	 * @param monthInterval 日期相差的月数
	 * @param yearMonthStart 开始日期
	 * @param yearMonthEnd 结束日期
	 * @param machineType 机械类别
	 * @param pageType 页面类型
	 * @return
	 */
	@Override
	public List<SummaryOfDepartmentalUnitPricesBean> getNaturalGasSummaryInfos(int monthInterval, String yearMonthStart, String yearMonthEnd, String machineType, String pageType) throws Exception {
		// 「天然气/氮气」汇总信息集合
		List<SummaryOfDepartmentalUnitPricesBean> naturalGasSummaryInfos = new ArrayList<>();
		// 天燃气单价实绩
		BigDecimal gasUnitPriceActAvg = BigDecimal.ZERO;
		// 氮气单价实绩
		BigDecimal nitrogenUnitPriceActAvg = BigDecimal.ZERO;

		// 根据日期范围检索[燃气费用表]取得「天燃气单价实绩」、「氮气单价实绩」
		List<PowerShCoefficientBean> powerOtherList  =  this.selectPowerOtherByDateRange(yearMonthStart, yearMonthEnd);
		for(PowerShCoefficientBean powerOther:powerOtherList) {
			//部门: 05 EH天然气  06 UF氮气
			if("05".equals(powerOther.getDepartment())) {
				gasUnitPriceActAvg = Optional.ofNullable(powerOther.getNitrogenUnitPriceActAvg()).orElse(BigDecimal.ZERO).setScale(3,  RoundingMode.HALF_UP);
			}else {
				nitrogenUnitPriceActAvg = Optional.ofNullable(powerOther.getNitrogenUnitPriceActAvg()).orElse(BigDecimal.ZERO).setScale(3,  RoundingMode.HALF_UP);
			}
		}

		if ("MW".equals(pageType)) {
			if (org.apache.commons.lang3.StringUtils.isNotBlank(machineType) && !machineType.equals("EH")) {
				return naturalGasSummaryInfos;
			}

			// 检索燃气费用表获取天然气实际使用量平均「部门: 05 EH天然气  06 UF氮气」
			BigDecimal gasActAvg = Optional.ofNullable(this.selectNitrogenActAvgByDateRange("05", yearMonthStart, yearMonthEnd)).orElse(BigDecimal.ZERO);
			// 机械时间实际
			HashMap<String,Float> totalDepartScrapAmountMap = xieFenXiService.listxieFenXiBeanForMachineTime(monthInterval, yearMonthStart, yearMonthEnd);

			// 机械时间
			BigDecimal machineTimeActAvg = BigDecimal.valueOf(Optional.ofNullable(totalDepartScrapAmountMap.get("EH")).orElse(0f));

			if (gasActAvg.compareTo(BigDecimal.ZERO) == 0 && gasUnitPriceActAvg.compareTo(BigDecimal.ZERO) == 0) {
				return naturalGasSummaryInfos;
			}

			// SH系数
			BigDecimal shCoefficient = BigDecimal.ZERO;
			// MW天燃气费
			BigDecimal mwNaturalGasCost = BigDecimal.ZERO;
			if (machineTimeActAvg.compareTo(BigDecimal.ZERO) > 0) {
				// SH系数=使用量/机械时间
				shCoefficient = gasActAvg.divide(machineTimeActAvg, 4, RoundingMode.HALF_UP);
				// MW天燃气费=天燃气单价实绩*SH系数
				mwNaturalGasCost = gasUnitPriceActAvg.multiply(shCoefficient).setScale(3,  RoundingMode.HALF_UP);
			}

			SummaryOfDepartmentalUnitPricesBean naturalGasSummaryInfo = new SummaryOfDepartmentalUnitPricesBean();
			naturalGasSummaryInfo.setDepartmentCode("EH");
			naturalGasSummaryInfo.setExpenseItem("天然气");
			naturalGasSummaryInfo.setAttribute("直接");
			naturalGasSummaryInfo.setUnitPrice(gasUnitPriceActAvg);
			naturalGasSummaryInfo.setMachineType("EH");
			naturalGasSummaryInfo.setShCoefficient(shCoefficient);
			naturalGasSummaryInfo.setProjectCost(mwNaturalGasCost);

			naturalGasSummaryInfos.add(naturalGasSummaryInfo);
		} else {
			//检索燃气费用表获取氮气实际使用量平均「部门: 05 EH天然气  06 UF氮气」
			BigDecimal nitrogenActAvg = this.selectNitrogenActAvgByDateRange("06", yearMonthStart, yearMonthEnd);

			// 可查询相关数据的部门
			List<String> queryDepartments = Arrays.asList("EE", "ES", "EU", "EW");
			// 检索预定直接部门回收计算表超细不包含下记部门
			String[] departArr = {"DE", "DFA", "DS", "DU", "UFINS", "DH", "DM"} ;

			if (org.apache.commons.lang3.StringUtils.isNotBlank(machineType)) {
				queryDepartments = Arrays.asList(machineType);
			}

			List<NewYearEleCoeffucientBean> newYearEleCoeffucientBeanList = this.listDirectRecyclingForUF(departArr, monthInterval, yearMonthStart, yearMonthEnd);
			if (CollectionUtils.isNotEmpty(newYearEleCoeffucientBeanList)) {
				// 予定氮气合计
				BigDecimal nitrogenPreTotal = new BigDecimal(0);
				for (NewYearEleCoeffucientBean nitrogenPreTemp : newYearEleCoeffucientBeanList) {
					if(nitrogenPreTemp.getNitrogenPreAvg() != null) {
						nitrogenPreTotal = nitrogenPreTotal.add(nitrogenPreTemp.getNitrogenPreAvg());
					}
				}

				//新年度予定氮气系数查询返回结果实体类
				for (NewYearEleCoeffucientBean newYearEleCoeffucientBean : newYearEleCoeffucientBeanList) {
					if (queryDepartments.contains(newYearEleCoeffucientBean.getDepartment())) {
						SummaryOfDepartmentalUnitPricesBean naturalGasSummaryInfo = new SummaryOfDepartmentalUnitPricesBean();
						naturalGasSummaryInfo.setDepartmentCode(newYearEleCoeffucientBean.getDepartment());
						naturalGasSummaryInfo.setExpenseItem("氮气");
						naturalGasSummaryInfo.setAttribute("直接");
						naturalGasSummaryInfo.setUnitPrice(nitrogenUnitPriceActAvg);
						naturalGasSummaryInfo.setMachineType(newYearEleCoeffucientBean.getDepartment());

						BigDecimal newYearPreCoefficient = BigDecimal.ZERO;
						if (nitrogenPreTotal.compareTo(BigDecimal.ZERO) > 0) {
							// 予定比例
							newYearEleCoeffucientBean.setProportionPre(Optional.ofNullable(newYearEleCoeffucientBean.getNitrogenPreAvg()).orElse(BigDecimal.ZERO).divide(nitrogenPreTotal, 3, RoundingMode.HALF_UP));
							// 实绩氮气量分配 = 予定比例*超细实际氮气量平均
							newYearEleCoeffucientBean.setNitrogenActAllocation(newYearEleCoeffucientBean.getProportionPre().multiply(nitrogenActAvg).setScale(3, RoundingMode.HALF_UP));
							// 新年度予定SYS系数
							newYearPreCoefficient = newYearEleCoeffucientBean.getNitrogenActAllocation().divide(newYearEleCoeffucientBean.getMachineTimePreAvg(), 3, RoundingMode.HALF_UP);
						}

						naturalGasSummaryInfo.setShCoefficient(newYearPreCoefficient);
						// UF氮气费 = 氮气单价实绩*SH系数(新年度予定SYS系数)
						naturalGasSummaryInfo.setProjectCost(nitrogenUnitPriceActAvg.multiply(newYearPreCoefficient).setScale(3,  RoundingMode.HALF_UP));

						naturalGasSummaryInfos.add(naturalGasSummaryInfo);
					}
				}
			}
		}

		return naturalGasSummaryInfos;
	}

	/**
	 * 根据条件获取部门「人件」汇总信息
	 * @param monthInterval 日期相差的月数
	 * @param yearMonthStart 开始日期
	 * @param yearMonthEnd 结束日期
	 * @param machineType 机械类别
	 * @param pageType 页面类型
	 * @param directDepartments 直接部门集合
	 * @return
	 */
	@Override
	public List<SummaryOfDepartmentalUnitPricesBean> getPersonManHourSummaryInfos(int monthInterval, String yearMonthStart, String yearMonthEnd, String machineType, String pageType, List<String> directDepartments) throws Exception {
		// 「人件」汇总信息集合
		List<SummaryOfDepartmentalUnitPricesBean> personManHourSummaryInfos = new ArrayList<>();
		// 人件费SH系数明细
		List<PersonManHourShCoefficientBean> personManHourShCoefficientBeanList;
		//查询单价表
		//UnitPriceRecord unitPriceRecord = Optional.ofNullable(statService.selectUnitPriceRecordByYearMonth(YearUtil.getYear(yearMonthStart))).orElse(new UnitPriceRecord());

		//根据年月获取人件工时明细
		List<HumanHourCost> humanHourCostList = costService.getHumanHourCostsByYearMonth(yearMonthStart, yearMonthEnd);
		//根据年月获取人件费用明细
		List<HumanPriceCost> HumanPriceCostList = costService.getHumanPriceCostByYearMonth(yearMonthStart, yearMonthEnd);

		// 正式工实际总工时
		BigDecimal formalWorkerActualTotalWorkingHours = BigDecimal.ZERO;
		// 劳务工实际总工时
		BigDecimal laoWuWorkerActualTotalWorkingHours = BigDecimal.ZERO;
		// 保全实际总工时
		BigDecimal baoQuanActualTotalWorkingHours = BigDecimal.ZERO;
		// 获取年度
		String year = YearUtil.getYear(yearMonthStart);
		if(CollectionUtils.isNotEmpty(humanHourCostList)){
			// 过滤出所有部门编码
			//Set<String> departmentCodes = humanHourCostList.stream().map(HumanHourCost::getDepartmentCode).collect(Collectors.toSet());
			// 过滤出所有年度
//			Set<String> years = humanHourCostList.stream().map(HumanHourCost::getYearMonth).collect(Collectors.toSet());

			// 根据年度及部门批量查询相关比例信息集合
			List<Proportion> proportions = statService.selectProportionsByYearAndDepartment(year, null);

			// 根据年度和部门分组，key为年度+部门编码，value为比例信息集合
			Map<String, List<Proportion>> proportionsMap = proportions.stream()
					.collect(Collectors.groupingBy(item ->  Optional.ofNullable(item.getYear()).orElse("").trim() + Optional.ofNullable(item.getDepartmentCode()).orElse("").trim()));

			for(HumanHourCost hu : humanHourCostList){
				String departmentCode = hu.getDepartmentCode();

				if(ObjectUtils.isNotEmpty(hu.getWorkHour())){
					if(departmentCode.contains("伸线")){
						departmentCode = "伸线";
					}

					// 根据年度及部门获取比例信息
					Proportion proportion = Optional.ofNullable(Optional.ofNullable(proportionsMap.get(YearUtil.getYear(hu.getYearMonth()) + Optional.ofNullable(departmentCode).orElse("").trim())).orElse(Arrays.asList(new Proportion())).get(0)).orElse(new Proportion());

					//如果不是正式工 跳过;如果是正式工，性质为保全 也跳过
					if("正式工".equals(hu.getWorkType()) && !"保全".equals(hu.getWorkClass())){
						// 计算正式工工时
						if (Arrays.asList("直接", "辅助").contains(hu.getWorkClass())) {
							formalWorkerActualTotalWorkingHours = formalWorkerActualTotalWorkingHours
									.add(Optional.ofNullable(proportion.getEm()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()))
									.add(Optional.ofNullable(proportion.getEf()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()))
									.add(Optional.ofNullable(proportion.getEr()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()));
						}
					}

					if("劳务工".equals(hu.getWorkType())){
						// 计算劳务工工时
						if (Arrays.asList("直接", "辅助").contains(hu.getWorkClass())) {
							laoWuWorkerActualTotalWorkingHours = laoWuWorkerActualTotalWorkingHours
									.add(Optional.ofNullable(proportion.getEm()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()))
									.add(Optional.ofNullable(proportion.getEf()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()))
									.add(Optional.ofNullable(proportion.getEr()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()));
						}
					}

					if(hu.getDepartmentCode().contains("保全")){
						// 计算保全工时
						baoQuanActualTotalWorkingHours = baoQuanActualTotalWorkingHours
								.add(Optional.ofNullable(proportion.getEm()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()))
								.add(Optional.ofNullable(proportion.getEf()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()))
								.add(Optional.ofNullable(proportion.getEr()).orElse(BigDecimal.ZERO).multiply(hu.getWorkHour()));
					}
				}


			}
		}

		// 正式工实际总费用
		BigDecimal formalWorkerActualTotalCost = BigDecimal.ZERO;
		// 劳务工实际总费用
		BigDecimal laoWuWorkerActualTotalCost = BigDecimal.ZERO;
		// 外包工实际总费用
		BigDecimal waiBaoWorkerActualTotalCost = BigDecimal.ZERO;
		// 保全实际总费用
		BigDecimal baoQuanActualTotalCost = BigDecimal.ZERO;

		if(CollectionUtils.isNotEmpty(HumanPriceCostList)){
			// 过滤出所有部门编码
			//Set<String> departmentCodes = HumanPriceCostList.stream().map(HumanPriceCost::getHumanPriceCode).collect(Collectors.toSet());
//			// 过滤出所有年度
//			Set<String> years = HumanPriceCostList.stream().map(HumanPriceCost::getYearMonth).collect(Collectors.toSet());

			// 根据年度及部门批量查询相关比例信息集合
			List<Proportion> proportions = statService.selectProportionsByYearAndDepartment(year, null);
//			List<Proportion> proportions = Optional.ofNullable(statService.selectProportionsByYearAndDepartment(new ArrayList<String>(years), null)).orElse(new ArrayList<>());

			// 根据年度和部门分组，key为年度+部门编码，value为比例信息集合
			Map<String, List<Proportion>> proportionsMap = proportions.stream()
					.collect(Collectors.groupingBy(item ->  Optional.ofNullable(item.getYear()).orElse("").trim() + Optional.ofNullable(item.getDepartmentCode()).orElse("").trim()));

			for(HumanPriceCost hu : HumanPriceCostList){
				String humanPriceCode = hu.getHumanPriceCode();
				BigDecimal humanPrice = hu.getHumanPrice();

				if(ObjectUtils.isNotEmpty(humanPrice)){
					if(humanPriceCode.contains("直接工资-")){
						humanPriceCode = humanPriceCode.replace("直接工资-","");
					}

					// 根据部门获取比例表数据
					Proportion proportion = Optional.ofNullable(Optional.ofNullable(proportionsMap.get(YearUtil.getYear(hu.getYearMonth()) + Optional.ofNullable(humanPriceCode).orElse("").trim())).orElse(Arrays.asList(new Proportion())).get(0)).orElse(new Proportion());

					// 工种为空：一般社员(如果工种不为空 代表非一般社员 跳过; 部门包含‘保全’ -->对应的是保全 也需要跳过)
					if(StringUtil.isStringEmpty(hu.getWorkType()) && !hu.getHumanPriceCode().contains("保全")){
						// 计算正式工费用
						formalWorkerActualTotalCost = formalWorkerActualTotalCost
								.add(Optional.ofNullable(proportion.getEm()).orElse(BigDecimal.ZERO).multiply(humanPrice))
								.add(Optional.ofNullable(proportion.getEf()).orElse(BigDecimal.ZERO).multiply(humanPrice))
								.add(Optional.ofNullable(proportion.getEr()).orElse(BigDecimal.ZERO).multiply(humanPrice));
					}

					if("劳务工".equals(hu.getWorkType()) || "退休工".equals(hu.getWorkType())) {
						// 计算劳务工费用
						laoWuWorkerActualTotalCost = laoWuWorkerActualTotalCost
								.add(Optional.ofNullable(proportion.getEm()).orElse(BigDecimal.ZERO).multiply(humanPrice))
								.add(Optional.ofNullable(proportion.getEf()).orElse(BigDecimal.ZERO).multiply(humanPrice))
								.add(Optional.ofNullable(proportion.getEr()).orElse(BigDecimal.ZERO).multiply(humanPrice));
					}

					if("外包工".equals(hu.getWorkType())) {
						//外包工的EM 的场合需做特殊处理⇒金额全部分配给EM，不需要调用「比例表」
						if("EM".equals(humanPriceCode)){
							waiBaoWorkerActualTotalCost = waiBaoWorkerActualTotalCost.add(humanPrice);
						} else {
							// 计算外包工费用
							waiBaoWorkerActualTotalCost = waiBaoWorkerActualTotalCost
									.add(Optional.ofNullable(proportion.getEm()).orElse(BigDecimal.ZERO).multiply(humanPrice))
									.add(Optional.ofNullable(proportion.getEf()).orElse(BigDecimal.ZERO).multiply(humanPrice))
									.add(Optional.ofNullable(proportion.getEr()).orElse(BigDecimal.ZERO).multiply(humanPrice));
						}
					}

					if(hu.getHumanPriceCode().contains("保全")) {
						// 计算保全费用
						baoQuanActualTotalCost = baoQuanActualTotalCost
								.add(Optional.ofNullable(proportion.getEm()).orElse(BigDecimal.ZERO).multiply(humanPrice))
								.add(Optional.ofNullable(proportion.getEf()).orElse(BigDecimal.ZERO).multiply(humanPrice))
								.add(Optional.ofNullable(proportion.getEr()).orElse(BigDecimal.ZERO).multiply(humanPrice));
					}
				}
			}

			// 正式工实际总费用 = 原始正式工总费用 - 劳务工实际总费用
			formalWorkerActualTotalCost = formalWorkerActualTotalCost.subtract(laoWuWorkerActualTotalCost);

			// 劳务工实际总费用 = 原始劳务工总费用 + 外包工实际总费用
			laoWuWorkerActualTotalCost = laoWuWorkerActualTotalCost.add(waiBaoWorkerActualTotalCost);
		}

		// 正式工单价
		BigDecimal formalWorkerUnitPrice = BigDecimal.ZERO;
		// 劳务工单价
		BigDecimal laoWuWorkerUnitPrice = BigDecimal.ZERO;
		// 保全单价
		BigDecimal baoQuanUnitPrice = BigDecimal.ZERO;

		// 计算正式工单价
		if (formalWorkerActualTotalWorkingHours.compareTo(BigDecimal.ZERO) > 0) {
			// 正式工单价 = 正式工实际总费用 / 正式工实际总工时
			formalWorkerUnitPrice = formalWorkerActualTotalCost.divide(formalWorkerActualTotalWorkingHours, 2, RoundingMode.HALF_UP);
		}

		// 计算劳务工单价
		if (laoWuWorkerActualTotalWorkingHours.compareTo(BigDecimal.ZERO) > 0) {
			// 劳务工单价 = 劳务工实际总费用 / 劳务工实际总工时
			laoWuWorkerUnitPrice = laoWuWorkerActualTotalCost.divide(laoWuWorkerActualTotalWorkingHours, 2, RoundingMode.HALF_UP);
		}

		// 计算保全单价
		if (baoQuanActualTotalWorkingHours.compareTo(BigDecimal.ZERO) > 0) {
			// 保全单价 = 保全实际总费用 / 保全实际总工时
			baoQuanUnitPrice = baoQuanActualTotalCost.divide(baoQuanActualTotalWorkingHours, 2, RoundingMode.HALF_UP);
		}

		if("MW".equals(pageType)) {
			//人件费SH系数明细(MW)
			personManHourShCoefficientBeanList = getPersonManHourShCoefficientBeanList(monthInterval, yearMonthStart, yearMonthEnd, null);
		} else {
			//人件费SH系数明细(UF)
			personManHourShCoefficientBeanList = getUfPersonManHourShCoefficientBeanList(monthInterval, yearMonthStart, yearMonthEnd, null);
		}

		if (CollectionUtils.isNotEmpty(personManHourShCoefficientBeanList)) {
			//// 未整合的「人件」汇总信息集合
			//List<SummaryOfDepartmentalUnitPricesBean> unGroupPersonManHourSummaryInfos = new ArrayList<>();


			for (PersonManHourShCoefficientBean personManHourShCoefficientBean : personManHourShCoefficientBeanList) {
				if (org.apache.commons.lang3.StringUtils.isBlank(machineType) || machineType.equals(personManHourShCoefficientBean.getMachineType())) {
					SummaryOfDepartmentalUnitPricesBean personManHourSummaryInfo = new SummaryOfDepartmentalUnitPricesBean();
					personManHourSummaryInfo.setWorkHour(personManHourShCoefficientBean.getWorkHour());
					personManHourSummaryInfo.setMachineTimePreAvg(personManHourShCoefficientBean.getMachineTimePreAvg());
					personManHourSummaryInfo.setExpenseItem(personManHourShCoefficientBean.getWorkType());
					personManHourSummaryInfo.setAttribute(personManHourShCoefficientBean.getWorkClass());
					personManHourSummaryInfo.setDepartmentCode(personManHourShCoefficientBean.getDepartmentCode());

					if ("保全".equals(personManHourShCoefficientBean.getWorkClass())) {
						if ("保全".equals(personManHourShCoefficientBean.getWorkType())) {
							personManHourSummaryInfo.setExpenseItem(personManHourShCoefficientBean.getWorkType());
						} else {
							personManHourSummaryInfo.setExpenseItem("保全" + personManHourShCoefficientBean.getWorkType());
						}

						if (directDepartments.contains(personManHourShCoefficientBean.getDepartmentCode())) {
							personManHourSummaryInfo.setAttribute("直接");
						} else {
							personManHourSummaryInfo.setAttribute("辅助");
						}

						personManHourSummaryInfo.setUnitPrice(baoQuanUnitPrice);
					} else if ("正式工".equals(personManHourShCoefficientBean.getWorkType())) {
						personManHourSummaryInfo.setUnitPrice(formalWorkerUnitPrice);
					} else if ("劳务工".equals(personManHourShCoefficientBean.getWorkType())) {
						personManHourSummaryInfo.setUnitPrice(laoWuWorkerUnitPrice);
					}

					personManHourSummaryInfo.setMachineType(personManHourShCoefficientBean.getMachineType());
					personManHourSummaryInfo.setShCoefficient(new BigDecimal(Optional.ofNullable(personManHourShCoefficientBean.getPersonManHourSH()).orElse("0")));

					// 人件费 = 各工种人员单价 * MH工时SH系数
					personManHourSummaryInfo.setProjectCost(personManHourSummaryInfo.getUnitPrice().multiply(personManHourSummaryInfo.getShCoefficient()).setScale(3,  RoundingMode.HALF_UP));

					personManHourSummaryInfos.add(personManHourSummaryInfo);
				}
			}

			//// 根据「机械类别、费用项目、属性」进行分组
			//Map<String, List<SummaryOfDepartmentalUnitPricesBean>> personManHourSummaryInfosMap = unGroupPersonManHourSummaryInfos.stream()
			//	.collect(Collectors.groupingBy(item ->  item.getMachineType() + item.getExpenseItem() + item.getAttribute()));
			//
			//// 根据「机械类别、费用项目、属性」整合计算「人件费」
			//for (Map.Entry<String, List<SummaryOfDepartmentalUnitPricesBean>> entry : personManHourSummaryInfosMap.entrySet()) {
			//	List<SummaryOfDepartmentalUnitPricesBean> groupList = entry.getValue();
			//
			//	if (CollectionUtils.isNotEmpty(groupList)) {
			//		SummaryOfDepartmentalUnitPricesBean summaryOfDepartmentalUnitPricesBean = groupList.get(0);
			//
			//		if (groupList.size() > 1) {
			//			// 工时合计
			//			BigDecimal workHourTotal = BigDecimal.ZERO;
			//			// 予定机械时间合计
			//			BigDecimal machineTimePreAvgTotal = BigDecimal.ZERO;
			//
			//			for (SummaryOfDepartmentalUnitPricesBean summaryOfDepartmentalUnitPrices : groupList) {
			//				workHourTotal = workHourTotal.add(Optional.ofNullable(summaryOfDepartmentalUnitPrices.getWorkHour()).orElse(BigDecimal.ZERO));
			//				machineTimePreAvgTotal = machineTimePreAvgTotal.add(Optional.ofNullable(summaryOfDepartmentalUnitPrices.getMachineTimePreAvg()).orElse(BigDecimal.ZERO));
			//			}
			//
			//			// SH系数 = 工时合计 / 予定机械时间合计
			//			summaryOfDepartmentalUnitPricesBean.setShCoefficient(workHourTotal.divide(machineTimePreAvgTotal, 4, RoundingMode.HALF_UP));
			//			// 人件费 = 各工种人员单价 * MH工时SH系数
			//			summaryOfDepartmentalUnitPricesBean.setProjectCost(summaryOfDepartmentalUnitPricesBean.getUnitPrice().multiply(summaryOfDepartmentalUnitPricesBean.getShCoefficient()).setScale(3,  RoundingMode.HALF_UP));
			//		}
			//
			//		personManHourSummaryInfos.add(summaryOfDepartmentalUnitPricesBean);
			//	}
			//}

			// 根据「属性、费用项目、机械类别」进行排序
			personManHourSummaryInfos.sort(Comparator.comparing(SummaryOfDepartmentalUnitPricesBean::getAttribute)
					.thenComparing(SummaryOfDepartmentalUnitPricesBean::getExpenseItem)
					.thenComparing(SummaryOfDepartmentalUnitPricesBean::getMachineType));

		}

		return personManHourSummaryInfos;
	}

	/**
	 * 根据条件获取部门「补修、辅材」汇总信息
	 * @param monthInterval 日期相差的月数
	 * @param yearMonthStart 开始日期
	 * @param yearMonthEnd 结束日期
	 * @param machineType 机械类别
	 * @param pageType 页面类型
	 * @param directDepartments 直接部门集合
	 * @return
	 */
	@Override
	public List<SummaryOfDepartmentalUnitPricesBean> getRepairAndAuxiliarySummaryInfos(int monthInterval, String yearMonthStart, String yearMonthEnd, String machineType, String pageType, List<String> directDepartments) throws Exception {
		// 「补修、辅材」汇总信息集合
		List<SummaryOfDepartmentalUnitPricesBean> repairAndAuxiliarySummaryInfos = new ArrayList<>();
		// 补修及补修分配额SH系数明细
		List<RepairAndAuxiliaryShCoefficientBean> repairAndAuxiliaryShCoefficientBeanList = new ArrayList<>();

		if("MW".equals(pageType)) {
			// 根据开始年月取得年度
			String year = YearUtil.getYear(yearMonthStart);
			// 检索[补辅部门列表] 取得MW 的直接部门
			List<String> directDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_1, RepairAndAuxiliaryMaterialDepartment.CATEGORY_1);
			if(CollectionUtils.isEmpty(directDepartList)) {
				return repairAndAuxiliarySummaryInfos;
			}

			// 补修分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> repairShCoefficientList = getRepairAndAuxiliaryShCoefficientBeanList(directDepartList, monthInterval, yearMonthStart, yearMonthEnd, AuxiliaryRecycling.EXPENSEITEM_0080, "补修费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(repairShCoefficientList);
			// 辅材分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> auxiliaryShCoefficient = getRepairAndAuxiliaryShCoefficientBeanList(directDepartList, monthInterval, yearMonthStart, yearMonthEnd,AuxiliaryRecycling.EXPENSEITEM_0090, "辅材费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(auxiliaryShCoefficient);
		} else {
			// 补修分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> repairShCoefficientBeanList = getRepairAndAuxiliaryShCoefficientUFBeanList(
					monthInterval, yearMonthStart, yearMonthEnd, "补修费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(repairShCoefficientBeanList);
			// 辅材分配额SH系数明细
			List<RepairAndAuxiliaryShCoefficientBean> auxiliaryShCoefficientBeanList = getRepairAndAuxiliaryShCoefficientUFBeanList(
					monthInterval, yearMonthStart, yearMonthEnd, "辅材费");
			repairAndAuxiliaryShCoefficientBeanList.addAll(auxiliaryShCoefficientBeanList);
		}

		if (CollectionUtils.isNotEmpty(repairAndAuxiliaryShCoefficientBeanList)) {
			for (RepairAndAuxiliaryShCoefficientBean repairAndAuxiliaryShCoefficientBean : repairAndAuxiliaryShCoefficientBeanList) {
				if (org.apache.commons.lang3.StringUtils.isBlank(machineType) || machineType.equals(repairAndAuxiliaryShCoefficientBean.getMachineType())) {
					SummaryOfDepartmentalUnitPricesBean repairAndAuxiliarySummaryInfo = new SummaryOfDepartmentalUnitPricesBean();
					repairAndAuxiliarySummaryInfo.setMoneyAct(repairAndAuxiliaryShCoefficientBean.getMoneyAct());
					repairAndAuxiliarySummaryInfo.setMachineTimePreAvg(repairAndAuxiliaryShCoefficientBean.getMachineTimePreAvg());
					repairAndAuxiliarySummaryInfo.setExpenseItem(repairAndAuxiliaryShCoefficientBean.getExpenseItem());

					if (directDepartments.contains(repairAndAuxiliaryShCoefficientBean.getDepartment())) {
						repairAndAuxiliarySummaryInfo.setAttribute("直接");
					} else {
						repairAndAuxiliarySummaryInfo.setAttribute("辅助");
					}

					repairAndAuxiliarySummaryInfo.setDepartmentCode(repairAndAuxiliaryShCoefficientBean.getDepartment());
					// 单位所要率(固定值:1)
					repairAndAuxiliarySummaryInfo.setUnitPrice(BigDecimal.ONE);
					repairAndAuxiliarySummaryInfo.setMachineType(repairAndAuxiliaryShCoefficientBean.getMachineType());
					repairAndAuxiliarySummaryInfo.setShCoefficient(Optional.ofNullable(repairAndAuxiliaryShCoefficientBean.getRepairAndAuxiliarySH()).orElse(BigDecimal.ZERO));

					// MW补修费、辅材 = 分配额(补辅材分配额SH系数) * 单位所要率(固定值:1)
					repairAndAuxiliarySummaryInfo.setProjectCost(repairAndAuxiliarySummaryInfo.getUnitPrice().multiply(repairAndAuxiliarySummaryInfo.getShCoefficient()).setScale(3,  RoundingMode.HALF_UP));

					repairAndAuxiliarySummaryInfos.add(repairAndAuxiliarySummaryInfo);
				}
			}

			//// 根据「机械类别、费用项目、属性」进行分组
			//Map<String, List<SummaryOfDepartmentalUnitPricesBean>> repairAndAuxiliarySummaryInfosMap = repairAndAuxiliarySummaryInfos.stream()
			//	.collect(Collectors.groupingBy(item ->  item.getMachineType() + item.getExpenseItem() + item.getAttribute()));
			//
			//// 根据「机械类别、费用项目、属性」整合计算「补修费、辅材费」
			//for (Map.Entry<String, List<SummaryOfDepartmentalUnitPricesBean>> entry : repairAndAuxiliarySummaryInfosMap.entrySet()) {
			//	List<SummaryOfDepartmentalUnitPricesBean> groupList = entry.getValue();
			//
			//	if (CollectionUtils.isNotEmpty(groupList)) {
			//		SummaryOfDepartmentalUnitPricesBean summaryOfDepartmentalUnitPricesBean = groupList.get(0);
			//
			//		if (groupList.size() > 1) {
			//			// 实际金额合计
			//			BigDecimal moneyActTotal = BigDecimal.ZERO;
			//			// 予定机械时间合计
			//			BigDecimal machineTimePreAvgTotal = BigDecimal.ZERO;
			//
			//			for (SummaryOfDepartmentalUnitPricesBean summaryOfDepartmentalUnitPrices : groupList) {
			//				moneyActTotal = moneyActTotal.add(Optional.ofNullable(summaryOfDepartmentalUnitPrices.getMoneyAct()).orElse(BigDecimal.ZERO));
			//				machineTimePreAvgTotal = machineTimePreAvgTotal.add(Optional.ofNullable(summaryOfDepartmentalUnitPrices.getMachineTimePreAvg()).orElse(BigDecimal.ZERO));
			//			}
			//
			//			// SH系数 = 实际金额合计 / 予定机械时间合计
			//			summaryOfDepartmentalUnitPricesBean.setShCoefficient(moneyActTotal.divide(machineTimePreAvgTotal, 4, RoundingMode.HALF_UP));
			//			// 补修费、辅材费 = 实际金额合计 * SH系数
			//			summaryOfDepartmentalUnitPricesBean.setProjectCost(summaryOfDepartmentalUnitPricesBean.getUnitPrice().multiply(summaryOfDepartmentalUnitPricesBean.getShCoefficient()).setScale(3,  RoundingMode.HALF_UP));
			//		}
			//
			//		repairAndAuxiliarySummaryInfos.add(summaryOfDepartmentalUnitPricesBean);
			//	}
			//}

			// 根据「属性、费用项目、机械类别」进行排序
			repairAndAuxiliarySummaryInfos.sort(Comparator.comparing(SummaryOfDepartmentalUnitPricesBean::getAttribute)
					.thenComparing(SummaryOfDepartmentalUnitPricesBean::getExpenseItem)
					.thenComparing(SummaryOfDepartmentalUnitPricesBean::getMachineType));

		}

		return repairAndAuxiliarySummaryInfos;

	}

	/**
	 * 补修辅材分配额SH系数明细(UF)
	 * @param monthInterval  日期相差的月数
	 * @param timeStartStr 开始日期
	 * @param timeEndStr 结束日期
	 * @param expenseCode 费用编号 （0080:补修 / 0090:辅材）
	 * @param expenseType 费用项目
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/02/01 09:43
	 * @return  List<RepairAndAuxiliaryShCoefficientBean>
	 * @throws ParseException
	 */
	@Override
	public List<RepairAndAuxiliaryShCoefficientBean> getRepairAndAuxiliaryShCoefficientUFBeanList(int monthInterval, String timeStartStr, String timeEndStr, String expenseType) throws Exception{

		List<RepairAndAuxiliaryShCoefficientBean> repairAndAuxiliaryShCoefficientBeanList = new ArrayList<>();
		// 根据开始年月取得年度
		String year = YearUtil.getYear(timeStartStr);

		// 辅助部门实际金额总计(加权平均)
		BigDecimal auxiliaryActAmountTotal =new BigDecimal(0);
		// 检索[补辅部门列表] 取得UF 的直接部门(机械类别)
		List<String> directDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_1, RepairAndAuxiliaryMaterialDepartment.CATEGORY_2);
		// 检索[补辅部门列表] 取得UF 的辅助部门
		List<String> auxiliaryDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_2, RepairAndAuxiliaryMaterialDepartment.CATEGORY_2);

		// 遍历UF 的辅助部门 获取辅助部门实际金额总计
		for(int i= 0; i < auxiliaryDepartList.size(); i++) {
			// 辅助部门实际金额
			BigDecimal auxiliaryActAmount = isBigDecimalNotNull(this.selectAuxMaterialCostSum(timeStartStr, timeEndStr,expenseType, null, auxiliaryDepartList.get(i),  RepairAndAuxiliaryMaterialDepartment.CATEGORY_2));
			auxiliaryActAmountTotal = auxiliaryActAmountTotal.add(auxiliaryActAmount.divide(new BigDecimal(monthInterval), 4 , RoundingMode.HALF_UP));
		}
		// MW补修/辅材分摊UF金额
		BigDecimal shareAmountOfMW = getShareAmountForUF(monthInterval, timeStartStr, timeEndStr , expenseType);
		auxiliaryActAmountTotal = auxiliaryActAmountTotal.add(shareAmountOfMW);

		//UF检索条件直接部门(对象外)
		String[] departArr = {"DH", "DM"} ;
		// 取得预定直接部门回收计算表取得[补修费费用]、[辅材费费用]（加权平均）
		List<DirectRecycling> directRecyclingList = this.listDirectRecyclingUFByDateRange(departArr, monthInterval, timeStartStr, timeEndStr);
		if(directRecyclingList != null && directRecyclingList.size() > 0) {
			// 所有部门予定金额总计(加权平均)
			BigDecimal auxiliaryPreAmountTotal =new BigDecimal(0);
			// 遍历List 取得所有部门予定金额合计
			for(DirectRecycling directRecycling : directRecyclingList) {
				// 补修费的场合
				if("补修费".equals(expenseType) ) {
					if(directRecycling.getRepairCost() != null) {
						auxiliaryPreAmountTotal=auxiliaryPreAmountTotal.add(directRecycling.getRepairCost());
					}
				}else {
					// 辅材费的场合
					if(directRecycling.getMaterialCost() != null) {
						auxiliaryPreAmountTotal=auxiliaryPreAmountTotal.add(directRecycling.getMaterialCost());
					}
				}
			}
			// UF机械时间予定
			Map<String, BigDecimal> ufMachineTimePreMap = getUfMachineTimePreMap(directDepartList, monthInterval,timeStartStr, timeEndStr);

			for(DirectRecycling directRecycling : directRecyclingList) {
				RepairAndAuxiliaryShCoefficientBean  RepairAndAuxiliaryShCoefficientBean = new RepairAndAuxiliaryShCoefficientBean();
				for (int i = 0; i < directDepartList.size(); i++) {
					if(directRecycling.getDepartmentCode().equals(directDepartList.get(i))) {
						RepairAndAuxiliaryShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
						RepairAndAuxiliaryShCoefficientBean.setDepartment(directRecycling.getDepartmentCode()); //部门
						RepairAndAuxiliaryShCoefficientBean.setExpenseItem(expenseType);//费用项目
						// 予定金额
						if("补修费".equals(expenseType) ) {//  辅材费的场合
							RepairAndAuxiliaryShCoefficientBean.setMoneyPre(isBigDecimalNotNull(directRecycling.getRepairCost()));
						}else {
							//  辅材费的场合
							RepairAndAuxiliaryShCoefficientBean.setMoneyPre(isBigDecimalNotNull(directRecycling.getMaterialCost()));
						}
						// 直接部门实际金额=直接部门实际金额总计/月数差
						BigDecimal directActAmountDE = isBigDecimalNotNull(this.selectAuxMaterialCostSum(timeStartStr, timeEndStr,expenseType, null, directRecycling.getDepartmentCode(), RepairAndAuxiliaryMaterialDepartment.CATEGORY_2)).divide(new BigDecimal(monthInterval),10,RoundingMode.HALF_UP);
						//实际辅助部门实际分摊金额=（部门予定金额/全部门予定金额） * 辅助部门实际金额总计
						BigDecimal auxiliaryActAmountOfShareDE = RepairAndAuxiliaryShCoefficientBean.getMoneyPre().divide(auxiliaryPreAmountTotal, 8, RoundingMode.HALF_UP).multiply(auxiliaryActAmountTotal);//实际辅助部门实际分摊金额
						// 实际金额=直接部门实际金额+辅助部门实际分摊金额
						RepairAndAuxiliaryShCoefficientBean.setMoneyAct(directActAmountDE.add(auxiliaryActAmountOfShareDE));
						RepairAndAuxiliaryShCoefficientBean.setMachineTimePreAvg(ufMachineTimePreMap.get(RepairAndAuxiliaryShCoefficientBean.getMachineType()));//机械时间予定
						//补辅材分配额SH系数  = 实际金额（直接部门+辅助部门） /机械时间予定
						RepairAndAuxiliaryShCoefficientBean.setRepairAndAuxiliarySH(RepairAndAuxiliaryShCoefficientBean.getMoneyAct().divide(RepairAndAuxiliaryShCoefficientBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
						repairAndAuxiliaryShCoefficientBeanList.add(RepairAndAuxiliaryShCoefficientBean);
						break;
					}
				}
			}
		}

		return repairAndAuxiliaryShCoefficientBeanList;
	}

	/**
	 * 补修辅材分配额SH系数明细(MW)
	 * @param monthInterval  日期相差的月数
	 * @param timeStartStr 开始日期
	 * @param timeEndStr 结束日期
	 * @param expenseCode 费用编号 （0080:补修 / 0090:辅材）
	 * @param expenseType 费用项目
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/02/01 09:43
	 * @return  List<RepairAndAuxiliaryShCoefficientBean>
	 * @throws ParseException
	 */
	@Override
	public List<RepairAndAuxiliaryShCoefficientBean> getRepairAndAuxiliaryShCoefficientBeanList(List<String> directDepartList, int monthInterval, String timeStartStr, String timeEndStr, String expenseCode, String expenseItem) throws Exception{
		List<RepairAndAuxiliaryShCoefficientBean> repairAndAuxiliaryShCoefficientBeanList = new ArrayList<>();
		// 根据开始年月取得年度
		String year = YearUtil.getYear(timeStartStr);

		// 检索[补辅部门列表] 取得MW 的直接部门
//    	List<String> directDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_1, RepairAndAuxiliaryMaterialDepartment.CATEGORY_1);
		String[] departArr = directDepartList.toArray(new String[directDepartList.size()]);

		// 获取MW平均予定机械时间
		Map<String, BigDecimal> mwMachineTimePreMap =getMwMachineTimePreMap(directDepartList, monthInterval, timeStartStr, timeEndStr);

		// 机械类别DH予定费用总计(直接＋辅助)
		BigDecimal amountPreTotalDH = new BigDecimal(0);
		// 机械类别DM予定费用总计(直接＋辅助)
		BigDecimal amountPreTotalDM = new BigDecimal(0);
		// 机械类别EM予定费用总计(直接＋辅助)
		BigDecimal amountPreTotalEM = new BigDecimal(0);
		// 机械类别EF予定费用总计(直接＋辅助)
		BigDecimal amountPreTotalEF = new BigDecimal(0);
		// 机械类别EF09予定费用总计(直接＋辅助)
		BigDecimal amountPreTotalEF09 = new BigDecimal(0);
		// 机械类别ER予定费用总计(直接＋辅助)
		BigDecimal amountPreTotalER = new BigDecimal(0);
		// 机械类别EH予定费用总计(直接＋辅助)
		BigDecimal amountPreTotalEH = new BigDecimal(0);
		// 机械类别ALL予定费用总计
		BigDecimal amountPreTotal = new BigDecimal(0);

		// 预定直接部门回收-补修辅助金额（平均）
		List<DirectRecycling> DirectRecyclingList = this.listDirectRecyclingByDateRange(departArr, monthInterval, 0, timeStartStr, timeEndStr);

		// 直接部门的场合：直接部门=机械类别
		// 补修直接部门设定
		for(DirectRecycling directRecycling:DirectRecyclingList) {
			RepairAndAuxiliaryShCoefficientBean  coefficientOfDirectBean = new RepairAndAuxiliaryShCoefficientBean();
			switch(directRecycling.getDepartmentCode()) {
				case "DH":
					coefficientOfDirectBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别(直接部门)
					coefficientOfDirectBean.setDepartment(directRecycling.getDepartmentCode()); //部门
					coefficientOfDirectBean.setExpenseItem(expenseItem);//费用项目
					// 费用项目为0080（补修）的场合
					if(AuxiliaryRecycling.EXPENSEITEM_0080.equals(expenseCode)) {
						coefficientOfDirectBean.setMoneyPre(directRecycling.getRepairCost()); // 补修予定金额
					}else {
						//上记以外的场合0090（辅材）
						coefficientOfDirectBean.setMoneyPre(directRecycling.getMaterialCost()); // 辅材予定金额
					}
					coefficientOfDirectBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr, coefficientOfDirectBean.getExpenseItem(),coefficientOfDirectBean.getDepartment())); // 实际金额
					coefficientOfDirectBean.setMachineTimePreAvg(mwMachineTimePreMap.get(coefficientOfDirectBean.getMachineType()));//机械时间予定
					//补辅材分配额SH系数  = 实际金额 /机械时间予定
					coefficientOfDirectBean.setRepairAndAuxiliarySH(coefficientOfDirectBean.getMoneyAct().divide(coefficientOfDirectBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
					repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfDirectBean);

					// DH部门予定费用总计
					amountPreTotalDH = amountPreTotalDH.add(coefficientOfDirectBean.getMoneyPre());
					break;
				case "DM":
					coefficientOfDirectBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别(直接部门)
					coefficientOfDirectBean.setDepartment(directRecycling.getDepartmentCode()); //部门
					coefficientOfDirectBean.setExpenseItem(expenseItem);//费用项目
					// 费用项目为0080（补修）的场合
					if(AuxiliaryRecycling.EXPENSEITEM_0080.equals(expenseCode)) {
						coefficientOfDirectBean.setMoneyPre(directRecycling.getRepairCost()); // 补修予定金额
					}else {
						//上记以外的场合0090（辅材）
						coefficientOfDirectBean.setMoneyPre(directRecycling.getMaterialCost()); // 辅材予定金额
					}
					coefficientOfDirectBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr, coefficientOfDirectBean.getExpenseItem(),coefficientOfDirectBean.getDepartment())); // 实际金额
					coefficientOfDirectBean.setMachineTimePreAvg(mwMachineTimePreMap.get(coefficientOfDirectBean.getMachineType()));//机械时间予定
					//补辅材分配额SH系数  = 实际金额 /机械时间予定
					coefficientOfDirectBean.setRepairAndAuxiliarySH(coefficientOfDirectBean.getMoneyAct().divide(coefficientOfDirectBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
					repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfDirectBean);

					// DH部门予定费用总计
					amountPreTotalDM = amountPreTotalDM.add(coefficientOfDirectBean.getMoneyPre());
					break;
				case "EM":
					coefficientOfDirectBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别(直接部门)
					coefficientOfDirectBean.setDepartment(directRecycling.getDepartmentCode()); //部门
					coefficientOfDirectBean.setExpenseItem(expenseItem);//费用项目
					// 费用项目为0080（补修）的场合
					if(AuxiliaryRecycling.EXPENSEITEM_0080.equals(expenseCode)) {
						coefficientOfDirectBean.setMoneyPre(directRecycling.getRepairCost()); // 补修予定金额
					}else {
						//上记以外的场合0090（辅材）
						coefficientOfDirectBean.setMoneyPre(directRecycling.getMaterialCost()); // 辅材予定金额
					}
					coefficientOfDirectBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
							coefficientOfDirectBean.getExpenseItem(),coefficientOfDirectBean.getDepartment())); // 实际金额
					coefficientOfDirectBean.setMachineTimePreAvg(mwMachineTimePreMap.get(coefficientOfDirectBean.getMachineType()));//机械时间予定
					//补辅材分配额SH系数  = 实际金额 /机械时间予定
					coefficientOfDirectBean.setRepairAndAuxiliarySH(coefficientOfDirectBean.getMoneyAct().divide(coefficientOfDirectBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
					repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfDirectBean);

					// DH部门予定费用总计
					amountPreTotalEM = amountPreTotalEM.add(coefficientOfDirectBean.getMoneyPre());
					break;
				case "EF":
					coefficientOfDirectBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别(直接部门)
					coefficientOfDirectBean.setDepartment(directRecycling.getDepartmentCode()); //部门
					coefficientOfDirectBean.setExpenseItem(expenseItem);//费用项目
					// 费用项目为0080（补修）的场合
					if(AuxiliaryRecycling.EXPENSEITEM_0080.equals(expenseCode)) {
						coefficientOfDirectBean.setMoneyPre(directRecycling.getRepairCost()); // 补修予定金额
					}else {
						//上记以外的场合0090（辅材）
						coefficientOfDirectBean.setMoneyPre(directRecycling.getMaterialCost()); // 辅材予定金额
					}
					coefficientOfDirectBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
							coefficientOfDirectBean.getExpenseItem(),coefficientOfDirectBean.getDepartment())); // 实际金额
					coefficientOfDirectBean.setMachineTimePreAvg(mwMachineTimePreMap.get(coefficientOfDirectBean.getMachineType()));//机械时间予定
					//补辅材分配额SH系数  = 实际金额 /机械时间予定
					coefficientOfDirectBean.setRepairAndAuxiliarySH(coefficientOfDirectBean.getMoneyAct().divide(coefficientOfDirectBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
					repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfDirectBean);

					// DH部门予定费用总计
					amountPreTotalEF = amountPreTotalEF.add(coefficientOfDirectBean.getMoneyPre());
					break;
				case "EF09":
					coefficientOfDirectBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别(直接部门)
					coefficientOfDirectBean.setDepartment(directRecycling.getDepartmentCode()); //部门
					coefficientOfDirectBean.setExpenseItem(expenseItem);//费用项目
					// 费用项目为0080（补修）的场合
					if(AuxiliaryRecycling.EXPENSEITEM_0080.equals(expenseCode)) {
						coefficientOfDirectBean.setMoneyPre(directRecycling.getRepairCost()); // 补修予定金额
					}else {
						//上记以外的场合0090（辅材）
						coefficientOfDirectBean.setMoneyPre(directRecycling.getMaterialCost()); // 辅材予定金额
					}
					coefficientOfDirectBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
							coefficientOfDirectBean.getExpenseItem(),coefficientOfDirectBean.getDepartment())); // 实际金额
					coefficientOfDirectBean.setMachineTimePreAvg(mwMachineTimePreMap.get(coefficientOfDirectBean.getMachineType()));//机械时间予定
					//补辅材分配额SH系数  = 实际金额 /机械时间予定
					coefficientOfDirectBean.setRepairAndAuxiliarySH(coefficientOfDirectBean.getMoneyAct().divide(coefficientOfDirectBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
					repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfDirectBean);

					// DH部门予定费用总计
					amountPreTotalEF09 = amountPreTotalEF09.add(coefficientOfDirectBean.getMoneyPre());
					break;
				case "ER":
					coefficientOfDirectBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别(直接部门)
					coefficientOfDirectBean.setDepartment(directRecycling.getDepartmentCode()); //部门
					coefficientOfDirectBean.setExpenseItem(expenseItem);//费用项目
					// 费用项目为0080（补修）的场合
					if(AuxiliaryRecycling.EXPENSEITEM_0080.equals(expenseCode)) {
						coefficientOfDirectBean.setMoneyPre(directRecycling.getRepairCost()); // 补修予定金额
					}else {
						//上记以外的场合0090（辅材）
						coefficientOfDirectBean.setMoneyPre(directRecycling.getMaterialCost()); // 辅材予定金额
					}
					coefficientOfDirectBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
							coefficientOfDirectBean.getExpenseItem(),coefficientOfDirectBean.getDepartment())); // 实际金额
					coefficientOfDirectBean.setMachineTimePreAvg(mwMachineTimePreMap.get(coefficientOfDirectBean.getMachineType()));//机械时间予定
					//补辅材分配额SH系数  = 实际金额 /机械时间予定
					coefficientOfDirectBean.setRepairAndAuxiliarySH(coefficientOfDirectBean.getMoneyAct().divide(coefficientOfDirectBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
					repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfDirectBean);

					// DH部门予定费用总计
					amountPreTotalER = amountPreTotalER.add(coefficientOfDirectBean.getMoneyPre());
					break;
				case "EH":
					coefficientOfDirectBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别(直接部门)
					coefficientOfDirectBean.setDepartment(directRecycling.getDepartmentCode()); //部门
					coefficientOfDirectBean.setExpenseItem(expenseItem);//费用项目
					// 费用项目为0080（补修）的场合
					if(AuxiliaryRecycling.EXPENSEITEM_0080.equals(expenseCode)) {
						coefficientOfDirectBean.setMoneyPre(directRecycling.getRepairCost()); // 补修予定金额
					}else {
						//上记以外的场合0090（辅材）
						coefficientOfDirectBean.setMoneyPre(directRecycling.getMaterialCost()); // 辅材予定金额
					}
					coefficientOfDirectBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
							coefficientOfDirectBean.getExpenseItem(),coefficientOfDirectBean.getDepartment())); // 实际金额
					coefficientOfDirectBean.setMachineTimePreAvg(mwMachineTimePreMap.get(coefficientOfDirectBean.getMachineType()));//机械时间予定
					//补辅材分配额SH系数  = 实际金额 /机械时间予定
					coefficientOfDirectBean.setRepairAndAuxiliarySH(coefficientOfDirectBean.getMoneyAct().divide(coefficientOfDirectBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
					repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfDirectBean);

					// DH部门予定费用总计
					amountPreTotalEH = amountPreTotalEH.add(coefficientOfDirectBean.getMoneyPre());
					break;
			}
		}
		// 检索[补辅部门列表] 取得MW 的辅助部门
		List<String> auxiliaryDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_2, RepairAndAuxiliaryMaterialDepartment.CATEGORY_1);

		// 预定辅助部门回收-补修金额（平均）
		List<AuxiliaryRecycling> auxiliaryRecyclingList = this.listAuxiliaryRecyclingByDateRange(monthInterval, 0, expenseCode, timeStartStr, timeEndStr, departArr);
		//「预定辅助部门回收计算补修辅材表」取得各部门金额总计（加权平均）
		Map<String, BigDecimal> amountPreTotalMap=getAmountPreTotalMap(auxiliaryDepartList, monthInterval, 0, expenseCode, timeStartStr, timeEndStr, departArr);
		// 补修辅助部门设定
		for (AuxiliaryRecycling auxiliaryRecycling : auxiliaryRecyclingList) {
			RepairAndAuxiliaryShCoefficientBean coefficientOfAuxiliaryBean = new RepairAndAuxiliaryShCoefficientBean();
			// 直接部门
			switch (auxiliaryRecycling.getDirectDepartmentCode()) {
				case "DH":
					for (int i = 0; i < auxiliaryDepartList.size(); i++) {
						// 辅材费用表的辅助部门是否和补辅部门列表的辅助部门是否一致
						if(auxiliaryRecycling.getAuxiliaryDepartmentCode().equals(auxiliaryDepartList.get(i))) {
							coefficientOfAuxiliaryBean.setMachineType(auxiliaryRecycling.getDirectDepartmentCode()); // 机械类别(直接部门)
							coefficientOfAuxiliaryBean.setDepartment(auxiliaryRecycling.getAuxiliaryDepartmentCode()); //部门
							coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
							coefficientOfAuxiliaryBean.setMoneyPre(auxiliaryRecycling.getAmount()); // 予定金额
							// 予定金额总计不为0的场合
							if(BigDecimal.ZERO.compareTo(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode())) != 0) {
								// 实际分配金额 = 实际金额*(予定金额/予定金额总计)
								coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
										coefficientOfAuxiliaryBean.getExpenseItem(),coefficientOfAuxiliaryBean.getDepartment()).multiply(coefficientOfAuxiliaryBean.getMoneyPre().divide(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode()), 4, RoundingMode.HALF_UP)));
								coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(auxiliaryRecycling.getDirectDepartmentCode()));//机械时间予定
								//补辅材分配额SH系数  = 实际分配金额/机械时间予定
								coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(),4, RoundingMode.HALF_UP));
							}
							repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);

							// DH部门予定费用总计
							amountPreTotalDH = amountPreTotalDH.add(coefficientOfAuxiliaryBean.getMoneyPre());
							break;
						}
					}
					break;
				case "DM":
					for (int i = 0; i < auxiliaryDepartList.size(); i++) {
						// 辅材费用表的辅助部门是否和补辅部门列表的辅助部门是否一致
						if(auxiliaryRecycling.getAuxiliaryDepartmentCode().equals(auxiliaryDepartList.get(i))) {
							coefficientOfAuxiliaryBean.setMachineType(auxiliaryRecycling.getDirectDepartmentCode()); // 机械类别(直接部门)
							coefficientOfAuxiliaryBean.setDepartment(auxiliaryRecycling.getAuxiliaryDepartmentCode()); //部门
							coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
							coefficientOfAuxiliaryBean.setMoneyPre(auxiliaryRecycling.getAmount()); // 予定金额
							// 予定金额总计不为0的场合
							if(BigDecimal.ZERO.compareTo(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode())) != 0) {
								// 实际分配金额 = 实际金额*(予定金额/予定金额总计)
								coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
										coefficientOfAuxiliaryBean.getExpenseItem(),coefficientOfAuxiliaryBean.getDepartment()).multiply(coefficientOfAuxiliaryBean.getMoneyPre().divide(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode()), 4, RoundingMode.HALF_UP)));
								coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(auxiliaryRecycling.getDirectDepartmentCode()));//机械时间予定
								//补辅材分配额SH系数  = 实际分配金额/机械时间予定
								coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(),4, RoundingMode.HALF_UP));
							}
							repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);

							// DM部门予定费用总计
							amountPreTotalDM = amountPreTotalDM.add(coefficientOfAuxiliaryBean.getMoneyPre());
							break;
						}
					}
					break;
				case "EM":
					for (int i = 0; i < auxiliaryDepartList.size(); i++) {
						// 辅材费用表的辅助部门是否和补辅部门列表的辅助部门是否一致
						if(auxiliaryRecycling.getAuxiliaryDepartmentCode().equals(auxiliaryDepartList.get(i))) {
							coefficientOfAuxiliaryBean.setMachineType(auxiliaryRecycling.getDirectDepartmentCode()); // 机械类别(直接部门)
							coefficientOfAuxiliaryBean.setDepartment(auxiliaryRecycling.getAuxiliaryDepartmentCode()); //部门
							coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
							coefficientOfAuxiliaryBean.setMoneyPre(auxiliaryRecycling.getAmount()); // 予定金额
							// 予定金额总计不为0的场合
							if(BigDecimal.ZERO.compareTo(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode())) != 0) {
								// 实际分配金额 = 实际金额*(予定金额/予定金额总计)
								coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
										coefficientOfAuxiliaryBean.getExpenseItem(),coefficientOfAuxiliaryBean.getDepartment()).multiply(coefficientOfAuxiliaryBean.getMoneyPre().divide(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode()), 4, RoundingMode.HALF_UP)));
								coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(auxiliaryRecycling.getDirectDepartmentCode()));//机械时间予定
								//补辅材分配额SH系数  = 实际分配金额/机械时间予定
								coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(),4, RoundingMode.HALF_UP));
							}
							repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);

							// EM部门予定费用总计
							amountPreTotalEM = amountPreTotalEM.add(coefficientOfAuxiliaryBean.getMoneyPre());
							break;
						}
					}
					break;
				case "EF":
					for (int i = 0; i < auxiliaryDepartList.size(); i++) {
						// 辅材费用表的辅助部门是否和补辅部门列表的辅助部门是否一致
						if(auxiliaryRecycling.getAuxiliaryDepartmentCode().equals(auxiliaryDepartList.get(i))) {
							coefficientOfAuxiliaryBean.setMachineType(auxiliaryRecycling.getDirectDepartmentCode()); // 机械类别(直接部门)
							coefficientOfAuxiliaryBean.setDepartment(auxiliaryRecycling.getAuxiliaryDepartmentCode()); //部门
							coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
							coefficientOfAuxiliaryBean.setMoneyPre(auxiliaryRecycling.getAmount()); // 予定金额
							// 予定金额总计不为0的场合
							if(BigDecimal.ZERO.compareTo(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode())) != 0) {
								// 实际分配金额 = 实际金额*(予定金额/予定金额总计)
								coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
										coefficientOfAuxiliaryBean.getExpenseItem(),coefficientOfAuxiliaryBean.getDepartment()).multiply(coefficientOfAuxiliaryBean.getMoneyPre().divide(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode()), 4, RoundingMode.HALF_UP)));
								coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(auxiliaryRecycling.getDirectDepartmentCode()));//机械时间予定
								//补辅材分配额SH系数  = 实际分配金额/机械时间予定
								coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(),4, RoundingMode.HALF_UP));
							}
							repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);

							// EF部门予定费用总计
							amountPreTotalEF = amountPreTotalEF.add(coefficientOfAuxiliaryBean.getMoneyPre());
							break;
						}
					}
					break;
				case "EF09":
					for (int i = 0; i < auxiliaryDepartList.size(); i++) {
						// 辅材费用表的辅助部门是否和补辅部门列表的辅助部门是否一致
						if(auxiliaryRecycling.getAuxiliaryDepartmentCode().equals(auxiliaryDepartList.get(i))) {
							coefficientOfAuxiliaryBean.setMachineType(auxiliaryRecycling.getDirectDepartmentCode()); // 机械类别(直接部门)
							coefficientOfAuxiliaryBean.setDepartment(auxiliaryRecycling.getAuxiliaryDepartmentCode()); //部门
							coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
							coefficientOfAuxiliaryBean.setMoneyPre(auxiliaryRecycling.getAmount()); // 予定金额
							// 予定金额总计不为0的场合
							if(BigDecimal.ZERO.compareTo(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode())) != 0) {
								// 实际分配金额 = 实际金额*(予定金额/予定金额总计)
								coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
										coefficientOfAuxiliaryBean.getExpenseItem(),coefficientOfAuxiliaryBean.getDepartment()).multiply(coefficientOfAuxiliaryBean.getMoneyPre().divide(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode()), 4, RoundingMode.HALF_UP)));
								coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(auxiliaryRecycling.getDirectDepartmentCode()));//机械时间予定
								//补辅材分配额SH系数  = 实际分配金额/机械时间予定
								coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(),4, RoundingMode.HALF_UP));
							}
							repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);

							// EF09部门予定费用总计
							amountPreTotalEF09 = amountPreTotalEF09.add(coefficientOfAuxiliaryBean.getMoneyPre());
							break;
						}
					}
					break;
				case "ER":
					for (int i = 0; i < auxiliaryDepartList.size(); i++) {
						// 辅材费用表的辅助部门是否和补辅部门列表的辅助部门是否一致
						if(auxiliaryRecycling.getAuxiliaryDepartmentCode().equals(auxiliaryDepartList.get(i))) {
							coefficientOfAuxiliaryBean.setMachineType(auxiliaryRecycling.getDirectDepartmentCode()); // 机械类别(直接部门)
							coefficientOfAuxiliaryBean.setDepartment(auxiliaryRecycling.getAuxiliaryDepartmentCode()); //部门
							coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
							coefficientOfAuxiliaryBean.setMoneyPre(auxiliaryRecycling.getAmount()); // 予定金额
							// 予定金额总计不为0的场合
							if(BigDecimal.ZERO.compareTo(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode())) != 0) {
								// 实际分配金额 = 实际金额*(予定金额/予定金额总计)
								coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
										coefficientOfAuxiliaryBean.getExpenseItem(),coefficientOfAuxiliaryBean.getDepartment()).multiply(coefficientOfAuxiliaryBean.getMoneyPre().divide(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode()), 4, RoundingMode.HALF_UP)));
								coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(auxiliaryRecycling.getDirectDepartmentCode()));//机械时间予定
								//补辅材分配额SH系数  = 实际分配金额/机械时间予定
								coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(),4, RoundingMode.HALF_UP));
							}
							repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);

							// ER部门予定费用总计
							amountPreTotalER = amountPreTotalER.add(coefficientOfAuxiliaryBean.getMoneyPre());
							break;
						}
					}
					break;
				case "EH":
					for (int i = 0; i < auxiliaryDepartList.size(); i++) {
						// 辅材费用表的辅助部门是否和补辅部门列表的辅助部门是否一致
						if(auxiliaryRecycling.getAuxiliaryDepartmentCode().equals(auxiliaryDepartList.get(i))) {
							coefficientOfAuxiliaryBean.setMachineType(auxiliaryRecycling.getDirectDepartmentCode()); // 机械类别(直接部门)
							coefficientOfAuxiliaryBean.setDepartment(auxiliaryRecycling.getAuxiliaryDepartmentCode()); //部门
							coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
							coefficientOfAuxiliaryBean.setMoneyPre(auxiliaryRecycling.getAmount()); // 予定金额
							// 予定金额总计不为0的场合
							if(BigDecimal.ZERO.compareTo(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode())) != 0) {
								// 实际分配金额 = 实际金额*(予定金额/予定金额总计)
								coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
										coefficientOfAuxiliaryBean.getExpenseItem(),coefficientOfAuxiliaryBean.getDepartment()).multiply(coefficientOfAuxiliaryBean.getMoneyPre().divide(amountPreTotalMap.get(auxiliaryRecycling.getAuxiliaryDepartmentCode()), 4, RoundingMode.HALF_UP)));
								coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(auxiliaryRecycling.getDirectDepartmentCode()));//机械时间予定
								//补辅材分配额SH系数  = 实际分配金额/机械时间予定
								coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(),4, RoundingMode.HALF_UP));
							}
							repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);

							// EH部门予定费用总计
							amountPreTotalEH = amountPreTotalEH.add(coefficientOfAuxiliaryBean.getMoneyPre());
							break;
						}
					}
					break;
			}
		}

		// 全予定费用总计 =  部门予定费用总计(直接＋辅助)「DH+DM+EM+EF+EF09+ER+EH」
		amountPreTotal = amountPreTotalDH.add(amountPreTotalDM.add(amountPreTotalEM
				.add(amountPreTotalEF.add(amountPreTotalEF09.add(amountPreTotalER.add(amountPreTotalEH))))));

		//共通部门处理
		// 机器类别
		Map<String, BigDecimal> machineTypeMap = new HashMap<String, BigDecimal>();
		machineTypeMap.put("DH", amountPreTotalDH);
		machineTypeMap.put("DM", amountPreTotalDM);
		machineTypeMap.put("EM", amountPreTotalEM);
		machineTypeMap.put("EF", amountPreTotalEF);
		machineTypeMap.put("EF09", amountPreTotalEF09);
		machineTypeMap.put("ER", amountPreTotalER);
		machineTypeMap.put("EH", amountPreTotalEH);
		// MW共通部门特殊处理
		// 检索[补辅部门列表] 取得MW 的共通部门
		List<String> commomDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_3, RepairAndAuxiliaryMaterialDepartment.CATEGORY_1);
		// 循环遍历直接部门
		for (String directDepart : directDepartList) {
			// 循环遍历共通部门
			for (String commomDepart : commomDepartList) {
				RepairAndAuxiliaryShCoefficientBean coefficientOfAuxiliaryBean = new RepairAndAuxiliaryShCoefficientBean();
				coefficientOfAuxiliaryBean.setMachineType(directDepart); // 机械类别(直接部门)
				coefficientOfAuxiliaryBean.setDepartment(commomDepart); //共通部门
				coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
				coefficientOfAuxiliaryBean.setMoneyPre(BigDecimal.ZERO); // 分配金额
				// 实际分配金额 =部门补修辅材实际费用总计 *（ 部门予定费用总计/全部门予定费用总计）
				coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
						coefficientOfAuxiliaryBean.getExpenseItem(),commomDepart).multiply(Optional.ofNullable(machineTypeMap.get(coefficientOfAuxiliaryBean.getMachineType())).orElse(BigDecimal.ZERO).divide(amountPreTotal, 4, RoundingMode.HALF_UP) ) );
				coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(coefficientOfAuxiliaryBean.getMachineType()));//机械时间予定
				//补辅材分配额SH系数  = 实际金额 /机械时间予定
				coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
				repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);
			}
		}

		//特殊部门处理
		// 检索[补辅部门列表] 取得MW 的特殊部门
		List<RepairAndAuxiliaryMaterialDepartment> repairAndAuxiliaryMaterialDepartmentList= settingService.listRepairAndAuxiliaryMaterialDepartment(year, RepairAndAuxiliaryMaterialDepartment.STATE_4, RepairAndAuxiliaryMaterialDepartment.CATEGORY_1);
		// 特殊部门循环处理
		for(RepairAndAuxiliaryMaterialDepartment repairAndAuxiliaryMaterialDepartment : repairAndAuxiliaryMaterialDepartmentList) {
			if (!StringUtil.isStringEmpty(repairAndAuxiliaryMaterialDepartment.getAssignDepartment())
					&& !StringUtil.isStringEmpty(repairAndAuxiliaryMaterialDepartment.getRatio())) {

				// 分配部门通过「,」分割成分配部门List
				List<String> assignDepartmentList = new ArrayList<>();
				StringTokenizer assignDepartmentTokenizer = new StringTokenizer(repairAndAuxiliaryMaterialDepartment.getAssignDepartment(), ",");
				while (assignDepartmentTokenizer.hasMoreTokens()) {
					String token = assignDepartmentTokenizer.nextToken();
					assignDepartmentList.add(token);
				}
				//分配比例通过「,」分割成分配比例List
				List<String> ratioList = new ArrayList<>();
				StringTokenizer ratioTokenizer = new StringTokenizer(repairAndAuxiliaryMaterialDepartment.getRatio(), ",");
				while (ratioTokenizer.hasMoreTokens()) {
					String token = ratioTokenizer.nextToken();
					ratioList.add(token);
				}
				// 分配部门循环处理
				for (int i = 0; i < assignDepartmentList.size(); i++) {
					RepairAndAuxiliaryShCoefficientBean coefficientOfAuxiliaryBean = new RepairAndAuxiliaryShCoefficientBean();
					coefficientOfAuxiliaryBean.setMachineType(assignDepartmentList.get(i)); //机械类别(分配部门)
					coefficientOfAuxiliaryBean.setDepartment(repairAndAuxiliaryMaterialDepartment.getDepartment()); //特殊部门
					coefficientOfAuxiliaryBean.setExpenseItem(expenseItem);//费用项目
					coefficientOfAuxiliaryBean.setMoneyPre(BigDecimal.ZERO); // 分配金额
					// 实际分配金额 =部门补修辅材实际费用总计 *分配比例
					coefficientOfAuxiliaryBean.setMoneyAct(getAmount(monthInterval, timeStartStr, timeEndStr,
							coefficientOfAuxiliaryBean.getExpenseItem(),repairAndAuxiliaryMaterialDepartment.getDepartment()).multiply(new BigDecimal(ratioList.get(i))));
					coefficientOfAuxiliaryBean.setMachineTimePreAvg(mwMachineTimePreMap.get(assignDepartmentList.get(i)));//机械时间予定
					//补辅材分配额SH系数  = 实际金额 /机械时间予定
					coefficientOfAuxiliaryBean.setRepairAndAuxiliarySH(coefficientOfAuxiliaryBean.getMoneyAct().divide(coefficientOfAuxiliaryBean.getMachineTimePreAvg(), 4, RoundingMode.HALF_UP));
					repairAndAuxiliaryShCoefficientBeanList.add(coefficientOfAuxiliaryBean);
				}
			}
		}

		return repairAndAuxiliaryShCoefficientBeanList;
	}

	/**
	 * 人件费SH系数明细(MW)
	 * @param monthInterval  日期相差的月数
	 * @param timeStartStr 开始日期
	 * @param timeEndStr 结束日期
	 * @param expenseType 费用项目
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/02/01 09:43
	 * @return  List<RepairAndAuxiliaryShCoefficientBean>
	 * @throws ParseException
	 */
	@Override
	public List<PersonManHourShCoefficientBean> getPersonManHourShCoefficientBeanList (int monthInterval, String timeStartStr, String timeEndStr,String departmentCode) throws Exception{
		// 根据开始年月取得年度
		String year = YearUtil.getYear(timeStartStr);
		// 检索[补辅部门列表] 取得MW 的直接部门(机械类别)
		List<String> directDepartList= settingService.getDepartmentList(year, RepairAndAuxiliaryMaterialDepartment.STATE_1, RepairAndAuxiliaryMaterialDepartment.CATEGORY_1);
		// 获取MW平均予定机械时间
		Map<String, BigDecimal> mwMachineTimePreMap =getMwMachineTimePreMap(directDepartList, monthInterval, timeStartStr, timeEndStr);

		// 检索条件直接部门
		String[] departArr = {"DH", "DM", "EF", "EF09", "EH", "EM", "ER"} ;
		// 检索条件辅助部门
		String[] supportDepartArr = { "DICE"};
		// 检索条件费用项目
		String[] expenseItemArr = {"0010"};

		// 预定辅助部门人件工时
		List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificialList = this.listMwAuxiliaryRecyclingArtificialByDateRange(departArr, supportDepartArr, expenseItemArr, monthInterval, timeStartStr, timeEndStr);
		// 模具DH部门予定平均工时
		BigDecimal shPreDiceDH = new BigDecimal(0);
		// 模具DM部门予定平均工时
		BigDecimal shPreDiceDM = new BigDecimal(0);
		// 模具全部门予定平均工时总计
		BigDecimal shPreDiceTotal = new BigDecimal(0);

		for(AuxiliaryRecyclingArtificial auxiliaryRecyclingArtificial:auxiliaryRecyclingArtificialList) {
			switch(auxiliaryRecyclingArtificial.getDirectDepartmentCode()) {
				case "DH":
					shPreDiceDH = auxiliaryRecyclingArtificial.getsHNum();
					shPreDiceTotal = shPreDiceTotal.add(shPreDiceDH);
					break;
				case "DM":
					shPreDiceDM = auxiliaryRecyclingArtificial.getsHNum();
					shPreDiceTotal = shPreDiceTotal.add(shPreDiceDM);
					break;
				default:
					shPreDiceTotal = shPreDiceTotal.add(auxiliaryRecyclingArtificial.getsHNum());
					break;
			}
		}

		// 保全DH部门予定平均工时
		BigDecimal shPreMaintDH = new BigDecimal(0);
		// 保全DM部门予定平均工时
		BigDecimal shPreMaintDM = new BigDecimal(0);
		// 保全全部门予定平均工时总计
		BigDecimal shPreMaintTotal = new BigDecimal(0);

		// 抽取直接部门人件工时（平均）
		List<DirectRecycling> DirectRecyclingList = this.listDirectRecyclingForMW(departArr, monthInterval, timeStartStr, timeEndStr);
		for(DirectRecycling directRecycling:DirectRecyclingList) {
			switch(directRecycling.getDepartmentCode()) {
				case "DH":
					shPreMaintDH = directRecycling.getSecurityCostSH();
					shPreMaintTotal = shPreMaintTotal.add(shPreMaintDH);
					break;
				case "DM":
					shPreMaintDM = directRecycling.getSecurityCostSH();
					shPreMaintTotal = shPreMaintTotal.add(shPreMaintDM);
					break;
				default:
					shPreMaintTotal = shPreMaintTotal.add(directRecycling.getSecurityCostSH());
					break;
			}
		}
		// 模具DH,DM工时总计
		BigDecimal shPreDiceWorkHourOfDhAndDm=BigDecimal.ZERO;
		// 保全DH,DM工时总计
		BigDecimal shPreMaintWorkHourOfDhAndDm=BigDecimal.ZERO;

		List<PersonManHourShCoefficientBean> personManHourShCoefficientBeanList = new ArrayList<>();
		List<HumanHourCost> humanHourCostList= this.listHumanHourCostByDateRange(timeStartStr, timeEndStr, departmentCode);
		// 遍历人件工时
		for(HumanHourCost humanHourCost:humanHourCostList) {
			// 取得部门编号
			String humanPriceCode = humanHourCost.getDepartmentCode();
			// 直接部门
			if (humanPriceCode.contains("伸线") || humanPriceCode.contains("EM") || humanPriceCode.contains("EF")
					|| humanPriceCode.contains("ER") || humanPriceCode.contains("EH") || humanPriceCode.contains("UF") ) {
				PersonManHourShCoefficientBean personManHourShCoefficientBean= new PersonManHourShCoefficientBean();
				PersonManHourShCoefficientBean personManHourShCoefficientBeanEF09= new PersonManHourShCoefficientBean();

				personManHourShCoefficientBean.setWorkClass(humanHourCost.getWorkClass());
				personManHourShCoefficientBeanEF09.setWorkClass(humanHourCost.getWorkClass());

				humanHourCost.setWorkHour(Optional.ofNullable(humanHourCost.getWorkHour()).orElse(BigDecimal.ZERO));
				switch(humanHourCost.getDepartmentCode()) {
					case "伸线DH":
						// 工时
						personManHourShCoefficientBean.setWorkHour(humanHourCost.getWorkHour());
						// 工种
						personManHourShCoefficientBean.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBean.setDepartmentCode("DH");
						// 机器类别
						personManHourShCoefficientBean.setMachineType("DH");
						// 机械时间予定
						personManHourShCoefficientBean.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBean.getDepartmentCode()));
						// 人间工时SH系数 = 人间工时/机械时间予定
						personManHourShCoefficientBean.setPersonManHourSH(humanHourCost.getWorkHour().divide(personManHourShCoefficientBean.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
						break;
					case "伸线DM":
						// 工时
						personManHourShCoefficientBean.setWorkHour(humanHourCost.getWorkHour());
						// 工种
						personManHourShCoefficientBean.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBean.setDepartmentCode("DM");
						// 机器类别
						personManHourShCoefficientBean.setMachineType("DM");
						// 机械时间予定
						personManHourShCoefficientBean.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBean.getDepartmentCode()));
						// 人间工时SH系数 = 人间工时/机械时间予定
						personManHourShCoefficientBean.setPersonManHourSH(humanHourCost.getWorkHour().divide(personManHourShCoefficientBean.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
						break;
					case "EM工时":
						// 工时
						personManHourShCoefficientBean.setWorkHour(humanHourCost.getWorkHour());
						// 工种
						personManHourShCoefficientBean.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBean.setDepartmentCode("EM");
						// 机器类别
						personManHourShCoefficientBean.setMachineType("EM");
						// 机械时间予定
						personManHourShCoefficientBean.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBean.getDepartmentCode()));
						// 人间工时SH系数 = 人间工时/机械时间予定
						personManHourShCoefficientBean.setPersonManHourSH(humanHourCost.getWorkHour().divide(personManHourShCoefficientBean.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
						break;
					case "EF":
						// EF的场合，需将将其拆分成EF和EF09
						//检索EF09比例系数
						Proportion proportionEF09 = Optional.ofNullable(statService.selectProportionByCode(year, "EF09")).orElse(new Proportion());
						// EF09工时
						BigDecimal workHourEF09= humanHourCost.getWorkHour().multiply(Optional.ofNullable(proportionEF09.getEf()).orElse(BigDecimal.ZERO)).setScale(3, RoundingMode.HALF_UP);
						// EF工时
						BigDecimal workHourEF= humanHourCost.getWorkHour().subtract(workHourEF09).setScale(3, RoundingMode.HALF_UP);
						// EF的场合
						// 工时
						personManHourShCoefficientBean.setWorkHour(workHourEF);
						// 工种
						personManHourShCoefficientBean.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBean.setDepartmentCode("EF");
						// 机器类别
						personManHourShCoefficientBean.setMachineType("EF");
						// 机械时间予定
						personManHourShCoefficientBean.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBean.getDepartmentCode()));
						// 人间工时SH系数 = 人间工时/机械时间予定
						personManHourShCoefficientBean.setPersonManHourSH(personManHourShCoefficientBean.getWorkHour().divide(personManHourShCoefficientBean.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());
						// 增加EF数据LIST
						personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);

						// EF09的场合
						// 工时
						personManHourShCoefficientBeanEF09.setWorkHour(workHourEF09);
						// 工种
						personManHourShCoefficientBeanEF09.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBeanEF09.setDepartmentCode("EF09");
						// 机器类别
						personManHourShCoefficientBeanEF09.setMachineType("EF09");
						// 机械时间予定
						personManHourShCoefficientBeanEF09.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanEF09.getDepartmentCode()));
						// 人间工时SH系数 = 人间工时/机械时间予定
						personManHourShCoefficientBeanEF09.setPersonManHourSH(personManHourShCoefficientBeanEF09.getWorkHour().divide(personManHourShCoefficientBeanEF09.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						// 增加EF09数据LIST
						personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanEF09);
						break;
					case "ER":
						// 工时
						personManHourShCoefficientBean.setWorkHour(humanHourCost.getWorkHour());
						// 工种
						personManHourShCoefficientBean.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBean.setDepartmentCode("ER");
						// 机器类别
						personManHourShCoefficientBean.setMachineType("ER");
						// 机械时间予定
						personManHourShCoefficientBean.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBean.getDepartmentCode()));
						// 人间工时SH系数 = 人间工时/机械时间予定
						personManHourShCoefficientBean.setPersonManHourSH(humanHourCost.getWorkHour().divide(personManHourShCoefficientBean.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
						break;
					case "EH":
						// 工时
						personManHourShCoefficientBean.setWorkHour(humanHourCost.getWorkHour());
						// 工种
						personManHourShCoefficientBean.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBean.setDepartmentCode("EH");
						// 机器类别
						personManHourShCoefficientBean.setMachineType("EH");
						// 机械时间予定
						personManHourShCoefficientBean.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBean.getDepartmentCode()));
						// 人间工时SH系数 = 人间工时/机械时间予定
						personManHourShCoefficientBean.setPersonManHourSH(humanHourCost.getWorkHour().divide(personManHourShCoefficientBean.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
						break;
				}
			}else {
				if(humanPriceCode.contains("MW-模具")) {
					PersonManHourShCoefficientBean personManHourShCoefficientBeanDiceDH= new PersonManHourShCoefficientBean();
					PersonManHourShCoefficientBean personManHourShCoefficientBeanDiceDM= new PersonManHourShCoefficientBean();

					personManHourShCoefficientBeanDiceDH.setWorkClass(humanHourCost.getWorkClass());
					personManHourShCoefficientBeanDiceDM.setWorkClass(humanHourCost.getWorkClass());

					// 工时
					personManHourShCoefficientBeanDiceDH.setWorkHour(humanHourCost.getWorkHour().multiply(shPreDiceDH.divide(shPreDiceTotal, 4, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP));
					// 工种
					personManHourShCoefficientBeanDiceDH.setWorkType(humanHourCost.getWorkType());
					//部门
					personManHourShCoefficientBeanDiceDH.setDepartmentCode(humanHourCost.getDepartmentCode());
					// 机器类别
					personManHourShCoefficientBeanDiceDH.setMachineType("DH");
					// 机械时间予定
					personManHourShCoefficientBeanDiceDH.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanDiceDH.getMachineType()));
					// 人间工时SH系数 = 工时/机械时间予定
					personManHourShCoefficientBeanDiceDH.setPersonManHourSH(personManHourShCoefficientBeanDiceDH.getWorkHour().divide(personManHourShCoefficientBeanDiceDH.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanDiceDH);

					// 工时
					personManHourShCoefficientBeanDiceDM.setWorkHour(humanHourCost.getWorkHour().multiply(shPreDiceDM.divide(shPreDiceTotal, 4, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP));
					// 工种
					personManHourShCoefficientBeanDiceDM.setWorkType(humanHourCost.getWorkType());
					//部门
					personManHourShCoefficientBeanDiceDM.setDepartmentCode(humanHourCost.getDepartmentCode());
					// 机器类别
					personManHourShCoefficientBeanDiceDM.setMachineType("DM");
					// 机械时间予定
					personManHourShCoefficientBeanDiceDM.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanDiceDM.getMachineType()));

					// 人件工时SH系数 = 工时/机械时间予定
					personManHourShCoefficientBeanDiceDM.setPersonManHourSH(personManHourShCoefficientBeanDiceDM.getWorkHour().divide(personManHourShCoefficientBeanDiceDM.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanDiceDM);

					//DH+DM工时
					shPreDiceWorkHourOfDhAndDm  = personManHourShCoefficientBeanDiceDH.getWorkHour().add(personManHourShCoefficientBeanDiceDM.getWorkHour());
				}else if(humanPriceCode.contains("MW-保全")) {
					PersonManHourShCoefficientBean personManHourShCoefficientBeanMaintDH= new PersonManHourShCoefficientBean();
					PersonManHourShCoefficientBean personManHourShCoefficientBeanMaintDM= new PersonManHourShCoefficientBean();

					personManHourShCoefficientBeanMaintDH.setWorkClass(humanHourCost.getWorkClass());
					personManHourShCoefficientBeanMaintDM.setWorkClass(humanHourCost.getWorkClass());

					// 工时
					personManHourShCoefficientBeanMaintDH.setWorkHour(humanHourCost.getWorkHour().multiply(shPreMaintDH.divide(shPreMaintTotal, 4, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP));
					// 工种
					personManHourShCoefficientBeanMaintDH.setWorkType(humanHourCost.getWorkType());
					//部门
					personManHourShCoefficientBeanMaintDH.setDepartmentCode(humanHourCost.getDepartmentCode());
					// 机器类别
					personManHourShCoefficientBeanMaintDH.setMachineType("DH");
					// 机械时间予定
					personManHourShCoefficientBeanMaintDH.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanMaintDH.getMachineType()));
					// 人间工时SH系数 = 工时/机械时间予定
					personManHourShCoefficientBeanMaintDH.setPersonManHourSH(personManHourShCoefficientBeanMaintDH.getWorkHour().divide(personManHourShCoefficientBeanMaintDH.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanMaintDH);

					// 工时
					personManHourShCoefficientBeanMaintDM.setWorkHour(humanHourCost.getWorkHour().multiply(shPreMaintDM.divide(shPreMaintTotal, 4, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP));
					// 工种
					personManHourShCoefficientBeanMaintDM.setWorkType(humanHourCost.getWorkType());
					//部门
					personManHourShCoefficientBeanMaintDM.setDepartmentCode(humanHourCost.getDepartmentCode());
					// 机器类别
					personManHourShCoefficientBeanMaintDM.setMachineType("DM");
					// 机械时间予定
					personManHourShCoefficientBeanMaintDM.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanMaintDM.getMachineType()));
					// 人间工时SH系数 = 工时/机械时间予定
					personManHourShCoefficientBeanMaintDM.setPersonManHourSH(personManHourShCoefficientBeanMaintDM.getWorkHour().divide(personManHourShCoefficientBeanMaintDM.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanMaintDM);

					//DH+DM 工时
					shPreMaintWorkHourOfDhAndDm  = personManHourShCoefficientBeanMaintDH.getWorkHour().add(personManHourShCoefficientBeanMaintDM.getWorkHour());
				}
				//辅助部门
				// 根据部门编号获取比例表数据
				Proportion proportion = statService.selectProportionByCode(year, humanPriceCode);
				PersonManHourShCoefficientBean personManHourShCoefficientBeanEM= new PersonManHourShCoefficientBean();
				PersonManHourShCoefficientBean personManHourShCoefficientBeanEF= new PersonManHourShCoefficientBean();
				PersonManHourShCoefficientBean personManHourShCoefficientBeanER= new PersonManHourShCoefficientBean();
				PersonManHourShCoefficientBean personManHourShCoefficientBeanEH= new PersonManHourShCoefficientBean();
				PersonManHourShCoefficientBean personManHourShCoefficientBeanEF09= new PersonManHourShCoefficientBean();

				personManHourShCoefficientBeanEM.setWorkClass(humanHourCost.getWorkClass());
				personManHourShCoefficientBeanEF.setWorkClass(humanHourCost.getWorkClass());
				personManHourShCoefficientBeanER.setWorkClass(humanHourCost.getWorkClass());
				personManHourShCoefficientBeanEH.setWorkClass(humanHourCost.getWorkClass());
				personManHourShCoefficientBeanEF09.setWorkClass(humanHourCost.getWorkClass());

				if(proportion != null){
					// EM的场合
					if( proportion.getEm() != null && proportion.getEm().compareTo(BigDecimal.ZERO) == 1){
						// 工时
						if(humanPriceCode.contains("MW-模具")) {
							personManHourShCoefficientBeanEM.setWorkHour(humanHourCost.getWorkHour().subtract(shPreDiceWorkHourOfDhAndDm).multiply(proportion.getEm()).setScale(3, RoundingMode.HALF_UP));
						}else if(humanPriceCode.contains("MW-保全")){
							personManHourShCoefficientBeanEM.setWorkHour(humanHourCost.getWorkHour().subtract(shPreMaintWorkHourOfDhAndDm).multiply(proportion.getEm()).setScale(3, RoundingMode.HALF_UP));
						}else {
							personManHourShCoefficientBeanEM.setWorkHour(humanHourCost.getWorkHour().multiply(proportion.getEm()).setScale(3, RoundingMode.HALF_UP));
						}
						// 工种
						personManHourShCoefficientBeanEM.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBeanEM.setDepartmentCode(humanHourCost.getDepartmentCode());
						// 机器类别
						personManHourShCoefficientBeanEM.setMachineType("EM");
						// 机械时间予定
						personManHourShCoefficientBeanEM.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanEM.getMachineType()));
						// 人间工时SH系数 = 工时/机械时间予定
						personManHourShCoefficientBeanEM.setPersonManHourSH(personManHourShCoefficientBeanEM.getWorkHour().divide(personManHourShCoefficientBeanEM.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanEM);
					}
					// EF的场合
					if(proportion.getEf() != null && proportion.getEf().compareTo(BigDecimal.ZERO) == 1){
						// EF的场合，需将将其拆分成EF和EF09
						//检索EF09比例系数
						Proportion proportionEF09 = statService.selectProportionByCode(year, "EF09");
						// EF工时(未分配)
						BigDecimal workHourEF = new BigDecimal(0);
						if(humanPriceCode.contains("MW-模具")) {
							workHourEF = humanHourCost.getWorkHour().subtract(shPreDiceWorkHourOfDhAndDm).multiply(proportion.getEf());
						}else if(humanPriceCode.contains("MW-保全")){
							workHourEF = humanHourCost.getWorkHour().subtract(shPreMaintWorkHourOfDhAndDm).multiply(proportion.getEf());
						}else {
							workHourEF= humanHourCost.getWorkHour().multiply(proportion.getEf());
						}
						// EF09工时
						BigDecimal workHourEF09= workHourEF.multiply(proportionEF09.getEf()).setScale(3, RoundingMode.HALF_UP);

						// EF的场合
						// 工时(分配ef09之后剩余)
						personManHourShCoefficientBeanEF.setWorkHour(workHourEF.subtract(workHourEF09).setScale(3, RoundingMode.HALF_UP));
						// 工种
						personManHourShCoefficientBeanEF.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBeanEF.setDepartmentCode(humanHourCost.getDepartmentCode());
						// 机器类别
						personManHourShCoefficientBeanEF.setMachineType("EF");
						// 机械时间予定
						personManHourShCoefficientBeanEF.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanEF.getMachineType()));
						// 人间工时SH系数 = 工时/机械时间予定
						personManHourShCoefficientBeanEF.setPersonManHourSH(personManHourShCoefficientBeanEF.getWorkHour().divide(personManHourShCoefficientBeanEF.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());
						// 增加EF数据LIST
						personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanEF);

						// EF09的场合
						// 工时
						personManHourShCoefficientBeanEF09.setWorkHour(workHourEF09);
						// 工种
						personManHourShCoefficientBeanEF09.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBeanEF09.setDepartmentCode(humanHourCost.getDepartmentCode());
						// 机器类别
						personManHourShCoefficientBeanEF09.setMachineType("EF09");
						// 机械时间予定
						personManHourShCoefficientBeanEF09.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanEF09.getMachineType()));
						// 人间工时SH系数 = 工时/机械时间予定
						personManHourShCoefficientBeanEF09.setPersonManHourSH(personManHourShCoefficientBeanEF09.getWorkHour().divide(personManHourShCoefficientBeanEF09.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						// 增加EF09数据LIST
						personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanEF09);
					}
					// ER的场合
					if(proportion.getEr() != null && proportion.getEr().compareTo(BigDecimal.ZERO) == 1){
						// 工时
						if(humanPriceCode.contains("MW-模具")) {
							personManHourShCoefficientBeanER.setWorkHour(humanHourCost.getWorkHour().subtract(shPreDiceWorkHourOfDhAndDm).multiply(proportion.getEr()).setScale(3, RoundingMode.HALF_UP));
						}else if(humanPriceCode.contains("MW-保全")){
							personManHourShCoefficientBeanER.setWorkHour(humanHourCost.getWorkHour().subtract(shPreMaintWorkHourOfDhAndDm).multiply(proportion.getEr()).setScale(3, RoundingMode.HALF_UP));
						}else {
							personManHourShCoefficientBeanER.setWorkHour(humanHourCost.getWorkHour().multiply(proportion.getEr()).setScale(3, RoundingMode.HALF_UP));
						}
						// 工种
						personManHourShCoefficientBeanER.setWorkType(humanHourCost.getWorkType());
						// 部门
						personManHourShCoefficientBeanER.setDepartmentCode(humanHourCost.getDepartmentCode());
						// 机器类别
						personManHourShCoefficientBeanER.setMachineType("ER");
						// 机械时间予定
						personManHourShCoefficientBeanER.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanER.getMachineType()));
						// 人间工时SH系数 = 工时/机械时间予定
						personManHourShCoefficientBeanER.setPersonManHourSH(personManHourShCoefficientBeanER.getWorkHour().divide(personManHourShCoefficientBeanER.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanER);
					}
					// EH的场合
					if(proportion.getEh() != null && proportion.getEh().compareTo(BigDecimal.ZERO) == 1){
						// 工时
						if(humanPriceCode.contains("MW-模具")) {
							personManHourShCoefficientBeanEH.setWorkHour(humanHourCost.getWorkHour().subtract(shPreDiceWorkHourOfDhAndDm).multiply(proportion.getEh()).setScale(3, RoundingMode.HALF_UP));
						}else if(humanPriceCode.contains("MW-保全")){
							personManHourShCoefficientBeanEH.setWorkHour(humanHourCost.getWorkHour().subtract(shPreMaintWorkHourOfDhAndDm).multiply(proportion.getEh()).setScale(3, RoundingMode.HALF_UP));
						}else {
							personManHourShCoefficientBeanEH.setWorkHour(humanHourCost.getWorkHour().multiply(proportion.getEh()).setScale(3, RoundingMode.HALF_UP));                    	}
						// 工种
						personManHourShCoefficientBeanEH.setWorkType(humanHourCost.getWorkType());
						//部门
						personManHourShCoefficientBeanEH.setDepartmentCode(humanHourCost.getDepartmentCode());
						// 机器类别
						personManHourShCoefficientBeanEH.setMachineType("EH");
						// 机械时间予定
						personManHourShCoefficientBeanEH.setMachineTimePreAvg(mwMachineTimePreMap.get(personManHourShCoefficientBeanEH.getMachineType()));
						// 人间工时SH系数 = 工时/机械时间予定
						personManHourShCoefficientBeanEH.setPersonManHourSH(personManHourShCoefficientBeanEH.getWorkHour().divide(personManHourShCoefficientBeanEH.getMachineTimePreAvg(), 4,  RoundingMode.HALF_UP).toString());

						personManHourShCoefficientBeanList.add(personManHourShCoefficientBeanEH);
					}
				}
			}
		}
		return personManHourShCoefficientBeanList;
	}

	/**
	 * 人件工时实绩明细(UF)
	 * @param monthInterval  日期相差的月数
	 * @param timeStartStr 开始日期
	 * @param timeEndStr 结束日期
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/02/01 09:43
	 * @return  List<RepairAndAuxiliaryShCoefficientBean>
	 * @throws ParseException
	 */
	@Override
	public List<PersonManHourShCoefficientBean> getUfPersonManHourShCoefficientBeanList (int monthInterval, String timeStartStr, String timeEndStr, String departmentCode) throws Exception{

		List<PersonManHourShCoefficientBean> personManHourShCoefficientBeanList = new ArrayList<>();

		// 机械类别「DE」-机械时间予定(平均)
		BigDecimal machineTimePreAvgDE = new BigDecimal(0);
		// 机械类别「DFA」-机械时间予定(平均)
		BigDecimal machineTimePreAvgDF = new BigDecimal(0);
		// 机械类别「DS」-机械时间予定(平均)
		BigDecimal machineTimePreAvgDS = new BigDecimal(0);
		// 机械类别「DU」-机械时间予定(平均)
		BigDecimal machineTimePreAvgDU = new BigDecimal(0);
		// 机械类别「EE」-机械时间予定(平均)
		BigDecimal machineTimePreAvgEE = new BigDecimal(0);
		// 机械类别「ES」-机械时间予定(平均)
		BigDecimal machineTimePreAvgES = new BigDecimal(0);
		// 机械类别「EU」-机械时间予定(平均)
		BigDecimal machineTimePreAvgEU = new BigDecimal(0);
		// 机械类别「EW」-机械时间予定(平均)
		BigDecimal machineTimePreAvgEW = new BigDecimal(0);
		// 机械类别「UFINS」-机械时间予定(平均)
		BigDecimal machineTimePreAvgUFINS = new BigDecimal(0);


		// 获取两个日期相差的月数(加权平均用)
//    	int monthInterval =  getMonthDiff(timeStartStr,timeEndStr);

		//根据条件获取机械时间予定明细信息
		List<MachineTimePreBean> machineTimePreBeanList = this.listMachineTimePreBeanForUF(monthInterval, timeStartStr, timeEndStr);
		//械时间予定
		for(MachineTimePreBean machineTimePreBean:machineTimePreBeanList) {
			switch(machineTimePreBean.getDepartment()) {
				case "DE":
					machineTimePreAvgDE = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
				case "DFA":
					machineTimePreAvgDF = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
				case "DS":
					machineTimePreAvgDS = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
				case "DU":
					machineTimePreAvgDU = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
				case "EE":
					machineTimePreAvgEE = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
				case "ES":
					machineTimePreAvgES = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
				case "EU":
					machineTimePreAvgEU = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
				case "EW":
					machineTimePreAvgEW = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
				case "UFINS":
					machineTimePreAvgUFINS = new BigDecimal(machineTimePreBean.getMachineTimePreAvg());
					break;
			}
		}
		// 实绩人件工时
		// 「UF」-正式工
		BigDecimal manHourFormalWorkUF = new BigDecimal(0);
		// 「UF-QC」-正式工
		BigDecimal manHourFormalWorkUFQC = new BigDecimal(0);
		// 「UF-保全」-正式工
		BigDecimal manHourFormalWorkUFMaintenance  = new BigDecimal(0);
		// 「UF-模具」-正式工
		BigDecimal manHourFormalWorkUFDice = new BigDecimal(0);
		// 「UF」-劳务工
		BigDecimal manHourLaborWorkUF = new BigDecimal(0);
		// 「UF-QC」-劳务工
		BigDecimal manHourLaborWorkUFQC = new BigDecimal(0);
		// 「UF-保全」-劳务工
		BigDecimal manHourLaborWorkUFMaintenance  = new BigDecimal(0);
		// 「UF-模具」-劳务工
		BigDecimal manHourLaborWorkUFDice = new BigDecimal(0);

		// 「UF」--工作类型
		String ufWorkClass = null;
		// 「UF-QC」--工作类型
		String ufqcWorkClass = null;
		// 「UF-保全」--工作类型
		String ufMaintenanceWorkClass = null;
		// 「UF-模具」--工作类型
		String ufDiceWorkClass = null;

		// 人件工时实绩
		List<HumanHourCost> humanHourCostList= this.listHumanHourCostByDateRange(timeStartStr, timeEndStr, departmentCode);
		if(humanHourCostList != null && humanHourCostList.size() > 0) {
			for(HumanHourCost humanHourCost:humanHourCostList) {
				switch(humanHourCost.getDepartmentCode()) {
					case "UF":
						ufWorkClass = humanHourCost.getWorkClass();
						if("正式工".equals(humanHourCost.getWorkType())) {
							manHourFormalWorkUF = humanHourCost.getWorkHour();
						}else {
							manHourLaborWorkUF = humanHourCost.getWorkHour();
						}
						break;
					case "UF-QC":
						ufqcWorkClass = humanHourCost.getWorkClass();
						if("正式工".equals(humanHourCost.getWorkType())) {
							manHourFormalWorkUFQC = humanHourCost.getWorkHour();
						}else {
							manHourLaborWorkUFQC = humanHourCost.getWorkHour();
						}
						break;
					case "UF-保全":
						ufMaintenanceWorkClass = humanHourCost.getWorkClass();
						if("正式工".equals(humanHourCost.getWorkType())) {
							manHourFormalWorkUFMaintenance = humanHourCost.getWorkHour();
						}else {
							manHourLaborWorkUFMaintenance = humanHourCost.getWorkHour();
						}
						break;
					case "UF-模具":
						ufDiceWorkClass = humanHourCost.getWorkClass();
						if("正式工".equals(humanHourCost.getWorkType())) {
							manHourFormalWorkUFDice = humanHourCost.getWorkHour();
						}else {
							manHourLaborWorkUFDice = humanHourCost.getWorkHour();
						}
						break;
				}
			}
		}

		// 检索条件直接部门
		String[] departArr = {"DE", "DFA", "DS", "DU", "EE", "ES", "EU", "EW", "UFINS"} ;
		// 检索条件辅助部门
		String[] supportDepartArr = {"INSP", "DICE"};
		// 检索条件费用项目(正式工)
		String[] expenseItemArr = {"0010"};

		// 检索条件费用项目(劳务工)
		String[] expenseItemLaborArr = {"0020"};

		// 抽取直接部门人件工时（平均）
		List<DirectRecycling> DirectRecyclingList = this.listDirectRecyclingByDateRange(departArr, monthInterval,1,timeStartStr, timeEndStr);
		// 操作工人件工时总计(正式工)
		BigDecimal pmhOfFormalWorkTotal = new BigDecimal(0);
		// 劳务工人件工时总计(正式工)
		BigDecimal pmhOfLaborWorkTotal = new BigDecimal(0);
		// 保全人件工时总计(正式工)
		BigDecimal pmhOfMaintenanceTotal = new BigDecimal(0);
		// 予定各人件工时总计
		for(int i=0; i<DirectRecyclingList.size(); i++) {
			pmhOfFormalWorkTotal = pmhOfFormalWorkTotal.add(DirectRecyclingList.get(i).getOperatorCostSH());
			pmhOfLaborWorkTotal = pmhOfLaborWorkTotal.add(DirectRecyclingList.get(i).getLaborCostSH());
			pmhOfMaintenanceTotal = pmhOfMaintenanceTotal.add(DirectRecyclingList.get(i).getSecurityCostSH());
		}

		// 预定直接部门人件工时
		// 正式工
		for(DirectRecycling directRecycling:DirectRecyclingList) {
			//操作工人件工时(正式工)
			PersonManHourShCoefficientBean personManHourShCoefficientBean = new PersonManHourShCoefficientBean();
			personManHourShCoefficientBean.setWorkClass(ufWorkClass);

			switch(directRecycling.getDepartmentCode()) {
				case "DE":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					//实绩人件工时SH分配 = 予定人件工时/予定人件工时总*实绩人件工时总
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDE);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DFA":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDF, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDF);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DS":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDS);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DU":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDU, 4,  RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDU);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EE":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEE);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "ES":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgES, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgES);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EU":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					//予定人件工时>0的场合
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEU, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEU);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EW":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEW, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEW);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "UFINS":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getOperatorCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfFormalWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgUFINS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgUFINS);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
			}
		}
		// 劳务工人件工时
		for(DirectRecycling directRecycling:DirectRecyclingList) {
			PersonManHourShCoefficientBean personManHourShCoefficientBean = new PersonManHourShCoefficientBean();
			personManHourShCoefficientBean.setWorkClass(ufWorkClass);

			switch(directRecycling.getDepartmentCode()) {
				case "DE":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDE);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DFA":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDF, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDF);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DS":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDS);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DU":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDU, 4,  RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDU);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EE":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEE);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "ES":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgES, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgES);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EU":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEU, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEU);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EW":
					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEW, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEW);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "UFINS":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getLaborCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUF.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfLaborWorkTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgUFINS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgUFINS);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 			}
 		}
 		// 保全操作工人件工时
 		for(DirectRecycling directRecycling:DirectRecyclingList) {
 			PersonManHourShCoefficientBean personManHourShCoefficientBean = new PersonManHourShCoefficientBean();
 			personManHourShCoefficientBean.setWorkClass(ufMaintenanceWorkClass);

 			switch(directRecycling.getDepartmentCode()) {
 				case "DE":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDE);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "DFA":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDF, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDF);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "DS":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDS);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "DU":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDU, 4,  RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDU);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "EE":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEE);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "ES":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgES, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgES);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "EU":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEU, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEU);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "EW":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEW, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEW);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "UFINS":
 					personManHourShCoefficientBean.setMachineType(directRecycling.getDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(directRecycling.getDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("保全");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(directRecycling.getSecurityCostSH());//予定人件工时
 					personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFMaintenance.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfMaintenanceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgUFINS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgUFINS);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 			}
 		}

 		// 预定辅助部门人件工时
 		List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificialList = this.listAuxiliaryRecyclingArtificialByDateRange(departArr, supportDepartArr, expenseItemArr, monthInterval, timeStartStr, timeEndStr);
 		// 模具予定人件工时总计(正式工)
 		BigDecimal pmhOfDiceTotal = new BigDecimal(0);
 		// 検査予定件工时总计(正式工)
 		BigDecimal pmhOfInspTotal = new BigDecimal(0);

 		// 予定各人件工时总计
 		for(int i=0; i<auxiliaryRecyclingArtificialList.size(); i++) {
 			if("DICE".equals(auxiliaryRecyclingArtificialList.get(i).getAuxiliaryDepartmentCode())) {
 				pmhOfDiceTotal = pmhOfDiceTotal.add(auxiliaryRecyclingArtificialList.get(i).getsHNum());
 			}else {
 				pmhOfInspTotal = pmhOfInspTotal.add(auxiliaryRecyclingArtificialList.get(i).getsHNum());
 			}
 		}

 		// 正式工人件工时
 		for(AuxiliaryRecyclingArtificial auxiliaryRecyclingArtificial:auxiliaryRecyclingArtificialList) {
 			PersonManHourShCoefficientBean personManHourShCoefficientBean = new PersonManHourShCoefficientBean();

 			switch(auxiliaryRecyclingArtificial.getDirectDepartmentCode()) {
 				case "DE":
 					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
 					// 模具的场合
 					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
 						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
 					}else {
 						// 検査的场合
 						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
 					}
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDE);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "DFA":
 					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
 					// 模具的场合
 					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
 						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
 					}else {
 						// 検査的场合
 						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
 					}
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDF, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDF);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
 				case "DS":
 					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
 					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
 					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
 					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
 					// 模具的场合
 					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
 						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
 					}else {
 						// 検査的场合
 						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
 						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
 					}
 					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
 					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDS);//机械时间予定
 					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
 					break;
				case "DU":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDU, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDU);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EE":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEE);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "ES":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgES, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgES);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EU":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEU, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEU);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EW":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEW, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEW);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "UFINS":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificial.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("正式工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificial.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificial.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourFormalWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgUFINS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgUFINS);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
			}
		}

		// 预定辅助部门人件工时(劳务工)
		List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificialLaborList = this.listAuxiliaryRecyclingArtificialByDateRange(departArr, supportDepartArr, expenseItemLaborArr, monthInterval, timeStartStr, timeEndStr);
		// 模具予定人件工时总计(劳务工)
		BigDecimal pmhLaborOfDiceTotal = new BigDecimal(0);
		// 検査予定件工时总计(劳务工)
		BigDecimal pmhLaborOfInspTotal = new BigDecimal(0);

		// 予定各人件工时总计(劳务工)
		for(int i=0; i<auxiliaryRecyclingArtificialLaborList.size(); i++) {
			if("DICE".equals(auxiliaryRecyclingArtificialList.get(i).getAuxiliaryDepartmentCode())) {
				pmhLaborOfDiceTotal = pmhLaborOfDiceTotal.add(auxiliaryRecyclingArtificialLaborList.get(i).getsHNum());
			}else {
				pmhLaborOfInspTotal = pmhLaborOfInspTotal.add(auxiliaryRecyclingArtificialLaborList.get(i).getsHNum());
			}
		}

		// 劳务工人件工时
		for(AuxiliaryRecyclingArtificial auxiliaryRecyclingArtificialLabor:auxiliaryRecyclingArtificialLaborList) {
			PersonManHourShCoefficientBean personManHourShCoefficientBean = new PersonManHourShCoefficientBean();
			switch(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()) {
				case "DE":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDE);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DFA":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDF, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDF);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DS":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDS);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "DU":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgDU, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgDU);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EE":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEE, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEE);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "ES":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgES, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgES);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EU":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEU, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEU);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "EW":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgEW, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgEW);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
				case "UFINS":
					personManHourShCoefficientBean.setMachineType(auxiliaryRecyclingArtificialLabor.getDirectDepartmentCode()); // 机械类别
					personManHourShCoefficientBean.setDepartmentCode(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode()); // 部门
					personManHourShCoefficientBean.setWorkType("劳务工");//人件工种
					personManHourShCoefficientBean.setPersonManHourPreAvg(auxiliaryRecyclingArtificialLabor.getsHNum());//予定人件工时
					// 模具的场合
					if("DICE".equals(auxiliaryRecyclingArtificialLabor.getAuxiliaryDepartmentCode())) {
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFDice.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfDiceTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufDiceWorkClass);
					}else {
						// 検査的场合
						personManHourShCoefficientBean.setPersonManHourActAvg(manHourLaborWorkUFQC.multiply((personManHourShCoefficientBean.getPersonManHourPreAvg().divide(pmhLaborOfInspTotal, 8, RoundingMode.HALF_UP))));//实绩人件工时SH分配
						personManHourShCoefficientBean.setWorkClass(ufqcWorkClass);
					}
					personManHourShCoefficientBean.setPersonManHourSH(String.valueOf(personManHourShCoefficientBean.getPersonManHourActAvg().divide(machineTimePreAvgUFINS, 4, RoundingMode.HALF_UP)));	//新年度人件工时SH系数
					personManHourShCoefficientBean.setMachineTimePreAvg(machineTimePreAvgUFINS);//机械时间予定
					personManHourShCoefficientBeanList.add(personManHourShCoefficientBean);
					break;
			}
		}
		return personManHourShCoefficientBeanList;
	}

	/**
	 * UF分摊MW
	 * @param monthInterval  日期相差的月数
	 * @param timeStartStr 开始日期
	 * @param timeEndStr 结束日期
	 * @param expenseType 费用项目
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/02/01 09:43
	 * @return  List<RepairAndAuxiliaryShCoefficientBean>
	 * @throws ParseException
	 */
	@Override
	public BigDecimal getShareAmountForUF( int monthInterval, String timeStartStr, String timeEndStr, String expenseType) throws Exception{
		String year =YearUtil.getYear(timeStartStr);
		// UF分摊MW金额总计
		BigDecimal shareAmountTotal = new BigDecimal(0);
		//取得含有UF分摊比例的小部门编号
		List<SmallDepartment> smallDepartmentList =this.listShareRateForUF(expenseType, year);
		if(smallDepartmentList != null && smallDepartmentList.size() > 0 ) {
			for(int i = 0; i<smallDepartmentList.size(); i++) {
				// 根据费用种类、部门编号检索「辅材费用表」取得实际金额合计
				BigDecimal shareAmountSum = this.selectAuxMaterialCostSum(timeStartStr, timeEndStr, expenseType, smallDepartmentList.get(i).getDepartmentCode(), null, RepairAndAuxiliaryMaterialDepartment.CATEGORY_2);
				if("补修费".equals(expenseType) &&shareAmountSum!=null && shareAmountSum.compareTo(BigDecimal.ZERO)>0) {
					// 根据补修UF分摊比例取得 分摊UF金额
					shareAmountTotal = shareAmountTotal.add(shareAmountSum.multiply(smallDepartmentList.get(i).getUfShareRateOfRepaire()));
				}else if("辅材费".equals(expenseType) && shareAmountSum!=null && shareAmountSum.compareTo(BigDecimal.ZERO)>0) {
					// 根据辅材UF分摊比例取得 分摊UF金额
					shareAmountTotal = shareAmountTotal.add(shareAmountSum.multiply(smallDepartmentList.get(i).getUfShareRateOfAuxiliary()));
				}
			}
		}
		// 将取得UF分摊MW金额总计加权平均
		shareAmountTotal = shareAmountTotal.divide(new BigDecimal(monthInterval), 4, RoundingMode.HALF_UP);
		return shareAmountTotal;
	}

	/**
	 * UF予定机械时间
	 * @param monthInterval
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/04 09:43
	 * @return int
	 * @throws ParseException
	 */
	@Override
	public  Map<String, BigDecimal> getUfMachineTimePreMap(List<String> directDepartList, int monthInterval,String timeStartStr, String timeEndStr) throws Exception {
		// 根据时间范围获取机械时间予定明细(MW)
		List<MachineTimePreBean> machineTimePreBeanList = this.listMachineTimePreBeanForUF(monthInterval, timeStartStr, timeEndStr);

		Map<String, BigDecimal> ufMachineTimePreMap=new HashMap<String, BigDecimal>();
		//械时间予定
		for(MachineTimePreBean machineTimePreBean:machineTimePreBeanList) {
			for (int i = 0; i < directDepartList.size(); i++) {
				if(machineTimePreBean.getDepartment().equals(directDepartList.get(i))) {
					ufMachineTimePreMap.put(machineTimePreBean.getDepartment(),  new BigDecimal(machineTimePreBean.getMachineTimePreAvg()));
					break;
				}
			}
		}
		return ufMachineTimePreMap;
	}

	/**
	 * 判断BigDecimal类型值是否为NULL
	 * @param bigDecimal
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/02/01 09:43
	 * @return  BigDecimal
	 * @throws ParseException
	 */
	@Override
	public BigDecimal isBigDecimalNotNull(BigDecimal bigDecimal) {

		if (bigDecimal == null) {
			return BigDecimal.ZERO;
		}else {
			return	bigDecimal;
		}
	}

	/**
	 * MW补修辅材实际费用（分摊UF后的加权平均）
	 * @param monthInterval  日期相差的月数
	 * @param timeStartStr 开始日期
	 * @param timeEndStr 结束日期
	 * @param expenseType 费用种类
	 * @param department 部门
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/04 09:43
	 * @return int
	 * @throws ParseException
	 */
	@Override
	public BigDecimal getAmount(int monthInterval, String timeStartStr, String timeEndStr, String expenseType, String department) throws Exception {
		BigDecimal amountTotal = new BigDecimal(0);
		String year =YearUtil.getYear(timeStartStr);
		List<AuxiliaryMaterialCost>  auxiliaryMaterialCostList =  this.listAuxiliaryMaterialCostByDateRange(monthInterval, timeStartStr, timeEndStr, expenseType, department, "1", year);
		if(auxiliaryMaterialCostList != null && auxiliaryMaterialCostList.size() > 0) {
			for(AuxiliaryMaterialCost auxiliaryMaterialCost:auxiliaryMaterialCostList) {
				// 根据部门编号检索「小部门明细表」取得UF分摊比例
				BigDecimal ufShareRate = BigDecimal.ZERO;
				if("补修费".equals(expenseType)) {
					ufShareRate = this.selectRepaireUfShareRate(auxiliaryMaterialCost.getDepartmentCode(), year);
				}else if("辅材费".equals(expenseType)) {
					ufShareRate = this.selectAuxiliaryUfShareRate(auxiliaryMaterialCost.getDepartmentCode(), year);
				}
				if (ufShareRate.compareTo(BigDecimal.ZERO) > 0) {
					// MW补修辅材实际费用（分摊UF后） = MW补修辅材实际费用 - MW分摊UF的金额
					BigDecimal amountAfterUfShareRate = auxiliaryMaterialCost.getAmount().subtract(auxiliaryMaterialCost.getAmount().multiply(ufShareRate));
					amountTotal= amountTotal.add(amountAfterUfShareRate);
				}else {
					amountTotal= amountTotal.add(auxiliaryMaterialCost.getAmount());
				}
			}
		}
		return amountTotal;
	}

	/**
	 * 预定辅助部门回收计算补修辅材金额总计
	 * @param monthInterval
	 * @param flag 0:MW / 1:UF
	 * @param expenseItem 费用项目
	 * @param timeStartStr
	 * @param timeEndStr
	 * @param departArr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/30 09:43
	 * @return Map
	 * @throws ParseException
	 */
	@Override
	public  Map<String, BigDecimal> getAmountPreTotalMap(List<String> auxiliaryDepartList, int monthInterval, int flag, String expenseItem, String timeStartStr, String timeEndStr, String[] departArr) throws Exception {
		// 检索「预定辅助部门回收计算补修辅材表」取得各辅助部门金额总计（加权平均）
		List<AuxiliaryRecycling> AuxiliaryRecyclingList = this.listAuxiliaryRecyclingAmountTotal(monthInterval, flag, expenseItem, timeStartStr, timeEndStr, departArr);

		Map<String, BigDecimal> amountPreTotalMap=new HashMap<String, BigDecimal>();
		for(AuxiliaryRecycling auxiliaryRecycling:AuxiliaryRecyclingList) {
			for (int i = 0; i < auxiliaryDepartList.size(); i++) {
				if(auxiliaryRecycling.getAuxiliaryDepartmentCode().equals(auxiliaryDepartList.get(i))) {
					amountPreTotalMap.put(auxiliaryRecycling.getAuxiliaryDepartmentCode(), auxiliaryRecycling.getAmount());
					break;
				}
			}
		}
		return amountPreTotalMap;

	}

	/**
	 * MW予定机械时间
	 * @param monthInterval
	 * @param timeStartStr
	 * @param timeEndStr
	 * @throws Exception
	 * <AUTHOR>
	 * @create 2024/01/04 09:43
	 * @return int
	 * @throws ParseException
	 */
	@Override
	public  Map<String, BigDecimal> getMwMachineTimePreMap(List<String> directDepartList, int monthInterval,String timeStartStr, String timeEndStr) throws Exception {
		// 根据时间范围获取机械时间予定明细(MW)
		List<MachineTimePreBean> machineTimePreBeanList = this.listMachineTimePreBeanForMW(monthInterval, timeStartStr, timeEndStr);
		//检索MW直接部门（机械类别）
		Map<String, BigDecimal> machineTimePreMap=new HashMap<String, BigDecimal>();

		for(MachineTimePreBean machineTimePreBean:machineTimePreBeanList) {
			for (int i = 0; i < directDepartList.size(); i++) {
				if(machineTimePreBean.getDepartment().equals(directDepartList.get(i))) {
					machineTimePreMap.put(machineTimePreBean.getDepartment(),  new BigDecimal(machineTimePreBean.getMachineTimePreAvg()));
					break;
				}
			}
		}
		return machineTimePreMap;
	}
}