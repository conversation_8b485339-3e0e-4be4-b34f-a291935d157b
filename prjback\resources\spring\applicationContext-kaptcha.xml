<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    
	<description>kaptcha Configuration</description>
	<bean id="captchaProducer" class="com.google.code.kaptcha.impl.DefaultKaptcha">  
		<property name="config">  
			<bean class="com.google.code.kaptcha.util.Config">  
				<constructor-arg>  
					<props>  
						<prop key="kaptcha.border">no</prop>
				            <prop key="kaptcha.textproducer.font.color">black</prop>
				            <prop key="kaptcha.image.width">85</prop>
				            <prop key="kaptcha.image.height">42</prop>
				            <prop key="kaptcha.textproducer.font.size">30</prop>
				            <prop key="kaptcha.obscurificator.impl">com.google.code.kaptcha.impl.ShadowGimpy</prop>
				            <prop key="kaptcha.session.key">code</prop>
				            <prop key="kaptcha.noise.impl">com.google.code.kaptcha.impl.NoNoise</prop>
				            <prop key="kaptcha.background.clear.from">95,184,120</prop>
				            <prop key="kaptcha.background.clear.to">white</prop>
				            <prop key="kaptcha.textproducer.char.length">4</prop>
				            <prop key="kaptcha.textproducer.font.names">彩云,宋体,楷体,微软雅黑</prop>
				            <prop key="kaptcha.textproducer.char.string">1234567890</prop>
					</props>  
				</constructor-arg>  
			</bean>  
		</property>  
	</bean>	
</beans>