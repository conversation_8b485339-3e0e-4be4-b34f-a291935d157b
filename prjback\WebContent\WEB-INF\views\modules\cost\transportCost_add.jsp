<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>运输费用添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/costPrice/transportCost/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>年月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="yearMonth" name="yearMonth" value=""/>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 100px"><span class="star">*</span>区分:</label>
                        <div class="layui-input-block" style="margin-left: 130px">
                            <select class="layui-select" id="transportCode" name="transportCode" lay-verify="required" lay-search="true"  required>
                                <option value="">请选择</option>
                                <option value="1">EM</option>
                                <option value="2">EF</option>
                                <option value="3">EF-09</option>
                                <option value="4">ER</option>
                                <option value="5">EH</option>
                                <option value="6">UF</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label" style="width: 100px"><span class="star">*</span>地区:</label>
                        <div class="layui-input-block" style="margin-left: 130px">
                            <select class="layui-select" id="area" name="area" lay-verify="required" lay-search="true"  required>
                                <option value="">请选择</option>
                                <option value="1">自提</option>
                                <option value="2">華南</option>
                                <option value="3">華東</option>
                                <option value="4">華北</option>
                                <option value="5">その他計</option>
                                <option value="6">UF国内</option>
                                <option value="7">UF出口</option>
                            </select>
                        </div>
                    </div>

<%--                    <div class="layui-inline layui-col-md5">--%>
<%--                        <label class="layui-form-label"><span class="star">*</span>运输费单价:</label>--%>
<%--                        <div class="layui-input-block">--%>
<%--                            <input type="text" class="layui-input" id="transportPrice" name="transportPrice" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>--%>
<%--                        </div>--%>
<%--                    </div>--%>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>出库量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="outNumber" name="outNumber" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>运输费金额:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="transportAmount" name="transportAmount" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>回收运输费单价:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="recoveryPrice" name="recoveryPrice" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>销售量:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="salesVolume" name="salesVolume" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>回收运输费金额:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="recoveryAmount" name="recoveryAmount" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/cost/transportCost_add.js?time=91"></script>
</myfooter>
</body>
</html>
