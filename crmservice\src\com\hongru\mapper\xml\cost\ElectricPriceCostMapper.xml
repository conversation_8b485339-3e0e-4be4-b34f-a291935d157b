<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.ElectricPriceCostMapper">
    <sql id="electricPriceCost_sql">
		ele.[流水号] AS costId,ele.[状态] AS state,ele.[年月] AS yearMonth,ele.[电费计算标记] AS calculateTag,
		ele.[电费单价] AS eleSinglePrice,ele.[电费单价实际] AS eleSinglePriceAct,ele.[用电量] AS eleCost,ele.[用电量实际] AS eleCostAct,ele.[年] AS year,ele.[月] AS month,
		ele.[创建人标识] AS creatorId,ele.[创建人姓名] AS creatorName,ele.[创建时间] AS createdTime
	</sql>

    <insert id="insertelectricPriceCost" parameterType="com.hongru.entity.cost.ElectricPriceCost">
        INSERT INTO [CostPrice].[dbo].[电费用表]
		(
		[状态],
		[年月],
		[电费计算标记],
		[电费单价],
		[用电量],
		[电费单价实际],
		[用电量实际],
		[年],
		[月],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{electricPriceCost.state},
		#{electricPriceCost.yearMonth},
		#{electricPriceCost.calculateTag},
		#{electricPriceCost.eleSinglePrice},
		#{electricPriceCost.eleCost},
		#{electricPriceCost.eleSinglePriceAct},
		#{electricPriceCost.eleCostAct},
		#{electricPriceCost.year},
		#{electricPriceCost.month},
		#{electricPriceCost.creatorId},
		#{electricPriceCost.creatorName},
		#{electricPriceCost.createdTime}
		)
    </insert>

    <select id="selectByCostId" resultType="com.hongru.entity.cost.ElectricPriceCost">
        SELECT
        <include refid="electricPriceCost_sql"/>
        FROM [CostPrice].[dbo].[电费用表] ele
        <where>
            ele.[状态] != 9
            <if test="costId != null">
                AND ele.[流水号] = #{costId}
            </if>
        </where>
    </select>

    <select id="selectByYearMonth" resultType="com.hongru.entity.cost.ElectricPriceCost">
        SELECT
        TOP 1
        <include refid="electricPriceCost_sql"/>
        FROM [CostPrice].[dbo].[电费用表] ele
        <where>
            ele.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND ele.[年月] = #{yearMonth}
            </if>
            <if test="calculateTag != null and calculateTag != ''">
                AND ele.[电费计算标记] = #{calculateTag}
            </if>
        </where>
    </select>

    <select id="listCostPricePage" resultType="com.hongru.entity.cost.ElectricPriceCost">
        SELECT
        <include refid="electricPriceCost_sql"/>
        FROM [CostPrice].[dbo].[电费用表] ele
        <where>
            ele.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND ele.[年月] = #{yearMonth}
            </if>
        </where>
        <if test="pageInfo.sort != null">
            ORDER BY ${pageInfo.sort} ${pageInfo.order}
        </if>
        <if test="pageInfo.sort == null">
            ORDER BY ele.[年月] DESC, ele.[电费计算标记] DESC
        </if>
        <if test="pageInfo.limit != null">
            OFFSET ${pageInfo.offset} ROWS
            FETCH NEXT ${pageInfo.limit} ROWS ONLY
        </if>
    </select>

    <select id="listCostPricePageCount" resultType="integer">
        SELECT
        COUNT(1)
        FROM [CostPrice].[dbo].[电费用表] ele
        <where>
            ele.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND ele.[年月] = #{yearMonth}
            </if>
        </where>
    </select>

    <update id="updateState">
		UPDATE [CostPrice].[dbo].[电费用表]
		SET [状态] = #{state}
		WHERE [流水号] = #{costId}
    </update>

    <select id="selectListByYearMonth" resultType="com.hongru.entity.cost.ElectricPriceCost">
        SELECT
        <include refid="electricPriceCost_sql"/>
        FROM [CostPrice].[dbo].[电费用表] ele
        <where>
            ele.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND ele.[年月] = #{yearMonth}
            </if>
        </where>
        ORDER BY ele.[年月] DESC
    </select>

    <select id="selectElectricPriceCostListByYearMonth" resultType="com.hongru.entity.cost.ElectricPriceCost">
        SELECT
        <include refid="electricPriceCost_sql"/>
        FROM [CostPrice].[dbo].[电费用表] ele
        <where>
            ele.[状态] != 9
            <if test="yearMonth != null and yearMonth != ''">
                AND ele.[年月] = #{yearMonth}
            </if>
        </where>
        ORDER BY ele.[年月] DESC
    </select>

    <update id="updateElectricPriceCost">
        UPDATE [CostPrice].[dbo].[电费用表]
        <set>
            <if test="electricPriceCost.yearMonth != null and electricPriceCost.yearMonth != ''">
                [年月] = #{electricPriceCost.yearMonth},
            </if>
            <if test="electricPriceCost.calculateTag != null and electricPriceCost.calculateTag != ''">
                [电费计算标记] = #{electricPriceCost.calculateTag},
            </if>
            <if test="electricPriceCost.eleSinglePrice != null">
                [电费单价] = #{electricPriceCost.eleSinglePrice},
            </if>
            <if test="electricPriceCost.eleCost != null">
                [用电量] = #{electricPriceCost.eleCost},
            </if>
            <if test="electricPriceCost.eleSinglePriceAct != null">
                [电费单价实际] = #{electricPriceCost.eleSinglePriceAct},
            </if>
            <if test="electricPriceCost.eleCostAct != null">
                [用电量实际] = #{electricPriceCost.eleCostAct},
            </if>
            <if test="electricPriceCost.year != null">
                [年] = #{electricPriceCost.year},
            </if>
            <if test="electricPriceCost.month != null">
                [月] = #{electricPriceCost.month},
            </if>
            <if test="electricPriceCost.lastModifierId != null">
                [最后修改人标识] = #{electricPriceCost.lastModifierId},
            </if>
            <if test="electricPriceCost.lastModifierName != null and electricPriceCost.lastModifierName != ''">
                [最后修改人姓名] = #{electricPriceCost.lastModifierName},
            </if>
            <if test="electricPriceCost.lastModifiedTime != null">
                [最后修改时间] = #{electricPriceCost.lastModifiedTime}
            </if>
        </set>
        WHERE [流水号] = #{electricPriceCost.costId}
    </update>
    
    <select id="selectPowerElectricByDateRange" resultType="com.hongru.entity.yearRevise.PowerShCoefficientBean">
        SELECT
         SUM([用电量实际]) AS eleUsedActTotal, SUM([电费单价实际]*[用电量实际]) AS elePriceTotal
        FROM [CostPrice].[dbo].[电费用表]
        <where>
            [状态] != 9
            <if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
        </where>
    </select>
    
</mapper>