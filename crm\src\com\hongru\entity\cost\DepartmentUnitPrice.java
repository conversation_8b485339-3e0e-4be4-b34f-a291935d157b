package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

/**
 * 部门单价表实体类
 * 
 * <AUTHOR>
 */
@TableName("部门单价表")
public class DepartmentUnitPrice {

    /* 状态-正常 */
    public static final short STATE_NORMAL = 0;
    /* 状态-删除 */
    public static final short STATE_DELETED = 9;

    /* 流水号 */
    @TableId(value = "流水号", type = IdType.AUTO)
    protected int serialNumber;

    /* 年度 */
    @TableField("年度")
    protected String year;

    /* 机械类别 */
    @TableField("机械类别")
    protected String machineType;

    /* 属性 */
    @TableField("属性")
    protected String attribute;

    /* 费用项目 */
    @TableField("费用项目")
    protected String expenseItem;

    /* 工场区分 */
    @TableField("工场区分")
    protected String factoryType;

    /* SH系数 */
    @TableField("SH系数")
    protected BigDecimal shCoefficient;

    /* 单价 */
    @TableField("单价")
    protected BigDecimal unitPrice;

    /* 项目费用 */
    @TableField("项目费用")
    protected BigDecimal projectCost;

    /* 创建人姓名 */
    @TableField("创建人姓名")
    protected String creatorName;

    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;

    /* 更新人姓名 */
    @TableField("更新人姓名")
    protected String updaterName;

    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;

    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMachineType() {
        return machineType;
    }

    public void setMachineType(String machineType) {
        this.machineType = machineType;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }

    public String getExpenseItem() {
        return expenseItem;
    }

    public void setExpenseItem(String expenseItem) {
        this.expenseItem = expenseItem;
    }

    public String getFactoryType() {
        return factoryType;
    }

    public void setFactoryType(String factoryType) {
        this.factoryType = factoryType;
    }

    public BigDecimal getShCoefficient() {
        return shCoefficient;
    }

    public void setShCoefficient(BigDecimal shCoefficient) {
        this.shCoefficient = shCoefficient;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getProjectCost() {
        return projectCost;
    }

    public void setProjectCost(BigDecimal projectCost) {
        this.projectCost = projectCost;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }
}
