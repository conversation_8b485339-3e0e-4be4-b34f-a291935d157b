@charset "utf-8";
/* CSS Document */

/* === Custom font === */

@font-face {
    font-family: 'SingleSleeveRegular';
    src: url('../font/sing-webfont.eot');
    src: url('../font/sing-webfont.eot?#iefix') format('embedded-opentype'),  /* === Custom font === */
         url('../font/sing-webfont.woff') format('woff'),
         url('../font/sing-webfont.ttf') format('truetype'),
         url('../font/sing-webfont.svg#SingleSleeveRegular') format('svg');
    font-weight: normal;
    font-style: normal;

}

/* === General stuff === */

html, body{
	
	height:100%;
	background:#186aa9 url(../images/sky-background.png) top repeat-x;	
	overflow:hidden;
	padding:0;
	margin:0;
	font-family:Arial, Helvetica, sans-serif;
	
}

a{
	
	color:#3680b1;	
	
}

img, a img{
	
	border:0px none;	
	outline:none;
	
}

/* === Preloader === */

#universal-preloader{
	
	position:fixed;
	top:0px;
	left:0px;
	width:100%;
	height:100%;
	opacity:0.96;
	background:url(../images/sky-background.png) repeat;
	z-index:999999999;
	
}

#universal-preloader>.preloader{
	
	width:24px;
	height:24px;
	position:absolute;
	top:50%;
	left:50%;
	text-align:center;
	
}

.universal-preloader-preloader{
	
	position:absolute; 
	top:0px; 
	left:0px;	
	
}

/* === Main Section === */

#wrapper{
	
	width:980px;
	margin:0px auto;
	position:relative;
	height:100%;
	background:url(../images/sky-shine.jpg) top left no-repeat;
	
}

h1.not-found-text{
	font-size: 21px;
	color: #fff;
	font-family: "SingleSleeveRegular", cursive;
	letter-spacing: 2px;
	margin-bottom: 20px;
	
}

div.not-found-text{
	position: absolute;
	top: 80px;
	right: 0px;
	width: 430px;
	
}

div.graphic404{
	
	position:absolute;
	top:80px;
	left:0px;
	background:url(../images/404.png) top left no-repeat;
	width:494px;
	height:331px;
	
}
div.graphic400{
	
	position:absolute;
	top:80px;
	left:0px;
	background:url(../images/400.png) top left no-repeat;
	width:494px;
	height:331px;
	
}
div.graphic403{
	
	position:absolute;
	top:80px;
	left:0px;
	background:url(../images/403.png) top left no-repeat;
	width:494px;
	height:331px;
	
}
div.graphic500{
	
	position:absolute;
	top:80px;
	left:0px;
	background:url(../images/500.png) top left no-repeat;
	width:494px;
	height:331px;
	
}

div.planet{

	position:absolute;
	bottom:-1100px;
	margin:0px auto;
	width:980px;
	background:url(../images/planet.png) center no-repeat;
	height:1920px;
	z-index:0;
	
}

div.dog-wrapper{
	
	position:absolute;
	bottom:300px;
	left:440px;
	
}

div.dog{
	
	position:absolute;
	bottom:0px;
	left:0px;
	width:80px;
	height:80px;
	z-index:999;
	background:url(../images/dog.png) 0px 0px no-repeat;
	
}

div.search{
	
	position:absolute;
	top:145px;
	right:0px;
	width:340px;
	background:#eeeeee;
	box-shadow:1px 1px 0 #ffffff inset, 5px 5px 0px #3680b1;
	color:#555555;
	font-size:14px;
	text-shadow:1px 1px 0 #ffffff;
	border-radius:30px;
	padding:10px;
	z-index:999;
	
}

div.search input[type=submit]:hover{
	
	background:#ffffff url(../images/search.png) center center no-repeat;	
	cursor:pointer;
	
}

div.search input[type=submit]{
	
	box-shadow:1px 1px 0 #ffffff inset, 5px 5px 0px #3680b1;
	background:#eeeeee url(../images/search.png) center center no-repeat;
	border-radius:50px;
	position:absolute;
	top:0px;
	right:380px;
	width:50px;
	height:40px;
	border:none;
	
	
}

div.search input[type=text]{
	
	padding:0px 10px;
	border:0px none;
	background:none;
	color:#999999;
	width:300px;
	height:20px;
	
}

div.top-menu{
	
	position:absolute;
	top:200px;
	right:0px;
	color:#ffffff;
	z-index:998;
	
}

div.top-menu a{
	
	text-decoration:none;
	color:#eeeeee;
	margin:0px 5px;
	font-size:12px;
	
}

div.top-menu a:hover{
	
	color:#abd7fa;	
	
}

div.dog-bubble{
	
	font-size:14px;
	line-height:1.5;
	font-style:italic;
	height:179px;
	width:246px;
	background:url(../images/bubble.png) top center no-repeat;
	padding:20px 0px;
	position:absolute;
	bottom:0px;
	left:30px;
	z-index:999;
	opacity:0;
	color:#555555;
	font-size:14px;
	text-shadow:1px 1px 0 #ffffff;
	
}

div.dog-bubble>p{
	
	text-align:center;
	padding:0px 35px;	
	
}

div.bubble-options{

	opacity:0;
	visibility:hidden;
	display:none;
	
}
