package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import java.math.BigDecimal;

/**
 * 导体单价明细表实体类
 * <AUTHOR>
 */
@TableName("导体单价明细表")
public class ConductorUnitPriceDetail {
    
    /* 年度 */
    @TableField("年度")
    protected String year;
    
    /* 品目 */
    @TableField("品目")
    protected String itemCode;
    
    /* 品目名 */
    @TableField("品目名")
    protected String itemName;
    
    /* 原料区分 */
    @TableField("原料区分")
    protected String materialType;
    
    /* 关税率 */
    @TableField("关税率")
    protected BigDecimal tariffRate;
    
    /* 采购单价 */
    @TableField("采购单价")
    protected BigDecimal purchaseUnitPrice;
    
    /* 溢价 */
    @TableField("溢价")
    protected BigDecimal premium;
    
    /* 附随费用 */
    @TableField("附随费用")
    protected BigDecimal incidentalExpenses;
    
    /* 屑铜原料单价 */
    @TableField("屑铜原料单价")
    protected BigDecimal scrapCopperUnitPrice;
    
    /* 比重 */
    @TableField("比重")
    protected BigDecimal specificGravity;
    
    /* 创建人姓名 */
    @TableField("创建人姓名")
    protected String creatorName;
    
    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;
    
    /* 更新人姓名 */
    @TableField("更新人姓名")
    protected String updaterName;
    
    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public BigDecimal getTariffRate() {
        return tariffRate;
    }

    public void setTariffRate(BigDecimal tariffRate) {
        this.tariffRate = tariffRate;
    }

    public BigDecimal getPurchaseUnitPrice() {
        return purchaseUnitPrice;
    }

    public void setPurchaseUnitPrice(BigDecimal purchaseUnitPrice) {
        this.purchaseUnitPrice = purchaseUnitPrice;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getIncidentalExpenses() {
        return incidentalExpenses;
    }

    public void setIncidentalExpenses(BigDecimal incidentalExpenses) {
        this.incidentalExpenses = incidentalExpenses;
    }

    public BigDecimal getScrapCopperUnitPrice() {
        return scrapCopperUnitPrice;
    }

    public void setScrapCopperUnitPrice(BigDecimal scrapCopperUnitPrice) {
        this.scrapCopperUnitPrice = scrapCopperUnitPrice;
    }

    public BigDecimal getSpecificGravity() {
        return specificGravity;
    }

    public void setSpecificGravity(BigDecimal specificGravity) {
        this.specificGravity = specificGravity;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }
}
