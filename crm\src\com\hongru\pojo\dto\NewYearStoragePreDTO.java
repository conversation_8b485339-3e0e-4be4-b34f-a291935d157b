package com.hongru.pojo.dto;

import com.hongru.entity.yearRevise.NewYearStoragePre;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class NewYearStoragePreDTO {

    private PageInfo pageInfo;

    private List<NewYearStoragePre> newYearStoragePreList;

    public NewYearStoragePreDTO(PageInfo pageInfo, List<NewYearStoragePre> newYearStoragePreList) {
        super();
        this.pageInfo = pageInfo;
        this.newYearStoragePreList = newYearStoragePreList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

	public List<NewYearStoragePre> getNewYearStoragePreList() {
		return newYearStoragePreList;
	}

	public void setNewYearStoragePreList(List<NewYearStoragePre> newYearStoragePreList) {
		this.newYearStoragePreList = newYearStoragePreList;
	}
}
