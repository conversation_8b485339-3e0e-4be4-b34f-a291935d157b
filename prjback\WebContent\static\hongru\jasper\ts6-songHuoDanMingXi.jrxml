<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report1" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="47bd7318-3552-4d9b-a4b5-70f11b3866ba">
	<property name="ireport.zoom" value="1.2100000000000009"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<subDataset name="Table Dataset 1" uuid="14d1bf7c-8998-42be-9c45-1cd91c59806c">
		<parameter name="totalWeight" class="java.lang.String"/>
		<field name="productCode" class="java.lang.String"/>
		<field name="batchNo" class="java.lang.String"/>
		<field name="weightStr" class="java.lang.String"/>
		<field name="totalNum" class="java.lang.String"/>
		<field name="stockWeightStr" class="java.lang.String"/>
	</subDataset>
	<parameter name="contextPath" class="java.lang.String"/>
	<field name="totalWeight" class="java.lang.String"/>
	<field name="stockOutList" class="java.util.List"/>
	<field name="customerName" class="java.lang.String"/>
	<field name="printTime" class="java.lang.String"/>
	<pageHeader>
		<band height="79">
			<staticText>
				<reportElement uuid="5666b52d-8dee-488b-8de2-b9e72df0a694" x="0" y="3" width="555" height="42"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="24" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[送货单明细]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="9bbbe23b-ec9a-428f-933b-c84fc5e5ff23" x="0" y="50" width="555" height="25"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="102" splitType="Stretch">
			<componentElement>
				<reportElement uuid="d5676b7e-4b51-4dc6-928c-a582aba383bf" stretchType="RelativeToBandHeight" x="0" y="0" width="555" height="96"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="Table Dataset 1" uuid="277d9a0d-883d-4e69-a310-b29325a796f9">
						<datasetParameter name="totalWeight">
							<datasetParameterExpression><![CDATA[$F{totalWeight}]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{stockOutList})]]></dataSourceExpression>
					</datasetRun>
					<jr:columnGroup width="555" uuid="8c44d380-abab-4ba2-b4e6-292952ed4a44">
						<jr:columnGroup width="555" uuid="18535751-eeb3-4ffc-bb49-c04b28ab9c5f">
							<jr:column width="355" uuid="31ec370a-00c7-4392-92b7-c810f4f8fa2a">
								<jr:tableFooter height="30" rowSpan="1"/>
								<jr:columnHeader height="30" rowSpan="1">
									<staticText>
										<reportElement uuid="47c61d25-0125-471c-8daa-b811809ebbca" mode="Transparent" x="0" y="0" width="355" height="30"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle">
											<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
										</textElement>
										<text><![CDATA[型号尺寸]]></text>
									</staticText>
								</jr:columnHeader>
								<jr:detailCell height="30" rowSpan="1">
									<textField isStretchWithOverflow="true">
										<reportElement uuid="5789cfec-687a-4fd9-ac28-74cda784845d" x="0" y="0" width="355" height="30"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle">
											<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="false"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{productCode}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="100" uuid="ae459f64-baf0-4449-9234-a7613f518cab">
								<jr:tableFooter height="30" rowSpan="1">
									<staticText>
										<reportElement uuid="4f621a07-eb2d-40ac-b9de-842e34191bc8" mode="Transparent" x="0" y="0" width="100" height="30"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle">
											<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
										</textElement>
										<text><![CDATA[总计]]></text>
									</staticText>
								</jr:tableFooter>
								<jr:columnHeader height="30" rowSpan="1">
									<staticText>
										<reportElement uuid="f00ee182-33a5-477e-9011-68fefa92c963" mode="Transparent" x="0" y="0" width="100" height="30"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle">
											<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
										</textElement>
										<text><![CDATA[批号]]></text>
									</staticText>
								</jr:columnHeader>
								<jr:detailCell height="30" rowSpan="1">
									<textField isStretchWithOverflow="true">
										<reportElement uuid="5715c017-767c-49a8-bdb7-f65b4b2e8c5c" x="0" y="0" width="100" height="30"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle">
											<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="false"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{batchNo}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="100" uuid="57b4125d-be6e-47f8-9d13-c5a777295379">
								<jr:tableFooter height="30" rowSpan="1">
									<textField isStretchWithOverflow="true">
										<reportElement uuid="a23e2ed0-fe34-4d57-8c8a-944c5afbc71d" x="0" y="0" width="100" height="30"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle">
											<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="false"/>
										</textElement>
										<textFieldExpression><![CDATA[$P{totalWeight}]]></textFieldExpression>
									</textField>
								</jr:tableFooter>
								<jr:columnHeader height="30" rowSpan="1">
									<staticText>
										<reportElement uuid="65160669-94fa-4eea-bd67-d19402decc8c" mode="Transparent" x="0" y="0" width="100" height="30"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle">
											<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
										</textElement>
										<text><![CDATA[重量]]></text>
									</staticText>
								</jr:columnHeader>
								<jr:detailCell height="30" rowSpan="1">
									<textField isStretchWithOverflow="true">
										<reportElement uuid="da8f65ec-1a28-47cd-8fe8-67f65fda8dae" x="0" y="0" width="100" height="30"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle">
											<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="false"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{weightStr}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<lastPageFooter>
		<band height="50">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="13562ce9-103b-4c25-bd46-1e8a185debff" x="410" y="25" width="145" height="25"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{printTime}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
</jasperReport>
