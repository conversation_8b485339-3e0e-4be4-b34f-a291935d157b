<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.system.MenuMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.hongru.entity.system.Menu">
		<id column="menu_id" property="menuId" />
		<result column="parent_id" property="parentId" />
		<result column="menu_type" property="menuType" />
		<result column="menu_code" property="menuCode" />
		<result column="menu_name" property="menuName" />
		<result column="sort" property="sort" />
		<result column="href" property="href" />
		<result column="icon" property="icon" />
		<result column="status" property="status" />
		<result column="permission" property="permission" />
		<result column="create_time" property="createTime" />
		<result column="create_by" property="createBy" />
		<result column="update_time" property="updateTime" />
		<result column="update_by" property="updateBy" />
		<result column="remarks" property="remarks" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        menu_id AS menuId, parent_id AS parentId, menu_type AS menuType, menu_code AS menuCode, menu_name AS menuName, sort, href, icon, status, permission, create_time AS createTime, create_by AS createBy, update_time AS updateTime, update_by AS updateBy, remarks
    </sql>
    
    <!-- 根据目录类型查询目录列表 -->
    <select id="listByType" resultType="com.hongru.entity.system.Menu">
    	SELECT
    		<include refid="Base_Column_List" />
    	FROM
    		hr_system_menu
    	WHERE
    		menu_type = #{menuType}
    	ORDER BY
    		sort ASC
    </select>  
    
     <!-- 根据目录父级标识查询目录列表 -->
    <select id="selectMenuForTree" resultType="com.hongru.entity.system.Menu">
    	SELECT
    		<include refid="Base_Column_List" />
    	FROM
    		hr_system_menu
    		<where>
				<if test="status !=null">
					status = #{status}
				</if>
				<if test="parentId !=0">
					parent_id = #{parentId}
				</if>
				<if test="parentId ==0">
					parent_id &lt;&gt; #{parentId}
				</if>
			</where>
    	ORDER BY
    		sort ASC
    </select>  
    
    <!-- 更新目录状态，冻结目录及其及目录 -->
    <update id="updateStatusByIds">
    	UPDATE
    		hr_system_menu
    	SET status = #{status}
    	WHERE
    		menu_id IN
    		<foreach collection="menuIds" item="menuId" open="(" close=")" separator=",">
    			#{menuId}
    		</foreach>
    </update> 
    
    <!-- 通过目录ID删除角色授权记录 -->
    <delete id="deleteRoleMenus">
    	DELETE 
    	FROM
    		hr_admin_role_menu
    	WHERE
    		menu_id IN
	        <foreach collection="menuIds" item="menuId" open="(" close=")" separator=",">
	    		#{menuId}
	    	</foreach>    	    
    </delete>

</mapper>
