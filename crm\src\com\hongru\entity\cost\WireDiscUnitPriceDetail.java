package com.hongru.entity.cost;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import java.math.BigDecimal;

/**
 * 线盘单价明细表实体类
 * <AUTHOR>
 */
@TableName("线盘单价明细表")
public class WireDiscUnitPriceDetail {
    
    /* 年度 */
    @TableField("年度")
    protected String year;
    
    /* 品目 */
    @TableField("品目")
    protected String itemCode;
    
    /* 品目名 */
    @TableField("品目名")
    protected String itemName;
    
    /* 原料区分 */
    @TableField("原料区分")
    protected String materialType;
    
    /* 原料单价 */
    @TableField("原料单价")
    protected BigDecimal materialUnitPrice;
    
    /* 每卷标准重量 */
    @TableField("每卷标准重量")
    protected BigDecimal standardWeightPerRoll;
    
    /* 捆包费 */
    @TableField("捆包费")
    protected BigDecimal bundlingFee;
    
    /* 创建人姓名 */
    @TableField("创建人姓名")
    protected String creatorName;
    
    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;
    
    /* 更新人姓名 */
    @TableField("更新人姓名")
    protected String updaterName;
    
    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public BigDecimal getMaterialUnitPrice() {
        return materialUnitPrice;
    }

    public void setMaterialUnitPrice(BigDecimal materialUnitPrice) {
        this.materialUnitPrice = materialUnitPrice;
    }

    public BigDecimal getStandardWeightPerRoll() {
        return standardWeightPerRoll;
    }

    public void setStandardWeightPerRoll(BigDecimal standardWeightPerRoll) {
        this.standardWeightPerRoll = standardWeightPerRoll;
    }

    public BigDecimal getBundlingFee() {
        return bundlingFee;
    }

    public void setBundlingFee(BigDecimal bundlingFee) {
        this.bundlingFee = bundlingFee;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }
}
