package com.hongru.controller.setting;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.*;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.cost.*;
import com.hongru.entity.stat.Proportion;
import com.hongru.pojo.dto.*;
import com.hongru.service.admin.IUserService;
import com.hongru.service.cost.ICostService;
import com.hongru.service.setting.ISettingService;
import com.hongru.service.stat.IStatService;
import com.hongru.support.page.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;


@Controller
@RequestMapping(value = "/setting")
public class SettingController extends BaseController {

    @Autowired
    private ISettingService settingService;
    @Autowired
    private ICostService costService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IStatService statService;
    
    /*=================================人件比例用表======================================*/
    /**
     * 人件比例用表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @GetMapping("/proportion/list/view")
    public String proportionListView(Model model) throws Exception{
        return "/modules/setting/proportion_list";
    }
    
    /**
     * 人件比例用表明细
     * @param
     * @throws Exception
      * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @PostMapping("/proportion/list")
    @ResponseBody
    public Object  proportionList(short isSearch, String year) throws Exception{
        if(isSearch == 1){
        	List<Proportion> proportionList = statService.listProportion(year);
            return new HrPageResult(proportionList, proportionList.size());
        }else{
            return new HrPageResult(new ArrayList<Proportion>(), 0);
        }
    }  

    /**
     * 添加人件比例用表画面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @GetMapping("proportion/add/view")
    public String proportionAddView(Model model) throws Exception{
    	return "/modules/setting/proportion_add";
    }
    
    /**
     * 添加人件比例用表
     * @param proportion
     * @throws Exception
      * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @PostMapping("/proportion/add")
    @ResponseBody
    public Object proportionAdd(Proportion proportion) throws Exception{
		try {
			statService.addProportion(proportion);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("人件比例明细新增异常信息：", e);
			return new HrResult(0, e.getMessage());
		}
		return new HrResult(CommonReturnCode.SUCCESS);
	}

    /**
     * 编辑人件比例用表画面
     * @param model
     * @param statId
     * @throws Exception
     * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @GetMapping("proportion/modify/view")
    public String proportionModifyView(Model model,Integer statId) throws Exception{
    	Proportion proportion = statService.selectBystatId(statId);
        model.addAttribute("proportion", proportion);
    	return "/modules/setting/proportion_modify";
    }
    
    /**
     * 编辑人件比例用表
     * @param proportion
     * @throws Exception
      * <AUTHOR>
     * @create 2024/02/05 17:43
     * @return
     */
    @PostMapping("/proportion/modify")
    @ResponseBody
    public Object proportionModify(Proportion proportion) throws Exception{
		try {
			statService.modifyProportion(proportion);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("人件比例明细编辑异常信息：", e);
			return new HrResult(0, e.getMessage());
		}
		return new HrResult(CommonReturnCode.SUCCESS);
	}
    
    /**
     * 删除人件比例用表
     * @param costId
      * <AUTHOR>
     * @create 2024/02/05 17:43
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/proportion/remove")
    @ResponseBody
    public Object proportionRemove(Integer statId) throws Exception{
        try {
        	statService.removeProportion(statId);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /*==================================部门区分列表======================================*/
    	    /**
    	     * 使用区分表页面
    	     * @param model
    	     * @throws Exception
    	     * <AUTHOR>
    	     * @create 2023/8/10 17:43
    	     * @return
    	     */
    	    @GetMapping("/paintUseCode/list/view")
    	    public String paintUseCodeListView(Model model) throws Exception{
    	        return "/modules/setting/paintUseCode_list";
    	    }

    	    /**
    	     * 使用区分表
    	     * @param
    	     * @throws Exception
    	     * <AUTHOR>
    	     * @create 2023/8/10 17:43
    	     * @return
    	     */
    	    @PostMapping("/paintUseCode/listPage")
    	    @ResponseBody
    	    public Object paintUseCodeListPage(short isSearch,String smallCode, String smallDepartment,String midCode, String midDepartment,PageInfo pageInfo) throws Exception{
    	        if(isSearch == 1){
    	            PaintUseCodeDTO paintUseCodeDTO = costService.listPaintUseCodePage(smallCode,smallDepartment,midCode,midDepartment,pageInfo);
    	            return new HrPageResult(paintUseCodeDTO.getPaintUseCodeList(), paintUseCodeDTO.getPageInfo().getTotal());
    	        }else{
    	            return new HrPageResult(new ArrayList<PaintUseCode>(), 0);
    	        }
    	    }

    	    /**
    	     * 新增使用区分表页面
    	     * @param model
    	     * @throws Exception
    	     * <AUTHOR>
    	     * @create 2023/8/10 17:43
    	     * @return
    	     */
    	    @GetMapping("/paintUseCode/add/view")
    	    public String paintUseCodeAddView(Model model) throws Exception{
    	        return "/modules/setting/paintUseCode_add";
    	    }

    	    /**
    	     * 新增使用区分表
    	     * @param paintUseCode
    	     * @throws Exception
    	     * <AUTHOR>
    	     * @create 2023/8/10 17:43
    	     * @return
    	     */
    	    @PostMapping("/paintUseCode/add")
    	    @ResponseBody
    	    public Object paintUseCodeAdd(PaintUseCode paintUseCode) throws Exception{
    	        try {
    	            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
    	            if(authorizingUser == null ) {
    	                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
    	            }
    	            User user = userService.getById(authorizingUser.getUserId());
    	            paintUseCode.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
    	            paintUseCode.setCreatorName(user.getUserName());
    	            paintUseCode.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
    	            costService.addPaintUseCode(paintUseCode);
    	        }catch (Exception e){
    	            e.printStackTrace();
    	            logger.error("使用区分新增异常信息：", e);
    	            return new HrResult(0,e.getMessage());
    	        }
    	        return new HrResult(CommonReturnCode.SUCCESS);
    	    }

    	    /**
    	     * 删除使用区分表
    	     * @param costId
    	     * @throws Exception
    	     * <AUTHOR>
    	     * @create 2023/8/10 17:43
    	     * @return
    	     */
    	    @PostMapping("/paintUseCode/remove")
    	    @ResponseBody
    	    public Object paintUseCodeRemove(Integer costId) throws Exception{
    	        try {
    	            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
    	            if(authorizingUser == null ) {
    	                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
    	            }
    	            costService.removePaintUseCode(costId);
    	        }catch (Exception e){
    	            e.printStackTrace();
    	            return new HrResult(0,e.getMessage());
    	        }
    	        return new HrResult(CommonReturnCode.SUCCESS);
    	    }
    /*=================================补辅比例列表======================================*/
    /**
     * 补辅比例列表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/smallDepartment/list/view")
    public String smallDepartmentListView(Model model) throws Exception{
        return "/modules/setting/smallDepartment_list";
    }

    /**
     * 补辅比例列表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/smallDepartment/listPage")
    @ResponseBody
    public Object smallDepartmentListPage(short isSearch,String year,String departmentCode, String departmentName, String category, PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
            SmallDepartmentDTO smallDepartmentDTO = costService.listSmallDepartmentPage(year, departmentCode,departmentName, category,pageInfo);
            return new HrPageResult(smallDepartmentDTO.getSmallDepartmentList(), smallDepartmentDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<SmallDepartment>(), 0);
        }
    }
    
    /**
     * 新增补辅比例列表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @GetMapping("/smallDepartment/add/view")
    public String smallDepartmentAddView(Model model) throws Exception{
        return "/modules/setting/smallDepartment_add";
    }

    /**
     * 新增补辅比例列表
     * @param smallDepartment
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/smallDepartment/add")
    @ResponseBody
    public Object smallDepartmentAdd(SmallDepartment smallDepartment) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            smallDepartment.setCreatorId(Integer.valueOf(String.valueOf(user.getUserId())));
            smallDepartment.setCreatorName(user.getUserName());
            smallDepartment.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            costService.addSmallDepartment(smallDepartment);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("小部门明细新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 编辑补辅比例列表页面
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @GetMapping("/smallDepartment/edit/view")
    public String smallDepartmentEditView(Model model,Integer costId) throws Exception{
    	SmallDepartment smallDepartment = costService.getSmallDepartmentById(costId);
        model.addAttribute("smallDepartment",smallDepartment);
        return "/modules/setting/smallDepartment_modify";
    }
    /**
     * 编辑补辅比例列表
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/smallDepartment/edit")
    @ResponseBody
    public Object smallDepartmentEdit(SmallDepartment smallDepartment) throws Exception{
        try {
            costService.modifySmallDepartment(smallDepartment);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("补辅比例修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
   
    /**
     * 删除补辅比例列表
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/smallDepartment/remove")
    @ResponseBody
    public Object smallDepartmentRemove(Integer costId) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            costService.removeSmallDepartment(costId);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /*=================================补辅部门列表======================================*/
    /**
     * 补辅部门列表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/11/14
     * @return
     */
    @GetMapping("/repairAndAuxiliaryMaterialDepartment/list/view")
    public String repairAndAuxiliaryMaterialDepartmentListView(Model model) throws Exception{
        return "/modules/setting/repairAndAuxiliaryMaterialDepartment_list";
    }

    /**
     * 检索补辅部门列表
     * @param
     * @throws Exception
     * <AUTHOR>
     * @create 2024/11/14
     * @return
     */
    @PostMapping("/repairAndAuxiliaryMaterialDepartment/listPage")
    @ResponseBody
    public Object repairAndAuxiliaryMaterialDepartmentListPage(short isSearch,String year, String  departmentAttributes, String  category, PageInfo pageInfo) throws Exception{
        if(isSearch == 1){
        	RepairAndAuxiliaryMaterialDepartmentDTO repairAndAuxiliaryMaterialDepartmentDTO = settingService.listRepairAndAuxiliaryMaterialDepartmentPage(year, departmentAttributes, category, pageInfo);
            return new HrPageResult(repairAndAuxiliaryMaterialDepartmentDTO.getRepairAndAuxiliaryMaterialDepartmentList(), repairAndAuxiliaryMaterialDepartmentDTO.getPageInfo().getTotal());
        }else{
            return new HrPageResult(new ArrayList<RepairAndAuxiliaryMaterialDepartment>(), 0);
        }
    }
    
    /**
     * 新增补辅部门列表页面
     * @param model
     * @throws Exception
     * <AUTHOR>
     * @create 2024/11/15
     * @return
     */
    @GetMapping("/repairAndAuxiliaryMaterialDepartment/add/view")
    public String repairAndAuxiliaryMaterialDepartmentAddView(Model model) throws Exception{
        return "/modules/setting/repairAndAuxiliaryMaterialDepartment_add";
    }

    /**
     * 新增补辅部门列表
     * @param repairAndAuxiliaryMaterialDepartment
     * @throws Exception
     * <AUTHOR>
     * @create 2024/11/15
     * @return
     */
    @PostMapping("/repairAndAuxiliaryMaterialDepartment/add")
    @ResponseBody
    public Object repairAndAuxiliaryMaterialDepartmentAdd(RepairAndAuxiliaryMaterialDepartment repairAndAuxiliaryMaterialDepartment) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            repairAndAuxiliaryMaterialDepartment.setCreatorName(user.getUserName());
            repairAndAuxiliaryMaterialDepartment.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            settingService.addRepairAndAuxiliaryMaterialDepartment(repairAndAuxiliaryMaterialDepartment);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("补辅部门新增异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
    /**
     * 编辑补辅部门列表页面
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2024/11/15
     * @return
     */
    @GetMapping("/repairAndAuxiliaryMaterialDepartment/edit/view")
    public String repairAndAuxiliaryMaterialDepartmentEditView(Model model,Integer id) throws Exception{
    	// 根据ID
    	RepairAndAuxiliaryMaterialDepartment repairAndAuxiliaryMaterialDepartment = settingService.getRepairAndAuxiliaryMaterialDepartmentById(id);
        model.addAttribute("repairAndAuxiliaryMaterialDepartment",repairAndAuxiliaryMaterialDepartment);
        return "/modules/setting/repairAndAuxiliaryMaterialDepartment_modify";
    }
    
    /**
     * 编辑补辅部门列表
     * @param model
     * @param costId
     * @throws Exception
     * <AUTHOR>
     * @create 2023/7/5 17:43
     * @return
     */
    @PostMapping("/repairAndAuxiliaryMaterialDepartment/edit")
    @ResponseBody
    public Object repairAndAuxiliaryMaterialDepartmentEdit(RepairAndAuxiliaryMaterialDepartment repairAndAuxiliaryMaterialDepartment) throws Exception{
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            repairAndAuxiliaryMaterialDepartment.setCreatorName(user.getUserName());
            repairAndAuxiliaryMaterialDepartment.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
            
        	// 更新补辅部门列表
        	settingService.modifyRepairAndAuxiliaryMaterialDepartment(repairAndAuxiliaryMaterialDepartment);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("补辅部门修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
   
    /**
     * 删除补辅部门列表
     * @param id
     * @throws Exception
     * <AUTHOR>
     * @create 2023/8/10 17:43
     * @return
     */
    @PostMapping("/repairAndAuxiliaryMaterialDepartment/remove")
    @ResponseBody
    public Object repairAndAuxiliaryMaterialDepartmentRemove(Integer id) throws Exception{
        try {
        	//根据流水号删除补辅部门列表数据
            settingService.removeRepairAndAuxiliaryMaterialDepartment(id);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }
    
}
