package com.hongru.service.admin;

import java.util.List;

import com.baomidou.mybatisplus.service.IService;
import com.hongru.base.BasePageDTO;
import com.hongru.entity.admin.Organization;
import com.hongru.pojo.vo.OrganizationVO;
import com.hongru.support.page.PageInfo;

/**
 * 
*    
* 类名称：IOrganizationService   
* 类描述：Organization / 部门表 业务逻辑层接口                  
* 创建人：hongru   
* 创建时间：2017年3月31日 下午5:48:31   
*
 */
public interface IOrganizationService extends IService<Organization> {
	
	/**
	 * 创建部门
	 * @param organization 部门信息
	 * @param userName 操作人
	 * @return
	 */
	Integer insertOrganization(Organization organization, String userName);
	
	/**
	 * 根据部门状态查找部门列表
	 * @param status 部门状态
	 * @return List<Organization>
	 */ 
	List<Organization> listBySataus(Integer status);
	
	/**
	 * 根据分页信息/搜索内容查找部门列表
	 * @param pageInfo 分页信息
	 * @param search 搜索内容
	 * @return
	 */
	BasePageDTO<Organization> listByPage(PageInfo pageInfo, String search);
	
	/**
	 * 查找部门及其部门人员
	 * @return
	 */
	List<OrganizationVO> listOrganizationsDetail();
	
	/**
	 * 更新部门状态
	 * @param organizationId 部门ID
	 * @return
	 */
	Integer updateStatus(Long organizationId);
	
	/**
	 * 更新部门信息
	 * @param organization 部门信息
	 * @param userName 操作人
	 * @return
	 */
	Integer updateOrganization(Organization organization, String userName);
	
	/**
	 * 根据部门ID删除部门信息,同时重置用户表部门ID
	 * @param organizationId
	 * @return
	 */
	Integer deleteByOrganizationId(Long organizationId);

	/**
	* 批量删除部门
	* @throws
	* <AUTHOR>
	* @create 2022/2/11 11:33
	* @return
	*/
	void deleteOrganizations(Integer[] organizationIdArr);
}
