package com.hongru.entity.yearRevise;

import java.math.BigDecimal;

/**
* 入库量查询返回结果实体类
* <AUTHOR>
* @create 2024/01/04 09:55
*/
public class StorageInActBean {

	/* EM入库量实绩 */
	private BigDecimal storageInActEM;
	/* EF入库量实绩 */
	private BigDecimal storageInActEF;
	/* EF09入库量实绩 */
	private BigDecimal storageInActEF09;
	/* ER入库量实绩 */
	private BigDecimal storageInActER;
	/* EH入库量实绩 */
	private BigDecimal storageInActEH;
	
	public BigDecimal getStorageInActEM() {
		return storageInActEM;
	}
	public void setStorageInActEM(BigDecimal storageInActEM) {
		this.storageInActEM = storageInActEM;
	}
	public BigDecimal getStorageInActEF() {
		return storageInActEF;
	}
	public void setStorageInActEF(BigDecimal storageInActEF) {
		this.storageInActEF = storageInActEF;
	}
	public BigDecimal getStorageInActEF09() {
		return storageInActEF09;
	}
	public void setStorageInActEF09(BigDecimal storageInActEF09) {
		this.storageInActEF09 = storageInActEF09;
	}
	public BigDecimal getStorageInActER() {
		return storageInActER;
	}
	public void setStorageInActER(BigDecimal storageInActER) {
		this.storageInActER = storageInActER;
	}
	public BigDecimal getStorageInActEH() {
		return storageInActEH;
	}
	public void setStorageInActEH(BigDecimal storageInActEH) {
		this.storageInActEH = storageInActEH;
	}
}
