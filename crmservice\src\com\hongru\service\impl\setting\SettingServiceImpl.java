package com.hongru.service.impl.setting;

import com.hongru.entity.cost.RepairAndAuxiliaryMaterialDepartment;
import com.hongru.mapper.cost.RepairAndAuxiliaryMaterialDepartmentMapper;
import com.hongru.pojo.dto.RepairAndAuxiliaryMaterialDepartmentDTO;
import com.hongru.service.setting.ISettingService;
import com.hongru.support.page.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;


@Service
public class SettingServiceImpl implements ISettingService {
	
	@Autowired
	private RepairAndAuxiliaryMaterialDepartmentMapper repairAndAuxiliaryMaterialDepartmentMapper;


	/*=================================补辅部门列表======================================*/
	/**
	 * 添加补辅部门列表
	 * @param repairAndAuxiliaryMaterialDepartmentMapper
	 * @throws
	 * <AUTHOR>
	 * @create 2024/11/14 11:08
	 * @return int
	 */
	@Override
	public int addRepairAndAuxiliaryMaterialDepartment(RepairAndAuxiliaryMaterialDepartment repairAndAuxiliaryMaterialDepartment) throws Exception {
		repairAndAuxiliaryMaterialDepartmentMapper.insertRepairAndAuxiliaryMaterialDepartment(repairAndAuxiliaryMaterialDepartment);
		return repairAndAuxiliaryMaterialDepartment.getId();
	}

	/**
	 * 删除补辅部门列表
	 * @param id
	 * @throws
	 * <AUTHOR>
	 * @create 2024/11/14 11:08
	 */
	@Override
	public void removeRepairAndAuxiliaryMaterialDepartment(Integer id) {
		repairAndAuxiliaryMaterialDepartmentMapper.deleteRepairAndAuxiliaryMaterialDepartment(id);
	}
	
	/**
	 * 编辑补辅比例列表
	 * @param repairAndAuxiliaryMaterialDepartmentMapper
	 * @throws
	 * <AUTHOR>
	 * @create 2024/11/14 11:08
	 */
	@Override
	public void modifyRepairAndAuxiliaryMaterialDepartment(RepairAndAuxiliaryMaterialDepartment repairAndAuxiliaryMaterialDepartment) throws Exception{
		repairAndAuxiliaryMaterialDepartmentMapper.updateRepairAndAuxiliaryMaterialDepartment(repairAndAuxiliaryMaterialDepartment);
	}
	
	/**
	 * 检索补辅比例列表
	 * @param year
	 * @throws
	 * <AUTHOR>
	 * @create 2024/11/14 11:08
	 */
	@Override
	public RepairAndAuxiliaryMaterialDepartmentDTO listRepairAndAuxiliaryMaterialDepartmentPage(String year, String departmentAttributes, String category, PageInfo pageInfo) throws Exception {
		List<RepairAndAuxiliaryMaterialDepartment> repairAndAuxiliaryMaterialDepartmentList = repairAndAuxiliaryMaterialDepartmentMapper.listRepairAndAuxiliaryMaterialDepartmentPage(year, departmentAttributes,category, pageInfo);
		Integer total = repairAndAuxiliaryMaterialDepartmentMapper.listRepairAndAuxiliaryMaterialDepartmentPageCount(year, category);
		pageInfo.setTotal(total);
		return new RepairAndAuxiliaryMaterialDepartmentDTO(pageInfo, repairAndAuxiliaryMaterialDepartmentList);
	}
	
	/**
	 * 补辅部门列表分页列表
	 * @param id
	 * @throws
	 * <AUTHOR>
	 * @create 2024/11/14
	 * @return RepairAndAuxiliaryMaterialDepartment
	 */
	@Override
	public RepairAndAuxiliaryMaterialDepartment getRepairAndAuxiliaryMaterialDepartmentById(int id)throws Exception{
		RepairAndAuxiliaryMaterialDepartment repairAndAuxiliaryMaterialDepartment = repairAndAuxiliaryMaterialDepartmentMapper.selectById(id);
		//部门属性
		HashMap<String,String> departmentAttributesMap = new HashMap<String, String>();
		departmentAttributesMap.put("1","直接部门");
		departmentAttributesMap.put("2","辅助部门");
		departmentAttributesMap.put("3","共通部门");
		departmentAttributesMap.put("4","特殊部门");
		repairAndAuxiliaryMaterialDepartment.setDepartmentAttributesStr(departmentAttributesMap.get(repairAndAuxiliaryMaterialDepartment.getDepartmentAttributes()));
		//类别
		HashMap<String,String> categoryMap = new HashMap<String, String>();
		categoryMap.put("1","MW");
		categoryMap.put("2","UF");
		repairAndAuxiliaryMaterialDepartment.setCategoryStr(categoryMap.get(repairAndAuxiliaryMaterialDepartment.getCategory()));
		
		return repairAndAuxiliaryMaterialDepartment;
	}
	
	/**
	 * 检索补辅部门
	 * @param departmentAttributes 部门属性
	 * @param category 类别
	 * @throws
	 * <AUTHOR>
	 * @create 2024/11/14
	 * @return List<Sting>
	 */
	@Override
	public List<String> getDepartmentList(String year, String departmentAttributes, String category) throws Exception {
		List<String> departmentList = repairAndAuxiliaryMaterialDepartmentMapper.getDepartmentList(year, departmentAttributes, category, null);
		return departmentList;
	}

	/**
	 * 检索补辅部门
	 * @param year 年度
	 * @param excludeDepAttribute 排除部门属性（不查询这部分数据）
	 * @throws
	 * @return List<Sting>
	 */
	@Override
	public List<String> getDepartmentList(String year, String excludeDepAttribute) throws Exception {
		List<String> departmentList = repairAndAuxiliaryMaterialDepartmentMapper.getDepartmentList(year, null, null, excludeDepAttribute);
		return departmentList;
	}

	/**
	* 补辅部门列表List
	* @param year
	* @param departmentAttributes
	* @param category
	* @throws
     * <AUTHOR>
     * @create 2024/11/14
	* @return List<RepairAndAuxiliaryMaterialDepartment>
	*/
	@Override
	public List<RepairAndAuxiliaryMaterialDepartment> listRepairAndAuxiliaryMaterialDepartment(String year, String departmentAttributes, String category) throws Exception {
		List<RepairAndAuxiliaryMaterialDepartment> repairAndAuxiliaryMaterialDepartmentList = repairAndAuxiliaryMaterialDepartmentMapper.listRepairAndAuxiliaryMaterialDepartment(year, departmentAttributes, category);
		return repairAndAuxiliaryMaterialDepartmentList;
	}

}