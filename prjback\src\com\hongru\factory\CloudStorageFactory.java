package com.hongru.factory;

import com.hongru.common.enums.CloudServiceEnum;
import com.hongru.service.IBaseCloudStorageService;

/**
 * 
* 类名称：CloudStorageFactory   
* 类描述：CloudStorageFactory 云存储工厂类接口   
* 创建人：hongru   
* 创建时间：2017年7月30日 下午10:11:59   
*
 */
public interface CloudStorageFactory {
	
	/**
	 * 获取云存储处理器
	 * 
	 * @param cloudServiceEnum				云存储名称
	 * @return IBaseCloudStorageService		云存储上传文件基类接口
	 */
	IBaseCloudStorageService getBaseCloudStorageService(CloudServiceEnum cloudServiceEnum);

}
