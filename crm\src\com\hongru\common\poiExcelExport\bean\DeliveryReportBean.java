package com.hongru.common.poiExcelExport.bean;

/**
* 出库报表bean
* <AUTHOR>
* @create 2022/11/02 20:58
*/
public class DeliveryReportBean {
	/* 客户简称 */
	protected String customerAbbreviation;
	/* 线盘名称 */
	protected String wireReelName;
	/* 木托总数 */
	protected int totalPalletNum;
	/* 总重量 */
	protected Double totalWeight;
	/* 总线盘数 */
	protected int totalCoilNum;
	/* 上行(型号) */
	protected String upstream;
	/* 尺寸 */
	protected Double size;
	/* 客户代码(代码) */
	protected String customerCode;
	/* 机器 */
	protected String machine;
	/* 备注 */
	protected String remark;
	/* 产品代码 */
	protected String productCode;
	/* 尺寸Str */
	protected String sizeStr;

	public String getCustomerAbbreviation() {
		return customerAbbreviation;
	}

	public void setCustomerAbbreviation(String customerAbbreviation) {
		this.customerAbbreviation = customerAbbreviation;
	}

	public String getWireReelName() {
		return wireReelName;
	}

	public void setWireReelName(String wireReelName) {
		this.wireReelName = wireReelName;
	}

	public int getTotalPalletNum() {
		return totalPalletNum;
	}

	public void setTotalPalletNum(int totalPalletNum) {
		this.totalPalletNum = totalPalletNum;
	}

	public Double getTotalWeight() {
		return totalWeight;
	}

	public void setTotalWeight(Double totalWeight) {
		this.totalWeight = totalWeight;
	}

	public int getTotalCoilNum() {
		return totalCoilNum;
	}

	public void setTotalCoilNum(int totalCoilNum) {
		this.totalCoilNum = totalCoilNum;
	}

	public String getUpstream() {
		return upstream;
	}

	public void setUpstream(String upstream) {
		this.upstream = upstream;
	}

	public Double getSize() {
		return size;
	}

	public void setSize(Double size) {
		this.size = size;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getMachine() {
		return machine;
	}

	public void setMachine(String machine) {
		this.machine = machine;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getSizeStr() {
		return sizeStr;
	}

	public void setSizeStr(String sizeStr) {
		this.sizeStr = sizeStr;
	}
}
