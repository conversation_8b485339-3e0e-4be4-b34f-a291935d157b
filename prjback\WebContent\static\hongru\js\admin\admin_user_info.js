layui.use(['layer', 'form', 'element', 'admin'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var element = layui.element;
    var admin = layui.admin;

    $('#userInfoHead').click(function () {
        admin.cropImg({
            imgSrc: $('#userInfoHead>img').attr('src'),
            onCrop: function (res) {
                $('#userInfoHead>img').attr('src', res);
                parent.layui.jquery('.layui-layout-admin>.layui-header .layui-nav img.layui-nav-img').attr('src', res);


                var formData = new FormData();
                formData.append("enctype","multipart/form-data");
                formData.append("imgBase64",encodeURIComponent(res));
                debugger;
                var index = layer.load(2,{
                    shade:[0.1,'#fff']
                });
                $.ajax({
                    data: formData,
                    dataType : "json",
                    type : "POST",
                    timeout : 10000,
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    url : baselocation + '/uploads/img/cropper/oss',
                    success : function(result) {
                        layer.closeAll();
                        if(result.code == 1){
                            layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {

                            });
                            var picImg = result.data.finalImageUrl;
                            $("#picImg").val(picImg);
                            $.ajax({
                                data :  $('#userInfoForm').serialize(),
                                dataType : "json",
                                type : "post",
                                url : baselocation + '/administrator/info/edit',
                                success : function(result) {
                                    layer.closeAll();
                                    if(result.code == 1){
                                        layer.msg("操作成功!", {
                                            shade : 0.3,
                                            time : 1500
                                        }, function() {
                                            location.reload();
                                        });
                                    }else{
                                        layer.alert(result.message);
                                    }
                                }
                            })
                        }else{
                            layer.alert(result.message);
                        }
                    }
                })
            }
        });
    });

    form.on('submit(userInfoSubmit)', function (data) {
        var index = layer.load(2,{
            shade:[0.1,'#fff']
        });
        $.ajax({
            data : $('#userInfoForm').serialize(),
            dataType : "json",
            type : "post",
            url : baselocation + '/administrator/info/edit',
            success : function(result) {
                layer.closeAll();
                if(result.code == 1){
                    layer.msg("操作成功!", {
                        shade : 0.3,
                        time : 1500
                    }, function() {
                        location.reload();
                    });
                }else{
                    layer.alert(result.message);
                }
            }
        })
        return false;
    });
    form.on('submit(pwdSubmit)', function (data) {
        var index = layer.load(2,{
            shade:[0.1,'#fff']
        });
        $.ajax({
            data : $('#pwdForm').serialize(),
            dataType : "json",
            type : "put",
            url : baselocation + '/administrator/info/edit/psw',
            success : function(result) {
                layer.closeAll();
                if(result.code == 1){
                    layer.msg("操作成功!", {
                        shade : 0.3,
                        time : 1500
                    }, function() {
                        $("input[name='confirmPwd']").val("");
                        $("input[name='newPassword']").val("");
                        $("input[name='nowPassword']").val("");
                    });
                }else{
                    layer.alert(result.message);
                }
            }
        })
        return false;
    });
});