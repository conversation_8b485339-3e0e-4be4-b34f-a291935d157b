package com.hongru.mapper.cost;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.cost.*;
import com.hongru.entity.sumitomo.Customer;
import com.hongru.entity.sumitomo.Product;
import com.hongru.entity.pims.ProductDesignData;
import com.hongru.support.page.PageInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品成本设计Mapper接口
 * 
 * <AUTHOR>
 */
public interface ProductCostDesignMapper extends BaseMapper<ProductCostDesign> {

    /**
     * 分页查询产品成本设计列表
     *
     * @param year 年度
     * @param customerName 客户简称
     * @param productCategory 产品分类
     * @param productCode 产品代码
     * @param pageInfo 分页信息
     * @return 产品成本设计列表
     */
    List<ProductCostDesign> listProductCostDesignPage(@Param("year") String year,
                                                     @Param("customerName") String customerName,
                                                     @Param("productCategory") String productCategory,
                                                     @Param("productCode") String productCode,
                                                     @Param("pageInfo") PageInfo pageInfo);

    /**
     * 查询产品成本设计总数
     *
     * @param year 年度
     * @param customerName 客户简称
     * @param productCategory 产品分类
     * @param productCode 产品代码
     * @return 总数
     */
    int listProductCostDesignPageCount(@Param("year") String year,
                                      @Param("customerName") String customerName,
                                      @Param("productCategory") String productCategory,
                                      @Param("productCode") String productCode);

    /**
     * 根据年度和原料区分查询原料项目单价
     *
     * @param year 年度
     * @param materialType 原料区分
     * @return 原料项目单价列表
     */
    List<RawMaterialItem> getRawMaterialItems(@Param("year") String year, @Param("materialType") String materialType);

    /**
     * 根据年度查询运费单价
     * 
     * @param year 年度
     * @return 运费单价列表
     */
    List<TransportUnitPrice> getTransportUnitPrices(@Param("year") String year);

    /**
     * 查询客户列表
     * 
     * @return 客户列表
     */
    List<Customer> getCustomers();

    /**
     * 根据客户简称和产品分类查询产品列表
     * 
     * @param customerName 客户简称
     * @param productCategory 产品分类
     * @return 产品列表
     */
    List<Product> getProducts(@Param("customerName") String customerName, @Param("productCategory") String productCategory);

    /**
     * 根据产品代码查询产品设计数据
     * 
     * @param productCode 产品代码
     * @return 产品设计数据
     */
    ProductDesignData getProductDesignData(@Param("productCode") String productCode);

    /**
     * 根据条件查询产品制造设计
     * 
     * @param year 年度
     * @param productCode 产品代码
     * @param productBarcode 产品条码
     * @param customerName 客户简称
     * @return 产品制造设计列表
     */
    List<ProductManufacturingDesign> getProductManufacturingDesigns(@Param("year") String year, 
            @Param("productCode") String productCode, @Param("productBarcode") String productBarcode, 
            @Param("customerName") String customerName);

    /**
     * 新增产品制造设计
     * 
     * @param productManufacturingDesign 产品制造设计
     * @return 影响行数
     */
    int addProductManufacturingDesign(ProductManufacturingDesign productManufacturingDesign);

    /**
     * 更新产品制造设计
     * 
     * @param productManufacturingDesign 产品制造设计
     * @return 影响行数
     */
    int updateProductManufacturingDesign(ProductManufacturingDesign productManufacturingDesign);

    /**
     * 新增产品成本
     * 
     * @param productCost 产品成本
     * @return 影响行数
     */
    int addProductCost(ProductCost productCost);

    /**
     * 更新产品成本
     * 
     * @param productCost 产品成本
     * @return 影响行数
     */
    int updateProductCost(ProductCost productCost);
}
