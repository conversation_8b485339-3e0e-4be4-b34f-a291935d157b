# 产品成本设计模块实施说明

## 概述
本文档说明了产品成本设计模块的完整实施过程，该模块已成功添加到成本参数模块下，提供完整的CRUD功能。

## 已完成的工作

### 1. 数据库实体类
已创建以下实体类：

#### 1.1 ProductCostDesign.java
- 路径：`crm/src/com/hongru/entity/cost/ProductCostDesign.java`
- 功能：产品成本设计表实体类
- 字段：包含20+个字段，如年度、成本键、客户信息、产品信息、材料重量等

#### 1.2 ProductManufacturingDesign.java
- 路径：`crm/src/com/hongru/entity/cost/ProductManufacturingDesign.java`
- 功能：产品制造设计表实体类
- 字段：包含制造相关字段，如条码、材料类型、油漆规格等

#### 1.3 ProductCost.java
- 路径：`crm/src/com/hongru/entity/cost/ProductCost.java`
- 功能：产品成本表实体类
- 字段：包含成本计算相关字段

#### 1.4 辅助实体类
- `Customer.java`：客户信息
- `Product.java`：产品信息
- `ProductDesignData.java`：产品设计数据
- `RawMaterialItem.java`：原料项目单价
- `TransportUnitPrice.java`：运费单价
- `ProductManufacturingDesignReference.java`：产品制造设计参考数据

### 2. 服务层
#### 2.1 服务接口
- 路径：`crmservice/src/com/hongru/service/cost/IProductCostDesignService.java`
- 功能：定义产品成本设计相关的业务方法

#### 2.2 服务实现
- 路径：`crmservice/src/com/hongru/service/impl/cost/ProductCostDesignServiceImpl.java`
- 功能：实现业务逻辑，包括分页查询、CRUD操作、下拉数据查询等

#### 2.3 数据访问层
- Mapper接口：`crmservice/src/com/hongru/mapper/cost/ProductCostDesignMapper.java`
- XML映射：`crmservice/src/com/hongru/mapper/xml/cost/ProductCostDesignMapper.xml`

### 3. 控制器层
- 路径：`prjback/src/com/hongru/controller/cost/ProductCostDesignController.java`
- 功能：处理HTTP请求，包括页面跳转和AJAX接口
- 权限控制：已添加Shiro权限注解

### 4. 前端页面
#### 4.1 列表页面
- 路径：`prjback/WebContent/WEB-INF/views/modules/cost/productCostDesign_list.jsp`
- 功能：显示产品成本设计列表，支持查询、新增、编辑、删除操作

#### 4.2 新增页面
- 路径：`prjback/WebContent/WEB-INF/views/modules/cost/productCostDesign_add.jsp`
- 功能：新增产品成本设计，包含复杂的下拉联动和参考功能

#### 4.3 编辑页面
- 路径：`prjback/WebContent/WEB-INF/views/modules/cost/productCostDesign_edit.jsp`
- 功能：编辑现有产品成本设计记录

### 5. 菜单配置
#### 5.1 SQL脚本
- 路径：`docs/sql/add_product_cost_design_menu.sql`
- 功能：添加产品成本设计模块的菜单配置
- 包含：主菜单和操作权限菜单（查看、新增、编辑、删除、导出）

## 功能特性

### 1. 查询功能
- 支持按年度、客户简称、产品分类等条件查询
- 分页显示查询结果
- 支持排序功能

### 2. 新增功能
- 复杂的表单验证
- 下拉数据源联动（客户→产品分类→产品代码）
- 原料品目根据年度和材料类型动态加载
- 参考功能：可从产品制造设计表获取参考数据
- 同时保存到三个表：产品成本设计表、产品制造设计表、产品成本表

### 3. 编辑功能
- 预填充现有数据
- 支持所有字段的修改
- 保持数据一致性

### 4. 删除功能
- 支持单条记录删除
- 数据完整性检查

### 5. 下拉数据源
- 客户列表（来源：sumitomo数据库）
- 产品列表（根据客户和产品分类筛选）
- 原料项目单价（根据年度和材料类型筛选）
- 运费单价（根据年度筛选）

## 权限配置

### 权限标识
- `cost:productCostDesign:view`：查看权限
- `cost:productCostDesign:add`：新增权限
- `cost:productCostDesign:edit`：编辑权限
- `cost:productCostDesign:delete`：删除权限
- `cost:productCostDesign:export`：导出权限

### 控制器权限注解
所有控制器方法都已添加相应的`@RequiresPermissions`注解。

## 部署说明

### 1. 数据库配置
执行SQL脚本：`docs/sql/add_product_cost_design_menu.sql`

注意事项：
- 需要根据实际的成本参数模块menu_id调整parent_id
- 需要根据实际的管理员角色ID调整role_id

### 2. 编译部署
1. 重新编译项目
2. 部署到应用服务器
3. 重启应用

### 3. 权限分配
通过系统管理界面为相关角色分配产品成本设计模块的权限。

## 技术栈
- **后端框架**：Spring MVC + MyBatis Plus
- **前端框架**：Layui + JSP
- **权限控制**：Apache Shiro
- **数据库**：SQL Server（多数据库：CostPrice、sumitomo、PIMS、sample）

## 注意事项
1. 该模块涉及多个数据库的数据查询，需要确保数据库连接配置正确
2. 下拉数据源的加载依赖于年度参数，使用时需要先选择年度
3. 参考功能需要产品制造设计表中有对应的数据
4. 权限控制严格，需要为用户分配相应权限才能访问

## 后续扩展
1. 可以添加数据导出功能
2. 可以添加批量操作功能
3. 可以添加数据审核流程
4. 可以添加成本计算功能

---
**实施完成日期**：2025-07-29
**实施人员**：系统开发团队
