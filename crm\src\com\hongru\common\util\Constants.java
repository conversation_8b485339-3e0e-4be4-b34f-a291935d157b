package com.hongru.common.util;


import com.fasterxml.jackson.databind.node.JsonNodeFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;

public class Constants {
	public static JsonNodeFactory factory = new JsonNodeFactory(false);
	
	/** The name of the ResourceBundle used in this application */
	public static String IMAGE_UPLOAD_PATH;//保存上传文件的目录，相对于Web应用程序的根路径
	public static String IMAGE_OSS_PATH_READ;
	public static String FILE_PATH;
    public static final String ENDPOINT = "http://oss-cn-hangzhou.aliyuncs.com";

	public static String PRINTER_NAME;
	public static String TAGPRINTER_COMM;
	public static String TAGPRINTER_COMM2;
	public static String WEIGH_COMM;
	public static String TRACK_COMM;
	public static String VERSION;
	public static   int    STOP_TIMER=1;//(0:否(开启)   1:是(关闭))

	public static String OPEN_EMAIL;
	public static String SMTP_HOST;
	public static String SMTP_PORT;
	public static String FROM_EMAIL;
	public static String EMAIL_PWD;
//	public static String EMAIL_ADDRESS;
	public static String EMAILADDRESS_JIHUA_1;
	public static String EMAILADDRESS_JIHUA_2;
	public static String EMAILADDRESS_JIHUA_3;
	public static String EMAILADDRESS_JIHUA_4;

    static{
		InputStream inStream=null;
		InputStream baseInStream=null;
		InputStream vInStream=null;
		InputStream emailInStream=null;
    	try {
    		Properties p = new Properties();
        	inStream = Constants.class.getClassLoader().getResourceAsStream("/properties/oss.properties");
			p.load(inStream);
			IMAGE_UPLOAD_PATH = p.getProperty("IMAGE_UPLOAD_PATH");
			IMAGE_OSS_PATH_READ = p.getProperty("IMAGE_OSS_PATH_READ");
			FILE_PATH = p.getProperty("FILE_PATH");
			inStream.close();

			Properties baseP = new Properties();
			baseInStream = Constants.class.getClassLoader().getResourceAsStream("/properties/baseConfig.properties");
			baseP.load(new InputStreamReader(baseInStream,"UTF-8"));
			PRINTER_NAME = baseP.getProperty("PRINTER_NAME");
			TAGPRINTER_COMM = baseP.getProperty("TAGPRINTER_COMM");
			TAGPRINTER_COMM2 = baseP.getProperty("TAGPRINTER_COMM2");
			WEIGH_COMM = baseP.getProperty("WEIGH_COMM");
			TRACK_COMM = baseP.getProperty("TRACK_COMM");
			if(!StringUtil.isStringEmpty(baseP.getProperty("STOP_TIMER"))) {
				STOP_TIMER =Integer.parseInt(baseP.getProperty("STOP_TIMER"));
			}
			baseInStream.close();

			Properties baseV = new Properties();
			vInStream = Constants.class.getClassLoader().getResourceAsStream("/properties/version.properties");
			baseV.load(new InputStreamReader(vInStream,"UTF-8"));
			VERSION = baseV.getProperty("VERSION");
			vInStream.close();


			Properties baseE = new Properties();
			emailInStream = Constants.class.getClassLoader().getResourceAsStream("/properties/email.properties");
			baseE.load(new InputStreamReader(emailInStream,"UTF-8"));
			OPEN_EMAIL = baseE.getProperty("openEmail");
			SMTP_HOST = baseE.getProperty("smtpHost");
			SMTP_PORT = baseE.getProperty("smtpPort");
			FROM_EMAIL = baseE.getProperty("fromEmail");
			EMAIL_PWD = baseE.getProperty("emailPwd");
//			EMAIL_ADDRESS = baseE.getProperty("emailAddress");
			EMAILADDRESS_JIHUA_1 = baseE.getProperty("emailAddress_jihua_1");
			EMAILADDRESS_JIHUA_2 = baseE.getProperty("emailAddress_jihua_2");
			EMAILADDRESS_JIHUA_3 = baseE.getProperty("emailAddress_jihua_3");
			EMAILADDRESS_JIHUA_4 = baseE.getProperty("emailAddress_jihua_4");
			emailInStream.close();

		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			try {
				if(inStream !=null){
					inStream.close();
				}
				if(baseInStream !=null){
					baseInStream.close();
				}
				if(vInStream !=null){
					vInStream.close();
				}
				if(emailInStream !=null){
					emailInStream.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
    }
}
