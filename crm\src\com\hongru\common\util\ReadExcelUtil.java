package com.hongru.common.util;

import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.LinkedList;
import java.util.List;

public class ReadExcelUtil {
    private static Logger logger = Logger.getLogger("xmlInfo");
    private HSSFWorkbook wb = null;
    private HSSFSheet sheet = null;
    private HSSFRow row = null;
    private int sheetNum = 0;
    private int rowNum = 0;
    private FileInputStream fis = null;
    private File file = null;

    public static final  int sheet1 = 0;
    public static final  int sheet2 = 1;
    public static final  int sheet3 = 2;
    public static final  int sheet4 = 3;

    public static final String TEMPORARY_FILE_PATH = "C:\\upload";

    public ReadExcelUtil() {
    }

    public ReadExcelUtil(File file) {
        this.file = file;
    }

    public void setRowNum(int rowNum) {
        this.rowNum = rowNum;
    }

    public void setSheetNum(int sheetNum) {
        this.sheetNum = sheetNum;
    }

    public void setFile(File file) {
        this.file = file;
    }

    /**
     * 读取excel文件获得HSSFWorkbook对象
     */
    public void open() throws IOException {
        fis = new FileInputStream(file);
        wb = new HSSFWorkbook(new POIFSFileSystem(fis));
        fis.close();
    }

    /**
     * 返回sheet表数目
     */
    public int getSheetCount() {
        int sheetCount = -1;
        sheetCount = wb.getNumberOfSheets();
        return sheetCount;
    }

    /**
     * sheetNum下的记录行数
     */
    public int getRowCount() {
        if (wb == null)
            System.out.println("=============>WorkBook为空");
        HSSFSheet sheet = wb.getSheetAt(this.sheetNum);
        int rowCount = -1;
        rowCount = sheet.getLastRowNum();
        return rowCount;
    }

    /**
     * 读取指定sheetNum的rowCount
     */
    public int getRowCount(int sheetNum) {
        HSSFSheet sheet = wb.getSheetAt(sheetNum);
        int rowCount = -1;
        rowCount = sheet.getLastRowNum();
        return rowCount;
    }


    /**
     * 得到指定行的内容
     */
    public String[] readExcelLine(int lineNum) {
        return readExcelLine(this.sheetNum, lineNum);
    }


    /**
     * 指定工作表和行数的内容
     */
    public String[] readExcelLine(int sheetNum, int lineNum) {
        if (sheetNum < 0 || lineNum < 0)
            return null;
        String[] strExcelLine = null;
        try {
            sheet = wb.getSheetAt(sheetNum);
            row = sheet.getRow(lineNum);

            int cellCount = row.getLastCellNum();
            strExcelLine = new String[cellCount + 1];
            for (int i = 0; i <= cellCount; i++) {
                strExcelLine[i] = readStringExcelCell(lineNum, i);
            }
        } catch (Exception e) {
            logger.error(e.toString());
        }
        return strExcelLine;
    }


    /**
     * 读取指定列的内容
     */
    public String readStringExcelCell(int cellNum) {
        return readStringExcelCell(this.rowNum, cellNum);
    }


    /**
     * 指定行和列编号的内容
     */
    public String readStringExcelCell(int rowNum, int cellNum) {
        return readStringExcelCell(this.sheetNum, rowNum, cellNum);
    }


    /**
     * 指定工作表、行、列下的内容
     */
    public String readStringExcelCell(int sheetNum, int rowNum, int cellNum) {
        if (sheetNum < 0 || rowNum < 0)
            return "";
        String strExcelCell = "";
        try {
            sheet = wb.getSheetAt(sheetNum);
            row = sheet.getRow(rowNum);

            if (row.getCell((short) cellNum) != null) {
                switch (row.getCell((short) cellNum).getCellTypeEnum()) {
                    case NUMERIC: {
                        strExcelCell = String.valueOf(row.getCell((short) cellNum)
                                .getNumericCellValue());
                    }
                    case STRING:
                        strExcelCell = row.getCell((short) cellNum).getStringCellValue();
                        break;
                    case FORMULA:
                        strExcelCell = "FORMULA ";
                        break;
                    case BLANK:
                        strExcelCell = "";
                        break;
                    case BOOLEAN:
                        break;
                    default:
                        strExcelCell = "";
                        break;
                }
            }
        } catch (Exception e) {
            logger.error(e.toString());
        }
        return strExcelCell;
    }


    /**
     * 对外提供读取excel 的方法
     */
    public static List<List<Object>> readExcel(File file) throws IOException {
        String fileName = file.getName();
        String extension = fileName.lastIndexOf(".") == -1 ? "" : fileName
                .substring(fileName.lastIndexOf(".") + 1);
        if ("xls".equals(extension)) {
            return read2003Excel(file);
        } else if ("xlsx".equals(extension)) {
            return read2007Excel(file);
        } else {
            throw new IOException("不支持的文件类型");
        }
    }

    /**
     * 读取 office 2003 excel
     *
     * @throws IOException
     * @throws FileNotFoundException
     */
    private static List<List<Object>> read2003Excel(File file)
            throws IOException {
        List<List<Object>> list = new LinkedList<List<Object>>();
        HSSFWorkbook hwb = new HSSFWorkbook(new FileInputStream(file));
        //后面使用它来执行计算公式 核心代码
        FormulaEvaluator evaluator = hwb.getCreationHelper().createFormulaEvaluator();
        HSSFSheet sheet = hwb.getSheetAt(0);
        Object value = null;
        HSSFRow row = null;
        HSSFCell cell = null;
        System.out.println("读取office 2003 excel内容如下：");
        for (int i = sheet.getFirstRowNum(); i <= sheet
                .getPhysicalNumberOfRows(); i++) {
            row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            List<Object> linked = new LinkedList<Object>();
            for (int j = row.getFirstCellNum(); j <= row.getLastCellNum(); j++) {
                cell = row.getCell(j);
                if (cell == null) {
                    continue;
                }
                if (j >= 25) {
                    System.out.println(cell.getCellTypeEnum());
                }
                DecimalFormat df = new DecimalFormat("0");// 格式化 number String
                // 字符
                SimpleDateFormat sdf = new SimpleDateFormat(
                        "yyyy-MM-dd HH:mm:ss");// 格式化日期字符串
                DecimalFormat nf = new DecimalFormat("0.00");// 格式化数字
                switch (cell.getCellTypeEnum()) {
                    case STRING:
                        // System.out.println(i + "行" + j + " 列 is String type");
                        value = cell.getStringCellValue();
                        System.out.print("  " + value + "  ");
                        break;
                    case NUMERIC:
                        // System.out.println(i + "行" + j
                        // + " 列 is Number type ; DateFormt:"
                        // + cell.getCellStyle().getDataFormatString());
                        if ("@".equals(cell.getCellStyle().getDataFormatString())) {
                            value = df.format(cell.getNumericCellValue());

                        } else if ("General".equals(cell.getCellStyle()
                                .getDataFormatString())) {
                            value = nf.format(cell.getNumericCellValue());
                        } else {
                            value = sdf.format(HSSFDateUtil.getJavaDate(cell
                                    .getNumericCellValue()));
                        }
//                        System.out.print("  " + value + "  ");
                        break;
                    case BOOLEAN:
                        // System.out.println(i + "行" + j + " 列 is Boolean type");
                        value = cell.getBooleanCellValue();
                        System.out.print("  " + value + "  ");
                        break;
                    case BLANK:
                        // System.out.println(i + "行" + j + " 列 is Blank type");
                        value = "";
                        System.out.print("  " + value + "  ");
                        break;
                    case FORMULA:
                        //公式
                        // System.out.println(cell.getCellFormula());
                        //获取公式，可以理解为已String类型获取cell的值输出
                        String cellFormula = cell.getCellFormula();
                        System.out.println(cellFormula);
                        //执行公式，此处cell的值就是公式  核心代码
                        CellValue evaluate = evaluator.evaluate(cell);
                        System.out.println(evaluate.formatAsString());
                        value=evaluate.formatAsString();
                        break;
                    default:
                        // System.out.println(i + "行" + j + " 列 is default type");
                        value = cell.toString();
                        System.out.print("  " + value + "  ");
                }
                if (value == null || "".equals(value)) {
//                    value = "无";
                    value = "";
                }
                linked.add(value);

            }
            System.out.println("");
            list.add(linked);
        }

        return list;
    }

    /**
     * 读取Office 2007 excel
     */

    private static List<List<Object>> read2007Excel(File file)
            throws IOException {

        List<List<Object>> list = new LinkedList<List<Object>>();
        // String path = System.getProperty("user.dir") +
        // System.getProperty("file.separator")+"dd.xlsx";
        // System.out.println("路径："+path);
        // 构造 XSSFWorkbook 对象，strPath 传入文件路径
        XSSFWorkbook xwb = new XSSFWorkbook(new FileInputStream(file));
        //后面使用它来执行计算公式 核心代码
        FormulaEvaluator evaluator = xwb.getCreationHelper().createFormulaEvaluator();
        // 读取第一章表格内容
        XSSFSheet sheet = xwb.getSheetAt(0);
        Object value = null;
        XSSFRow row = null;
        XSSFCell cell = null;
        System.out.println("读取office 2007 excel内容如下：");
        for (int i = sheet.getFirstRowNum(); i <= sheet
                .getPhysicalNumberOfRows(); i++) {
            row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            List<Object> linked = new LinkedList<Object>();
            for (int j = row.getFirstCellNum(); j <= row.getLastCellNum(); j++) {
                cell = row.getCell(j);
                System.out.println("i:" + i + "j:" + j);
                if (cell == null) {
                    cell = row.createCell(1);
                    cell.setCellValue("");
                }
                DecimalFormat df = new DecimalFormat("0");// 格式化 number String
                // 字符
//				SimpleDateFormat sdf = new SimpleDateFormat(
//						"yyyy-MM-dd HH:mm:ss");// 格式化日期字符串
                DecimalFormat nf = new DecimalFormat("0.00");// 格式化数字

                switch (cell.getCellTypeEnum()) {
                    case STRING:   //字符串型
                        // System.out.println(i + "行" + j + " 列 is String type");
                        value = cell.getStringCellValue();
//						System.out.print("  " + value + "  ");
                        break;
                    case NUMERIC:   //数值型
                        // System.out.println(i + "行" + j
                        // + " 列 is Number type ; DateFormt:"
                        // + cell.getCellStyle().getDataFormatString());
                        if ("@".equals(cell.getCellStyle().getDataFormatString())) {
                            value = df.format(cell.getNumericCellValue());

                        } else if ("General".equals(cell.getCellStyle()
                                .getDataFormatString())) {
//							System.out.println(cell.getNumericCellValue());
                            value = nf.format(cell.getNumericCellValue());
                        } else {
//							value = sdf.format(HSSFDateUtil.getJavaDate(cell
//									.getNumericCellValue()));
//							System.out.println(cell.getNumericCellValue());
                            value = nf.format(cell.getNumericCellValue());
                        }
//						System.out.print("  " + value + "  ");
                        break;
                    case BOOLEAN:  //布尔值
                        // System.out.println(i + "行" + j + " 列 is Boolean type");
                        value = cell.getBooleanCellValue();
//						System.out.print("  " + value + "  ");
                        break;
                    case BLANK:  //空值
                        // System.out.println(i + "行" + j + " 列 is Blank type");
//                        value = "无";
                        value = "";
                        // System.out.println(value);
                        break;
                    case FORMULA:
                        //公式
                        // System.out.println(cell.getCellFormula());
                        //获取公式，可以理解为已String类型获取cell的值输出
                        String cellFormula = cell.getCellFormula();
                        System.out.println(cellFormula);
                        //执行公式，此处cell的值就是公式  核心代码
                        CellValue evaluate = evaluator.evaluate(cell);
                        System.out.println(evaluate.formatAsString());
                        value=evaluate.formatAsString();
                        break;
                    default:
                        //System.out.println(i + "行" + j + " 列 is default type");
                        value = cell.toString();
//						System.out.print("  " + value + "  ");
                }
                if (value == null || "".equals(value)) {
//                    value = "无";
                    value = "";
                }
                linked.add(value);
            }
//			System.out.println("");
            list.add(linked);
        }
        return list;
    }

    //0823：新增 读取某个sheet的内容
    /**
     * 对外提供读取excel中某个sheet内容 的方法
     */
    public static List<List<Object>> readExcelSheet(File file,int sheetNum) throws IOException {
        String fileName = file.getName();
        String extension = fileName.lastIndexOf(".") == -1 ? "" : fileName
                .substring(fileName.lastIndexOf(".") + 1);
        if ("xls".equals(extension)) {
            return read2003ExcelSheet(file,sheetNum);
        } else if ("xlsx".equals(extension)) {
            return read2007ExcelSheet(file,sheetNum);
        } else {
            throw new IOException("不支持的文件类型");
        }
    }

    /**
     * 读取 office 2003 excel某个sheet内容
     *
     * @throws IOException
     * @throws FileNotFoundException
     */
    private static List<List<Object>> read2003ExcelSheet(File file,int sheetNum)
            throws IOException {
        List<List<Object>> list = new LinkedList<List<Object>>();
        HSSFWorkbook hwb = new HSSFWorkbook(new FileInputStream(file));
        //后面使用它来执行计算公式 核心代码
        FormulaEvaluator evaluator = hwb.getCreationHelper().createFormulaEvaluator();
        HSSFSheet sheet = hwb.getSheetAt(sheetNum);
        Object value = null;
        HSSFRow row = null;
        HSSFCell cell = null;
        System.out.println("读取office 2003 excel内容如下：");
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            List<Object> linked = new LinkedList<Object>();
            for (int j = row.getFirstCellNum(); j <= row.getLastCellNum(); j++) {
                cell = row.getCell(j);
                if (cell == null) {
                    continue;
                }
                if (j >= 25) {
                    System.out.println(cell.getCellTypeEnum());
                }
//                DecimalFormat df = new DecimalFormat("0");// 格式化 number String
                // 字符
//                SimpleDateFormat sdf = new SimpleDateFormat(
//                        "yyyy-MM-dd HH:mm:ss");// 格式化日期字符串
//                DecimalFormat nf = new DecimalFormat("0.00");// 格式化数字
                switch (cell.getCellTypeEnum()) {
                    case STRING:
                        // System.out.println(i + "行" + j + " 列 is String type");
                        value = cell.getStringCellValue();
                        System.out.print("  " + value + "  ");
                        break;
                    case NUMERIC:
                        // System.out.println(i + "行" + j
                        // + " 列 is Number type ; DateFormt:"
                        // + cell.getCellStyle().getDataFormatString());
//                        if ("@".equals(cell.getCellStyle().getDataFormatString())) {
//                            value = df.format(cell.getNumericCellValue());
//
//                        } else if ("General".equals(cell.getCellStyle()
//                                .getDataFormatString())) {
//                            value = nf.format(cell.getNumericCellValue());
//                        } else {
//                            value = sdf.format(HSSFDateUtil.getJavaDate(cell
//                                    .getNumericCellValue()));
//                        }
//                        System.out.print("  " + value + "  ");
                        value = cell.getNumericCellValue();
                        break;
                    case BOOLEAN:
                        // System.out.println(i + "行" + j + " 列 is Boolean type");
                        value = cell.getBooleanCellValue();
                        System.out.print("  " + value + "  ");
                        break;
                    case BLANK:
                        // System.out.println(i + "行" + j + " 列 is Blank type");
                        value = "";
                        System.out.print("  " + value + "  ");
                        break;
                    case FORMULA:
                        try{
                            //公式
                            // System.out.println(cell.getCellFormula());
                            //获取公式，可以理解为已String类型获取cell的值输出
                            String cellFormula = cell.getCellFormula();
                            System.out.println(cellFormula);
                            //执行公式，此处cell的值就是公式  核心代码
                            CellValue evaluate = evaluator.evaluate(cell);
                            System.out.println(evaluate.formatAsString());
                            value=evaluate.formatAsString();
                        }catch(Exception e){
                            e.printStackTrace();
                        }
                        break;
                    default:
                        // System.out.println(i + "行" + j + " 列 is default type");
                        value = cell.toString();
                        System.out.print("  " + value + "  ");
                }
                if (value == null || "".equals(value)) {
//                    value = "无";
                    value = "";
                }
                linked.add(value);

            }
            System.out.println("");
            list.add(linked);
        }

        return list;
    }

    /**
     * 读取Office 2007 excel某个sheet内容
     */
    private static List<List<Object>> read2007ExcelSheet(File file,int sheetNum)
            throws IOException {

        List<List<Object>> list = new LinkedList<List<Object>>();
        // String path = System.getProperty("user.dir") +
        // System.getProperty("file.separator")+"dd.xlsx";
        // System.out.println("路径："+path);
        // 构造 XSSFWorkbook 对象，strPath 传入文件路径
        XSSFWorkbook xwb = new XSSFWorkbook(new FileInputStream(file));
        //后面使用它来执行计算公式 核心代码
        FormulaEvaluator evaluator = xwb.getCreationHelper().createFormulaEvaluator();
        // 读取第一章表格内容
        XSSFSheet sheet = xwb.getSheetAt(sheetNum);
        Object value = null;
        XSSFRow row = null;
        XSSFCell cell = null;
        System.out.println("读取office 2007 excel内容如下：");

        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            row = sheet.getRow(i);
            if (row == null || row.getFirstCellNum() < 0) {
                continue;
            }
            List<Object> linked = new LinkedList<Object>();
            for (int j = row.getFirstCellNum(); j <= row.getLastCellNum(); j++) {
                cell = row.getCell(j);
                System.out.println("i:" + i + "j:" + j);
                if (cell == null) {
                    cell = row.createCell(1);
                    cell.setCellValue("");
                }

                switch (cell.getCellTypeEnum()) {
                    case STRING:   //字符串型
                        // System.out.println(i + "行" + j + " 列 is String type");
                        value = cell.getStringCellValue();
//						System.out.print("  " + value + "  ");
                        break;
                    case NUMERIC:   //数值型
                        value = cell.getNumericCellValue();
                        break;
                    case BOOLEAN:  //布尔值
                        // System.out.println(i + "行" + j + " 列 is Boolean type");
                        value = cell.getBooleanCellValue();
//						System.out.print("  " + value + "  ");
                        break;
                    case BLANK:  //空值
                        // System.out.println(i + "行" + j + " 列 is Blank type");
//                        value = "无";
                        value = "";
                        // System.out.println(value);
                        break;
                    case FORMULA:
                        try{
                            //公式
                            // System.out.println(cell.getCellFormula());
                            //获取公式，可以理解为已String类型获取cell的值输出
                            String cellFormula = cell.getCellFormula();
                            System.out.println(cellFormula);
                            //执行公式，此处cell的值就是公式  核心代码
                            CellValue evaluate = evaluator.evaluate(cell);
                            System.out.println(evaluate.formatAsString());
                            value=evaluate.formatAsString();
                        }catch(Exception e){
                            e.printStackTrace();
                        }
                        break;
                    default:
                        //System.out.println(i + "行" + j + " 列 is default type");
                        value = cell.toString();
//						System.out.print("  " + value + "  ");
                }
                if (value == null || "".equals(value)) {
//                    value = "无";
                    value = "";
                }
                linked.add(value);
            }
//			System.out.println("");
            list.add(linked);
        }
        return list;
    }

    /**
     * 对外提供读取excel中某个sheet名
     */
    public static String readExcelSheetName(File file,int sheetNum) throws IOException {
        String fileName = file.getName();
        String extension = fileName.lastIndexOf(".") == -1 ? "" : fileName
                .substring(fileName.lastIndexOf(".") + 1);
        if ("xls".equals(extension)) {
            return read2003ExcelSheetName(file,sheetNum);
        } else if ("xlsx".equals(extension)) {
            return read2007ExcelSheetName(file,sheetNum);
        } else {
            throw new IOException("不支持的文件类型");
        }
    }

    /**
     * 读取 office 2003 excel某个sheet内容
     *
     * @throws IOException
     * @throws FileNotFoundException
     */
    private static String read2003ExcelSheetName(File file,int sheetNum)
            throws IOException {
        List<List<Object>> list = new LinkedList<List<Object>>();
        HSSFWorkbook hwb = new HSSFWorkbook(new FileInputStream(file));
        //后面使用它来执行计算公式 核心代码
        FormulaEvaluator evaluator = hwb.getCreationHelper().createFormulaEvaluator();
        HSSFSheet sheet = hwb.getSheetAt(sheetNum);
        return sheet.getSheetName();
    }

    /**
     * 读取Office 2007 excel某个sheet内容
     */
    private static String read2007ExcelSheetName(File file,int sheetNum)
            throws IOException {

        List<List<Object>> list = new LinkedList<List<Object>>();
        XSSFWorkbook xwb = new XSSFWorkbook(new FileInputStream(file));
        //后面使用它来执行计算公式 核心代码
        FormulaEvaluator evaluator = xwb.getCreationHelper().createFormulaEvaluator();
        // 读取第一章表格内容
        XSSFSheet sheet = xwb.getSheetAt(sheetNum);
        return sheet.getSheetName();
    }

    /**
     * 主函数用于测试
     */
    public static void main(String args[]) {
        File file = new File("D:\\test.xls");
        ReadExcelUtil readExcel = new ReadExcelUtil(file);
        try {
            readExcel.open();
        } catch (IOException e) {
            logger.error(e.toString());
        }
        readExcel.setSheetNum(0);
        int count = readExcel.getRowCount();
        for (int i = 0; i <= count; i++) {
            String[] rows = readExcel.readExcelLine(i);
            for (int j = 0; j < rows.length; j++) {
                System.out.print(rows[j] + " ");
            }
            System.out.print("\n");
        }
    }
}
