package com.hongru.pojo.dto;

import com.hongru.entity.cost.PaintCode;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class PaintCodeDTO {

    private PageInfo pageInfo;

    private List<PaintCode> paintCodeList;

    public PaintCodeDTO(PageInfo pageInfo, List<PaintCode> paintCodeList) {
        super();
        this.pageInfo = pageInfo;
        this.paintCodeList = paintCodeList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<PaintCode> getPaintCodeList() {
        return paintCodeList;
    }

    public void setPaintCodeList(List<PaintCode> paintCodeList) {
        this.paintCodeList = paintCodeList;
    }
}
