<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.AuxiliaryRecyclingArtificialMapper">
    <sql id="auxiliaryRecyclingArtificial_sql">
		au.[流水号] AS costId,au.[导入标识] AS importId,au.[年月] AS yearMonth,au.[年] AS year,au.[月] AS month,
		au.[区分] AS auxiliaryCode,au.[直接部门] AS directDepartmentCode,au.[辅助部门] AS auxiliaryDepartmentCode,
		au.[费用项目] AS expenseItem,au.[SMCH] AS sMCHNum,au.[SH] AS sHNum,au.[单价] AS unitPrice,
		au.[金额] AS amount,au.[创建人标识] AS creatorId,au.[创建人姓名] AS creatorName,au.[创建时间] AS createdTime
	</sql>

	<insert id="insertAuxiliaryRecyclingArtificial" parameterType="com.hongru.entity.cost.AuxiliaryRecyclingArtificial">
		INSERT INTO [CostPrice].[dbo].[预定辅助部门回收计算人工表]
		(
		[导入标识],
		[年月],
		[年],
		[月],
		[区分],
		[直接部门],
		[辅助部门],
		[费用项目],
		[SMCH],
		[SH],
		[单价],
		[金额],
		[创建人标识],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{auxiliaryRecyclingArtificial.importId},
		#{auxiliaryRecyclingArtificial.yearMonth},
		#{auxiliaryRecyclingArtificial.year},
		#{auxiliaryRecyclingArtificial.month},
		#{auxiliaryRecyclingArtificial.auxiliaryCode},
		#{auxiliaryRecyclingArtificial.directDepartmentCode},
		#{auxiliaryRecyclingArtificial.auxiliaryDepartmentCode},
		#{auxiliaryRecyclingArtificial.expenseItem},
		#{auxiliaryRecyclingArtificial.sMCHNum},
		#{auxiliaryRecyclingArtificial.sHNum},
		#{auxiliaryRecyclingArtificial.unitPrice},
		#{auxiliaryRecyclingArtificial.amount},
		#{auxiliaryRecyclingArtificial.creatorId},
		#{auxiliaryRecyclingArtificial.creatorName},
		#{auxiliaryRecyclingArtificial.createdTime}
		)
	</insert>
	
	<delete id="deleteAuxiliaryRecyclingArtificialByCostId">
		DELETE FROM [CostPrice].[dbo].[预定辅助部门回收计算人工表] WHERE [流水号] = #{costId}
	</delete>
	
	<select id="listAuxiliaryRecyclingArtificialByYearMonth" resultType="com.hongru.entity.cost.AuxiliaryRecyclingArtificial">
		SELECT
		<include refid="auxiliaryRecyclingArtificial_sql"/>
		FROM [CostPrice].[dbo].[预定辅助部门回收计算人工表] au
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND au.[年月] = #{yearMonth}
			</if>
		</where>
	</select>

	<select id="listAuxiliaryRecyclingArtificialByParam" resultType="com.hongru.entity.cost.AuxiliaryRecyclingArtificial">
		SELECT
		<include refid="auxiliaryRecyclingArtificial_sql"/>
		FROM [CostPrice].[dbo].[预定辅助部门回收计算人工表] au
		<where>
			<if test="timeMin != null and timeMin != ''">
				AND CONVERT ( DATETIME, au.[年月] + '-01 00:00:00' ) &gt;= #{timeMin}
			</if>
			<if test="timeMax != null and timeMax != ''">
				AND CONVERT ( DATETIME, au.[年月] + '-01 00:00:00' ) &lt;= #{timeMax}
			</if>
			<if test="yearMonth != null and yearMonth != ''">
				AND au.[年月] = #{yearMonth}
			</if>
			<if test="year != null">
				AND au.[年] = #{year}
			</if>
			<if test="month != null">
				AND au.[月] = #{month}
			</if>
			<if test="expenseItemStrArr != null and expenseItemStrArr != ''">
				AND au.[费用项目] IN
				<foreach collection="expenseItemStrArr" item="expenseItem" open="(" close=")" separator=",">
					#{expenseItem}
				</foreach>
			</if>
			<if test="auxiliaryCodeStrArr != null and auxiliaryCodeStrArr != ''">
				AND au.[区分] IN
				<foreach collection="auxiliaryCodeStrArr" item="auxiliaryCode" open="(" close=")" separator=",">
					#{auxiliaryCode}
				</foreach>
			</if>
		</where>
	</select>

	<select id="listAuxiliaryRecyclingArtificialPage" resultType="com.hongru.entity.cost.AuxiliaryRecyclingArtificial">
		SELECT
		<include refid="auxiliaryRecyclingArtificial_sql"/>
		FROM [CostPrice].[dbo].[预定辅助部门回收计算人工表] au
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND au.[年月] = #{yearMonth}
			</if>
			<if test="auxiliaryCode != null and auxiliaryCode != ''">
				AND au.[区分] = #{auxiliaryCode}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY au.[年月] DESC,au.[流水号] DESC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listAuxiliaryRecyclingArtificialPageCount" resultType="integer">
		SELECT
		count(1)
		FROM [CostPrice].[dbo].[预定辅助部门回收计算人工表] au
		<where>
			<if test="yearMonth != null and yearMonth != ''">
				AND au.[年月] = #{yearMonth}
			</if>
			<if test="auxiliaryCode != null and auxiliaryCode != ''">
				AND au.[区分] = #{auxiliaryCode}
			</if>	
		</where>
	</select>
	<select id="listMwAuxiliaryRecyclingArtificialByDateRange" resultType="com.hongru.entity.cost.AuxiliaryRecyclingArtificial">
		SELECT
		[直接部门] AS directDepartmentCode, [辅助部门] AS auxiliaryDepartmentCode, [费用项目] AS expenseItem, SUM([SH])/ #{monthInterval} AS sHNum
		FROM [CostPrice].[dbo].[预定辅助部门回收计算人工表] 
		<where>
			[SH]>0 AND 区分!=6
		    <if test="departArr != null and departArr != ''">
                AND [直接部门]  IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>
            <if test="supportDepartArr != null and supportDepartArr != ''">
                AND [辅助部门]  IN
                <foreach collection="supportDepartArr"  item="supportDepart" open="(" close=")" separator=",">
                    #{supportDepart}
                </foreach>
            </if>
            <if test="expenseItemArr != null and expenseItemArr != ''">
                AND [费用项目]  IN
                <foreach collection="expenseItemArr"  item="expenseItem" open="(" close=")" separator=",">
                    #{expenseItem}
                </foreach>
            </if>
            <if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
		</where>
		GROUP BY [直接部门],[辅助部门],[费用项目]
		ORDER BY [辅助部门],[直接部门]
	</select>
	<select id="listAuxiliaryRecyclingArtificialByDateRange" resultType="com.hongru.entity.cost.AuxiliaryRecyclingArtificial">
		SELECT
		[直接部门] AS directDepartmentCode, [辅助部门] AS auxiliaryDepartmentCode, [费用项目] AS expenseItem, SUM([SH])/ #{monthInterval} AS sHNum
		FROM [CostPrice].[dbo].[预定辅助部门回收计算人工表] 
		<where>
			[SH]>0
		    <if test="departArr != null and departArr != ''">
                AND [直接部门]  IN
                <foreach collection="departArr"  item="depart" open="(" close=")" separator=",">
                    #{depart}
                </foreach>
            </if>
            <if test="supportDepartArr != null and supportDepartArr != ''">
                AND [辅助部门]  IN
                <foreach collection="supportDepartArr"  item="supportDepart" open="(" close=")" separator=",">
                    #{supportDepart}
                </foreach>
            </if>
            <if test="expenseItemArr != null and expenseItemArr != ''">
                AND [费用项目]  IN
                <foreach collection="expenseItemArr"  item="expenseItem" open="(" close=")" separator=",">
                    #{expenseItem}
                </foreach>
            </if>
            <if test="timeStartStr != null and timeStartStr != ''">
                AND [年月] &gt;=#{timeStartStr}
            </if>
             <if test="timeEndStr != null and timeEndStr != ''">
                AND [年月] &lt;= #{timeEndStr}
            </if>
		</where>
		GROUP BY [直接部门],[辅助部门],[费用项目]
		ORDER BY [辅助部门],[直接部门]
	</select>
</mapper>