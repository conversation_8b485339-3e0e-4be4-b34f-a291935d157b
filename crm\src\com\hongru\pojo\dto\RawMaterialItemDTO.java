package com.hongru.pojo.dto;

import com.hongru.entity.cost.RawMaterialItem;
import com.hongru.entity.cost.ConductorUnitPriceDetail;
import com.hongru.entity.cost.PaintUnitPriceDetail;
import com.hongru.entity.cost.WireDiscUnitPriceDetail;
import com.hongru.support.page.PageInfo;

import java.util.List;

/**
 * 原料项目表DTO
 * 
 * <AUTHOR>
 */
public class RawMaterialItemDTO {

    private List<RawMaterialItem> rawMaterialItemList;
    private PageInfo pageInfo;

    // 原料项目基础信息
    private RawMaterialItem rawMaterialItem;

    // 导体单价明细
    private ConductorUnitPriceDetail conductorUnitPriceDetail;

    // 油漆单价明细
    private PaintUnitPriceDetail paintUnitPriceDetail;

    // 线盘单价明细
    private WireDiscUnitPriceDetail wireDiscUnitPriceDetail;

    public List<RawMaterialItem> getRawMaterialItemList() {
        return rawMaterialItemList;
    }

    public void setRawMaterialItemList(List<RawMaterialItem> rawMaterialItemList) {
        this.rawMaterialItemList = rawMaterialItemList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public RawMaterialItem getRawMaterialItem() {
        return rawMaterialItem;
    }

    public void setRawMaterialItem(RawMaterialItem rawMaterialItem) {
        this.rawMaterialItem = rawMaterialItem;
    }

    public ConductorUnitPriceDetail getConductorUnitPriceDetail() {
        return conductorUnitPriceDetail;
    }

    public void setConductorUnitPriceDetail(ConductorUnitPriceDetail conductorUnitPriceDetail) {
        this.conductorUnitPriceDetail = conductorUnitPriceDetail;
    }

    public PaintUnitPriceDetail getPaintUnitPriceDetail() {
        return paintUnitPriceDetail;
    }

    public void setPaintUnitPriceDetail(PaintUnitPriceDetail paintUnitPriceDetail) {
        this.paintUnitPriceDetail = paintUnitPriceDetail;
    }

    public WireDiscUnitPriceDetail getWireDiscUnitPriceDetail() {
        return wireDiscUnitPriceDetail;
    }

    public void setWireDiscUnitPriceDetail(WireDiscUnitPriceDetail wireDiscUnitPriceDetail) {
        this.wireDiscUnitPriceDetail = wireDiscUnitPriceDetail;
    }
}
