<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<%@ page import="com.hongru.common.util.Constants" %>
<!DOCTYPE HTML>
<html>
<head>
	<title>添加默认项目进度</title>
</head>
<body >
<div class="wrapper wrapper-content">
	<div class="row">
		<div class="col-sm-12">
			<div class="ibox float-e-margins">
				<div class="ibox-content" style="padding: 20px;" >
					<input type="hidden" value="${ctx}" id="baselocation" name="baselocation" />
					<div class="layui-collapse" lay-accordion>
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">称重测试</h2>
							<div class="layui-colla-content layui-show" style="padding:20px;">
								<form class="layui-form layui-form-pane" action="${ctx}/system/test/readWeigh" method="post" id="readWeighForm">
									<div class="layui-form-item layui-row">
										<div class="layui-inline layui-col-md12">
											<label class="layui-form-label">默认comm口</label>
											<div class="layui-input-block">
												<input type="text" class="layui-input" readonly value="<%=Constants.WEIGH_COMM%>"/>
											</div>
										</div>
										<div class="layui-inline layui-col-md5">
											<label class="layui-form-label">comm口</label>
											<div class="layui-input-block">
												<select name="portName">
													<option value=""></option>
													<c:forEach items="${portList}" var="portName">
														<option value="${portName}"><c:out value="${portName}"/></option>
													</c:forEach>
												</select>
											</div>
										</div>
										<div class="layui-inline layui-col-md5">
											<button type="button" class="layui-btn" lay-submit lay-filter="readWeigh">读取数据</button>
										</div>
										<div class="layui-inline layui-col-md12">
											<label class="layui-form-label">读取结果</label>
											<div class="layui-input-block">
												<textarea placeholder="" class="layui-textarea" id="weighReslut"></textarea>
											</div>
										</div>
									</div>
									<%--<table style="width:100%;">
										<tr>
											<td style="width: 50%;">
												<div class="layui-form-item">
													<label class="layui-form-label">comm口</label>
													<div class="layui-input-block">
														<select name="portName">
															<option value=""></option>
															<c:forEach items="${portList}" var="portName">
																<option value="${portName}"><c:out value="${portName}"/></option>
															</c:forEach>
														</select>
													</div>
												</div>
											</td>
											<td style="width: 50%;">
												<div class="layui-form-item" >
													<div class="layui-input-block" style="text-align: center;margin-left: 0px;" >
														<button type="button" class="layui-btn" lay-submit lay-filter="readWeigh">读取数据</button>
													</div>
												</div>
											</td>
										</tr>
										<tr>
											<td colspan="2">
												<div class="layui-form-item layui-form-text">
													<label class="layui-form-label">结果</label>
													<div class="layui-input-block">
														<textarea placeholder="" class="layui-textarea" id="weighReslut"></textarea>
													</div>
												</div>
											</td>
										</tr>
									</table>--%>
								</form>
							</div>
						</div>
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">标签打印测试</h2>
							<div class="layui-colla-content" style="padding:20px;">
								<form class="layui-form layui-form-pane" action="${ctx}/system/test/tagPrint" method="post" id="tagPrintForm">
									<div class="layui-form-item layui-row">
										<div class="layui-inline layui-col-md12">
											<label class="layui-form-label">默认comm口</label>
											<div class="layui-input-block">
												<input type="text" class="layui-input" readonly value="<%=Constants.TAGPRINTER_COMM%>"/>
											</div>
										</div>
										<div class="layui-inline layui-col-md5">
											<label class="layui-form-label">comm口</label>
											<div class="layui-input-block">
												<select name="portName">
													<option value=""></option>
													<c:forEach items="${portList}" var="portName">
														<option value="${portName}"><c:out value="${portName}"/></option>
													</c:forEach>
												</select>
											</div>
										</div>
										<div class="layui-inline layui-col-md5">
											<button type="button" class="layui-btn" lay-submit lay-filter="tagPrint">打印</button>
											<button type="button" class="layui-btn" lay-submit lay-filter="blackPrint">针头测试</button>
										</div>
										<div class="layui-inline layui-col-md12">
											<label class="layui-form-label">打印指令</label>
											<div class="layui-input-block">
												<textarea placeholder="" class="layui-textarea" id="printCommand" name="printCommand"></textarea>
											</div>
										</div>
									</div>
									<%--<table style="width:100%;">
										<tr>
											<td style="width: 50%;">
												<div class="layui-form-item">
													<label class="layui-form-label">comm口</label>
													<div class="layui-input-block">
														<select name="portName">
															<option value=""></option>
															<c:forEach items="${portList}" var="portName">
																<option value="${portName}"><c:out value="${portName}"/></option>
															</c:forEach>
														</select>
													</div>
												</div>
											</td>
											<td style="width: 50%;">
												<div class="layui-form-item" >
													<div class="layui-input-block" style="text-align: center;margin-left: 0px;" >
														<button type="button" class="layui-btn" lay-submit lay-filter="tagPrint">打印</button>
														<button type="button" class="layui-btn" lay-submit lay-filter="blackPrint">针头测试</button>
													</div>
												</div>
											</td>
										</tr>
										<tr>
											<td colspan="2">
												<div class="layui-form-item layui-form-text">
													<label class="layui-form-label">打印指令</label>
													<div class="layui-input-block">
														<textarea placeholder="" class="layui-textarea" id="printCommand"></textarea>
													</div>
												</div>
											</td>
										</tr>
									</table>--%>
								</form>
							</div>
						</div>
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">A4纸打印测试</h2>
							<div class="layui-colla-content" style="padding:20px;">
								<form class="layui-form layui-form-pane" action="${ctx}/system/test/paperPrint" method="post" id="paperPrintForm">
									<div class="layui-form-item layui-row">
										<div class="layui-inline layui-col-md12">
											<label class="layui-form-label">默认打印机</label>
											<div class="layui-input-block">
												<input type="text" class="layui-input" readonly value="<%=Constants.PRINTER_NAME%>"/>
											</div>
										</div>
										<div class="layui-inline layui-col-md5">
											<label class="layui-form-label">打印机</label>
											<div class="layui-input-block">
												<select name="printName">
													<option value=""></option>
													<c:forEach items="${printServices}" var="printService">
														<option value="${printService.name}"><c:out value="${printService.name}"/></option>
													</c:forEach>
												</select>
											</div>
										</div>
										<div class="layui-inline layui-col-md5">
											<button type="button" class="layui-btn" lay-submit lay-filter="paperPrint">打印</button>
										</div>
									</div>
									<%--<table style="width:100%;">
										<tr>
											<td style="width: 50%;">
												<div class="layui-form-item">
													<label class="layui-form-label">打印机</label>
													<div class="layui-input-block">
														<select name="printName">
															<option value=""></option>
															<c:forEach items="${printServices}" var="printService">
																<option value="${printService.name}"><c:out value="${printService.name}"/></option>
															</c:forEach>
														</select>
													</div>
												</div>
											</td>
											<td style="width: 50%;">
												<div class="layui-form-item" >
													<div class="layui-input-block" style="text-align: center;margin-left: 0px;" >
														<button type="button" class="layui-btn" lay-submit lay-filter="paperPrint">打印</button>
													</div>
												</div>
											</td>
										</tr>
									</table>--%>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<myfooter>
	<!-- 自定义js -->
	<script>
		layui.use(['form','element'], function(){
			var form = layui.form;
			var element = layui.element;
			//监听提交
			//测试读取称的数据
			form.on('submit(readWeigh)', function(data){
				var index = layer.load(2,{
					shade:[0.1,'#fff']
				});
				var actionUrl =$("#readWeighForm").attr('action');
				$.ajax({
					url : actionUrl,
					type : 'post',
					data : $('#readWeighForm').serialize(),
					success : function(result) {
						layer.closeAll();
						if(result.code == 1){
							var widget = result.data;
							if(widget !=null && widget !=''){
								$("#weighReslut").val($("#weighReslut").val()+widget);
							}
						}else{
							layer.alert(result.message);
						}
					}
				});
				return false;
			});

			//标签打印
			form.on('submit(tagPrint)', function(data){
				// var printCommand='';
				// printCommand+='{D1070,0795,1050|}';
				// printCommand+='{C|}';
				// printCommand+='{PV00;0108,0581,0041,0042,B,00,B=SO-HGZ(A)RX|}';
				// printCommand+='{PV01;0108,0680,0043,0042,B,00,B=0.75|}';
				// printCommand+='{PV02;0458,0680,0041,0042,B,00,B=G077|}';
				// printCommand+='{PV03;0628,0763,0034,0035,B,00,B=049|}';
				// printCommand+='{PV04;0628,0805,0034,0035,B,00,B=4063|}';
				// printCommand+='{XB00;0087,0870,A,3,04,0,0067,+0000000000,000,0,00=>5004123006000120140519001>61|}';
				// printCommand+='{PV07;0171,0968,0027,0028,B,00,B=0041230060001201405190011|}';
				// printCommand+='{XS;I,0001,0011C5201|}';
				// $("#printCommand").val(printCommand);
				tagPrint();
				return false;
			});
			//针头测试打印
			form.on('submit(blackPrint)', function(data){
				var printCommand='';
				printCommand+='{D1020,0835,1000|}';
				printCommand+='{C|}';
				printCommand+='{LC;0026,0451,0803,0451,0,9|}';
				printCommand+='{LC;0026,0459,0803,0459,0,9|}';
				/* printCommand+='{LC;0026,0467,0803,0467,0,9|}';
				printCommand+='{LC;0026,0475,0803,0475,0,9|}';
				printCommand+='{LC;0026,0483,0803,0483,0,9|}';
				printCommand+='{LC;0026,0491,0803,0491,0,9|}'; */
				printCommand+='{LC;0026,0499,0803,0499,0,9|}';
				printCommand+='{LC;0026,0507,0803,0507,0,9|}';
				/*printCommand+='{LC;0026,0515,0803,0515,0,9|}';
				printCommand+='{LC;0026,0523,0803,0523,0,9|}';
				printCommand+='{LC;0026,0531,0803,0531,0,9|}';
				printCommand+='{LC;0026,0539,0803,0539,0,9|}'; */
				printCommand+='{LC;0026,0547,0803,0547,0,9|}';
				printCommand+='{LC;0026,0555,0803,0555,0,9|}';
				/* printCommand+='{LC;0026,0563,0803,0563,0,9|}';
				printCommand+='{LC;0026,0571,0803,0571,0,9|}';
				printCommand+='{LC;0026,0579,0803,0579,0,9|}';
				printCommand+='{LC;0026,0587,0803,0587,0,9|}';
				printCommand+='{LC;0026,0595,0803,0595,0,9|}';
				printCommand+='{LC;0026,0603,0803,0603,0,9|}';
				printCommand+='{LC;0026,0611,0803,0611,0,9|}';
				printCommand+='{LC;0026,0619,0803,0619,0,9|}';
				printCommand+='{LC;0026,0627,0803,0627,0,9|}';
				printCommand+='{LC;0026,0635,0803,0635,0,9|}';
				printCommand+='{LC;0026,0643,0803,0643,0,9|}';
				printCommand+='{LC;0026,0651,0803,0651,0,9|}';
				printCommand+='{LC;0026,0659,0803,0659,0,9|}';
				printCommand+='{LC;0026,0667,0803,0667,0,9|}';
				printCommand+='{LC;0026,0675,0803,0675,0,9|}';
				printCommand+='{LC;0026,0683,0803,0683,0,9|}';
				printCommand+='{LC;0026,0691,0803,0691,0,9|}';
				printCommand+='{LC;0026,0699,0803,0699,0,9|}';
				printCommand+='{LC;0026,0707,0803,0707,0,9|}';
				printCommand+='{LC;0026,0715,0803,0715,0,9|}';
				printCommand+='{LC;0026,0723,0803,0723,0,9|}';
				printCommand+='{LC;0026,0731,0803,0731,0,9|}';
				printCommand+='{LC;0026,0739,0803,0739,0,9|}';
				printCommand+='{LC;0026,0747,0803,0747,0,9|}';
				printCommand+='{LC;0026,0755,0803,0755,0,9|}';
				printCommand+='{LC;0026,0763,0803,0763,0,9|}';
				printCommand+='{LC;0026,0771,0803,0771,0,9|}';
				printCommand+='{LC;0026,0779,0803,0779,0,9|}';
				printCommand+='{LC;0026,0787,0803,0787,0,9|}';
				printCommand+='{LC;0026,0795,0803,0795,0,9|}';
				printCommand+='{LC;0026,0803,0803,0803,0,9|}';
				printCommand+='{LC;0026,0811,0803,0811,0,9|}';
				printCommand+='{LC;0026,0819,0803,0819,0,9|}';
				printCommand+='{LC;0026,0827,0803,0827,0,9|}';
				printCommand+='{LC;0026,0835,0803,0835,0,9|}';
				printCommand+='{LC;0026,0843,0803,0843,0,9|}';
				printCommand+='{LC;0026,0851,0803,0851,0,9|}';
				printCommand+='{LC;0026,0859,0803,0859,0,9|}';
				printCommand+='{LC;0026,0867,0803,0867,0,9|}';
				printCommand+='{LC;0026,0875,0803,0875,0,9|}';
				printCommand+='{LC;0026,0883,0803,0883,0,9|}';
				printCommand+='{LC;0026,0891,0803,0891,0,9|}';
				printCommand+='{LC;0026,0899,0803,0899,0,9|}';
				printCommand+='{LC;0026,0907,0803,0907,0,5|}'; */
				printCommand+='{XS;I,0001,0011C5201|}';
				$("#printCommand").val(printCommand);
				tagPrint();
				return false;
			});

			function tagPrint(){
				var index = layer.load(2,{
					shade:[0.1,'#fff']
				});
				var actionUrl =$("#tagPrintForm").attr('action');
				$.ajax({
					url : actionUrl,
					type : 'post',
					data : $('#tagPrintForm').serialize(),
					success : function(result) {
						layer.closeAll();
						if(result.code == 1){
							var printReslut = result.data;
							if(printReslut !=null && printReslut !=''){
								$("#printReslut").val(printReslut);
							}
						}else{
							layer.alert(result.message);
						}
					}
				});
			}

			//A4打印
			form.on('submit(paperPrint)', function(data){
				var index = layer.load(2,{
					shade:[0.1,'#fff']
				});
				var actionUrl =$("#paperPrintForm").attr('action');
				$.ajax({
					url : actionUrl,
					type : 'post',
					data : $('#paperPrintForm').serialize(),
					success : function(result) {
						layer.closeAll();
						if(result.code == 1){
						}else{
							layer.alert(result.message);
						}
					}
				});
				return false;
			});
		});
	</script>
</myfooter>
</body>
</html>
