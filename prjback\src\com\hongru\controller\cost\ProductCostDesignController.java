package com.hongru.controller.cost;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtils;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.cost.*;
import com.hongru.entity.sumitomo.Customer;
import com.hongru.entity.sumitomo.Product;
import com.hongru.entity.pims.ProductDesignData;
import com.hongru.service.cost.IProductCostDesignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品成本设计控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/productCostDesign")
public class ProductCostDesignController extends BaseController {

    @Autowired
    private IProductCostDesignService productCostDesignService;

    /**
     * 产品成本设计列表页面
     *
     * @param model
     * @return
     */
    @RequiresPermissions("cost:productCostDesign:view")
    @GetMapping("/view")
    public String list(Model model) {
        return "/modules/cost/productCostDesign_list";
    }

    /**
     * 分页查询产品成本设计列表
     *
     * @param params 查询参数
     * @return
     */
    @RequiresPermissions("cost:productCostDesign:view")
    @PostMapping("/list")
    @ResponseBody
    public Object list(@RequestParam Map<String, Object> params) {
        try {
            HrPageResult<ProductCostDesign> result = productCostDesignService.listProductCostDesign(params);
            return new HrResult(CommonReturnCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error("查询产品成本设计列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 新增产品成本设计页面
     *
     * @param model
     * @return
     */
    @RequiresPermissions("cost:productCostDesign:add")
    @GetMapping("/add/view")
    public String addView(Model model) {
        return "/modules/cost/productCostDesign_add";
    }

    /**
     * 编辑产品成本设计页面
     *
     * @param model
     * @param serialNumber 流水号
     * @return
     */
    @RequiresPermissions("cost:productCostDesign:edit")
    @GetMapping("/edit/view")
    public String editView(Model model, Integer serialNumber) {
        ProductCostDesign productCostDesign = productCostDesignService.getProductCostDesignById(serialNumber);
        model.addAttribute("productCostDesign", productCostDesign);
        return "/modules/cost/productCostDesign_edit";
    }

    /**
     * 新增产品成本设计
     *
     * @param productCostDesign 产品成本设计
     * @return
     */
    @RequiresPermissions("cost:productCostDesign:add")
    @PostMapping("/add")
    @ResponseBody
    public Object add(ProductCostDesign productCostDesign) {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 设置创建信息
            productCostDesign.setCreatorName(authorizingUser.getUserName());
            productCostDesign.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            // 设置成本键为产品条码
            productCostDesign.setCostKey(productCostDesign.getProductBarcode());

            // 新增产品成本设计
            int result1 = productCostDesignService.addProductCostDesign(productCostDesign);

            // 新增产品制造设计
            ProductManufacturingDesign manufacturingDesign = new ProductManufacturingDesign();
            copyToManufacturingDesign(productCostDesign, manufacturingDesign);
            manufacturingDesign.setCreatorName(authorizingUser.getUserName());
            manufacturingDesign.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            int result2 = productCostDesignService.addProductManufacturingDesign(manufacturingDesign);

            // 新增产品成本
            ProductCost productCost = new ProductCost();
            copyToProductCost(productCostDesign, productCost);
            productCost.setCostKey(productCostDesign.getProductBarcode());
            productCost.setSize(productCostDesign.getProductSize());
            productCost.setProductCategory(productCostDesign.getCalculationType());
            productCost.setCalculationType(productCostDesign.getProductCategory());
            productCost.setCostCalculationStatus("0"); // 0：未计算
            productCost.setCreatorName(authorizingUser.getUserName());
            productCost.setCreatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            int result3 = productCostDesignService.addProductCost(productCost);

            if (result1 > 0 && result2 > 0 && result3 > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "新增成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "新增失败");
            }
        } catch (Exception e) {
            logger.error("新增产品成本设计异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 编辑产品成本设计
     *
     * @param productCostDesign 产品成本设计
     * @return
     */
    @RequiresPermissions("cost:productCostDesign:edit")
    @PostMapping("/edit")
    @ResponseBody
    public Object edit(ProductCostDesign productCostDesign) {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 设置更新信息
            productCostDesign.setUpdaterName(authorizingUser.getUserName());
            productCostDesign.setUpdatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));

            // 设置成本键为产品条码
            productCostDesign.setCostKey(productCostDesign.getProductBarcode());

            // 更新产品成本设计
            int result1 = productCostDesignService.updateProductCostDesign(productCostDesign);

            // 更新产品制造设计
            ProductManufacturingDesign manufacturingDesign = new ProductManufacturingDesign();
            copyToManufacturingDesign(productCostDesign, manufacturingDesign);
            manufacturingDesign.setUpdaterName(authorizingUser.getUserName());
            manufacturingDesign.setUpdatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            int result2 = productCostDesignService.updateProductManufacturingDesign(manufacturingDesign);

            // 更新产品成本
            ProductCost productCost = new ProductCost();
            copyToProductCost(productCostDesign, productCost);
            productCost.setCostKey(productCostDesign.getProductBarcode());
            productCost.setSize(productCostDesign.getProductSize());
            productCost.setProductCategory(productCostDesign.getCalculationType());
            productCost.setCalculationType(productCostDesign.getProductCategory());
            productCost.setCostCalculationStatus("0"); // 0：未计算
            productCost.setUpdaterName(authorizingUser.getUserName());
            productCost.setUpdatedTime(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            int result3 = productCostDesignService.updateProductCost(productCost);

            if (result1 > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "更新成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "更新失败");
            }
        } catch (Exception e) {
            logger.error("更新产品成本设计异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 删除产品成本设计
     *
     * @param serialNumber 流水号
     * @return
     */
    @RequiresPermissions("cost:productCostDesign:delete")
    @PostMapping("/delete")
    @ResponseBody
    public Object delete(Integer serialNumber) {
        try {
            int result = productCostDesignService.deleteProductCostDesign(serialNumber);
            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "删除成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "删除失败");
            }
        } catch (Exception e) {
            logger.error("删除产品成本设计异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 获取原料项目单价
     * 
     * @param year 年度
     * @param materialType 原料区分
     * @return
     */
    @GetMapping("/getRawMaterialItems")
    @ResponseBody
    public Object getRawMaterialItems(String year, String materialType) {
        try {
            List<RawMaterialItem> items = productCostDesignService.getRawMaterialItems(year, materialType);
            return new HrResult(CommonReturnCode.SUCCESS, items);
        } catch (Exception e) {
            logger.error("获取原料项目单价异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 获取运费单价
     * 
     * @param year 年度
     * @return
     */
    @GetMapping("/getTransportUnitPrices")
    @ResponseBody
    public Object getTransportUnitPrices(String year) {
        try {
            List<TransportUnitPrice> prices = productCostDesignService.getTransportUnitPrices(year);
            return new HrResult(CommonReturnCode.SUCCESS, prices);
        } catch (Exception e) {
            logger.error("获取运费单价异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 获取客户列表
     * 
     * @return
     */
    @GetMapping("/getCustomers")
    @ResponseBody
    public Object getCustomers() {
        try {
            List<Customer> customers = productCostDesignService.getCustomers();
            return new HrResult(CommonReturnCode.SUCCESS, customers);
        } catch (Exception e) {
            logger.error("获取客户列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 获取产品列表
     * 
     * @param customerName 客户简称
     * @param productCategory 产品分类
     * @return
     */
    @GetMapping("/getProducts")
    @ResponseBody
    public Object getProducts(String customerName, String productCategory) {
        try {
            List<Product> products = productCostDesignService.getProducts(customerName, productCategory);
            return new HrResult(CommonReturnCode.SUCCESS, products);
        } catch (Exception e) {
            logger.error("获取产品列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 获取产品设计数据
     * 
     * @param productCode 产品代码
     * @return
     */
    @GetMapping("/getProductDesignData")
    @ResponseBody
    public Object getProductDesignData(String productCode) {
        try {
            ProductDesignData data = productCostDesignService.getProductDesignData(productCode);
            return new HrResult(CommonReturnCode.SUCCESS, data);
        } catch (Exception e) {
            logger.error("获取产品设计数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 参考按钮 - 获取产品制造设计
     * 
     * @param year 年度
     * @param productCode 产品代码
     * @param productBarcode 产品条码
     * @param customerName 客户简称
     * @return
     */
    @GetMapping("/getProductManufacturingDesigns")
    @ResponseBody
    public Object getProductManufacturingDesigns(String year, String productCode, String productBarcode, String customerName) {
        try {
            List<ProductManufacturingDesign> designs = productCostDesignService.getProductManufacturingDesigns(year, productCode, productBarcode, customerName);
            return new HrResult(CommonReturnCode.SUCCESS, designs);
        } catch (Exception e) {
            logger.error("获取产品制造设计异常：", e);
            return new HrResult(CommonReturnCode.FAILED, e.getMessage());
        }
    }

    /**
     * 复制到产品制造设计
     */
    private void copyToManufacturingDesign(ProductCostDesign source, ProductManufacturingDesign target) {
        target.setYear(source.getYear());
        target.setBarcode(source.getProductBarcode());
        target.setCustomerName(source.getCustomerName());
        target.setProductCode(source.getProductCode());
        target.setProductBarcode(source.getProductBarcode());
        target.setWireDiscName(source.getWireDiscName());
        target.setProductSize(source.getProductSize());
        target.setFilmThickness1(source.getFilmThickness1());
        target.setFilmThickness2(source.getFilmThickness2());
        target.setFilmThickness3(source.getFilmThickness3());
        target.setFilmThickness4(source.getFilmThickness4());
        target.setConductorMaterial(source.getConductorItem());
        target.setPaint1(source.getPaintItem1());
        target.setPaint2(source.getPaintItem2());
        target.setPaint3(source.getPaintItem3());
        target.setPaint4(source.getPaintItem4());
        target.setPaint5(source.getPaintItem5());
        target.setWireDiscMaterial(source.getWireDiscItem());
        target.setTransportFee(source.getTransportFee());
        target.setQuantityTestType(source.getQuantityTestType());
        target.setProductCategory(source.getProductCategory());
        target.setCalculationType(source.getCalculationType());
        target.setConductorWeight(source.getConductorWeight());
        target.setPaintWeight1(source.getPaintWeight1());
        target.setPaintWeight2(source.getPaintWeight2());
        target.setPaintWeight3(source.getPaintWeight3());
        target.setPaintWeight4(source.getPaintWeight4());
        target.setPaintWeight5(source.getPaintWeight5());
        target.setWireDiscWeight(source.getWireDiscWeight());
    }

    /**
     * 复制到产品成本
     */
    private void copyToProductCost(ProductCostDesign source, ProductCost target) {
        target.setYear(source.getYear());
        target.setCustomerName(source.getCustomerName());
        target.setProductCode(source.getProductCode());
        target.setProductBarcode(source.getProductBarcode());
        target.setWireDiscName(source.getWireDiscName());
        target.setConductorWeight(source.getConductorWeight());
        target.setPaintWeight1(source.getPaintWeight1());
        target.setPaintWeight2(source.getPaintWeight2());
        target.setPaintWeight3(source.getPaintWeight3());
        target.setPaintWeight4(source.getPaintWeight4());
        target.setPaintWeight5(source.getPaintWeight5());
        target.setWireDiscWeight(source.getWireDiscWeight());
    }
}
