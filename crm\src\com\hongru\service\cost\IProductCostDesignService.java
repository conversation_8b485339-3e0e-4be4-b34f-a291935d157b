package com.hongru.service.cost;

import com.baomidou.mybatisplus.service.IService;
import com.hongru.entity.cost.*;
import com.hongru.entity.pims.ProductDesignData;
import com.hongru.entity.sumitomo.Customer;
import com.hongru.entity.sumitomo.Product;
import com.hongru.support.page.PageInfo;

import java.util.List;

/**
 * 产品成本设计服务接口
 * 
 * <AUTHOR>
 */
public interface IProductCostDesignService extends IService<ProductCostDesign> {

    /**
     * 分页查询产品成本设计列表（标准分页方法）
     *
     * @param year 年度
     * @param customerName 客户简称
     * @param productCategory 产品分类
     * @param productCode 产品代码
     * @param pageInfo 分页信息
     * @return 产品成本设计DTO
     * @throws Exception
     */
    ProductCostDesignDTO listProductCostDesignPage(String year, String customerName, String productCategory,
                                                  String productCode, PageInfo pageInfo) throws Exception;

    /**
     * 根据流水号查询产品成本设计
     *
     * @param serialNumber 流水号
     * @return 产品成本设计
     */
    ProductCostDesign getProductCostDesignById(Integer serialNumber);

    /**
     * 新增产品成本设计
     * 
     * @param productCostDesign 产品成本设计
     * @return 影响行数
     */
    int addProductCostDesign(ProductCostDesign productCostDesign);

    /**
     * 更新产品成本设计
     * 
     * @param productCostDesign 产品成本设计
     * @return 影响行数
     */
    int updateProductCostDesign(ProductCostDesign productCostDesign);

    /**
     * 删除产品成本设计
     * 
     * @param serialNumber 流水号
     * @return 影响行数
     */
    int deleteProductCostDesign(Integer serialNumber);

    /**
     * 根据年度和原料区分查询原料项目单价
     * 
     * @param year 年度
     * @param materialType 原料区分
     * @return 原料项目单价列表
     */
    List<RawMaterialItem> getRawMaterialItems(String year, String materialType);

    /**
     * 根据年度查询运费单价
     * 
     * @param year 年度
     * @return 运费单价列表
     */
    List<TransportUnitPrice> getTransportUnitPrices(String year);

    /**
     * 查询客户列表
     * 
     * @return 客户列表
     */
    List<Customer> getCustomers();

    /**
     * 根据客户简称和产品分类查询产品列表
     * 
     * @param customerName 客户简称
     * @param productCategory 产品分类
     * @return 产品列表
     */
    List<Product> getProducts(String customerName, String productCategory);

    /**
     * 根据产品代码查询产品设计数据
     * 
     * @param productCode 产品代码
     * @return 产品设计数据
     */
    ProductDesignData getProductDesignData(String productCode);

    /**
     * 根据条件查询产品制造设计
     * 
     * @param year 年度
     * @param productCode 产品代码
     * @param productBarcode 产品条码
     * @param customerName 客户简称
     * @return 产品制造设计列表
     */
    List<ProductManufacturingDesign> getProductManufacturingDesigns(String year, String productCode, 
            String productBarcode, String customerName);

    /**
     * 新增产品制造设计
     * 
     * @param productManufacturingDesign 产品制造设计
     * @return 影响行数
     */
    int addProductManufacturingDesign(ProductManufacturingDesign productManufacturingDesign);

    /**
     * 更新产品制造设计
     * 
     * @param productManufacturingDesign 产品制造设计
     * @return 影响行数
     */
    int updateProductManufacturingDesign(ProductManufacturingDesign productManufacturingDesign);

    /**
     * 新增产品成本
     * 
     * @param productCost 产品成本
     * @return 影响行数
     */
    int addProductCost(ProductCost productCost);

    /**
     * 更新产品成本
     * 
     * @param productCost 产品成本
     * @return 影响行数
     */
    int updateProductCost(ProductCost productCost);
}
