package com.hongru.service.impl.xieFenXi;

import com.hongru.common.util.FloatUtil;
import com.hongru.entity.xieFenXi.EMScrapAmountReport;
import com.hongru.entity.xieFenXi.ProductParametricBean;
import com.hongru.entity.xieFenXi.ScrapReasonDetail;
import com.hongru.entity.xieFenXi.XieFenXiBean;
import com.hongru.mapper.xieFenXi.ScrapAmountMapper;
import com.hongru.mapper.xieFenXi.ScrapReasonDetailMapper;
import com.hongru.mapper.xieFenXi.YieldMapper;
import com.hongru.service.xieFenXi.IXieFenXiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class XieFenXiServiceImpl implements IXieFenXiService {
	@Autowired
	private YieldMapper yieldMapper;
	@Autowired
	private ScrapAmountMapper scrapAmountMapper;
	@Autowired
	private ScrapReasonDetailMapper scrapReasonDetailMapper;
	/**
	* 月度原价CR实绩报表所需数据
	* @throws 
	* <AUTHOR>
	* @create 2023/9/22 16:27
	* @return 
	*/
	@Override
	public List<XieFenXiBean> listXieFenXiBean(String produceDate) throws Exception {
		return yieldMapper.listXieFenXiBean(produceDate);
	}

	/**
	* ef设备断线时间
	* @throws
	* <AUTHOR>
	* @create 2023/9/22 17:23
	* @return
	*/
	@Override
	public Map<String, Float> efBreakNumInfoMap(String produceDate) throws Exception {
		List<XieFenXiBean> xieFenXiBeanList = yieldMapper.efBreakNumInfoMap(produceDate);
		//合并同一日期EF断线的稼动时间
		Map<String,Float> scrapAmountMap = new HashMap<String, Float>();
		if(xieFenXiBeanList != null && xieFenXiBeanList.size() > 0){
			for (int i = 0; i < xieFenXiBeanList.size(); i++) {
				XieFenXiBean xieFenXiBean = xieFenXiBeanList.get(i);
				String key = xieFenXiBean.getDate() + xieFenXiBean.getMachineId();
				scrapAmountMap.put(key, FloatUtil.round((float)xieFenXiBean.getDiffDate()/60, 1));
			}
		}
		return scrapAmountMap;
	}

	@Override
	public List<XieFenXiBean> listMonthOutputproduct(String produceDate) throws Exception {
		return yieldMapper.listMonthOutputproduct(produceDate);
	}

	@Override
	public List<XieFenXiBean> listMonthScrapAmount(String produceDate) throws Exception {
		return yieldMapper.listMonthScrapAmount(produceDate);
	}

	@Override
	public List<EMScrapAmountReport> listDailyScrapAmountForMonth(String machineId, String productDate) throws Exception {
		return scrapAmountMapper.listDailyScrapAmountForMonth(machineId,productDate);
	}

	@Override
	public List<ScrapReasonDetail> selectScrapReason() throws Exception {
		return scrapReasonDetailMapper.selectScrapReason();
	}
	
	/**
	 *  单位机械时间
    * @param timeStartStr
    * @param timeEndStr
    * @throws Exception
    * <AUTHOR>
    * @create 2024/01/04 09:43
    * @return
    */
	@Override 
	public HashMap<String,Float> listxieFenXiBeanForMachineTime(int monthInterval, String timeStartStr, String timeEndStr)throws Exception{
		
		HashMap<String,Float> mounthScrapAmountMap = new HashMap<String, Float>();//存放机械时间信息

		// 检索原因为断线的停机时间（EF需做特殊处理）
		List<XieFenXiBean> XieFenXiBeanReasonList=scrapAmountMapper.listEFbreakNumInfo( timeStartStr,  timeEndStr);
		//合并同一日期EF断线的稼动时间
		HashMap<String,Float> scrapAmountMap = new HashMap<String, Float>(); //hashMap
		if(XieFenXiBeanReasonList != null && XieFenXiBeanReasonList.size() > 0){
			for (int i = 0; i < XieFenXiBeanReasonList.size(); i++) {
				XieFenXiBean xieFenXiBean = XieFenXiBeanReasonList.get(i);
					String key = xieFenXiBean.getDate() + xieFenXiBean.getMachineId();
					scrapAmountMap.put(key, FloatUtil.round((float)xieFenXiBean.getDiffDate()/60, 1));
			}
		}
		
		// 检索「设备参数表」获取线头数
		List<ProductParametricBean> productParametricBeanList = scrapAmountMapper.listProductParametricInfo();
		HashMap<String, Object> mapLineNum = new HashMap<String, Object>(); //map
		// 将取得的设备号和线头数转成MAP形式
		for (int i = 0; i < productParametricBeanList.size(); i++) {
			mapLineNum.put(productParametricBeanList.get(i).getEquipmentNo(), productParametricBeanList.get(i).getLineNum());
		}	
		//根据时间范围获取机器时间
		List<XieFenXiBean> XieFenXiBeanList = scrapAmountMapper.listDiffDate(timeStartStr, timeEndStr);
		if(XieFenXiBeanList != null && XieFenXiBeanList.size() > 0){
			for (int i = 0; i < XieFenXiBeanList.size(); i++) {
				XieFenXiBean xieFenXiBean = XieFenXiBeanList.get(i);
				Integer lineNum =(Integer) mapLineNum.get(xieFenXiBean.getMachineId());
				if(lineNum == null){
					lineNum = 0;
				}
				// 机械时间KEY
				String key = xieFenXiBean.getMachineId().substring(0,3);
				
				//产品时间
				double productTime = 0.00;
				// 计算产品时间s
				if(xieFenXiBean.getDiffDate() != null){
					productTime = eMOREFDIFF(lineNum,xieFenXiBean,scrapAmountMap);
				}
				//机械时间
				double machineTime = 0.00;
				if(mounthScrapAmountMap.get(key) != null){
					if(xieFenXiBean.getDiffDate() != null){
						machineTime = productTime+xieFenXiBean.getMachineTime()*lineNum;
					}else{
						machineTime = 24.0*(double)lineNum;
					}
					mounthScrapAmountMap.put(key, FloatUtil.round((float)machineTime+mounthScrapAmountMap.get(key),1));
				}else{
					if(xieFenXiBean.getDiffDate() != null){
						machineTime = productTime+xieFenXiBean.getMachineTime()*lineNum;
					}else{
						machineTime = (24.0)*(double)lineNum;
					}
					mounthScrapAmountMap.put(key, FloatUtil.round((float)machineTime, 1));
				}
			}
		}
		//存放线头数
		Integer em01LineTotal = 0;  //EM01线头数合计
		Integer em02LineTotal = 0;  //EM02线头数合计
		Integer em03LineTotal = 0;  //EM03线头数合计
		Integer em04LineTotal = 0;  //EM04线头数合计
		Integer em06LineTotal = 0;  //EM06线头数合计
		Integer em07LineTotal = 0;  //EM07线头数合计
		Integer ef01LineTotal = 0;  //EF01线头数合计
		Integer ef02LineTotal = 0;  //EF02线头数合计
		Integer ef03LineTotal = 0;  //EF03线头数合计
		Integer ef04LineTotal = 0;  //EF04线头数合计
		Integer ef05LineTotal = 0;  //EF05线头数合计
		Integer ef06LineTotal = 0;  //EF06线头数合计
		Integer ef07LineTotal = 0;  //EF07线头数合计
		Integer ef09LineTotal = 0;  //EF09线头数合计
		Integer er01LineTotal = 0;  //ER01线头数合计
		Integer er02LineTotal = 0;  //ER02线头数合计
		Integer er06LineTotal = 0;  //ER06线头数合计
		Integer eh03LineTotal = 0;  //EH03线头数合计
		Integer eh04LineTotal = 0;  //EH04线头数合计
		Integer eh05LineTotal = 0;  //EH05线头数合计
		
		// 使用for-each循环遍历 机器号线头数 的key值和value值
		for(Map.Entry<String, Object> entryLineNum : mapLineNum.entrySet()) {
        	String key = entryLineNum.getKey();
        	String keyTemp = key.substring(0, 3);
        	Object value = entryLineNum.getValue();
        	
        	switch(keyTemp) {
        	case "401":
        		em01LineTotal = em01LineTotal+Integer.valueOf(value.toString());
        		break;
        	case "402":
        		em02LineTotal = em02LineTotal+Integer.valueOf(value.toString());
        		break;
        	case "403":
        		em03LineTotal = em03LineTotal+Integer.valueOf(value.toString());
        		break;
        	case "404":
        		em04LineTotal = em04LineTotal+Integer.valueOf(value.toString());
        		break;
        	case "406":
        		em06LineTotal = em06LineTotal+Integer.valueOf(value.toString());
        		break;
        	case "407":
        		em07LineTotal = em07LineTotal+Integer.valueOf(value.toString());
        		break;
        	case "501":
        		ef01LineTotal = ef01LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "502":
        		ef02LineTotal = ef02LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "503":
        		ef03LineTotal = ef03LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "504":
        		ef04LineTotal = ef04LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "505":
        		ef05LineTotal = ef05LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "506":
        		ef06LineTotal = ef06LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "507":
        		ef07LineTotal = ef07LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "509":
        		ef09LineTotal = ef09LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "601":
        		er01LineTotal = er01LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "602":
        		er02LineTotal = er02LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "606":
        		er06LineTotal = er06LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "603":
        		eh03LineTotal = eh03LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "604":
        		eh04LineTotal = eh04LineTotal+Integer.valueOf(value.toString());
        		break;	
        	case "605":
        		eh05LineTotal = eh05LineTotal+Integer.valueOf(value.toString());
        		break;	
        	}	
		}
		//存放单位机械时间（按部门区分）
		HashMap<String,Float> totalScrapAmountMap = new HashMap<String, Float>();
        // 使用for-each循环遍历 机械时间明细(mounthScrapAmountMap) 的key值和value值
        for (Map.Entry<String,Float> entry : mounthScrapAmountMap.entrySet()) {
        	String keyMachineTime = entry.getKey();
        	Float valueMachineTime = entry.getValue();
            
        	switch(keyMachineTime) {
        	case "401":
        		totalScrapAmountMap.put("EM01", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(em01LineTotal), 2));
        		break;
        	case "402":
        		totalScrapAmountMap.put("EM02", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(em02LineTotal), 2));
        		break;
        	case "403":
        		totalScrapAmountMap.put("EM03", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(em03LineTotal), 2));
        		break;
        	case "404":
        		totalScrapAmountMap.put("EM04", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(em04LineTotal), 2));
        		break;
        	case "406":
        		totalScrapAmountMap.put("EM06", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(em06LineTotal), 2));
        		break;
        	case "407":
        		totalScrapAmountMap.put("EM07", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(em07LineTotal), 2));
        		break;
        	case "501":
        		totalScrapAmountMap.put("EF01", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(ef01LineTotal), 2));
        		break;	
        	case "502":
        		totalScrapAmountMap.put("EF02", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(ef02LineTotal), 2));
        		break;	
        	case "503":
        		totalScrapAmountMap.put("EF03", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(ef03LineTotal), 2));
        		break;	
        	case "504":
        		totalScrapAmountMap.put("EF04", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(ef04LineTotal), 2));
        		break;	
        	case "505":
        		totalScrapAmountMap.put("EF05", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(ef05LineTotal), 2));
        		break;	
        	case "506":
        		totalScrapAmountMap.put("EF06", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(ef06LineTotal), 2));
        		break;	
        	case "507":
        		totalScrapAmountMap.put("EF07", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(ef07LineTotal), 2));
        		break;	
        	case "509":
        		totalScrapAmountMap.put("EF09", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(ef09LineTotal), 2));
        		break;	
        	case "601":
        		totalScrapAmountMap.put("ER01", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(er01LineTotal), 2));
        		break;	
        	case "602":
        		totalScrapAmountMap.put("ER02", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(er02LineTotal), 2));
        		break;	
        	case "606":
        		totalScrapAmountMap.put("ER06", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(er06LineTotal), 2));
        		break;	
        	case "603":
        		totalScrapAmountMap.put("EH03", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(eh03LineTotal), 2));
        		break;	
        	case "604":
        		totalScrapAmountMap.put("EH04", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(eh04LineTotal), 2));
        		break;	
        	case "605":
        		totalScrapAmountMap.put("EH05", FloatUtil.IntForFloat(valueMachineTime, Float.valueOf(eh05LineTotal), 2));
        		break;	
        	}
        }
      
		//存放单位机械时间合计(按部门区分)
		HashMap<String,Float> totalDepartScrapAmountMap = new HashMap<String, Float>();
		Float emMTimeUnitTotal = (float) 0;  //EM单位机械时间合计
		Float efMTimeUnitTotal =  (float) 0;  //EF单位机械时间合计
		Float ef09MTimeUnitTotal = (float) 0;  //EF09单位机械时间合计
		Float erMTimeUnitTotal = (float) 0;  //ER单位机械时间合计
		Float ehMTimeUnitTotal =  (float) 0;  //EH单位机械时间合计
//		Float allMTimeUnitTotal =  (float) 0;  //所有单位机械时间合计
        // 使用for-each循环遍历 机械时间信息(totalScrapAmountMap) 的key值和value值
        for (Map.Entry<String,Float> entry : totalScrapAmountMap.entrySet()) {
        	String key = entry.getKey();
        	Float value = entry.getValue();
//        	allMTimeUnitTotal = allMTimeUnitTotal + value;
        	switch(key) {
	            case "EM01":
	            	emMTimeUnitTotal = emMTimeUnitTotal + value;
	        		break;
	            case "EM02":
	            	emMTimeUnitTotal = emMTimeUnitTotal + value;
	            	break;
	            case "EM03":
	            	emMTimeUnitTotal = emMTimeUnitTotal + value;
	            	break;
	            case "EM04":
	            	emMTimeUnitTotal = emMTimeUnitTotal + value;
	            	break;
	            case "EM05":
	            	emMTimeUnitTotal = emMTimeUnitTotal + value;
	            	break;
	            case "EM06":
	            	emMTimeUnitTotal = emMTimeUnitTotal + value;
	            	break;
	            case "EM07":
	            	emMTimeUnitTotal = emMTimeUnitTotal + value;
	            	break;
	            case "EF01":
	            	efMTimeUnitTotal = efMTimeUnitTotal + value;
	            	break;
	            case "EF02":
	            	efMTimeUnitTotal = efMTimeUnitTotal + value;
	            	break;
	            case "EF03":
	            	efMTimeUnitTotal = efMTimeUnitTotal + value;
	            	break;
	            case "EF04":
	            	efMTimeUnitTotal = efMTimeUnitTotal + value;
	            	break;
	            case "EF05":
	            	efMTimeUnitTotal = efMTimeUnitTotal + value;
	            	break;
	            case "EF06":
	            	efMTimeUnitTotal = efMTimeUnitTotal + value;
	            	break;
	            case "EF07":
	            	ef09MTimeUnitTotal = ef09MTimeUnitTotal + value;
	            	break;
	            case "EF09":
	            	ef09MTimeUnitTotal = ef09MTimeUnitTotal + value;
	            	break;
	            case "ER01":
	            	erMTimeUnitTotal = erMTimeUnitTotal + value;
	            	break;
	            case "ER02":
	            	erMTimeUnitTotal = erMTimeUnitTotal + value;
	            	break;
	            case "ER06":
	            	erMTimeUnitTotal = erMTimeUnitTotal + value;
	            	break;
	            case "EH03":
	            	ehMTimeUnitTotal = ehMTimeUnitTotal + value;
	            	break;
	            case "EH04":
	            	ehMTimeUnitTotal = ehMTimeUnitTotal + value;
	            	break;
	            case "EH05":
	            	ehMTimeUnitTotal = ehMTimeUnitTotal + value;
	            	break;
        	}
        }

        // 将上记单位机械时间合计(按部门区分)结果存放MAP中
        totalDepartScrapAmountMap.put("EM", emMTimeUnitTotal/(float)monthInterval);
        totalDepartScrapAmountMap.put("EF", efMTimeUnitTotal/(float)monthInterval);
        totalDepartScrapAmountMap.put("EF09", ef09MTimeUnitTotal/(float)monthInterval);
        totalDepartScrapAmountMap.put("ER", erMTimeUnitTotal/(float)monthInterval);
        totalDepartScrapAmountMap.put("EH", ehMTimeUnitTotal/(float)monthInterval);  
//        totalDepartScrapAmountMap.put("ALL", allMTimeUnitTotal);  
        
		return totalDepartScrapAmountMap;
	}
	
	public double eMOREFDIFF(Integer lineNum,XieFenXiBean xieFenXiBean,HashMap<String, Float> hashMap){
		double result = 0.00f;
		if(xieFenXiBean.getMachineId().startsWith("5")){
			if(hashMap.get(xieFenXiBean.getDate() + xieFenXiBean.getMachineId()) !=null){
				//在有断线时间的情况下 产品时间=（24-总的停止时间）*线头数 +（线头数-1）*断线停止时间（相当于 24*线头数 - （除了断线原因停止时间）*线头数-断线停止时间）
				result = (24.0-((double)xieFenXiBean.getDiffDate()/60))*lineNum+(lineNum-1)*hashMap.get(xieFenXiBean.getDate() + xieFenXiBean.getMachineId());
			}else{
				result = (24.0-((double)xieFenXiBean.getDiffDate()/60))*lineNum;
			}		
		}else{
			result = (24.0-((double)xieFenXiBean.getDiffDate()/60))*lineNum;
		}
		return result;
	}
}