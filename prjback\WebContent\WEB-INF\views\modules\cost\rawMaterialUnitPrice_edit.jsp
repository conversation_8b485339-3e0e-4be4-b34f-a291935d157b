<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>编辑原料单价</title>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <form id="formEdit" class="layui-form" method="post" action="">
            <input type="hidden" name="serialNumber" value="${rawMaterialItem.serialNumber}">

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年度<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="year" id="year" value="${rawMaterialItem.year}" lay-verify="required" placeholder="请选择年度" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">品目<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="itemCode" id="itemCode" value="${rawMaterialItem.itemCode}" lay-verify="required" placeholder="请输入品目" autocomplete="off" class="layui-input" maxlength="3">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">品目名<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="itemName" id="itemName" value="${rawMaterialItem.itemName}" lay-verify="required" placeholder="请输入品目名" autocomplete="off" class="layui-input" maxlength="12">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">原料区分<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <select name="materialType" id="materialType" lay-verify="required">
                            <option value="">请选择原料区分</option>
                            <option value="01" ${rawMaterialItem.materialType == '01' ? 'selected' : ''}>导体</option>
                            <option value="02" ${rawMaterialItem.materialType == '02' ? 'selected' : ''}>油漆</option>
                            <option value="03" ${rawMaterialItem.materialType == '03' ? 'selected' : ''}>线盘</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formEdit">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="cancel();">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'layer', 'laydate'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;

    // 年度选择器
    laydate.render({
        trigger: 'click',
        type: 'year',
        elem: '#year',
        btns: ['clear','confirm']
    });

    //监听提交
    form.on('submit(formEdit)', function(data){
        $.ajax({
            url: baselocation + '/costParameters/unitPriceOfRawMaterials/edit',
            type: 'POST',
            data: data.field,
            success: function(result) {
                if(result.code == 1) {
                    layer.msg('编辑成功', {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统异常', {icon: 2});
            }
        });
        return false;
    });
});

// 取消按钮
function cancel() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>
<style>
    .layui-form-label {
        width: 120px;
    }
</style>
</body>
</html>
