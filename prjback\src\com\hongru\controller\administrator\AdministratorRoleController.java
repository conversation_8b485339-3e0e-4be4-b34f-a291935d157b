package com.hongru.controller.administrator;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.hongru.base.BaseController;
import com.hongru.base.BasePageDTO;
import com.hongru.common.enums.StatusEnum;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.Role;
import com.hongru.pojo.dto.RoleMenuDTO;
import com.hongru.pojo.dto.UserPageDTO;
import com.hongru.pojo.vo.RoleMenuVO;
import com.hongru.service.admin.IRoleMenuService;
import com.hongru.service.admin.IRoleService;
import com.hongru.service.admin.IUserRoleService;
import com.hongru.support.page.PageInfo;

/**
 * 
* 类名称：AdministratorRoleController   
* 类描述：角色管理表示层控制器        
* 创建人：hongru   
* 创建时间：2017年4月6日 下午11:32:34   
*
 */
@Controller
@RequestMapping(value = "/administrator/role")
public class AdministratorRoleController extends BaseController {
	
	@Autowired
	private IRoleService roleService;
	@Autowired
	private IRoleMenuService roleMenuService;
	@Autowired
	private IUserRoleService userRoleService;
	
	/**
	 * GET 角色列表页面
	 * @param model
	 * @return
	 */
	@RequiresPermissions("administrator:role:view")
	@GetMapping(value = "/view")
	public String getRolePage(Model model) {
		return "/modules/admin/admin_role_list";
	}
	
	/**
	 * Post 角色列表
	 * @param searchStr
	 * @return
	 */
	@RequiresPermissions("administrator:role:view")
	@PostMapping(value = "/")
	@ResponseBody
	public Object listRole(Integer page, Integer limit, String searchStr) {
		PageInfo pageInfo = new PageInfo(limit, page);
		BasePageDTO<Role> basePageDTO = roleService.listByPage(pageInfo, searchStr);
		return new HrPageResult(basePageDTO.getList(), basePageDTO.getPageInfo().getTotal());
	}
	
	/**
	 * GET 角色分类下管理员列表页面
	 * @param roleId
	 * @return
	 */
	@RequiresPermissions("administrator:role:view")
	@GetMapping(value = "/{roleId}/list")
	public String list(Model model, @PathVariable("roleId") Long roleId) {
		model.addAttribute("roleId", roleId);
		return "/modules/admin/admin_user_role";
	}
	
	/**
	 * 角色分类下的管理员列表
	 * @return
	 */
	@RequiresPermissions("administrator:list:view")
	@PostMapping(value = "/{roleId}/lists")
	@ResponseBody
	public Object listLogs(@PathVariable("roleId") Long roleId, Integer page, Integer limit,String searchStr) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			PageInfo pageInfo = new PageInfo(limit, page);
			// 用户日志
			UserPageDTO userPageDTO = userRoleService.listByRoleId(roleId, pageInfo, searchStr);
			return new HrPageResult(userPageDTO.getUserVOs(),
					userPageDTO.getPageInfo().getTotal());
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * PUT 启用/冻结角色
	 * @param roleId 角色ID
	 * @return
	 */
	@PutMapping(value = "/{roleId}/audit")
	@ResponseBody
	public Object audit(@PathVariable("roleId") Long roleId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			Integer count = roleService.updateStatus(roleId);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * DELETE 删除角色
	 * @param roleId 角色ID
	 * @return
	 */
	@RequiresPermissions("administrator:role:delete")
	@DeleteMapping(value = "/{roleId}")
	@ResponseBody
	public Object delete(@PathVariable("roleId") Long roleId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			Integer count = roleService.deleteByRoleId(roleId);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}

	/**
	* 批量删除角色
	* @throws
	* <AUTHOR>
	* @create 2022/2/11 15:21
	* @return
	*/
	@PostMapping(value = "/delete/forBatch")
	@ResponseBody
	public Object deleteRolesForBatch(String roleIds) throws Exception {
		try {
			String[] roleIdStrArr = roleIds.split(",");
			Integer[] roleIdArr = new Integer[roleIdStrArr.length];
			for(int i=0;i<roleIdArr.length;i++){
				roleIdArr[i] = Integer.parseInt(roleIdStrArr[i]);
			}
			roleService.deleteRoles(roleIdArr);
			return new HrResult(CommonReturnCode.SUCCESS);
		}catch (Exception e) {
			e.printStackTrace();
			return new HrResult(CommonReturnCode.FAILED.getCode(),"操作失败");
		}
	}
	
	/**
	 * GET 创建角色页面
	 * @return
	 */
	@RequiresPermissions("administrator:role:create")
	@GetMapping(value = "/create/view")
	public String getInsertPage(Model model) {
		List<RoleMenuDTO> menus = roleMenuService.listRoleMenus(StatusEnum.SHOW.getStatus());
		model.addAttribute("menus", JSON.toJSON(menus));
		return "/modules/admin/admin_role_add";
	}
	
	/**
	 * POST 创建角色
	 * @return
	 */
	@RequiresPermissions("administrator:role:create")
	@PostMapping(value = "/create")
	@ResponseBody
	public Object insert(Role role, @RequestParam(required = false, value = "menuIds") String menuId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			// 创建角色及插入角色目录记录
			if(!menuId.trim().equals("")){
				String[] menuIds = menuId.split(",");
				Integer count = roleService.insertRole(role, menuIds, authorizingUser.getUserName());
				return new HrResult(CommonReturnCode.SUCCESS, count);
			}else{
				Integer count = roleService.insertRole(role, null, authorizingUser.getUserName());
				return new HrResult(CommonReturnCode.SUCCESS, count);
			}
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * GET 更新角色页面
	 * @return
	 */
	@RequiresPermissions("administrator:role:edit")
	@GetMapping(value = "/{roleId}/edit/view")
	public String getUpdatePage(Model model, @PathVariable("roleId") Long roleId) {
		// 目录是否选中
		List<RoleMenuVO> menus = roleMenuService.listCheckedMenus(roleId, StatusEnum.SHOW.getStatus());
		model.addAttribute("menus", JSON.toJSON(menus));
		
		// 角色信息
		Role role = roleService.selectById(roleId);
		model.addAttribute("role", role);
		
		return "/modules/admin/admin_role_edit";
	}
	
	/**
	 * PUT 更新角色信息
	 * @return
	 */
	@RequiresPermissions("administrator:role:edit")
	@PostMapping(value = "/{roleId}/edit")
	@ResponseBody
	public Object update(Role role, @PathVariable("roleId") Long roleId,
			@RequestParam(required = false, value = "menuIds") String menuId) {
		String[] menuIds = menuId.split(",");
		
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			// 更新用户及角色记录
			Integer count = roleService.updateRole(role, menuIds, authorizingUser.getUserName());
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
}
