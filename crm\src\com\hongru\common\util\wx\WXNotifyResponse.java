package com.hongru.common.util.wx;

import javax.xml.bind.annotation.XmlRootElement;

import com.hongru.common.util.Md5Encrypt;

/*
 * 微信支付 notify
 */
@XmlRootElement
public class WXNotifyResponse {
	
	/*
	 * 返回状态码,SUCCESS/FAIL
	 * 此字段是通信标识，非交易标识，交易是否成功需要查
	 * 看result_code来判断
	 */
	private String return_code;
	/*
	 * 返回信息，如非空，为错误原因
	 */
	private String return_msg;

	/* ------------- 以下字段在return_code为SUCCESS的时候有返回 ----------------*/

	/*
	 * 微信分配的公众账号ID
	 */
	private String appid;
	
	/*
	 * 微信支付分配的商户号
	 */
	private String mch_id;

	/*
	 * 微信支付分配的终端设备号
	 */
	private String device_info;

	/*
	 * 随机字符串，不长于32位
	 */
	private String nonce_str;

	/*
	 * 签名
	 */
	private String sign;

	/*
	 * 业务结果,SUCCESS/FAIL
	 */
	private String result_code;

	/*
	 * 错误代码
	 */
	private String err_code;

	/*
	 * 错误代码描述
	 */
	private String err_code_des;
	
	/* ----------------------- 以下字段在return_code 和result_code都为SUCCESS的时候有返回 ------------------------*/

	/*
	 * 用户在商户appid下的唯一标识
	 */
	private String openid;

	/*
	 * 用户是否关注公众账号，Y-关注，N-未关注，仅在公众账号类型支付有效
	 */
	private String is_subscribe;
	
	/*
	 * 交易类型:JSAPI、NATIVE、MICROPAY、APP
	 */
	private String trade_type;
	/*
	 * 交易状态
	 */
	private String trade_state;
	
	/*
	 * 银行类型，采用字符串类型的银行标识
	 */
	private String bank_type;
	
	/*
	 * 订单总金额，单位为分
	 */
	private int total_fee;
	
	/*
	 * 现金券支付金额<=订单总金额，订单总金额-现金券金额为现金支付金额
	 */
	private int coupon_fee;
	
	
	/*
	 * 现金支付金额
	 */
	private int cash_fee;
	
	/*
	 * 货币类型，符合ISO 4217标准的三位字母代码，默认人民币：CNY
	 */
	private String fee_type;

	/*
	 * 微信支付订单号
	 */
	private String transaction_id;

	/*
	 * 商户系统的订单号，与请求一致。
	 */
	private String out_trade_no;

	/*
	 * 商家数据包，原样返回
	 */
	private String attach;

	/*
	 * 支付完成时间，格式为yyyyMMddhhmmss，如2009年12月27日9点10分10秒表示为**************。时区为GMT+8 beijing。该时间取自微信支付服务器
	 */
	private String time_end;
	
	private String sub_appid;
	private String sub_is_subscribe;
	private String sub_mch_id;
	private String sub_openid;
	
	private String coupon_count;
	private String coupon_fee_0;
	private String coupon_id_0;
	
	
//	public static void main(String[] args) throws Exception {
//		String resultXmlStr = "<wxNotifyResponse><appid><![CDATA[wx922a3bdb8fcb29de]]></appid>";
//		resultXmlStr += "<bank_type><![CDATA[CFT]]></bank_type>";
//		resultXmlStr += "<cash_fee><![CDATA[1]]></cash_fee>";
//		resultXmlStr += "<device_info><![CDATA[APP-001]]></device_info>";
//		resultXmlStr += "<fee_type><![CDATA[CNY]]></fee_type>";
//		resultXmlStr += "<is_subscribe><![CDATA[N]]></is_subscribe>";
//		resultXmlStr += "<mch_id><![CDATA[**********]]></mch_id>";
//		resultXmlStr += "<nonce_str><![CDATA[*********]]></nonce_str>";
//		resultXmlStr += "<openid><![CDATA[oCdIYsxl2CLVY8TAC_jEH4pkRoQk]]></openid>";
//		resultXmlStr += "<out_trade_no><![CDATA[sf_m_wxpay_42]]></out_trade_no>";
//		resultXmlStr += "<result_code><![CDATA[SUCCESS]]></result_code>";
//		resultXmlStr += "<return_code><![CDATA[SUCCESS]]></return_code>";
//		resultXmlStr += "<sign><![CDATA[EC5E74451766BAA0044A835F4079A0D7]]></sign>";
//		resultXmlStr += "<time_end><![CDATA[**************]]></time_end>";
//
//		resultXmlStr += "<total_fee>1</total_fee>";
//		resultXmlStr += "<trade_type><![CDATA[APP]]></trade_type>";
//		resultXmlStr += "<transaction_id><![CDATA[1010130207201603073796507291]]></transaction_id>";
//		resultXmlStr += "</wxNotifyResponse>";
//	
//		WXNotifyResponse response = XMLJdomUtil.parseXmlToBean(WXNotifyResponse.class, resultXmlStr);
//		String createsign = response.createSign();
//		System.out.println(createsign);
//	}
	
	
	/**
	 * 注：一定将参数按照参数名ASCII码从小到大排序（字典序）
	 */
	public String createSign(String apiKey){
		StringBuffer sb = new StringBuffer();
		sb.append("appid=").append(getAppid())
			.append("&bank_type=").append(getBank_type())
			.append("&cash_fee=").append(getCash_fee())
			.append("&device_info=").append(getDevice_info())
			.append("&fee_type=").append(getFee_type())
			.append("&is_subscribe=").append(getIs_subscribe())
			.append("&mch_id=").append(getMch_id())
			.append("&nonce_str=").append(getNonce_str())
			.append("&openid=").append(getOpenid())
			.append("&out_trade_no=").append(getOut_trade_no())
			.append("&result_code=").append(getResult_code())
			.append("&return_code=").append(getReturn_code())
			.append("&time_end=").append(getTime_end())
			.append("&total_fee=").append(getTotal_fee())
			.append("&trade_type=").append(getTrade_type())
			.append("&transaction_id=").append(getTransaction_id());
		sb.append("&key=").append(apiKey);
		String newsign = Md5Encrypt.md5(sb.toString()).toUpperCase();
		return newsign;
	}
	
	/**
	 * 微信网页h5支付校验
	 * 注：一定将参数按照参数名ASCII码从小到大排序（字典序）
	 */
	public String createSignForWxWeb(String apiKey){
		StringBuffer sb = new StringBuffer();
		sb.append("appid=").append(getAppid())
			.append("&bank_type=").append(getBank_type())
			.append("&cash_fee=").append(getCash_fee())
			.append("&fee_type=").append(getFee_type())
			.append("&is_subscribe=").append(getIs_subscribe())
			.append("&mch_id=").append(getMch_id())
			.append("&nonce_str=").append(getNonce_str())
			.append("&openid=").append(getOpenid())
			.append("&out_trade_no=").append(getOut_trade_no())
			.append("&result_code=").append(getResult_code())
			.append("&return_code=").append(getReturn_code())
			.append("&time_end=").append(getTime_end())
			.append("&total_fee=").append(getTotal_fee())
			.append("&trade_type=").append(getTrade_type())
			.append("&transaction_id=").append(getTransaction_id());
		sb.append("&key=").append(apiKey);
		String newsign = Md5Encrypt.md5(sb.toString()).toUpperCase();
		System.out.println("sb.toString : " + sb.toString());
		return newsign;
	}
	
	/**
	 * 微信网页h5支付校验
	 * 注：一定将参数按照参数名ASCII码从小到大排序（字典序）
	 */
	public String createSignForOpen(String apiKey){
		StringBuffer sb = new StringBuffer();
		sb.append("appid=").append(getAppid())
			.append("&bank_type=").append(getBank_type())
			.append("&cash_fee=").append(getCash_fee());
			if(getCash_fee() < getTotal_fee()) {
				sb.append("&coupon_count=").append(getCoupon_count())
				.append("&coupon_fee=").append(getCoupon_fee())
				.append("&coupon_fee_0=").append(getCoupon_fee_0())
				.append("&coupon_id_0=").append(getCoupon_id_0());
			}
			sb.append("&fee_type=").append(getFee_type())
			.append("&is_subscribe=").append(getIs_subscribe())
			.append("&mch_id=").append(getMch_id())
			.append("&nonce_str=").append(getNonce_str())
			.append("&openid=").append(getOpenid())
			.append("&out_trade_no=").append(getOut_trade_no())
			.append("&result_code=").append(getResult_code())
			.append("&return_code=").append(getReturn_code())
			.append("&sub_appid=").append(getSub_appid())
			.append("&sub_is_subscribe=").append(getSub_is_subscribe())
			.append("&sub_mch_id=").append(getSub_mch_id())
			.append("&sub_openid=").append(getSub_openid())
			.append("&time_end=").append(getTime_end())
			.append("&total_fee=").append(getTotal_fee())
			.append("&trade_type=").append(getTrade_type())
			.append("&transaction_id=").append(getTransaction_id());
		sb.append("&key=").append(apiKey);
		String newsign = Md5Encrypt.md5(sb.toString()).toUpperCase();
		System.out.println("sb.toString : " + sb.toString());
		return newsign;
	}
	
//	public static String createSignByCheckOrder(String outTradeNo){
//		StringBuffer sb = new StringBuffer();
//		sb.append("appid=").append(WechatUtils.getWxKaiFangAppid())
//			.append("&mch_id=").append(WechatUtils.MCHID)
//			.append("&nonce_str=").append(WechatUtils.noncestr)
//			.append("&out_trade_no=").append(outTradeNo);
//		sb.append("&key=").append(WechatUtils.getWxKaiFangAppSecret());
//		String newsign = Md5Encrypt.md5(sb.toString()).toUpperCase();
//		return newsign;
//	}
	public static void allparams(){
		
	}

	public String getReturn_code() {
		return return_code;
	}

	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	public String getReturn_msg() {
		return return_msg;
	}

	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getMch_id() {
		return mch_id;
	}

	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	public String getDevice_info() {
		return device_info;
	}

	public void setDevice_info(String device_info) {
		this.device_info = device_info;
	}

	public String getNonce_str() {
		return nonce_str;
	}

	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getResult_code() {
		return result_code;
	}

	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}

	public String getErr_code() {
		return err_code;
	}

	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	public String getErr_code_des() {
		return err_code_des;
	}

	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public String getIs_subscribe() {
		return is_subscribe;
	}

	public void setIs_subscribe(String is_subscribe) {
		this.is_subscribe = is_subscribe;
	}

	public String getTrade_type() {
		return trade_type;
	}

	public void setTrade_type(String trade_type) {
		this.trade_type = trade_type;
	}

	public String getBank_type() {
		return bank_type;
	}

	public void setBank_type(String bank_type) {
		this.bank_type = bank_type;
	}

	public int getTotal_fee() {
		return total_fee;
	}

	public void setTotal_fee(int total_fee) {
		this.total_fee = total_fee;
	}

	public int getCoupon_fee() {
		return coupon_fee;
	}

	public void setCoupon_fee(int coupon_fee) {
		this.coupon_fee = coupon_fee;
	}

	public String getFee_type() {
		return fee_type;
	}

	public void setFee_type(String fee_type) {
		this.fee_type = fee_type;
	}

	public String getTransaction_id() {
		return transaction_id;
	}

	public void setTransaction_id(String transaction_id) {
		this.transaction_id = transaction_id;
	}

	public String getOut_trade_no() {
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	public String getAttach() {
		return attach;
	}

	public void setAttach(String attach) {
		this.attach = attach;
	}

	public String getTime_end() {
		return time_end;
	}

	public void setTime_end(String time_end) {
		this.time_end = time_end;
	}

	public int getCash_fee() {
		return cash_fee;
	}

	public void setCash_fee(int cash_fee) {
		this.cash_fee = cash_fee;
	}

	public String getTrade_state() {
		return trade_state;
	}

	public void setTrade_state(String trade_state) {
		this.trade_state = trade_state;
	}

	public String getSub_appid() {
		return sub_appid;
	}

	public void setSub_appid(String sub_appid) {
		this.sub_appid = sub_appid;
	}

	public String getSub_is_subscribe() {
		return sub_is_subscribe;
	}

	public void setSub_is_subscribe(String sub_is_subscribe) {
		this.sub_is_subscribe = sub_is_subscribe;
	}

	public String getSub_mch_id() {
		return sub_mch_id;
	}

	public void setSub_mch_id(String sub_mch_id) {
		this.sub_mch_id = sub_mch_id;
	}

	public String getSub_openid() {
		return sub_openid;
	}

	public void setSub_openid(String sub_openid) {
		this.sub_openid = sub_openid;
	}

	public String getCoupon_count() {
		return coupon_count;
	}

	public void setCoupon_count(String coupon_count) {
		this.coupon_count = coupon_count;
	}

	public String getCoupon_fee_0() {
		return coupon_fee_0;
	}

	public void setCoupon_fee_0(String coupon_fee_0) {
		this.coupon_fee_0 = coupon_fee_0;
	}

	public String getCoupon_id_0() {
		return coupon_id_0;
	}

	public void setCoupon_id_0(String coupon_id_0) {
		this.coupon_id_0 = coupon_id_0;
	}

}
