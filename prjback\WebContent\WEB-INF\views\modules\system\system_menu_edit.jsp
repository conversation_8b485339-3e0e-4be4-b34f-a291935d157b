<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <title>编辑菜单</title>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/system/menu/${menu.menuId}" method="post" id="submitForm">
  <div class="layui-fluid hr-layui-fluid">
  <div class="layui-card">
    <div class="layui-card-body">
      <div class="layui-form-item layui-row">
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span class="star">*</span>菜单名称:</label>
          <div class="layui-input-block">
            <input type="text" id="menuName" name="menuName" required="" lay-verify="required" placeholder="请输入菜单名称" class="layui-input" value="${menu.menuName}"/>
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span class="star">*</span>上级菜单:</label>
          <div class="layui-input-block">
            <input type="text" disabled="" placeholder="${parentMenu.menuName}${menuName}" class="layui-input">
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span class="star">*</span>链接:</label>
          <div class="layui-input-block">
            <input type="text" id="href" name="href" required="" value="${menu.href}" lay-verify="required" placeholder="请输入链接" class="layui-input" />
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span class="star">*</span>序号:</label>
          <div class="layui-input-block">
            <input type="text" id="sort" name="sort" required="" lay-verify="required" placeholder="请输入排序" class="layui-input" value="${menu.sort}"/>
          </div>
        </div>
        <div class="layui-inline layui-col-md5">
          <label class="layui-form-label"><span  class="star">*</span>状态:</label>
          <div class="layui-input-block">
            <input type="radio" name="status" value="1" title="正常" ${menu.status eq '1'?'checked="checked"':''}>
            <input type="radio" name="status" value="0" title="隐藏" ${menu.status eq '0'?'checked="checked"':''}>
          </div>
        </div>
        <div class="layui-inline layui-col-md5" >
          <label class="layui-form-label">图标</label>
          <div class="layui-input-block" style="display: flex;justify-content: space-between">
            <input type="text" name="icon" id="iconPicker" lay-filter="iconPicker required" class="hide" value="${menu.icon}">
          </div>
        </div>
        <c:if test="${menu.menuType==0 || parentMenu.menuType==2}">
          <div class="layui-inline layui-col-md5">
            <label class="layui-form-label"><span class="star">*</span>权限标识:</label>
            <div class="layui-input-block">
              <input type="text" id="permission" name="permission" required="" lay-verify="required" placeholder="请输入权限标识" class="layui-input" value="${menu.permission}"/>
            </div>
          </div>
        </c:if>
        <div class="layui-inline layui-col-md12">
          <label class="layui-form-label">备注:</label>
          <div class="layui-input-block">
            <textarea class="layui-textarea" style="resize: none;" name="remarks" maxlength="220">${menu.remarks}</textarea>
          </div>
        </div>
      </div>
      <div class="layui-form-item layui-row">
        <div class="layui-inline layui-col-md12">
          <label class="layui-form-label"></label>
          <div class="layui-input-block">
            <input type="hidden" class="layui-input" name="menuType" value="${empty parentMenu.menuType?1:parentMenu.menuType==1?2:0}">
            <input type="hidden" class="layui-input" name="parentId" value="${empty parentMenu.menuId?1:parentMenu.menuId}">
            <button type="button" class="layui-btn" lay-submit lay-filter="formDemo"><i class="layui-icon layui-icon-ok"></i>保存</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();"><i class="layui-icon layui-icon-close"></i>取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</form>
<myfooter>
  <script src="${ctxsta}/common/icheck/icheck.min.js"></script>
  <script src="${ctxsta}/hongru/js/system/system_menu_addOrModify.js?timer=2"></script>
</myfooter>
</body>
</html>
