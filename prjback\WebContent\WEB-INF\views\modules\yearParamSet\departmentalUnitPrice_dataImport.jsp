<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>部门单价数据读取</title>
    <style>
        .layui-form-label {
            width: 120px;
            text-align: right;
        }
        .layui-input-block {
            margin-left: 150px;
        }
        .layui-input-inline {
            width: 200px;
        }
        .import-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .import-info h4 {
            color: #495057;
            margin-bottom: 10px;
        }
        .import-info p {
            color: #6c757d;
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="import-info">
            <h4>当前工场区分：${factoryType}</h4>
            <p>1. 数据读取前将删除指定年度的${factoryType}类型部门单价表数据</p>
            <p>2. 从年度改订部门单价汇总查询指定日期范围内的${factoryType}数据</p>
            <p>3. 将查询到的${factoryType}部门单价数据导入到部门单价表中</p>
        </div>
        
        <form id="importForm" class="layui-form" method="post" action="">
            <input type="hidden" name="factoryType" value="${factoryType}" />
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">开始日期<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="startDate" id="startDate" lay-verify="required" autocomplete="off" class="layui-input" readonly>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">结束日期<span style="color: red">*</span>:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="endDate" id="endDate" lay-verify="required" autocomplete="off" class="layui-input" readonly>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-bg-blue" onclick="submitImport()">
                        <i class="layui-icon layui-icon-download-circle"></i>开始读取
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="closeWindow()">
                        <i class="layui-icon layui-icon-close"></i>取消
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['laydate', 'layer', 'form'], function(){
    var laydate = layui.laydate
        ,layer = layui.layer
        ,form = layui.form;

    // 开始日期选择器（年月）
    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#startDate',
        btns: ['clear','confirm'],
        format: 'yyyy-MM'
    });

    // 结束日期选择器（年月）
    laydate.render({
        trigger: 'click',
        type: 'month',
        elem: '#endDate',
        btns: ['clear','confirm'],
        format: 'yyyy-MM'
    });

    // 设置默认日期为当前年月
    var currentDate = new Date();
    var currentYearMonth = currentDate.getFullYear() + '-' + String(currentDate.getMonth() + 1).padStart(2, '0');
    $("#startDate").val(currentYearMonth);
    $("#endDate").val(currentYearMonth);
});

// 提交导入
function submitImport() {
    var formData = $("#importForm").serializeJsonObject();
    
    // 验证必填项
    if (!formData.startDate) {
        layer.msg('请选择开始日期', {icon: 2});
        return;
    }
    if (!formData.endDate) {
        layer.msg('请选择结束日期', {icon: 2});
        return;
    }

    // 确认导入
    layer.confirm('确定要执行数据读取吗？此操作将删除指定年度的现有数据！', {
        btn: ['确定', '取消'],
        icon: 3,
        title: '确认操作'
    }, function(index) {
        layer.close(index);
        
        // 显示加载层
        var loadIndex = layer.load(2, {shade: [0.3, '#000']});
        
        $.ajax({
            url: baselocation + '/yearParamSet/departmentalUnitPrice/importData',
            type: 'POST',
            data: formData,
            success: function(result) {
                layer.close(loadIndex);
                if (result.code == 1) {
                    layer.msg(result.message || '数据读取成功', {icon: 1});
                    setTimeout(function() {
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }, 2000);
                } else {
                    layer.msg(result.message || '数据读取失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loadIndex);
                layer.msg('网络异常，请稍后重试', {icon: 2});
            }
        });
    });
}

// 关闭窗口
function closeWindow() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>
</body>
</html>
