package com.hongru.controller.system;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.system.Menu;
import com.hongru.service.system.IMenuService;

/**
 * 
* 类名称：SystemMenuController   
* 类描述：系统目录表示层控制器      
* 创建人：hongru   
* 创建时间：2017年4月7日 下午4:12:44   
*
 */
@Controller
@RequestMapping(value = "/system/menu")
public class SystemMenuController  extends BaseController {
	
	@Autowired
	private IMenuService menuService;
	
	/**
	 * GET 系统菜单
	 * @param model
	 * @return
	 */
	@GetMapping(value = "/view")
	public String list(Model model) {
		List<Menu> menus = menuService.list();
		model.addAttribute("menus", menus);
		return "/modules/system/system_menu_list";
	}
	
	/**
	 * PUT 显示/隐藏菜单
	 * @param menuId
	 * @return
	 */
//	@RequiresPermissions("system:menu:audit")
	@PutMapping(value = "/{menuId}/audit")
	@ResponseBody
	public Object audit(@PathVariable("menuId") Long menuId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			Integer count = menuService.updateStatus(menuId);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * DELETE 删除菜单
	 * @param menuId
	 * @return
	 */
	@RequiresPermissions("system:menu:delete")
	@DeleteMapping(value = "/{menuId}")
	@ResponseBody
	public Object delete(@PathVariable("menuId") Long menuId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			Integer count = menuService.deleteByMenuId(menuId);
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * GET 创建菜单页面
	 * @return
	 */
	@RequiresPermissions("system:menu:create")
	@GetMapping(value = "/{menuId}/create")
	public String getInsertPage(Model model, @PathVariable("menuId") Long menuId) {
		Menu parentMenu = menuService.getByMenuId(menuId);
		model.addAttribute("parentMenu", parentMenu);
		return "/modules/system/system_menu_add";
	}
	
	/**
	 * POST 创建菜单
	 * @return
	 */
	@RequiresPermissions("system:menu:create")
	@PostMapping(value = "")
	@ResponseBody
	public Object insert(Menu menu) {

		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			// 创建菜单及插入菜单目录记录
			Integer count = menuService.insertMenu(menu, authorizingUser.getUserName());
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * GET 更新菜单页面
	 * @return
	 */
	@RequiresPermissions("system:menu:edit")
	@GetMapping(value = "/{menuId}/edit")
	public String getUpdatePage(Model model, @PathVariable("menuId") Long menuId) {
		Menu menu = menuService.getByMenuId(menuId);
		model.addAttribute("menu", menu);

		Menu parentMenu = menuService.getByMenuId(menu.getParentId());
		model.addAttribute("parentMenu", parentMenu);

		return "/modules/system/system_menu_edit";
	}
	
	/**
	 * PUT 更新菜单信息
	 * @return
	 */
	@RequiresPermissions("system:menu:edit")
	@PostMapping(value = "/{menuId}")
	@ResponseBody
	public Object update(Menu menu, @PathVariable("menuId") Long menuId) {
		AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
		if (authorizingUser != null) {
			// 更新用户及菜单记录
			Integer count = menuService.updateMenu(menu, authorizingUser.getUserName());
			return new HrResult(CommonReturnCode.SUCCESS, count);
		} else {
			return new HrResult(CommonReturnCode.UNAUTHORIZED);
		}
	}
	
	/**
	 * 菜单树
	 * @param parentId
	 * @return
	 */
	@GetMapping(value = "tree/{parentId}")
	@ResponseBody
	public Object listMenuForTree(@PathVariable("parentId") Long parentId) {
		List<Menu> menus = null;
		try {
			menus = menuService.listMenuForTree(parentId);
		}catch(Exception e) {
			e.printStackTrace();
			return new HrResult(CommonReturnCode.FAILED,e.getMessage());
		}
		return new HrResult(CommonReturnCode.SUCCESS, menus);
	}
}
