package com.hongru.pojo.dto;

import com.hongru.entity.cost.ReservedRecycling;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class ReservedRecyclingDTO {

    private PageInfo pageInfo;

    private List<ReservedRecycling> reservedRecyclings;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<ReservedRecycling> getReservedRecyclings() {
        return reservedRecyclings;
    }

    public void setReservedRecyclings(List<ReservedRecycling> reservedRecyclings) {
        this.reservedRecyclings = reservedRecyclings;
    }

    public ReservedRecyclingDTO(PageInfo pageInfo, List<ReservedRecycling> reservedRecyclings) {
        this.pageInfo = pageInfo;
        this.reservedRecyclings = reservedRecyclings;
    }
}
