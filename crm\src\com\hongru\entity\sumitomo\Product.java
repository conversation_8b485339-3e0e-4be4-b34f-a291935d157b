package com.hongru.entity.sumitomo;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 产品表实体类 (sumitomo数据库)
 * 
 * <AUTHOR>
 */
@TableName("产品表")
public class Product {

    /* 产品代码 */
    @TableId(value = "产品代码", type = IdType.INPUT)
    protected String productCode;

    /* 产品信息 */
    @TableField("产品信息")
    protected String productInfo;

    /* 用户名 */
    @TableField("用户名")
    protected String userName;

    /* 平角线尺寸 */
    @TableField("平角线尺寸")
    protected String flatWireSize;

    // Getters and Setters
    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(String productInfo) {
        this.productInfo = productInfo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFlatWireSize() {
        return flatWireSize;
    }

    public void setFlatWireSize(String flatWireSize) {
        this.flatWireSize = flatWireSize;
    }
}
