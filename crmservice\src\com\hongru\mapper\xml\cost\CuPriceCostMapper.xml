<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.cost.CuPriceCostMapper">
    <sql id="cuPriceCost_sql">
		pc.[流水号] AS costId,pc.[状态] AS state,pc.[年月] AS yearMonth,pc.[供应商编码] AS supplierCode,
		pc.[供应商名称] AS supplierName,pc.[区分] AS lineType,pc.[铜购入量] AS purchasNum,pc.[铜加工费单价] AS procePrice,
		pc.[升水] AS ascendingWater,pc.[汇率] AS exchangeRate,pc.[年] AS year,pc.[月] AS month,
		pc.[铜单价] AS cuUnitPrice,pc.[综合加工费] AS comProcePrice,
		pc.[创建人标识] AS creatorId,pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[最后修改人标识] AS lastModifierId,pc.[最后修改人姓名] AS lastModifierName,pc.[最后修改时间] AS lastModifiedTime
	</sql>

	<insert id="insertCuPriceCost" parameterType="com.hongru.entity.cost.CuPriceCost">
		INSERT INTO [CostPrice].[dbo].[铜加工費用明细表]
		(
		[状态],
		[年月],
		[供应商编码],
		[供应商名称],
		[区分],
		[铜购入量],
		[铜加工费单价],
		[升水],
		[汇率],
		[年],
		[月],
		[铜单价],
		[综合加工费],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[最后修改人标识],
		[最后修改人姓名],
		[最后修改时间]
		)VALUES(
		#{cuPriceCost.state},
		#{cuPriceCost.yearMonth},
		#{cuPriceCost.supplierCode},
		#{cuPriceCost.supplierName},
		#{cuPriceCost.lineType},
		#{cuPriceCost.purchasNum},
		#{cuPriceCost.procePrice},
		#{cuPriceCost.ascendingWater},
		#{cuPriceCost.exchangeRate},
		#{cuPriceCost.year},
		#{cuPriceCost.month},
		#{cuPriceCost.cuUnitPrice},
		#{cuPriceCost.comProcePrice},
		#{cuPriceCost.creatorId},
		#{cuPriceCost.creatorName},
		#{cuPriceCost.createdTime},
		#{cuPriceCost.lastModifierId},
		#{cuPriceCost.lastModifierName},
		#{cuPriceCost.lastModifiedTime}
		)
	</insert>

	<select id="selectByCostId" resultType="com.hongru.entity.cost.CuPriceCost">
		SELECT
		<include refid="cuPriceCost_sql"/>
		FROM [CostPrice].[dbo].[铜加工費用明细表] pc
		<where>
			pc.[状态] != 9
			<if test="costId != null">
				AND pc.[流水号] = #{costId}
			</if>
		</where>
	</select>

	<select id="selectByYearMonth" resultType="com.hongru.entity.cost.CuPriceCost">
		SELECT
		TOP 1
		<include refid="cuPriceCost_sql"/>
		FROM [CostPrice].[dbo].[铜加工費用明细表] pc
		<where>
			pc.[状态] != 9
			<if test="supplierCode != null and supplierCode != ''">
				AND pc.[供应商编码] = #{supplierCode}
			</if>
			<if test="yearMonth != null and yearMonth != ''">
				AND pc.[年月] = #{yearMonth}
			</if>
			<if test="lineType != null">
				AND pc.[区分] = #{lineType}
			</if>
		</where>
	</select>

	<select id="listCostPricePage" resultType="com.hongru.entity.cost.CuPriceCost">
		SELECT
		<include refid="cuPriceCost_sql"/>
		FROM [CostPrice].[dbo].[铜加工費用明细表] pc
		<where>
			pc.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND pc.[年月] = #{yearMonth}
			</if>
			<if test="lineType != null and lineType != 0">
				AND pc.[区分] = #{lineType}
			</if>
		</where>
		<if test="pageInfo.sort != null">
			ORDER BY ${pageInfo.sort} ${pageInfo.order}
		</if>
		<if test="pageInfo.sort == null">
			ORDER BY pc.[年月] DESC,pc.[区分] ASC
		</if>
		<if test="pageInfo.limit != null">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="listCostPrice" resultType="com.hongru.entity.cost.CuPriceCost">
		SELECT
		<include refid="cuPriceCost_sql"/>
		FROM [CostPrice].[dbo].[铜加工費用明细表] pc
		<where>
			pc.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND pc.[年月] = #{yearMonth}
			</if>
			<if test="lineType != null and lineType != 0">
				AND pc.[区分] = #{lineType}
			</if>
		</where>
		ORDER BY pc.[供应商编码] ASC,pc.[流水号] DESC
	</select>

	<select id="listCostPricePageCount" resultType="integer">
		SELECT
		COUNT(1)
		FROM [CostPrice].[dbo].[铜加工費用明细表] pc
		<where>
			pc.[状态] != 9
			<if test="yearMonth != null and yearMonth != ''">
				AND pc.[年月] = #{yearMonth}
			</if>
			<if test="lineType != null and lineType != 0">
				AND pc.[区分] = #{lineType}
			</if>
		</where>
	</select>

	<update id="updateCostInfo">
		UPDATE [CostPrice].[dbo].[铜加工費用明细表]
		<set>
			<if test="cuPriceCost.yearMonth != null and cuPriceCost.yearMonth != ''">
				[年月] = #{cuPriceCost.yearMonth},
			</if>
			<if test="cuPriceCost.supplierCode != null and cuPriceCost.supplierCode != ''">
				[供应商编码] = #{cuPriceCost.supplierCode},
			</if>
			<if test="cuPriceCost.supplierName != null and cuPriceCost.supplierName != ''">
				[供应商名称] = #{cuPriceCost.supplierName},
			</if>
			<if test="cuPriceCost.lineType != null">
				[区分] = #{cuPriceCost.lineType},
			</if>
			<if test="cuPriceCost.purchasNum != null">
				[铜购入量] = #{cuPriceCost.purchasNum},
			</if>
			<if test="cuPriceCost.procePrice != null">
				[铜加工费单价] = #{cuPriceCost.procePrice},
			</if>
			<if test="cuPriceCost.ascendingWater != null">
				[升水] = #{cuPriceCost.ascendingWater},
			</if>
			<if test="cuPriceCost.exchangeRate != null">
				[汇率] = #{cuPriceCost.exchangeRate},
			</if>
			<if test="cuPriceCost.cuUnitPrice != null">
				[铜单价] = #{cuPriceCost.cuUnitPrice},
			</if>
			<if test="cuPriceCost.comProcePrice != null">
				[综合加工费] = #{cuPriceCost.comProcePrice},
			</if>
			<if test="cuPriceCost.year != null">
				[年] = #{cuPriceCost.year},
			</if>
			<if test="cuPriceCost.month != null">
				[月] = #{cuPriceCost.month},
			</if>
			<if test="cuPriceCost.lastModifierId != null">
				[最后修改人标识] = #{cuPriceCost.lastModifierId},
			</if>
			<if test="cuPriceCost.lastModifierName != null and cuPriceCost.lastModifierName != ''">
				[最后修改人姓名] = #{cuPriceCost.month},
			</if>
			<if test="cuPriceCost.lastModifiedTime != null">
				[最后修改时间] = #{cuPriceCost.lastModifiedTime},
			</if>
		</set>
		WHERE [流水号] = #{cuPriceCost.costId}
	</update>

	<update id="updateState">
		UPDATE [CostPrice].[dbo].[铜加工費用明细表]
		SET [状态] = #{state}
		WHERE [流水号] = #{costId}
	</update>

	<select id="selectCuPriceCostByYear" resultType="com.hongru.entity.cost.CuPriceCost">
		SELECT
		<include refid="cuPriceCost_sql"/>
		FROM [CostPrice].[dbo].[铜加工費用明细表] pc
		WHERE pc.[状态] != 9 AND pc.[年] = #{year}
		ORDER BY pc.[供应商编码] ASC, pc.[区分] ASC
	</select>

	<delete id="deleteCuPriceCostByYear">
		DELETE FROM [CostPrice].[dbo].[铜加工費用明细表]
		WHERE [年] = #{year}
	</delete>

	<insert id="batchInsertCuPriceCost" parameterType="java.util.List">
		INSERT INTO [CostPrice].[dbo].[铜加工費用明细表]
		(
		[状态],
		[年月],
		[供应商编码],
		[供应商名称],
		[区分],
		[铜购入量],
		[铜加工费单价],
		[升水],
		[汇率],
		[铜单价],
		[综合加工费],
		[年],
		[月],
		[创建人标识],
		[创建人姓名],
		[创建时间],
		[最后修改人标识],
		[最后修改人姓名],
		[最后修改时间]
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		(
		#{item.state},
		#{item.yearMonth},
		#{item.supplierCode},
		#{item.supplierName},
		#{item.lineType},
		#{item.purchasNum},
		#{item.procePrice},
		#{item.ascendingWater},
		#{item.exchangeRate},
		#{item.cuUnitPrice},
		#{item.comProcePrice},
		#{item.year},
		#{item.month},
		#{item.creatorId},
		#{item.creatorName},
		#{item.createdTime},
		#{item.lastModifierId},
		#{item.lastModifierName},
		#{item.lastModifiedTime}
		)
		</foreach>
	</insert>

</mapper>