package com.hongru.common;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 
* 类名称：SysLog   
* 类描述：SysLog 系统日志注解   
* 创建人：hongru   
* 创建时间：2017-10-25 上午11:01:40   
*
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SysLog {

	boolean isLog() default true;
	
}
